package com.boot.modules.openApi.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.openApi.entity.EdcReqLogEntity;

public interface EdcReqLogService extends IService<EdcReqLogEntity> {

    
    /**
     * 获取指定项目与empi列表最新同步信息
     * @param projectId
     * @param empiList
     * @return
     */
    Map<String, EdcReqLogEntity> getByEmpiList(Long projectId, List<String> empiList);
}
