package com.boot.modules.project.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @desc : 样本库需要返回的项目信息Vo对象
 * @create 2021-11-29
 */
@Data
@Accessors(chain = true)
public class ProjectInfoVo {

    private Long id;

    /**
     * 子项目名称
     */
    private String projectName;

    /**
     * 项目管理员id
     */
    private Long projectAdminId;

    /**
     * 项目管理员昵称
     */
    private String nickName;

    private String projectAdminMemberId;

    /**
     * 子项目的创建时间
     */
    private String createTime;

    /**
     * 子项目状态
     */
    private Integer status;

    private Long parentId;

    /**
     * 当前项目所有参与的用户的his工号
     */
    private List<String> memberIds;

}
