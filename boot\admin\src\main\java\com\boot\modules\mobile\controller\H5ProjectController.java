package com.boot.modules.mobile.controller;

import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.service.GroupConfigService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 移动端项目调用接口
 */
@Api(tags = "移动端项目调用接口")
@RestController
@RequestMapping("/h5/project")
@Slf4j
public class H5ProjectController extends AbstractController {

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private GroupConfigService groupConfigService;

    @ApiOperation(value = "获取项目", notes = "获取项目")
    @GetMapping({"/subProject/list"})
    public Result gcpProject(@RequestParam Map<String, Object> param) {
        Integer isHis = MapUtils.getValue(param, "isHis", Integer.class);
        if (isHis != null && isHis == 1) {
            PageUtils list = subProjectService.getHisRecProject(param, null);
            return R.success(list);
        } else {
            PageUtils list = subProjectService.getSubjectListByQuery(param, null);
            return R.success(list);
        }
    }

    @ApiOperation(value = "历史推荐项目", notes = "历史推荐项目")
    @GetMapping({"/history/subProject/list"})
    public Result hisProject(@RequestParam Map<String, Object> param) {
        Integer isHis = MapUtils.getValue(param, "isHis", Integer.class);
        PageUtils list = subProjectService.getHisRecProject(param, null);
        return R.success(list);
    }

    @GetMapping("/config-list")
    @ApiOperation(
            value = "查询指定项目医联体纳排管理配置列表(简略信息不返回表达式)",
            notes = "查询指定项目医联体纳排管理配置列表(简略信息不返回表达式)")
    public Result list(@RequestParam Long subProjectId,
                       @RequestParam(required = false) Integer type) {

        return R.success(groupConfigService.listYltVOBySubProjId(subProjectId, type));
    }
}
