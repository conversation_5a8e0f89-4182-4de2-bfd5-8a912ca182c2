package com.boot.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.project.entity.ProjDeptPermissionEntity;
import com.boot.modules.project.vo.ProjDeptPermissionVo;
import com.boot.modules.project.vo.ProjDeptsVo;
import com.boot.modules.sys.vo.UserDeptsVo;
import com.boot.modules.sys.vo.UserPermissionVo;

import java.util.List;

/**
 * <AUTHOR>
 * @desc :
 * @create 2022-12-05
 */
public interface ProjectDeptPermissionService extends IService<ProjDeptPermissionEntity> {

    /**
     * 获取子项目的医联体数据权限
     *
     * @param projectId
     * @return
     */
    List<ProjDeptPermissionVo> getProjectPermission(Long projectId);

    /**
     * 批量新增或者修改某个用户的机构权限
     *
     * @param vo
     */
    void saveOrUpdate(ProjDeptsVo vo);


}
