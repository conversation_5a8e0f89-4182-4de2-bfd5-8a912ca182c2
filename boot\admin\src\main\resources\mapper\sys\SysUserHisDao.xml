<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysUserHisDao">


    <select id="queryPage" resultType="com.boot.modules.sys.vo.UserHisForeignVo">
        SELECT
            a.*,
            b.nickname AS userNickname,
            c.`name` AS deptName,
            c.code AS deptCode
        FROM
            rp_sys_user_his a
                JOIN rp_sys_user b ON a.user_id = b.id
                JOIN rp_sys_dept c ON a.dept_id = c.id
            ${ew.customSqlSegment}
    </select>
    <select id="selectByQuery" resultType="com.boot.modules.sys.vo.UserHisForeignVo">
        SELECT
            a.*,
            b.nickname AS userNickname,
            c.`name` AS deptName,
            c.code AS deptCode
        FROM
            rp_sys_user_his a
                JOIN rp_sys_user b ON a.user_id = b.id
                JOIN rp_sys_dept c ON a.dept_id = c.id
            ${ew.customSqlSegment}

    </select>
    <select id="getByForeignAndDept" resultType="com.boot.modules.sys.entity.SysUserEntity">
        SELECT
            b.*, c.name as deptName
        FROM
            rp_sys_user_his a
                JOIN rp_sys_user b ON a.user_id = b.id
                LEFT JOIN rp_sys_dept c ON a.dept_id = c.id
            ${ew.customSqlSegment}
    </select>
</mapper>