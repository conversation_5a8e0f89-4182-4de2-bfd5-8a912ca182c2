package com.boot.modules.sys.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.vo.UserHisForeignVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 系统用户
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_user")
public class SysUserEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    @ExcelProperty(index = 0, value = "id")
    private Long id;

    /**
     * 用户名(登录账号)
     */
    @NotBlank(message = "用户名不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @ExcelProperty(index = 1, value = "username")
//    @Pattern(regexp = "[A-Za-z0-9_\\-]+", message = "用户名不能包含中文和特殊字符", groups = {AddGroup.class, UpdateGroup.class})
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空", groups = AddGroup.class)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @ExcelProperty(index = 2, value = "password")
    @Pattern(regexp = "^(?![0-9]+$)(?![a-zA-Z]+$)(?!([^(0-9a-zA-Z)]|[\\(\\)])+$)([^(0-9a-zA-Z)]|[\\(\\)]|[a-zA-Z]|[0-9]){6,16}$", message = "密码必须包含字母，数字，字符中的任意两种，长度在6到16位", groups = {AddGroup.class})
    private String password;

    /**
     * 用户昵称
     *
     * @ExcelProperty 的index 和 value 与数据库表结构顺序和字段名一致
     */
    @NotBlank(message = "用户昵称不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @ExcelProperty(index = 3, value = "nickname")
    private String nickname;


    /**
     * 盐
     */
    @ExcelProperty(index = 4, value = "salt")
    private String salt;

    /**
     * email
     */
    @ExcelProperty(index = 5, value = "email")
    private String email;

    /**
     * 电话
     */
    @ExcelProperty(index = 6, value = "mobile")
    private String mobile;

    /**
     * 部门ID
     */
    @NotNull(message = "部门不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @ExcelProperty(index = 8, value = "dept_id")
    private String deptId;

    /**
     * 创建时间
     */
    @ExcelProperty(index = 9, value = "create_time")
    private Date createTime;


    /**
     * 创建者ID
     */
    @ExcelProperty(index = 10, value = "create_user_id")
    private Long createUserId;

    /**
     * 过期时间
     */
    @ExcelProperty(index = 11, value = "expired_date")
    @NotNull(message = "过期时间不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Date expiredDate;

    /**
     * 关联其他系统id，如his工号
     */
    @ExcelProperty(index = 12, value = "foreign_id")
    @TableField(value = "foreign_id")
    private String foreignId;

    /**
     * 角色ID列表
     */
    @TableField(exist = false)
    private List<Long> roleIdList;

    @TableField(exist = false)
    private List<String> roleNameList;

    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * token
     */
    @TableField(exist = false)
    private String token;

    /**
     * 系统角色的首页url
     */
    @TableField(exist = false)
    private String roleIndexUrl;

    /**
     * 是否是系统管理员, 1. 是 0 否
     */
    @ApiModelProperty(hidden = true)
    @TableField(exist = false)
    private Integer isSysAdmin;

    /**
     * 是否超级管理员
     */
    @ApiModelProperty(hidden = true)
    @TableField(exist = false)
    private Integer isSuperAdmin;


    /**
     * 状态  0：禁用   1：正常
     * 0：锁住   1：正常
     */
    @ExcelProperty(index = 7, value = "status")
    private Integer status;

    /**
     * 十分钟内连续登录错误次数，十分钟内连续5次登录失败则锁定账号
     */
    @TableField(value = "login_error_count")
    private Integer loginErrorCount;

    /**
     * 第一次登录错误时间
     */
    @TableField(value = "first_error_login_time")
    private Date firstErrorLoginTime;

    /**
     * 是否开启微信公众号
     */
    @TableField(exist = false)
    private boolean enableWechat;

    /**
     * 是否开启导出审核
     */
    @TableField(exist = false)
    private boolean enableExportAudit;

    /**
     * 是否开启样本库
     */
    @TableField(exist = false)
    private boolean enableSample;

    /**
     * 用户的his工号
     */
    @TableField(exist = false)
    private UserHisForeignVo[] userHisList;

    private String caUserId;

    /**
     * 設置empi字段，后续获取患者填写表单需要
     */
    @TableField(exist = false)
    private String empi;

    @TableField(exist = false)
    private Long subProjPatientId;

    /**
     * 科室管理员医疗单元，用户角色是科室管理者时需要填写
     */
    private String adminDeptId;

    /**
     * 医联体新增字段
     * 医院ID
     */
    @TableField(exist = false)
    private Long medId;

    /**
     * 受试者体新增字段
     * 医生工号
     */
    @TableField(exist = false)
    private String doctorCode;

    /**
     * 受试者新增字段
     * 医疗机构编码
     */
    @TableField(exist = false)
    private String medCode;

    /**
     * 受试者新增字段
     * 角色编码返回
     */
    @TableField(exist = false)
    private String code;
}
