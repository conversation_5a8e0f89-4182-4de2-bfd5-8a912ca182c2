package com.boot.commons.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * HMACSha256加密工具
 *
 * <AUTHOR>
 * */
public class HmacSha256Util {

    /**
     * @param k byte数组类型，密钥（安全码）
     * @param s String类型，签名算法，默认 HmacSHA256
     * @param data String类型，需要加密的字符串
     * @return byte
     * */
    public static byte[] getHmacSha256(byte[] k,String s,String data){

        String type = "HMAC";
        if(type.equals(s)){
            s = "HmacSHA256";
        }
        System.out.println(data);
        SecretKeySpec signKey = new SecretKeySpec(k, s);
        Mac mac;
        byte[] stringSignTemp = null;
        try {
            mac = Mac.getInstance(s);
            mac.init(signKey);
            stringSignTemp = mac.doFinal(data.getBytes());

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return stringSignTemp ;
    }
}
