package com.boot.modules.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@ApiModel(value = "患者")
public class PatientHytDto  implements Serializable {

    /**
     * 患者名称
     */
    @ApiModelProperty(value = "患者名称", required = true)
    @NotBlank(message = "患者名称不能为空")
    private String name;

    /**
     * 证据类型
     */
    @ApiModelProperty(value = "证据类型", required = true)
    private Integer cardType;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号", required = true)
    private String cardNo;

    /**
     * 患者电话号码
     */
    @ApiModelProperty(value = "患者电话号码")
    private String phone;

    /**
     * 患者性别, 1 男， 2，女， 3， 不确定
     */
    @ApiModelProperty(value = "患者性别, 0 男， 1，女， 2， 不确定", required = true)
    private Integer gender;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族", required = true)
    private String nation;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String birthday;

    /**
     * 患者入径时间， 具体到时分秒
     */
    @ApiModelProperty(value = "患者入径时间", required = true)
    private String joinDatetime;

    /**
     * 入径操作用户id
     */
    @ApiModelProperty(value = "入径的用户ID", required = true)
    @NotNull(message = "入径用户不能为空")
    private Long joinUserId;

    /**
     * 入径的项目id
     */
    @ApiModelProperty(value = "入径的项目ID", required = true)
    @NotNull(message = "入径项目ID不能为空")
    private Long projectId;

    /**
     * 入径的医院id
     */
    @ApiModelProperty(value = "入径的医院ID", required = true)
    @NotNull(message = "入径医院不能为空")
    private Long medId;


    /**
     * 医生工号
     */
    @ApiModelProperty(value = "医生工号", required = true)
    private String doctorCode;

    /**
     * 医生姓名
     */
    @ApiModelProperty(value = "医生姓名", required = true)
    private String doctorName;

    /**
     * 医疗机构编码
     */
    @ApiModelProperty(value = "医疗机构编码", required = true)
    private String medCode;




}
