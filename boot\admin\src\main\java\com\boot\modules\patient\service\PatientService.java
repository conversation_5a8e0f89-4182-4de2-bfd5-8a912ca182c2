package com.boot.modules.patient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.patient.entity.PatImportRecordEntity;
import com.boot.modules.patient.entity.PatientEntity;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PatientService extends IService<PatientEntity> {
    /**
     * 批量导入到推荐池
     *
     * @param empiList empi集合
     */
    void intoGroup(List<String> empiList, Map<String, String> empiRegMap, Map<String, String> nationMap);

    /**
     * 获取项目对应受试者id
     *
     * @return
     */
    PageUtils listBySubProjectId(Map<String, Object> params);

    /**
     * 获取用户参与过的项目的受试者信息
     * @param params
     * @return
     */
    PageUtils listByUserId(Map<String, Object> params);

    /**
     * his获取已推荐患者
     *
     * @param params
     * @return
     */
    PageUtils hisList(Map<String, Object> params);

    void recommendPatSort(Long subProjectId);

    /**
     * excel导入病种
     *
     * @param file 文件
     * @return
     */
    void excelImport(MultipartFile file, PatImportRecordEntity patImportRecordEntity, Long projectId, Long subProjectId) throws IOException;

    /**
     * report获取已推荐患者
     *
     * @param params
     * @return
     */
    PageUtils patList(Map<String, Object> params);

    /**
     * 同步项目病例
     * @param projectId
     */
    void sync(Long projectId, List<String> empiList);

    /**
     * 单个移动端入组到推荐池
     * @param mobilePatientDTO
     */
    void intoGroupByMobile(MobilePatientDTO mobilePatientDTO, String empiid);

    /**
     * 对移动端患者信息同步
     */
    void resetInfo();
}
