package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 医联体患者自定义勾选纳排情况
 */
@Data
@TableName("rp_pat_custom_record")
public class PatCustomRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 患者EMPI
     */
    private String empiid;

    /**
     * 条件ID
     */
    private Long groupConfigId;


}
