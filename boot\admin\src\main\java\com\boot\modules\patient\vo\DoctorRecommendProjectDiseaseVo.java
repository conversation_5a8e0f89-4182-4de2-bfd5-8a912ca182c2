package com.boot.modules.patient.vo;

import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DoctorRecommendProjectDiseaseVo extends DoctorR<PERSON>ommendationEntity {
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 病种名称
     */
    private String diseaseName;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * PI
     */
    private String pi;

    /**
     * 患者状态：0-未锁定，1-锁定，默认值为0
     */
    private Integer patientStatus;

    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 民族
     */
    private String nation;
    /**
     * 性别
     */
    private Integer gender;

    private String outReason;
    /**
     * 入组人
     */
    private String inboundUserName;
}
