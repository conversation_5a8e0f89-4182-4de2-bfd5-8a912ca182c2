<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.ProjDeptPermissionDao">
    <cache-ref namespace="com.boot.modules.project.dao.ProjDeptPermissionDao"/>
    <select id="getProjectPermission" resultType="com.boot.modules.project.vo.ProjDeptPermissionVo">
        SELECT
            a.*,
            b.`name` as deptName
        FROM
            rp_project_dept_permission a
                LEFT JOIN rp_sys_dept b ON a.dept_id = b.id
            ${ew.customSqlSegment}
    </select>

</mapper>
