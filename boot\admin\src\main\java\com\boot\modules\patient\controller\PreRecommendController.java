package com.boot.modules.patient.controller;


import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.modules.patient.service.PreRecommendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Api(tags = "预推荐管理")
@RestController
@RequestMapping("/pre/recommend")
public class PreRecommendController {
    @Resource
    private PreRecommendService preRecommendService;

    @GetMapping("/count/{projectId}/{subProjectId}/{type}")
    @ApiOperation(
            value = "推荐列表（今日、全部）漏斗筛选数量",
            notes = "推荐列表（今日、全部）漏斗筛选数量")
    public Result count(@PathVariable long projectId,
                        @PathVariable long subProjectId,
                        @PathVariable int type,
                        @RequestParam Map<String, Object> params
    ) {
        return R.success(preRecommendService.getCount(projectId, subProjectId, type, params));
    }

    @PostMapping("/list")
    @ApiOperation(
            value = "推荐列表（今日、全部）漏斗筛选病例列表",
            notes = "推荐列表（今日、全部）漏斗筛选病例列表")
    public Result list(@RequestBody Map<String, Object> params) {
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        return R.success(preRecommendService.getByPage(projectId, subProjectId, type, params));
    }
}
