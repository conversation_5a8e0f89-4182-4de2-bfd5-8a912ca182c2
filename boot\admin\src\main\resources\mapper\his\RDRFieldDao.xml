<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.external.dao.RDRFieldDao">

    <select id="getByTableName" resultType="com.boot.modules.external.entity.RDRFieldEntity">
        SELECT b.*
        FROM rp_rdr_table a
            LEFT JOIN rp_rdr_field b ON a.id = b.table_id
        WHERE a.is_show = 1
            AND a.kl_table_name = #{tableName}
    </select>
</mapper>
