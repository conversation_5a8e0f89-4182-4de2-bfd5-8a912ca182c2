package com.boot.modules.patient.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 医联体报表对象
 */
@Data
public class IcoReportDto implements Serializable {

    /**
     * 机构Id
     */
    private Long medId;

    /**
     * 机构名称（医联体单位）
     */
    private String medName;

    /**
     * 总项目数（开通医联体权限的项目数）
     */
    private Integer allProject = 0;

    /**
     * 参与推荐项目数
     */
    private Integer totalProject = 0;

    /**
     * 新增参与推荐项目数
     */
    private Integer addProject = 0;

    /**
     * 累计推荐入组人数
     */
    private Integer totalRecommend = 0;

    /**
     * 新增推荐入组人数
     */
    private Integer addRecommend = 0;

    /**
     * 累计实际入组人数
     */
    private Integer totalInbound = 0;

    /**
     * 新增实际入组人数
     */
    private Integer addInbound = 0;
}
