package com.boot.modules.external.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.external.dao.RDRFieldDao;
import com.boot.modules.external.entity.RDRFieldEntity;
import com.boot.modules.external.service.RDRFieldService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class RDRFieldServiceImpl extends ServiceImpl<RDRFieldDao, RDRFieldEntity> implements RDRFieldService {

    @Override
    public List<RDRFieldEntity> getByTableName(String tableName){
        return baseMapper.getByTableName(tableName);
    }
}
