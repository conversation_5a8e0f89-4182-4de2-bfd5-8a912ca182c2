package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.modules.project.dao.ProjDeptDao;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.service.ProjDeptService;
import com.boot.modules.project.vo.ProjDeptVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @desc :项目中心
 * @create 2021-05-20
 */
@Service
public class ProjDeptServiceImpl extends ServiceImpl<ProjDeptDao, ProjDeptEntity> implements ProjDeptService {
    /**
     * 根据项目id获取项目中心数据
     *
     * @param projectId
     * @return
     */
    @Override
    public List<ProjDeptEntity> getByProjectId(Long projectId) {

        if (projectId == null) {
            return null;
        }
        QueryWrapper<ProjDeptEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjDeptEntity::getProjectId, projectId);
        List<ProjDeptEntity> list = this.list(qw);

        return list;

    }

    @Override
    public List<ProjDeptVo> getByQuery(Long projectId) {

        if (projectId == null) {
            return null;
        }
        QueryWrapper<ProjDeptEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjDeptEntity::getProjectId, projectId);
        List<ProjDeptVo> list = baseMapper.getByQuery(qw);

        return list;

    }

    /**
     * 通过subProjectId 查
     *
     * @param subProjectId
     * @return
     */
    @Override
    public List<ProjDeptEntity> getBySubProjectId(Long subProjectId) {

        if (subProjectId == null) {
            return null;
        }
        QueryWrapper<ProjDeptEntity> qw = new QueryWrapper<>();
        qw.eq("c.id", subProjectId);
        List<ProjDeptEntity> list = baseMapper.getBySubProjectId(qw);
        return list;
    }
}
