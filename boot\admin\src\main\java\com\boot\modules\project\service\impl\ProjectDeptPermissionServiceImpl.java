package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.ListUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.project.dao.ProjDeptPermissionDao;
import com.boot.modules.project.entity.ProjDeptPermissionEntity;
import com.boot.modules.project.service.ProjectDeptPermissionService;
import com.boot.modules.project.vo.ProjDeptPermissionVo;
import com.boot.modules.project.vo.ProjDeptsVo;
import com.boot.modules.sys.vo.DeptModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc :
 * @create 2022-12-05
 */
@Service
public class ProjectDeptPermissionServiceImpl extends ServiceImpl<ProjDeptPermissionDao, ProjDeptPermissionEntity> implements ProjectDeptPermissionService {
    /**
     * 获取项目的医联体数据权限
     *
     * @param projectId
     * @return
     */
    @Override
    public List<ProjDeptPermissionVo> getProjectPermission(Long projectId) {
        QueryWrapper<ProjDeptPermissionVo> qw = new QueryWrapper<>();
        qw.eq("a.project_id", projectId);
        List<ProjDeptPermissionVo> list = baseMapper.getProjectPermission(qw);

        if (!CollectionUtils.isEmpty(list)) {
            List<ProjDeptPermissionVo> result = new ArrayList<>();
            for (ProjDeptPermissionVo prjoectPermissionVo : list) {
                if (StringUtils.isNotBlank(prjoectPermissionVo.getDeptCode())) {
                    result.add(prjoectPermissionVo);
                }
            }
            return result;
        }

        return list;
    }

    /**
     * 批量新增或者修改某个子项目的机构权限
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(ProjDeptsVo vo) {
        boolean res = true;

        if (vo == null) {
            return;
        }

        List<ProjDeptPermissionEntity> list = new ArrayList<>();
        for (DeptModel deptModel : vo.getDeptModels()) {
            ProjDeptPermissionEntity projDeptEntity = new ProjDeptPermissionEntity();
            projDeptEntity.setProjectId(vo.getProjectId());
            projDeptEntity.setDeptId(deptModel.getDeptId());
            projDeptEntity.setDeptCode(deptModel.getDeptCode());
            list.add(projDeptEntity);
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("请至少选择一个机构");
        }
        List<ProjDeptPermissionEntity> oldProjDeptList = this.list(
                new QueryWrapper<ProjDeptPermissionEntity>().lambda().eq(ProjDeptPermissionEntity::getProjectId, vo.getProjectId())
        );

        if (CollectionUtils.isEmpty(oldProjDeptList)) {
            res = this.saveBatch(list);
        } else {

            List<Long> newDeptIds = list.stream().map(ProjDeptPermissionEntity::getDeptId).collect(Collectors.toList());
            List<Long> oldDeptIds = oldProjDeptList.stream().map(ProjDeptPermissionEntity::getDeptId).collect(Collectors.toList());

            List<Long> reduceDeptIdListThanList = ListUtils.getReduceListThanList(newDeptIds, oldDeptIds);
            List<Long> addDeptIdListThanList = ListUtils.getAddListThanList(newDeptIds, oldDeptIds);

            if (!CollectionUtils.isEmpty(reduceDeptIdListThanList)) {
                List<Long> reduceList = new ArrayList<>();
                for (Long deptId : reduceDeptIdListThanList) {
                    for (ProjDeptPermissionEntity projDeptEntity : oldProjDeptList) {
                        if (deptId.equals(projDeptEntity.getDeptId())) {
                            reduceList.add(projDeptEntity.getId());
                            break;
                        }
                    }
                }
                res = res && this.removeByIds(reduceList);
            }

            if (!CollectionUtils.isEmpty(addDeptIdListThanList)) {
                List<ProjDeptPermissionEntity> addListThanList = new ArrayList<>();
                for (Long deptId : addDeptIdListThanList) {
                    for (ProjDeptPermissionEntity projDeptEntity : list) {
                        if (deptId.equals(projDeptEntity.getDeptId())) {
                            addListThanList.add(projDeptEntity);
                            break;
                        }
                    }
                }
                res = res && this.saveBatch(addListThanList);
            }
        }
        if (!res) {
            throw new BusinessException("修改失败");
        }
    }
}
