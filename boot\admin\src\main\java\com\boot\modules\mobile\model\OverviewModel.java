package com.boot.modules.mobile.model;

import lombok.Data;

/**
 * 驾驶舱-数据总览
 */
@Data
public class OverviewModel {
//    /**
//     * 推荐状态 1已推荐 2-拟入组 3-已入组 4-异常退出 5-正常退出 6-推荐超过30天后回退 7-退回
//     * 推荐数  rp_doctor_recommendation type =2
//     */
//    private Integer recommendCount;
//    /**
//     * 接收数  rp_doctor_recommendation type =2 and status in (2,3,5)
//     */
//    private Integer acceptCount;
//    /**
//     * 入组数 rp_doctor_recommendation type =2 and status in (3,5)
//     */
//    private Integer inboundCount;

    /**
     * 项目数
     */
    private Integer projectCount;
    /**
     * 推荐状态 1已推荐 2-拟入组 3-已入组 4-异常退出 5-正常退出 6-推荐超过30天后回退 7-退回
     * 推荐数  rp_doctor_recommendation
     */
    private Integer recommendCount;
    /**
     * 入组数 rp_doctor_recommendation status in (3,5)
     */
    private Integer inboundCount;
}
