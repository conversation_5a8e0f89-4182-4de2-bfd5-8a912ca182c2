package com.boot.commons.enums;

/**
 * 外部接口数据来源系统枚举
 *
 * <AUTHOR>
 * @create
 */
public enum ExternalSystemEnum {

    /**
     * his系统
     */
    HIS(1, "his", "his系统"),

    /**
     * 探索系统
     */
    CSMSEARCH(2, "csmsearch", "探索系统"),

    /**
     * RDR系统
     */
    RDR(2, "rdr", "探索系统");

    private int type;

    private String code;

    private String desc;

    ExternalSystemEnum(int type, String code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}


