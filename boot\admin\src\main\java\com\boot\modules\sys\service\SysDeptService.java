package com.boot.modules.sys.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 部门管理
 *
 * <AUTHOR>
 */
public interface SysDeptService extends IService<SysDeptEntity> {

    /**
     * 获取所有机构信息
     *
     * @return
     */
    List<SysDeptEntity> getAll();

    List<SysDeptEntity> queryList(Map<String, Object> map);

    /**
     * 查询子部门ID列表
     *
     * @param parentId 上级部门ID
     */
    List<Long> queryDeptIdList(Long parentId);

    /**
     * 获取当前部门及全部子部门ID，用于数据过滤
     */
    List<Long> getSubDeptIdList(Long deptId);

    /**
     * 获取当前部门及全部子部门ID
     */
    List<Long> getSubDeptIdList(List<SysDeptEntity> allDeptList, Long deptId);

    /**
     * 获取当前部门及全部子部门ID，用于数据过滤
     */
    List<Long> getSubDeptIdList(List<Long> deptIds);

    /**
     * 只展示设为可以作为研究中心的机构
     *
     * @param
     * @return
     */
    List<SysDeptEntity> queryListShowResearchCenter();

    /**
     * 获取用户所属部门
     *
     * @param userId
     * @return
     */
    SysDeptEntity getByUserId(Long userId);

    /**
     * 获取所属机构
     * 机构的最上层机构
     *
     * @param code
     * @return
     */
    SysDeptEntity getTopDeptByCode(String code);

    List<SysDeptEntity> getParentDeptAndCodeNotNull();

    /**
     * 新增机构  如果排序为0  则设置为最大的
     *
     * @param dept
     * @return
     */
    boolean addDept(SysDeptEntity dept);

    boolean updateDept(SysDeptEntity dept);

    List<SysDeptEntity> queryDataCenter();

    SysDeptEntity getTopDeptById(List<SysDeptEntity> deptEntities, Long deptId);

    List<SysDeptEntity> getSubDeptList(List<Long> deptIds);

    /**
     * 统计相关结构项目病种关联数据
     *
     * @param deptName
     * @return
     */
    List<ReportDto> listCount(List<Long> deptIdList, String deptName, Date startTime, Date endTime);

    Long getIdByCode(String code);

    List<SysDeptEntity> queryList(Long topDeptId, List<SysUserEntity> sysUserEntityList, List<Long> deptIds, Integer isAll);

    List<SysDeptEntity> getParentDept(List<SysDeptEntity> deptEntities, List<SysDeptEntity> parentDeptEntityList, Long deptId);

    /**
     * 获取结构所属的所有医院id
     * @param deptIdList
     * @return
     */
    List<Long> getAllHosId(List<Long> deptIdList);

    /**
     * 获取所有医疗单元id
     * @return
     */
    List<Long> getAllMedicalUnitId();
}
