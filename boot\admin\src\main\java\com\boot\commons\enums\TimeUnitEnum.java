package com.boot.commons.enums;


/**
 * 时间单位枚举类
 * 参考java.util.concurrent.TimeUnit;
 * <AUTHOR>
 */
public enum TimeUnitEnum {
    /**
     * 年
     */
    YEAR{
        @Override
        public long toNanos(long d)   { return d; }
        @Override
        public long convert(long d, TimeUnitEnum u) { return u.toNanos(d); }
    },
    /**
     * 月
     */
    MONTH{
        @Override
        public long toNanos(long d)   { return d; }
        @Override
        public long convert(long d, TimeUnitEnum u) { return u.toMonths(d); }
    },
    /**
     * 周
     */
    WEEK{
        @Override
        public long toNanos(long d)   { return d; }
        @Override
        public long toDays(long d)    { return d/(C1/C0); }
        @Override
        public long toWeeks(long d)    { return d; }
        @Override
        public long toMonths(long d)    { return d/(C2/C0); }
        @Override
        public long toYears(long d)    { return d/(C3/C0); }
        @Override
        public long convert(long d, TimeUnitEnum u) { return u.toWeeks(d); }
    },
    /**
     * 日
     */
    DAYS{
        @Override
        public long toNanos(long d)   { return d; }
        @Override
        public long toDays(long d)    { return d; }
        @Override
        public long toWeeks(long d)    { return d/(C1/C0); }
        @Override
        public long toMonths(long d)    { return d/(C2/C0); }
        @Override
        public long toYears(long d)    { return d/(C3/C0); }
        @Override
        public long convert(long d, TimeUnitEnum u) { return u.toDays(d); }
    };

    static final long C0 = 1L;
    static final long C1 = C0 * 7L;
    static final long C2 = C0 * 30L;
    static final long C3 = C0 * 365L;

    static final long MAX = Long.MAX_VALUE;

    static long x(long d, long m, long over) {
        if (d >  over) {
            return Long.MAX_VALUE;
        }
        if (d < -over) {
            return Long.MIN_VALUE;
        }
        return d * m;
    }

    public long convert(long sourceDuration, TimeUnitEnum sourceUnit) {
        throw new AbstractMethodError();
    }
    public long toNanos(long duration) {
        throw new AbstractMethodError();
    }
    public long toDays(long duration) {
        throw new AbstractMethodError();
    }
    public long toWeeks(long duration) {
        throw new AbstractMethodError();
    }

    public long toMonths(long duration) {
        throw new AbstractMethodError();
    }
    public long toYears(long duration) {
        throw new AbstractMethodError();
    }
}
