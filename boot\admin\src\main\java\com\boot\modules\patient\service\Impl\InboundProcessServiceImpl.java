package com.boot.modules.patient.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.constant.HxConst;
import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.ObjectUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.openApi.service.EdcService;
import com.boot.modules.patient.dao.InboundProcessDao;
import com.boot.modules.patient.dto.ExitDto;
import com.boot.modules.patient.dto.PersonalReportDto;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.patient.vo.InboundProjectDieaseVo;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.project.vo.TotalProjectVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.boot.commons.enums.PatStatusEnum.LOCK;
import static com.boot.commons.enums.PatStatusEnum.UNLOCK;
import static com.boot.commons.enums.PatTypeEnum.*;

@Service
public class InboundProcessServiceImpl
        extends ServiceImpl<InboundProcessDao, InboundProcessEntity>
        implements InboundProcessService {
    /**
     * 默认未签署知情同意书过期时间为3
     */
    private static String NO_SIGN_EXIT_REASON = "未按时签署知情同意书";

    @Resource
    private ProjectService projectService;

    @Resource
    @Lazy
    private PatientService patientService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private EdcService edcService;

    @Resource
    private SubProjectService subProjectService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inbound(Long id, String empi, Long projectId, Long subProjectId, Long userId) {
        // 1.查询患者是否已入组
        List<InboundProjectDieaseVo> lockList = getInboundProject(empi, true);
        if (!CollectionUtils.isEmpty(lockList)) {
            throw new BusinessException("患者已加入GCP项目或干预性IIT项目，不能重复入组。");
        }
        // 判断项目是否为干预性项目
        ProjectEntity project = projectService.getById(projectId);
        Boolean isLockPrj = isLockProject(project);
        // 2.新增受试者入组对象
        InboundProcessEntity inboundProcessEntity = new InboundProcessEntity();
        inboundProcessEntity.setInboundTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        inboundProcessEntity.setInboundId(userId);
        inboundProcessEntity.setEmpiid(empi);
        inboundProcessEntity.setProjectId(projectId);
        inboundProcessEntity.setSubProjectId(subProjectId);
        inboundProcessEntity.setStatus(PatTypeEnum.PROPOSE_INBOUND.getCode());
        inboundProcessEntity.setDoctorRecommendId(id);
        inboundProcessEntity.setSignExpireDays(project.getSignDateline());
        DoctorRecommendationEntity doctorRecommendationEntity = doctorRecommendationService.getById(id);
        if (!ObjectUtils.isEmpty(doctorRecommendationEntity)) {
            inboundProcessEntity.setAdmType(doctorRecommendationEntity.getAdmType());
        }
        this.baseMapper.insert(inboundProcessEntity);

        // 3.更改推荐表中所有相关推荐的状态
        doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                .eq(DoctorRecommendationEntity::getId, id)
                .set(DoctorRecommendationEntity::getStatus, PROPOSE_INBOUND.getCode()));

        // 4.更改对应的推荐池中患者状态
        if (isLockPrj) {
            patientService.update(new UpdateWrapper<PatientEntity>().lambda()
                    .set(PatientEntity::getStatus, LOCK.getCode())
                    .eq(PatientEntity::getEmpiid, empi));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sign(Long id, String empi, Long subProjectId, String date, Long userId) {

        // 1.更新受试者入组对象
        InboundProcessEntity inboundProcessEntity = this.getById(id);
        if (ObjectUtils.isEmpty(inboundProcessEntity)) {
            throw new BusinessException("未查到该患者");
        }
        inboundProcessEntity.setSigningTime(StringUtils.isEmpty(date) ? DateUtils.getNowTimeStr(DateUtils.DATE_PATTERN) : date);
        inboundProcessEntity.setSigningOpTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        inboundProcessEntity.setSigningAuditId(userId);
        inboundProcessEntity.setStatus(PatTypeEnum.INBOUNDED.getCode());
        this.baseMapper.updateById(inboundProcessEntity);

        // 2.更改推荐表中所有相关推荐的状态
        doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                .eq(DoctorRecommendationEntity::getId, inboundProcessEntity.getDoctorRecommendId())
                .set(DoctorRecommendationEntity::getStatus, INBOUNDED.getCode()));

        SubProjectEntity subProjectEntity = subProjectService.getById(subProjectId);
        ProjectEntity project = projectService.getById(subProjectEntity.getProjectId());
        Integer enableSync = project.getEnableSync();

        if (enableSync == 1) {
            if (project.getEdcProjectId() != null && project.getEdcProjectId() != 0) {
                // 查指定empi的患者信息
                List<PatientEntity> patientEntityList = patientService.list(new QueryWrapper<PatientEntity>().lambda().eq(PatientEntity::getEmpiid, empi));
                String regno = CollectionUtils.isEmpty(patientEntityList) ? null : patientEntityList.get(0).getRegno();
                edcService.intoPatient(empi, regno, project.getId(), project.getEdcProjectId(), userId);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exit(ExitDto exitDto) {
        // 1.更新受试者入组对象
        InboundProcessEntity inboundProcessEntity = this.getById(exitDto.getId());
        if (ObjectUtils.isEmpty(inboundProcessEntity)) {
            throw new BusinessException("未查到该患者");
        }
        inboundProcessEntity.setOutReason(exitDto.getType() == 2 ? exitDto.getReason() : "");
        Integer exitStatus = exitDto.getType() == 2 ? PatTypeEnum.EXIT_ABNORMALLY.getCode() : PatTypeEnum.EXIT_NORMALLY.getCode();
        inboundProcessEntity.setStatus(exitStatus);
        inboundProcessEntity.setOutTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        this.baseMapper.updateById(inboundProcessEntity);

        // 2.更改推荐表中所有相关推荐的状态
        doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                .eq(DoctorRecommendationEntity::getId, inboundProcessEntity.getDoctorRecommendId())
                .set(DoctorRecommendationEntity::getStatus, exitStatus));

        // 判断项目是否为干预性项目
        ProjectEntity project = projectService.getById(exitDto.getProjectId());
        Boolean isLockPrj = isLockProject(project);
        if (isLockPrj) {
            // 3.更改对应的推荐池中患者状态
            patientService.update(new UpdateWrapper<PatientEntity>().lambda()
                    .set(PatientEntity::getStatus, UNLOCK.getCode())
                    .eq(PatientEntity::getEmpiid, exitDto.getEmpi()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revert(ExitDto exitDto) {
        InboundProcessEntity inboundProcessEntity = this.getById(exitDto.getId());
        if (ObjectUtils.isEmpty(inboundProcessEntity)) {
            throw new BusinessException("未查到该患者");
        }
        // 1.更新受试者入组状态
        inboundProcessEntity.setOutReason(exitDto.getReason());
        inboundProcessEntity.setStatus(PatTypeEnum.REVERT.getCode());
        inboundProcessEntity.setOutTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        this.baseMapper.updateById(inboundProcessEntity);

        // 2.更改推荐表中所有相关推荐的状态
        doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                .eq(DoctorRecommendationEntity::getId, inboundProcessEntity.getDoctorRecommendId())
                .set(DoctorRecommendationEntity::getStatus, RECOMMENDED.getCode())
                .set(DoctorRecommendationEntity::getIsRead, 0));

        // 判断项目是否为干预性项目
        ProjectEntity project = projectService.getById(exitDto.getProjectId());
        Boolean isLockPrj = isLockProject(project);
        if (isLockPrj) {
            // 3.更改对应的推荐池中患者状态
            patientService.update(new UpdateWrapper<PatientEntity>().lambda()
                    .set(PatientEntity::getStatus, UNLOCK.getCode())
                    .eq(PatientEntity::getEmpiid, exitDto.getEmpi()));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(ExitDto exitDto) {
        InboundProcessEntity inboundProcessEntity = this.getById(exitDto.getId());
        if (ObjectUtils.isEmpty(inboundProcessEntity)) {
            throw new BusinessException("未查到该患者");
        }
        // 1.更新受试者入组状态
        inboundProcessEntity.setOutReason(exitDto.getReason());
        inboundProcessEntity.setStatus(REFUSE.getCode());
        inboundProcessEntity.setOutTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        this.baseMapper.updateById(inboundProcessEntity);

        // 2.更改推荐表中所有相关推荐的状态
        doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                .eq(DoctorRecommendationEntity::getId, inboundProcessEntity.getDoctorRecommendId())
                .set(DoctorRecommendationEntity::getStatus, REFUSE.getCode())
                .set(DoctorRecommendationEntity::getIsRead, 0)
                .set(DoctorRecommendationEntity::getRefuseReason, exitDto.getReason()));

        // 判断项目是否为干预性项目
        ProjectEntity project = projectService.getById(exitDto.getProjectId());
        Boolean isLockPrj = isLockProject(project);
        if (isLockPrj) {
            // 3.更改对应的推荐池中患者状态
            patientService.update(new UpdateWrapper<PatientEntity>().lambda()
                    .set(PatientEntity::getStatus, UNLOCK.getCode())
                    .eq(PatientEntity::getEmpiid, exitDto.getEmpi()));
        }
    }

    /**
     * 查已入组未退出
     *
     * @return
     */
    @Override
    public List<String> getEmpi() {
        return baseMapper.getEmpi();
    }

    /**
     * 查本项目已入组未退出
     *
     * @return
     */
    @Override
    public List<String> getEmpiByProjectId(Long subProjectId) {
        return baseMapper.getEmpiByProjectId(subProjectId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void expiredPatClean() throws ParseException {
        // 查所有项目
        List<ProjectEntity> projectEntityList = projectService.list();
        for (ProjectEntity projectEntity : projectEntityList) {
            // 非在研项目跳过
            if (!projectEntity.getStatus().equals(1)) {
                continue;
            }
            //1.获取所有未签署确认书入组记录
            LambdaQueryWrapper<InboundProcessEntity> qw = new QueryWrapper<InboundProcessEntity>().lambda()
                    .eq(InboundProcessEntity::getProjectId, projectEntity.getId())
                    .eq(InboundProcessEntity::getStatus, PatTypeEnum.PROPOSE_INBOUND.getCode());

            List<InboundProcessEntity> inboundProcessEntities = this.baseMapper.selectList(qw);

            if (CollectionUtils.isEmpty(inboundProcessEntities)) {
                continue;
            }

            String now = DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
            List<Long> exitIdList = new ArrayList<>();
            // 2.更新入组入组记录剩余时间
            for (InboundProcessEntity inboundProcessEntity : inboundProcessEntities) {
                String inboundTime = inboundProcessEntity.getInboundTime();
                String signEndTime = DateUtils.getAfterDay(inboundTime, projectEntity.getSignDateline() == null ? "3" : projectEntity.getSignDateline().toString());
                Integer signExpireDays = DateUtils.getAbDistanceDays(now, signEndTime);

                // 3.过期时更改受试者状态为异常退出 退出原因为未按时签署知情同意书
                if (signExpireDays <= 0) {
                    inboundProcessEntity.setStatus(PatTypeEnum.EXIT_ABNORMALLY.getCode());
                    inboundProcessEntity.setOutReason(NO_SIGN_EXIT_REASON);
                    exitIdList.add(inboundProcessEntity.getDoctorRecommendId());
                }

                inboundProcessEntity.setSignExpireDays(signExpireDays);
            }

            // 4.批量更新受试者入组状态
            this.updateBatchById(inboundProcessEntities);

            // 批量更新医生推荐状态
            if (CollectionUtils.isEmpty(exitIdList)) {
                continue;
            }

            doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                    .in(DoctorRecommendationEntity::getId, exitIdList)
                    .set(DoctorRecommendationEntity::getStatus, RECOMMENDED.getCode()));

            // 批量更新患者状态
            List<String> empiList = inboundProcessEntities.stream().map(InboundProcessEntity::getEmpiid).collect(Collectors.toList());
            if (isLockProject(projectEntity) && !CollectionUtils.isEmpty(empiList)) {
                patientService.update(new UpdateWrapper<PatientEntity>().lambda()
                        .set(PatientEntity::getStatus, UNLOCK.getCode())
                        .in(PatientEntity::getEmpiid, empiList));
            }
        }
    }

    @Override
    public List<PersonalReportDto> realityPersonalData(String date) {
        // 1. 统计用户推荐病例数
        List<PersonalReportDto> result = doctorRecommendationService.personalRecommendData(date);

        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }

        // 2.按条件查询满足条件入组统计结果
        QueryWrapper<InboundProcessEntity> ibQw = new QueryWrapper<>();

        if (StringUtils.isNotBlank(date)) {
            Date startDate = DateUtils.getByDateStr(date + "-01");
            Date endDate = DateUtils.addDateMonths(startDate, 1);
            ibQw.le("signing_op_time", endDate).ge("signing_op_time", startDate);
        }

        // 3.统计总的入组数
        List<Integer> statusList = new ArrayList<>();
        statusList.add(PatTypeEnum.INBOUNDED.getCode());
        statusList.add(PatTypeEnum.EXIT_NORMALLY.getCode());
        statusList.add(PatTypeEnum.EXIT_ABNORMALLY.getCode());
        ibQw.in(HxConst.STATUS, statusList);

        List<PersonalReportDto> personalInboudReportDtoList = this.baseMapper.groupByDoctor(ibQw);
        Map<Long, List<PersonalReportDto>> personalInboudReportMap;

        if (!CollectionUtils.isEmpty(personalInboudReportDtoList)) {
            personalInboudReportMap = personalInboudReportDtoList.stream().collect(Collectors.groupingBy(PersonalReportDto::getDoctorId));
        } else {
            return result;
        }

        // 4.获取所有机构与医生信息
        List<Long> existDoctorList = new ArrayList<>();
        List<PersonalReportDto> addList = new ArrayList<>();

        for (PersonalReportDto personalReportDto : result) {
            Long doctorId = personalReportDto.getDoctorId();
            if (personalInboudReportMap.containsKey(doctorId)) {
                existDoctorList.add(doctorId);
                List<PersonalReportDto> personalReports = personalInboudReportMap.get(doctorId);

                if (personalReportDto.getProjectId() == null) {
                    addList.addAll(personalReports);
                } else {
                    for (PersonalReportDto personalReport : personalReports) {
                        if (personalReport.getProjectId() == personalReportDto.getProjectId()) {
                            personalReport.setSum(personalReportDto.getSum());
                            addList.add(personalReport);
                        }
                    }
                }
            }
        }

        result.removeIf(personalReport -> existDoctorList.contains(personalReport.getDoctorId()));

        // 5.按入组量排序
        addList.sort(Comparator.comparing(PersonalReportDto::getIntoGroup));
        addList.addAll(result);
        return addList;
    }

//    @Override
//    public List<DepartmentReportDto> realityDepartmentData(String date) {
//        // 1.获取相关推荐参数
//        List<DepartmentReportDto> result = doctorRecommendationService.departmentRecommendData(date);
//
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//
//        QueryWrapper<InboundProcessEntity> ibQw = new QueryWrapper<>();
//
//        if (StringUtils.isNotBlank(date)) {
//            // 2.拼接完整日期获取当月起始与结束日期
//            Date startDate = DateUtils.getByDateStr(date + "-01");
//            Date endDate = DateUtils.addDateMonths(startDate, 1);
//            ibQw.le("a.signing_time", endDate).ge("a.signing_time", startDate);
//        }
//
//        List<Integer> statusList = new ArrayList<>();
//        statusList.add(PatTypeEnum.INBOUNDED.getCode());
//        statusList.add(PatTypeEnum.EXIT_NORMALLY.getCode());
//        statusList.add(PatTypeEnum.EXIT_ABNORMALLY.getCode());
//        ibQw.in("a.status", statusList);
//
//        List<DepartmentReportDto> departmentReportDtoList = this.baseMapper.groupByDept(ibQw);
//        List<DepartmentReportDto> addList = new ArrayList<>();
//
//        if (!CollectionUtils.isEmpty(departmentReportDtoList)) {
//            Map<Long, List<DepartmentReportDto>> map = departmentReportDtoList.stream().collect(Collectors.groupingBy(DepartmentReportDto::getDeptId));
//
//            result.forEach(departmentReportDto -> {
//                Long deptId = departmentReportDto.getDeptId();
//                if (map.containsKey(deptId)) {
//                    departmentReportDto.setIntoGroup(map.get(deptId).get(0).getIntoGroup());
//                } else {
//                    addList.add(map.get(deptId).get(0));
//                }
//            });
//        }
//
//        result.sort((o1, o2) -> o2.getIntoGroup() - o1.getIntoGroup());
//
//        if (!CollectionUtils.isEmpty(addList)) {
//            doctorRecommendationService.setProperties(addList);
//            result.addAll(addList);
//        }
//
//        return result;
//    }

    /**
     * 病例已入组项目
     *
     * @param empi
     * @return
     */
    @Override
    public List<InboundProjectDieaseVo> getInboundProject(String empi, Boolean isLock) {
        List<InboundProjectDieaseVo> inboundProjectDieaseVoList = baseMapper.getInboundProject(new QueryWrapper<InboundProjectDieaseVo>()
                .eq("d.empiid", empi)
                .eq(isLock, "d.status", LOCK.getCode())
                .and(qw -> {
                    qw.eq("a.status", PROPOSE_INBOUND.getCode())
                            .or().eq("a.status", INBOUNDED.getCode());
                }));
        return inboundProjectDieaseVoList;
    }

    /**
     * 统计科室入组量
     *
     * @param result
     * @param startTime
     * @param endTime
     */
    @Override
    public void setInboundCount(List<ReportDto> result, Date startTime, Date endTime) {
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwIit = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwGcp = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwPre = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwSpe = new QueryWrapper<>();
        List<Long> deptIdList = result.stream().map(ReportDto::getDeptId).collect(Collectors.toList());
        qw.in("c.dept_id", deptIdList);
        qw.isNotNull("a.signing_op_time");
        List<ReportDto> allInboundCount = this.baseMapper.groupDept(qw);

        if (CollectionUtils.isEmpty(allInboundCount)) {
            return;
        } else {
            Map<Long, Integer> map = allInboundCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));
            // 门诊
            qwO.in("c.dept_id", deptIdList);
            qwO.isNotNull("a.signing_op_time");
            qwO.eq("a.adm_type", "O");
            List<ReportDto> inboundOCount = this.baseMapper.groupDept(qwO);
            Map<Long, Integer> mapO = inboundOCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            // 住院
            qwI.in("c.dept_id", deptIdList);
            qwI.isNotNull("a.signing_op_time");
            qwI.eq("a.adm_type", "I");
            List<ReportDto> inboundICount = this.baseMapper.groupDept(qwI);
            Map<Long, Integer> mapI = inboundICount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            // 急诊
            qwE.in("c.dept_id", deptIdList);
            qwE.isNotNull("a.signing_op_time");
            qwE.eq("a.adm_type", "E");
            List<ReportDto> inboundECount = this.baseMapper.groupDept(qwE);
            Map<Long, Integer> mapE = inboundECount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            // IIT
            qwIit.in("c.dept_id", deptIdList);
            qwIit.isNotNull("a.signing_op_time");
            qwIit.eq("b.project_type", 1);
            List<ReportDto> inboundIitCount = this.baseMapper.groupDept(qwIit);
            Map<Long, Integer> mapIit = inboundIitCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            // GCP
            qwGcp.in("c.dept_id", deptIdList);
            qwGcp.isNotNull("a.signing_op_time");
            qwGcp.eq("b.project_type", 2);
            List<ReportDto> inboundGcpCount = this.baseMapper.groupDept(qwGcp);
            Map<Long, Integer> mapGcp = inboundGcpCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            // Pre
            qwPre.in("c.dept_id", deptIdList);
            qwPre.isNotNull("a.signing_op_time");
            qwPre.eq("b.project_type", 3);
            List<ReportDto> inboundPreCount = this.baseMapper.groupDept(qwPre);
            Map<Long, Integer> mapPre = inboundPreCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            // Spe
            qwSpe.in("c.dept_id", deptIdList);
            qwSpe.isNotNull("a.signing_op_time");
            qwSpe.eq("b.project_type", 4);
            List<ReportDto> inboundSpeCount = this.baseMapper.groupDept(qwSpe);
            Map<Long, Integer> mapSpe = inboundSpeCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

            result.forEach(reportDto -> {
                Long deptId = reportDto.getDeptId();
                int totalInbound = map.get(deptId) == null ? 0 : map.get(deptId);
                reportDto.setTotalInbound(totalInbound);
                // 门诊
                int totalOInbound = mapO.get(deptId) == null ? 0 : mapO.get(deptId);
                reportDto.setTotalOInbound(totalOInbound);
                // 住院
                int totalIInbound = mapI.get(deptId) == null ? 0 : mapI.get(deptId);
                reportDto.setTotalIInbound(totalIInbound);
                // 急诊
                int totalEInbound = mapE.get(deptId) == null ? 0 : mapE.get(deptId);
                reportDto.setTotalEInbound(totalEInbound);

                //IIT
                int totalIitInbound = mapIit.get(deptId) == null ? 0 : mapIit.get(deptId);
                reportDto.setTotalIitInbound(totalIitInbound);
                //GCP
                int totalGcpInbound = mapGcp.get(deptId) == null ? 0 : mapGcp.get(deptId);
                reportDto.setTotalGcpInbound(totalGcpInbound);
                //PRE
                int totalPreInbound = mapPre.get(deptId) == null ? 0 : mapPre.get(deptId);
                reportDto.setTotalPreObsInbound(totalPreInbound);
                //SPE
                int totalSpeInbound = mapSpe.get(deptId) == null ? 0 : mapSpe.get(deptId);
                reportDto.setTotalSpecialInbound(totalSpeInbound);

                Integer totalWeightInbound = getTotalWeightInbound(reportDto);
                reportDto.setTotalWeightInbound(totalWeightInbound);
            });
        }

        if (startTime != null) {
            qw.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            List<ReportDto> addRecommendCount = this.baseMapper.groupDept(qw);

            if (!CollectionUtils.isEmpty(addRecommendCount)) {
                Map<Long, Integer> map = addRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // 门诊
                qwO.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundOCount = this.baseMapper.groupDept(qwO);
                Map<Long, Integer> mapO = inboundOCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // 住院
                qwI.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundICount = this.baseMapper.groupDept(qwI);
                Map<Long, Integer> mapI = inboundICount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // 急诊
                qwE.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundECount = this.baseMapper.groupDept(qwE);
                Map<Long, Integer> mapE = inboundECount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // IIT
                qwIit.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundIitCount = this.baseMapper.groupDept(qwIit);
                Map<Long, Integer> mapIit = inboundIitCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // GCP
                qwGcp.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundGcpCount = this.baseMapper.groupDept(qwGcp);
                Map<Long, Integer> mapGcp = inboundGcpCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // Pre
                qwPre.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundPreCount = this.baseMapper.groupDept(qwPre);
                Map<Long, Integer> mapPre = inboundPreCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                // Spe
                qwSpe.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundSpeCount = this.baseMapper.groupDept(qwSpe);
                Map<Long, Integer> mapSpe = inboundSpeCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalInbound));

                result.forEach(reportDto -> {
                    Long deptId = reportDto.getDeptId();
                    Integer addInbound = map.get(deptId) == null ? 0 : map.get(deptId);
                    reportDto.setAddInbound(addInbound == null ? 0 : addInbound);

                    // 门诊
                    int addOInbound = mapO.get(deptId) == null ? 0 : mapO.get(deptId);
                    reportDto.setAddOInbound(addOInbound);
                    // 住院
                    int addIInbound = mapI.get(deptId) == null ? 0 : mapI.get(deptId);
                    reportDto.setAddIInbound(addIInbound);
                    // 急诊
                    int addEInbound = mapE.get(deptId) == null ? 0 : mapE.get(deptId);
                    reportDto.setAddEInbound(addEInbound);

                    int dischargesCount = reportDto.getDischargesCount();
                    reportDto.setInboundRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addInbound * 10000 / reportDto.getDischargesCount()) / 100 + "%");
                    reportDto.setTargetInboundCompeteRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addInbound * 10000 / reportDto.getTargetInbound()) / 100 + "%");

                    // Iit
                    int addIitInbound = mapIit.get(deptId) == null ? 0 : mapIit.get(deptId);
                    // GCP
                    int addGcpInbound = mapGcp.get(deptId) == null ? 0 : mapGcp.get(deptId);
                    // Pre
                    int addPreInbound = mapPre.get(deptId) == null ? 0 : mapPre.get(deptId);
                    // Spe
                    int addSpeInbound = mapSpe.get(deptId) == null ? 0 : mapSpe.get(deptId);

                    ReportDto cal = new ReportDto();
                    cal.setTotalInbound(addInbound);
                    cal.setTotalIitInbound(addIitInbound);
                    cal.setTotalGcpInbound(addGcpInbound);
                    cal.setTotalPreObsInbound(addPreInbound);
                    cal.setTotalSpecialInbound(addSpeInbound);
                    Integer totalWeightInbound = getTotalWeightInbound(cal);
                    reportDto.setAddWeightInbound(totalWeightInbound);
                    reportDto.setWeightInboundRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(totalWeightInbound * 10000 / reportDto.getDischargesCount()) / 100 + "%");
                });
            }
        } else {
            result.forEach(reportDto -> {
                Integer addInbound = reportDto.getTotalInbound();
                reportDto.setAddInbound(addInbound);
                int dischargesCount = reportDto.getDischargesCount();
                reportDto.setInboundRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addInbound * 10000 / reportDto.getDischargesCount()) / 100 + "%");
                reportDto.setTargetInboundCompeteRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addInbound * 10000 / reportDto.getTargetInbound()) / 100 + "%");

                Integer totalWeightInbound = getTotalWeightInbound(reportDto);
                reportDto.setAddWeightInbound(totalWeightInbound);
                reportDto.setWeightInboundRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(totalWeightInbound * 10000 / reportDto.getDischargesCount()) / 100 + "%");
            });
        }
    }

    @Override
    public void setTotalInboundCount(ReportDto reportDto, Date startTime, Date endTime) {
        ReportDto calDto = new ReportDto();// 计算加权如组数
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        qw.isNotNull("a.signing_op_time");
        Integer totalInbound = this.baseMapper.totalInbound(qw);
        reportDto.setTotalInbound(totalInbound);

        // IIT project_type=1
        QueryWrapper<ReportDto> qwIit = new QueryWrapper<>();
        qwIit.isNotNull("a.signing_op_time");
        qwIit.eq("b.project_type", 1);
        Integer totalIitInbound = this.baseMapper.totalInbound(qwIit);
        reportDto.setTotalIitInbound(totalIitInbound);
        calDto.setTotalIitInbound(totalIitInbound);

        // GCP project_type=2
        QueryWrapper<ReportDto> qwGCP = new QueryWrapper<>();
        qwGCP.isNotNull("a.signing_op_time");
        qwGCP.eq("b.project_type", 2);
        Integer totalGcpInbound = this.baseMapper.totalInbound(qwGCP);
        reportDto.setTotalGcpInbound(totalGcpInbound);
        calDto.setTotalGcpInbound(totalGcpInbound);

        // PreObs project_type=3
        QueryWrapper<ReportDto> qwPO = new QueryWrapper<>();
        qwPO.isNotNull("a.signing_op_time");
        qwPO.eq("b.project_type", 3);
        Integer totalPOInbound = this.baseMapper.totalInbound(qwPO);
        reportDto.setTotalPreObsInbound(totalPOInbound);
        calDto.setTotalPreObsInbound(totalPOInbound);

        // Special project_type=4
        QueryWrapper<ReportDto> qwSP = new QueryWrapper<>();
        qwSP.isNotNull("a.signing_op_time");
        qwSP.eq("b.project_type", 4);
        Integer totalSPInbound = this.baseMapper.totalInbound(qwSP);
        reportDto.setTotalSpecialInbound(totalSPInbound);
        calDto.setTotalSpecialInbound(totalSPInbound);

        // 加权入组人数
        Integer totalWeightInbound = getTotalWeightInbound(calDto);
        reportDto.setTotalWeightInbound(totalWeightInbound);

        // 门诊
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        qwO.isNotNull("a.signing_op_time");
        qwO.eq("a.adm_type", "O");
        Integer totalOInbound = this.baseMapper.totalInbound(qwO);
        reportDto.setTotalOInbound(totalOInbound);

        // 住院
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        qwI.isNotNull("a.signing_op_time");
        qwI.eq("a.adm_type", "I");
        Integer totalIInbound = this.baseMapper.totalInbound(qwI);
        reportDto.setTotalIInbound(totalIInbound);

        // 急诊
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        qwE.isNotNull("a.signing_op_time");
        qwE.eq("a.adm_type", "E");
        Integer totalEInbound = this.baseMapper.totalInbound(qwE);
        reportDto.setTotalEInbound(totalEInbound);

        if (startTime != null) {
            qw.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addInbound = this.baseMapper.totalInbound(qw);
            reportDto.setAddInbound(addInbound);
            calDto.setTotalInbound(addInbound);
            // IIT project_type=1
            qwIit.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addIitInbound = this.baseMapper.totalInbound(qwIit);
            calDto.setTotalIitInbound(addIitInbound);

            // GCP project_type=2
            qwGCP.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addGcpInbound = this.baseMapper.totalInbound(qwGCP);
            calDto.setTotalGcpInbound(addGcpInbound);

            // PreObs project_type=3
            qwPO.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addPOInbound = this.baseMapper.totalInbound(qwPO);
            calDto.setTotalPreObsInbound(addPOInbound);

            // Special project_type=4
            qwSP.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addSPInbound = this.baseMapper.totalInbound(qwSP);
            calDto.setTotalSpecialInbound(addSPInbound);

            // 加权入组人数
            Integer addWeightInbound = getTotalWeightInbound(calDto);
            reportDto.setAddWeightInbound(addWeightInbound);

            // 门诊
            qwO.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addOInbound = this.baseMapper.totalInbound(qwO);
            reportDto.setAddOInbound(addOInbound);

            // 住院
            qwI.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addIInbound = this.baseMapper.totalInbound(qwI);
            reportDto.setAddIInbound(addIInbound);

            // 急诊
            qwE.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            Integer addEInbound = this.baseMapper.totalInbound(qwE);
            reportDto.setAddEInbound(addEInbound);

        } else {
            reportDto.setAddInbound(reportDto.getTotalInbound());
            // 加权入组人数
            Integer addWeightInbound = getTotalWeightInbound(reportDto);
            reportDto.setAddWeightInbound(addWeightInbound);

            reportDto.setAddOInbound(reportDto.getTotalOInbound());
            reportDto.setAddIInbound(reportDto.getTotalIInbound());
            reportDto.setAddEInbound(reportDto.getTotalEInbound());
        }

        Integer addInbound = reportDto.getAddInbound();
        int dischargesCount = reportDto.getDischargesCount();
        reportDto.setInboundRatio(dischargesCount == 0 ? "0.0%" : (double) Math.round(addInbound * 10000 / reportDto.getDischargesCount()) / 100 + "%");
        reportDto.setTargetInboundCompeteRatio(dischargesCount == 0 ? "0.0%" : (double) Math.round(addInbound * 10000 / reportDto.getTargetInbound()) / 100 + "%");

        Integer addWeightInbound = reportDto.getAddWeightInbound();
        reportDto.setWeightInboundRatio(dischargesCount == 0 ? "0.0%" : (double) Math.round(addWeightInbound * 10000 / reportDto.getDischargesCount()) / 100 + "%");
    }

    private Integer getTotalWeightInbound(ReportDto reportDto) {
        Integer totalGcpInbound = reportDto.getTotalGcpInbound() == null ? 0 : reportDto.getTotalGcpInbound();
        Integer totalIitInbound = reportDto.getTotalIitInbound() == null ? 0 : reportDto.getTotalIitInbound();
        Integer totalPreObsInbound = reportDto.getTotalPreObsInbound() == null ? 0 : reportDto.getTotalPreObsInbound();
        Integer totalSpecialInbound = reportDto.getTotalSpecialInbound() == null ? 0 : reportDto.getTotalSpecialInbound();
        return totalGcpInbound * 10 + totalIitInbound + totalPreObsInbound + totalSpecialInbound;
    }

    private Integer getTotalWeightInbound(ProjectVo reportDto) {
        Integer totalGcpInbound = reportDto.getTotalGcpInbound() == null ? 0 : reportDto.getTotalGcpInbound();
        Integer totalIitInbound = reportDto.getTotalIitInbound() == null ? 0 : reportDto.getTotalIitInbound();
        Integer totalPreObsInbound = reportDto.getTotalPreObsInbound() == null ? 0 : reportDto.getTotalPreObsInbound();
        Integer totalSpecialInbound = reportDto.getTotalSpecialInbound() == null ? 0 : reportDto.getTotalSpecialInbound();
        return totalGcpInbound * 10 + totalIitInbound + totalPreObsInbound + totalSpecialInbound;
    }

    private Integer getTotalWeightInbound(TotalProjectVo reportDto) {
        switch (reportDto.getProjectType()) {
            case 1:
                // IIT
                reportDto.setTotalIitInbound((int)reportDto.getInbound());
                break;
            case 2:
                // GCP
                reportDto.setTotalGcpInbound((int)reportDto.getInbound());
                break;
            case 3:
                // PreObs
                reportDto.setTotalPreObsInbound((int)reportDto.getInbound());
                break;
            case 4:
                // Spec
                reportDto.setTotalSpecialInbound((int)reportDto.getInbound());
                break;
        }
        Integer totalGcpInbound = reportDto.getTotalGcpInbound() == null ? 0 : reportDto.getTotalGcpInbound();
        Integer totalIitInbound = reportDto.getTotalIitInbound() == null ? 0 : reportDto.getTotalIitInbound();
        Integer totalPreObsInbound = reportDto.getTotalPreObsInbound() == null ? 0 : reportDto.getTotalPreObsInbound();
        Integer totalSpecialInbound = reportDto.getTotalSpecialInbound() == null ? 0 : reportDto.getTotalSpecialInbound();
        return totalGcpInbound * 10 + totalIitInbound + totalPreObsInbound + totalSpecialInbound;
    }

    @Override
    public void setProjectInboundCount(List<ProjectVo> records, Date startTime, Date endTime) {
        List<Long> projectIdList = records.stream().map(ProjectVo::getId).collect(Collectors.toList());
        QueryWrapper<ProjectVo> qw = new QueryWrapper<>();
        qw.in("b.id", projectIdList);
        qw.isNotNull("a.signing_op_time");
        List<ProjectVo> projectVos = this.baseMapper.groupProject(qw);
        if (CollectionUtils.isEmpty(projectVos)) {
            return;
        }

        Map<Long, Integer> map = projectVos.stream().collect(Collectors.toMap(ProjectVo::getId, ProjectVo::getInboundCount));

        //IIT
        QueryWrapper<ProjectVo> qwIit = new QueryWrapper<>();
        qwIit.in("b.id", projectIdList);
        qwIit.eq("b.project_type", 1);
        qwIit.isNotNull("a.signing_op_time");
        List<ProjectVo> projectIitVos = this.baseMapper.groupProject(qwIit);
        Map<Long, Integer> mapIit = CollectionUtils.isEmpty(projectIitVos) ? new LinkedHashMap<>() : projectIitVos.stream().collect(Collectors.toMap(ProjectVo::getId, ProjectVo::getInboundCount));

        //GCP
        QueryWrapper<ProjectVo> qwGcp = new QueryWrapper<>();
        qwGcp.in("b.id", projectIdList);
        qwGcp.eq("b.project_type", 2);
        qwGcp.isNotNull("a.signing_op_time");
        List<ProjectVo> projectGcpVos = this.baseMapper.groupProject(qwGcp);
        Map<Long, Integer> mapGcp = CollectionUtils.isEmpty(projectGcpVos) ? new LinkedHashMap<>() : projectGcpVos.stream().collect(Collectors.toMap(ProjectVo::getId, ProjectVo::getInboundCount));

        //PO
        QueryWrapper<ProjectVo> qwPreOb = new QueryWrapper<>();
        qwPreOb.in("b.id", projectIdList);
        qwPreOb.eq("b.project_type", 3);
        qwPreOb.isNotNull("a.signing_op_time");
        List<ProjectVo> projectPreObVos = this.baseMapper.groupProject(qwPreOb);
        Map<Long, Integer> mapPreOb = CollectionUtils.isEmpty(projectPreObVos) ? new LinkedHashMap<>() : projectPreObVos.stream().collect(Collectors.toMap(ProjectVo::getId, ProjectVo::getInboundCount));

        //SPE
        QueryWrapper<ProjectVo> qwSpe = new QueryWrapper<>();
        qwSpe.in("b.id", projectIdList);
        qwSpe.eq("b.project_type", 4);
        qwSpe.isNotNull("a.signing_op_time");
        List<ProjectVo> projectSpeVos = this.baseMapper.groupProject(qwSpe);
        Map<Long, Integer> mapSpe = CollectionUtils.isEmpty(projectSpeVos) ? new LinkedHashMap<>() : projectSpeVos.stream().collect(Collectors.toMap(ProjectVo::getId, ProjectVo::getInboundCount));

        // 新增入组数
        Map<Long, Integer> addMap = new LinkedHashMap<>();
        if (startTime != null) {
            qw.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            List<ProjectVo> addInbound = this.baseMapper.groupProject(qw);
            addMap = CollectionUtils.isEmpty(addInbound) ? new LinkedHashMap<>() : addInbound.stream().collect(Collectors.toMap(ProjectVo::getId, ProjectVo::getInboundCount));
        }
        for (ProjectVo project : records) {
            Long projectId = project.getId();
            Integer inboundCount = map.containsKey(projectId) ? map.get(projectId) : 0;
            project.setInboundCount(inboundCount);
            Integer iitInboundCount = mapIit.containsKey(projectId) ? mapIit.get(projectId) : 0;
            project.setTotalIitInbound(iitInboundCount);
            Integer gcpInboundCount = mapGcp.containsKey(projectId) ? mapGcp.get(projectId) : 0;
            project.setTotalGcpInbound(gcpInboundCount);
            Integer preObInboundCount = mapPreOb.containsKey(projectId) ? mapPreOb.get(projectId) : 0;
            project.setTotalPreObsInbound(preObInboundCount);
            Integer speInboundCount = mapSpe.containsKey(projectId) ? mapSpe.get(projectId) : 0;
            project.setTotalSpecialInbound(speInboundCount);
            Integer totalWeightInbound = getTotalWeightInbound(project);
            project.setTotalWeightInbound(totalWeightInbound);

            Integer addInboundCount = 0;
            if (startTime != null) {
                addInboundCount = addMap.containsKey(projectId) ? addMap.get(projectId) : 0;
            } else {
                addInboundCount = inboundCount;
            }
            project.setAddInboundCount(addInboundCount);
        }

    }
//
//    @Override
//    public Map<String, TotalProjectVo> groupInboundCountByType(List<Long> projectIdList, Date startTime, Date endTime) {
//        Map<String, TotalProjectVo> totalProjectMap = new HashMap<>();
//
//        if (CollectionUtils.isEmpty(projectIdList)) {
//            return totalProjectMap;
//        }
//        // 所有项目入组数
//        QueryWrapper<ProjectVo> qw = new QueryWrapper<ProjectVo>().in("b.id", projectIdList);
//        List<TotalProjectVo> maps = this.baseMapper.groupProjectType(qw);
//
//        if (!CollectionUtils.isEmpty(maps)) {
//            for (TotalProjectVo map : maps) {
//                ProjectStudyTypeEnum projectType = ProjectStudyTypeEnum.getByCode((Integer) map.getProjectType());
//                TotalProjectVo totalProjectVo = new TotalProjectVo();
//                totalProjectVo.setName(projectType.getName());
//                Long inboundCount = map.getInbound();
//                totalProjectVo.setInbound(inboundCount);
//
//                //加权如组数
//                totalProjectVo.setWeightInbound(getTotalWeightInbound(totalProjectVo));
//                totalProjectMap.put(projectType.getName(), totalProjectVo);
//            }
//        }
//
//
//        // todo增量入组数
//        if (startTime != null) {
//            qw.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
//            List<TotalProjectVo> addInbound = this.baseMapper.groupProjectType(qw);
//
////            reportDto.setAddInbound(addInboundDto.getTotalInbound());
////
////            // 加权入组人数
////            Integer addWeightInbound = getTotalWeightInbound(addInboundDto);
////            reportDto.setAddWeightInbound(addWeightInbound);
//
//            for (String projectTypeName : totalProjectMap) {
//                ProjectStudyTypeEnum projectType = ProjectStudyTypeEnum.getByCode((Integer) map.getProjectType());
//                List<TotalProjectVo> curTypeAddInbound = addInbound.stream().filter(p->p.getProjectType()==map.getProjectType()).collect(Collectors.toList());
//
//                TotalProjectVo totalProjectVo =
//                totalProjectVo.setName(projectType.getName());
//                Long inboundCount = map.getInbound();
//                totalProjectVo.setInbound(inboundCount);
//
//                //加权如组数
//                totalProjectVo.setWeightInbound(getTotalWeightInbound(totalProjectVo));
//                totalProjectMap.put(projectType.getName(), totalProjectVo);
//            }
//
//        } else {
//            totalProjectVo.setAddInbound(reportDto.getTotalInbound());
//            // 加权入组人数
//            Integer addWeightInbound = getTotalWeightInbound(reportDto);
//            reportDto.setAddWeightInbound(addWeightInbound);
//            for (TotalProjectVo map : maps) {
//                ProjectStudyTypeEnum projectType = ProjectStudyTypeEnum.getByCode((Integer) map.getProjectType());
//                TotalProjectVo totalProjectVo = new TotalProjectVo();
//                totalProjectVo.setName(projectType.getName());
//                Long inboundCount = map.getInbound();
//                totalProjectVo.setInbound(inboundCount);
//
//                //加权如组数
//                totalProjectVo.setWeightInbound(getTotalWeightInbound(totalProjectVo));
//                totalProjectMap.put(projectType.getName(), totalProjectVo);
//            }
//        }
//
//        return totalProjectMap;
//    }


    @Override
    public Map<String, TotalProjectVo> groupInboundCountByType(List<Long> projectIdList, Date startTime, Date endTime) {
        Map<String, TotalProjectVo> totalProjectMap = new HashMap<>();

        if (CollectionUtils.isEmpty(projectIdList)) {
            return totalProjectMap;
        }
        // 所有项目入组数
        QueryWrapper<ProjectVo> qw = new QueryWrapper<ProjectVo>().in("b.id", projectIdList);
        qw.isNotNull("a.signing_op_time");
        List<TotalProjectVo> maps = this.baseMapper.groupProjectType(qw);
        // 新增入组数
        List<TotalProjectVo> addInbound = new ArrayList<>();
        if (startTime != null) {
            qw.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            addInbound = this.baseMapper.groupProjectType(qw);
        }

        if (!CollectionUtils.isEmpty(maps)) {
            for (TotalProjectVo map : maps) {
                ProjectStudyTypeEnum projectType = ProjectStudyTypeEnum.getByCode((Integer) map.getProjectType());
                TotalProjectVo totalProjectVo = new TotalProjectVo();
                totalProjectVo.setName(projectType.getName());
                Long inboundCount = map.getInbound();
                totalProjectVo.setInbound(inboundCount);

                //加权入组数
                totalProjectVo.setWeightInbound(getTotalWeightInbound(map));

                List<TotalProjectVo> curTypeAddInbound = addInbound.stream().filter(p -> p.getProjectType() == map.getProjectType()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(curTypeAddInbound)) {
                    // 新增入组数
                    Long addInboundCount = curTypeAddInbound.get(0).getInbound();
                    totalProjectVo.setAddInbound(addInboundCount);
                    // 新增加权入组数
                    Integer addWeightInbound = getTotalWeightInbound(curTypeAddInbound.get(0));
                    totalProjectVo.setAddWeightInbound(addWeightInbound);
                } else {
                    totalProjectVo.setAddInbound(startTime == null ? map.getInbound() : 0);
                    totalProjectVo.setAddWeightInbound(startTime == null ? getTotalWeightInbound(map) : 0);
                }

                totalProjectMap.put(projectType.getName(), totalProjectVo);
            }
        }
        return totalProjectMap;
    }


    @Override
    public void setPerInboundCount(List<ReportDto> result, Date startTime, Date endTime) {
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwIit = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwGcp = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwPre = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwSpe = new QueryWrapper<>();
        List<Long> doctorIdList = result.stream().map(ReportDto::getDoctorId).distinct().collect(Collectors.toList());
        qw.in("b.project_admin_id", doctorIdList);
        qw.isNotNull("a.signing_op_time");
        List<ReportDto> allInboundCount = this.baseMapper.groupPer(qw);

        if (CollectionUtils.isEmpty(allInboundCount)) {
            return;
        } else {
            // IIT
            qwIit.in("b.project_admin_id", doctorIdList);
            qwIit.isNotNull("a.signing_op_time");
            qwIit.eq("b.project_type", 1);
            List<ReportDto> inboundIitCount = this.baseMapper.groupPer(qwIit);
            // GCP
            qwGcp.in("b.project_admin_id", doctorIdList);
            qwGcp.isNotNull("a.signing_op_time");
            qwGcp.eq("b.project_type", 2);
            List<ReportDto> inboundGcpCount = this.baseMapper.groupPer(qwGcp);
            // PreOb
            qwPre.in("b.project_admin_id", doctorIdList);
            qwPre.isNotNull("a.signing_op_time");
            qwPre.eq("b.project_type", 3);
            List<ReportDto> inboundPreCount = this.baseMapper.groupPer(qwPre);
            // Spec
            qwSpe.in("b.project_admin_id", doctorIdList);
            qwSpe.isNotNull("a.signing_op_time");
            qwSpe.eq("b.project_type", 4);
            List<ReportDto> inboundSpeCount = this.baseMapper.groupPer(qwSpe);

            // 门诊
            qwO.in("b.project_admin_id", doctorIdList);
            qwO.isNotNull("a.signing_op_time");
            qwO.eq("a.adm_type", "O");
            List<ReportDto> inboundOCount = this.baseMapper.groupPer(qwO);

            // 住院
            qwI.in("b.project_admin_id", doctorIdList);
            qwI.isNotNull("a.signing_op_time");
            qwI.eq("a.adm_type", "I");
            List<ReportDto> inboundICount = this.baseMapper.groupPer(qwI);

            // 急诊
            qwE.in("b.project_admin_id", doctorIdList);
            qwE.isNotNull("a.signing_op_time");
            qwE.eq("a.adm_type", "E");
            List<ReportDto> inboundECount = this.baseMapper.groupPer(qwE);
            result.forEach(reportDto -> {
                Long doctorId = reportDto.getDoctorId();
                Long deptId = reportDto.getDeptId();
                List<ReportDto> list = allInboundCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                reportDto.setTotalInbound(list.get(0).getTotalInbound());

                // 填充门诊实际入组量
                List<ReportDto> docOList = inboundOCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docOList)) {
                    reportDto.setTotalOInbound(docOList.get(0).getTotalInbound());
                }

                // 填充住院实际入组量
                List<ReportDto> docIList = inboundICount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docIList)) {
                    reportDto.setTotalIInbound(docIList.get(0).getTotalInbound());
                }

                // 填充急诊实际入组量
                List<ReportDto> docEList = inboundECount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docEList)) {
                    reportDto.setTotalEInbound(docEList.get(0).getTotalInbound());
                }

                // 填充IIT实际入组量
                List<ReportDto> docIitList = inboundIitCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docIitList)) {
                    reportDto.setTotalIitInbound(docIitList.get(0).getTotalInbound());
                }

                // 填充GCP实际入组量
                List<ReportDto> docGcpList = inboundGcpCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docGcpList)) {
                    reportDto.setTotalGcpInbound(docGcpList.get(0).getTotalInbound());
                }

                // 填充Pre实际入组量
                List<ReportDto> docPreList = inboundPreCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docPreList)) {
                    reportDto.setTotalPreObsInbound(docPreList.get(0).getTotalInbound());
                }

                // 填充Spe实际入组量
                List<ReportDto> docSpeList = inboundSpeCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                        && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(docSpeList)) {
                    reportDto.setTotalSpecialInbound(docSpeList.get(0).getTotalInbound());
                }
                // 累计实际加权入组人数
                Integer totalWeightInbound = getTotalWeightInbound(reportDto);
                reportDto.setTotalWeightInbound(totalWeightInbound);
            });
        }
        if (startTime != null) {
            qw.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
            List<ReportDto> addInboundCount = this.baseMapper.groupPer(qw);

            if (!CollectionUtils.isEmpty(addInboundCount)) {
                // IIT
                qwIit.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundIitCount = this.baseMapper.groupPer(qwIit);
                // GCP
                qwGcp.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundGcpCount = this.baseMapper.groupPer(qwGcp);
                // PreOb
                qwPre.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundPreCount = this.baseMapper.groupPer(qwPre);
                // Spec
                qwSpe.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundSpeCount = this.baseMapper.groupPer(qwSpe);

                // 门诊
                qwO.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundOCount = this.baseMapper.groupPer(qwO);

                // 住院
                qwI.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundICount = this.baseMapper.groupPer(qwI);

                // 急诊
                qwE.lt("a.signing_op_time", endTime).ge("a.signing_op_time", startTime);
                List<ReportDto> inboundECount = this.baseMapper.groupPer(qwE);
                result.forEach(reportDto -> {
                    ReportDto cal = new ReportDto();
                    Long doctorId = reportDto.getDoctorId();
                    Long deptId = reportDto.getDeptId();
                    List<ReportDto> list = addInboundCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }
                    reportDto.setAddInbound(list.get(0).getTotalInbound());
                    cal.setTotalInbound(reportDto.getAddInbound());

                    // 填充门诊实际入组量
                    List<ReportDto> docOList = inboundOCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docOList)) {
                        reportDto.setAddOInbound(docOList.get(0).getTotalInbound());
                    }

                    // 填充住院实际入组量
                    List<ReportDto> docIList = inboundICount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docIList)) {
                        reportDto.setAddIInbound(docIList.get(0).getTotalInbound());
                    }

                    // 填充急诊实际入组量
                    List<ReportDto> docEList = inboundECount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docEList)) {
                        reportDto.setAddEInbound(docEList.get(0).getTotalInbound());
                    }

                    // 填充IIT实际入组量
                    List<ReportDto> docIitList = inboundIitCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docIitList)) {
                        cal.setTotalIitInbound(docIitList.get(0).getTotalInbound());
                    }

                    // 填充GCP实际入组量
                    List<ReportDto> docGcpList = inboundGcpCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docGcpList)) {
                        cal.setTotalGcpInbound(docGcpList.get(0).getTotalInbound());
                    }

                    // 填充Pre实际入组量
                    List<ReportDto> docPreList = inboundPreCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docPreList)) {
                        cal.setTotalPreObsInbound(docPreList.get(0).getTotalInbound());
                    }

                    // 填充Spe实际入组量
                    List<ReportDto> docSpeList = inboundSpeCount.stream().filter(p -> p.getDoctorId() != null && doctorId != null && p.getDoctorId().equals(doctorId)
                            && p.getDeptId() != null && deptId != null && p.getDeptId().equals(deptId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(docSpeList)) {
                        cal.setTotalSpecialInbound(docSpeList.get(0).getTotalInbound());
                    }
                    // 新增实际加权入组人数
                    Integer addWeightInbound = getTotalWeightInbound(cal);
                    reportDto.setAddWeightInbound(addWeightInbound);
                });
            }
        } else {
            result.forEach(reportDto -> {
                reportDto.setAddInbound(reportDto.getTotalInbound());
                reportDto.setAddOInbound(reportDto.getTotalOInbound());
                reportDto.setAddIInbound(reportDto.getTotalIInbound());
                reportDto.setAddEInbound(reportDto.getTotalEInbound());
                // 新增实际加权入组人数
                Integer addWeightInbound = getTotalWeightInbound(reportDto);
                reportDto.setAddWeightInbound(addWeightInbound);
            });
        }
    }

    @Override
    public boolean isLockProject(ProjectEntity projectEntity) {
        return projectEntity.getProjectType().equals(ProjectStudyTypeEnum.GCP.getCode()) || projectEntity.getProjectType().equals(ProjectStudyTypeEnum.INTERVENTION_IIT.getCode());
    }

    /**
     * 获取未同步患者 empi 列表
     *
     * @param projectId
     */
    @Override
    public Map<String, String> listNotSync(Long projectId, List<String> empiList) {
        List<InboundProcessEntity> list = this.baseMapper.selectList(new QueryWrapper<InboundProcessEntity>().lambda()
                .eq(InboundProcessEntity::getProjectId, projectId)
                .eq(InboundProcessEntity::getStatus, PatTypeEnum.INBOUNDED.getCode())
                .in(!CollectionUtils.isEmpty(empiList), InboundProcessEntity::getEmpiid, empiList)
                .isNull(InboundProcessEntity::getEdcPatId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<String> empids = list.stream().map(InboundProcessEntity::getEmpiid).collect(Collectors.toList());
        List<PatientEntity> patientEntityList = patientService.list(new QueryWrapper<PatientEntity>().lambda().in(PatientEntity::getEmpiid, empids));
        Map<String, String> result = new LinkedHashMap<>();
        for (String empid : empids) {
            String regno = "";
            if (!CollectionUtils.isEmpty(patientEntityList)) {
                List<PatientEntity> curPatient = patientEntityList.stream().filter(p -> p.getEmpiid().equals(empid)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(curPatient)) {
                    regno = curPatient.get(0).getRegno();
                }
            }
            result.put(empid, regno);
        }
        return result;
    }
}

