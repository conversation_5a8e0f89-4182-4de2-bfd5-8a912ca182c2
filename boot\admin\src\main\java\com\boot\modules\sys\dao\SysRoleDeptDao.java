package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boot.modules.sys.entity.SysRoleDeptEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色与部门对应关系
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysRoleDeptDao extends BaseMapper<SysRoleDeptEntity> {
	
	/**
	 * 根据角色ID，获取部门ID列表
	 */
	List<Long> queryDeptIdList(Long[] roleIds);

	/**
	 * 根据角色ID数组，批量删除
	 */
	int deleteBatch(Long[] roleIds);
}
