package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.modules.sys.dao.SysTodoTaskRelatedDao;
import com.boot.modules.sys.entity.SysTodoTaskRelatedEntity;
import com.boot.modules.sys.service.SysTodoTaskRelatedService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 代办关联
 *
 * <AUTHOR>
 */
@Service
public class SysTodoTaskRelatedServiceImpl extends ServiceImpl<SysTodoTaskRelatedDao, SysTodoTaskRelatedEntity> implements SysTodoTaskRelatedService {

    /**
     * 查询代办任务关联
     */
    @Override
    public List<SysTodoTaskRelatedEntity> list(Map<String, Object> params) {
        Long id = MapUtils.getValue(params, "id", Long.class);
        Integer rangeType = MapUtils.getValue(params, "status", Integer.class);
        Long taskId = MapUtils.getValue(params, "taskId", Long.class);
        Long relatedId = MapUtils.getValue(params, "relatedId", Long.class);
        QueryWrapper<SysTodoTaskRelatedEntity> qwList = new QueryWrapper<>();
        qwList.lambda().eq(id != null, SysTodoTaskRelatedEntity::getId, id)
                .eq(rangeType != null, SysTodoTaskRelatedEntity::getRangeType, rangeType)
                .eq(taskId != null, SysTodoTaskRelatedEntity::getTaskId, taskId)
                .eq(relatedId != null, SysTodoTaskRelatedEntity::getRelatedId, relatedId);
        List<SysTodoTaskRelatedEntity> list = this.list(qwList);
        return list;
    }

    /**
     * 新增代办任务关联
     */
    @Override
    public boolean insert(SysTodoTaskRelatedEntity sysTodoTaskRelatedEntity) {
        boolean res = this.save(sysTodoTaskRelatedEntity);
        if (!res) {
            throw new BusinessException("插入失败");
        }
        return res;
    }

    /**
     * 修改代办任务关联
     */
    @Override
    public boolean update(SysTodoTaskRelatedEntity sysTodoTaskRelatedEntity) {
        boolean res = this.updateById(sysTodoTaskRelatedEntity);
        if (!res) {
            throw new BusinessException("插入失败");
        }
        return res;
    }

    /**
     * 删除代办任务关联
     */
    @Override
    public boolean delete(Long[] ids) {
        QueryWrapper<SysTodoTaskRelatedEntity> qw = new QueryWrapper<>();
        qw.lambda().in(SysTodoTaskRelatedEntity::getId, ids);
        boolean res = true;
        res = this.remove(qw);
        if (!res) {
            throw new BusinessException("删除失败");
        }
        return res;
    }
}
