//package com.boot.modules.sys.entity;
//
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableName;
//import com.boot.commons.validator.group.AddGroup;
//import com.boot.commons.validator.group.UpdateGroup;
//import lombok.Data;
//
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// * @desc : 用户数据权限表
// * @create 2022-05-19
// */
//@Data
//@TableName("rp_sys_user_dept")
//public class SysUserDeptEntity implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    @TableId
//    private Long id ;
//
//    @NotNull(message = "userID不许为空", groups = {AddGroup.class, UpdateGroup.class})
//    private Long userId;
//
//    @NotNull(message = "机构不许为空", groups = {AddGroup.class, UpdateGroup.class})
//    private Long deptId;
//
//    private String deptCode;
//
//}
