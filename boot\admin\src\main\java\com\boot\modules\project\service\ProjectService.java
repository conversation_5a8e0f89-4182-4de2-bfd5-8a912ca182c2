package com.boot.modules.project.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.project.vo.TotalProjectVo;
import com.boot.modules.sys.entity.SysUserEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :项目管理服务
 * @create 2021-02-23
 */
public interface ProjectService extends IService<ProjectEntity> {

    /**
     * 分页按条件查询项目
     *
     * @param params
     * @param isReturnOwnCreate 是否只返回自己创建
     * @return
     */
    PageUtils queryPage(Map<String, Object> params, SysUserEntity userEntity, boolean isReturnOwnCreate, boolean isReturnStatus);

    /**
     * 获取指定用户的全部项目
     *
     * @return
     */
    List<ProjectVo> queryList(Map<String, Object> params, SysUserEntity userEntity, boolean isReturnOwnCreate, boolean isReturnStatus);

    /**
     * 获取全部项目
     *
     * @return
     */
    List<ProjectVo> listAll();

    /**
     * 分页获取相关项目
     *
     * @param params
     * @return
     */
    PageUtils page(Map<String, Object> params);

    /**
     * 获取当前用户为项目管理员的所有项目列表
     * 如果是系统管理员 返回所有项目
     *
     * @param user
     * @return
     */
    List<ProjectVo> queryAdminProjectByUser(SysUserEntity user);

    /**
     * 根据id获取项目信息，包含项目中心
     *
     * @param projectId
     * @return
     */
    ProjectVo getInfoById(Long projectId);


    /**
     * 新增项目的同时  将项目业务表添加记录
     *
     * @param project
     * @return
     */
    boolean add(ProjectVo project);

    /**
     * 修改项目
     *
     * @param projectVo
     * @return
     */
    boolean updateProject(ProjectVo projectVo);

    /**
     * 设置项目用户角色信息
     *
     * @param result
     * @param userId
     */
    void getRoleInfo(List<ProjectVo> result, Long userId);

    /**
     * 修改过期项目的项目状态
     */
    void updateExpireProjects();

    /**
     * 批量删除项目
     *
     * @param idList
     * @return
     */
    boolean removeProjects(List<Long> idList);

    /**
     * excel 导入项目
     *
     * @param file
     */
    void excelImport(MultipartFile file, Long userId);

    /**
     * 获取指定条件下项目中各种总计统计结果
     *
     * @param params
     * @return
     */
    Collection<TotalProjectVo> total(Map<String, Object> params);

    /**
     * 导出为excel
     *
     * @param response
     * @param params
     */
    void export(HttpServletResponse response, Map<String, Object> params);

    /**
     * 修改医联体可见性
     * @param projectId
     * @return
     */
    boolean updateVisual(Long projectId);
}
