package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.annotation.DataFilter;
import com.boot.commons.constants.Const;
import com.boot.commons.enums.RoleLevelEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.modules.sys.dao.SysRoleDao;
import com.boot.modules.sys.entity.SysRoleDeptEntity;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.service.SysRoleDeptService;
import com.boot.modules.sys.service.SysRoleMenuService;
import com.boot.modules.sys.service.SysRoleService;
import com.boot.modules.sys.service.SysUserRoleService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 角色
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleDao, SysRoleEntity> implements SysRoleService {
    @Resource
    private SysRoleMenuService sysRoleMenuService;
    @Resource
    private SysRoleDeptService sysRoleDeptService;
    @Resource
    private SysUserRoleService sysUserRoleService;

    /**
     * 通过 {@see DataFilter} 过滤当前登录用户所在机构部门及子机构部门全部角色
     *
     * @param params
     * @return
     */
    @Override
    @DataFilter(subDept = false, user = false)
    public PageUtils queryPage(Map<String, Object> params) {
        String roleName = (String) params.get("roleName");

        QueryWrapper<SysRoleEntity> qw = MapUtils.getWrapperByParams(params, "create_time", "rp_sys_role", SysRoleEntity.class);
        qw.lambda().like(StringUtils.isNotBlank(roleName), SysRoleEntity::getRoleName, roleName);
//        qw.lambda().orderByDesc(SysRoleEntity::getCreateTime);

        // 机构过滤， 只能看当前机构下的角色
        if (params.get(Const.SQL_FILTER) != null) {
            List<SysRoleDeptEntity> entities = sysRoleDeptService.list(
                    new QueryWrapper<SysRoleDeptEntity>().lambda()
                            .apply(params.get(Const.SQL_FILTER) != null, (String) params.get(Const.SQL_FILTER))
            );

            if (!CollectionUtils.isEmpty(entities)) {
                qw.lambda().in(SysRoleEntity::getId,
                        entities.stream().map(SysRoleDeptEntity::getRoleId).collect(Collectors.toList()));
            } else {
                qw.lambda().eq(SysRoleEntity::getId, 0);
            }
        }

        IPage<SysRoleEntity> page = this.page(
                new Query<SysRoleEntity>().getPage(params), qw
        );

        return new PageUtils(page);
    }


    /**
     * 通过用户id，查询角色信息
     *
     * @param qwRole
     * @return
     */
    @Override
    public List<SysRoleEntity> getByQuery(QueryWrapper<SysRoleEntity> qwRole) {

        return baseMapper.getByQuery(qwRole);
    }

    /**
     * 获取指定范围的角色列表
     *
     * @param scope
     * @return
     */
    @Override
    public List<SysRoleEntity> getByScope(Integer scope) {

        QueryWrapper<SysRoleEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(SysRoleEntity::getRoleLevel, RoleLevelEnum.PROJECT.getLevel());

        List<SysRoleEntity> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<SysRoleEntity> result = new ArrayList<>();
        for (SysRoleEntity role : list) {
            List<Integer> roleScopeList = new ArrayList<>();
            String roleScopeStr = role.getScope();
            if (StringUtils.isNotBlank(roleScopeStr)) {
                //如果字符串最后一个字符是“,"   则去掉最后的”，“
                if (",".equals(roleScopeStr.substring(roleScopeStr.length() - 1))) {
                    roleScopeStr = roleScopeStr.substring(0, roleScopeStr.length() - 1);
                }

                String[] roleScopeStrArray = roleScopeStr.split(",");
                if (roleScopeStrArray.length > 0) {
                    for (String scopeStr : roleScopeStrArray) {
                        try {
                            if (StringUtils.isNotBlank(scopeStr)) {
                                Integer scopeInteger = Integer.valueOf(scopeStr);
                                roleScopeList.add(scopeInteger);
                            }
                        } catch (Exception e) {
                            throw new BusinessException("角色的使用范围设置错误");
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(roleScopeList)) {
                continue;
            }
            //如果当前角色使用范围包含指定的范围
            if (roleScopeList.contains(scope)) {
                result.add(role);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRole(SysRoleEntity role) {
        role.setCreateTime(new Date());


        this.save(role);

        //保存角色与菜单关系
        sysRoleMenuService.saveOrUpdate(role.getId(), null, role.getMenuIdList());

        // 保存角色与部门关系
        sysRoleDeptService.saveOrUpdate(role.getId(), role.getDeptIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysRoleEntity role) {
        this.updateById(role);

        //更新角色与菜单关系
        sysRoleMenuService.saveOrUpdate(role.getId(), null, role.getMenuIdList());

        //保存角色与部门关系
        sysRoleDeptService.saveOrUpdate(role.getId(), role.getDeptIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(Long[] roleIds) {
        //删除角色
        this.removeByIds(Arrays.asList(roleIds));

        //删除角色与菜单关联
        sysRoleMenuService.deleteBatch(roleIds);

        //删除角色与部门关联
        sysRoleDeptService.deleteBatch(roleIds);

        //删除角色与用户关联
        sysUserRoleService.deleteBatch(roleIds);
    }


}
