package com.boot.modules.openApi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("rp_edc_req_data_log")
@Data
public class EdcReqLogEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String url;

    private String name;

    private Long projectId;

    private String empiid;
    /**
     * 0 -失败 1-成功 -1默认值
     */
    private int status;

    private String errMessage;
    /**
     * 类型 1-项目 2-患者
     */
    private Integer type;
}
