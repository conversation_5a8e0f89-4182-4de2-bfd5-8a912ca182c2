package com.boot.modules.patient.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 医生推荐量统计返回参数
 */
@Data
public class DepartmentReportDto implements Serializable {

    /**
     * 科室名
     */
    private String department;

    /**
     * 科室名
     */
    private Long deptId;

    /**
     * 科室下医生总数
     */
    private Integer totalCount;


    /**
     * 参与推荐的医生总数
     */
    private Integer joinCount;

    /**
     * 推荐患者总数
     */
    private Integer sum;

    /**
     * 入组患者总数
     */
    private Integer intoGroup;
}
