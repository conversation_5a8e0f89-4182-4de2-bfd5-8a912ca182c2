package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc :待办任务关联表对应entity对象
 * @create 2021-05-13
 */
@Data
@Accessors(chain = true)
@TableName("rp_task_related")
public class SysTodoTaskRelatedEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 范围类型
     */
    @NotBlank(message = "范围类型不许为空", groups = {AddGroup.class})
    private Integer rangeType;
    /**
     * 任务表id
     */
    @NotBlank(message = "任务表id不许为空", groups = {AddGroup.class})
    private Long taskId;
    /**
     * 关联用户/角色id
     */
    @NotBlank(message = "关联id不许为空", groups = {AddGroup.class})
    private Long relatedId;
}
