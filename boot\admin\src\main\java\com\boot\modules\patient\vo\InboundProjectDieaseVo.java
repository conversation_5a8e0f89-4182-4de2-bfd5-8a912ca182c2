package com.boot.modules.patient.vo;

import com.boot.modules.patient.entity.InboundProcessEntity;
import lombok.Data;

@Data
public class InboundProjectDieaseVo extends InboundProcessEntity {
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 病种名称
     */
    private String diseaseName;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * PI
     */
    private String pi;

    /**
     * 患者状态：0-未锁定，1-锁定，默认值为0
     */
    private Integer patientStatus;
}
