package com.boot.modules.sys.service.impl;

public class BackupSqlServiceImpl {

    /**
     * 备份数据库
     *      eg： mysqldump -hlocalhost -P3306 -uroot -p123456 db > E:/back.sql
     * @param host
     * @param port
     * @param username
     * @param password
     * @param databasename
     * @param sqlname
     */
    public void dataBaseDump(String host, String port, String username, String password, String databasename, String sqlname){
        // 注意：mysqldump.exe路径中不能含有空格，我将这个文件拷贝到了项目中
    }
}
