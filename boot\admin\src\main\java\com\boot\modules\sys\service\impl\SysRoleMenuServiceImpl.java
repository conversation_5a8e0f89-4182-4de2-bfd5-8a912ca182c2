package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.ListUtils;
import com.boot.modules.sys.dao.SysRoleMenuDao;
import com.boot.modules.sys.entity.SysRoleMenuEntity;
import com.boot.modules.sys.service.SysRoleMenuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色与菜单对应关系
 *
 * <AUTHOR>
 */
@Service
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuDao, SysRoleMenuEntity> implements SysRoleMenuService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(Long roleId, Long appId, List<Long> menuIdList) {
        List<Long> menuIds = new ArrayList<>();
        if (appId != null) {
            menuIds = getMenuIdsByAppId(roleId, appId);

        } else {
            menuIds = queryMenuIdList(roleId);
        }
        //增加的元素组成的列表
        List<Long> aList = ListUtils.getAddListThanList(menuIdList, menuIds);
        List<SysRoleMenuEntity> add = new ArrayList<>();
        for (Long id : aList) {
            SysRoleMenuEntity sysRoleMenuEntity = new SysRoleMenuEntity();
            sysRoleMenuEntity.setMenuId(id);
            sysRoleMenuEntity.setRoleId(roleId);

            add.add(sysRoleMenuEntity);
        }
        if (!CollectionUtils.isEmpty(add)) {
            this.saveBatch(add);
        }

        //减少的元素组成的列表
        List<Long> bList = ListUtils.getReduceListThanList(menuIdList, menuIds);
        if (!CollectionUtils.isEmpty(bList)) {
            baseMapper.delete(
                    new QueryWrapper<SysRoleMenuEntity>()
                            .lambda()
                            .eq(SysRoleMenuEntity::getRoleId, roleId)
                            .in(SysRoleMenuEntity::getMenuId, bList)
            );
        }
    }

    @Override
    public List<Long> queryMenuIdList(Long roleId) {
        List<SysRoleMenuEntity> list = baseMapper.selectList(
                new QueryWrapper<SysRoleMenuEntity>()
                        .lambda().select(SysRoleMenuEntity::getMenuId)
                        .eq(SysRoleMenuEntity::getRoleId, roleId)
        );
        return list.stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(Long roleId, Long appId, List<Long> menuIdList, Long type) {
        List<Long> menuIds = new ArrayList<>();
        if (appId != null) {
            //chenwei 2022/10/24 查询添加字段(功能改造)
            menuIds = getMenuIdsByAppId(roleId, appId, type);

        } else {
            menuIds = queryMenuIdList(roleId, type);
        }
        //增加的元素组成的列表
        List<Long> aList = ListUtils.getAddListThanList(menuIdList, menuIds);
        List<SysRoleMenuEntity> add = new ArrayList<>();
        for (Long id : aList) {
            SysRoleMenuEntity sysRoleMenuEntity = new SysRoleMenuEntity();
            sysRoleMenuEntity.setMenuId(id);
            //chenwei 2022/10/24 1-内网 2-外网(功能改造)
            sysRoleMenuEntity.setType(type);
            sysRoleMenuEntity.setRoleId(roleId);

            add.add(sysRoleMenuEntity);
        }
        if (!CollectionUtils.isEmpty(add)) {
            this.saveBatch(add);
        }

        //减少的元素组成的列表
        List<Long> bList = ListUtils.getReduceListThanList(menuIdList, menuIds);
        if (!CollectionUtils.isEmpty(bList)) {
            baseMapper.delete(
                    new QueryWrapper<SysRoleMenuEntity>()
                            .lambda()
                            .eq(SysRoleMenuEntity::getRoleId, roleId)
                            //chenwei 2022/10/24 1-内网 2-外网(功能改造)
                            .eq(SysRoleMenuEntity::getType, type)
                            .in(SysRoleMenuEntity::getMenuId, bList)
            );
        }
    }

    @Override
    public List<Long> queryMenuIdList(Long roleId , Long type) {
        // select menu_id from sys_role_menu where role_id = #{value}
        // chenwei 2022/10/24 查询添加字段type(功能改造)
        List<SysRoleMenuEntity> list = baseMapper.selectList(
                new QueryWrapper<SysRoleMenuEntity>()
                        .lambda().select(SysRoleMenuEntity::getMenuId)
                        .eq(SysRoleMenuEntity::getRoleId, roleId)
                        .eq(SysRoleMenuEntity::getType,type)
        );
        return list.stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toList());
    }

    @Override
    public int deleteBatch(Long[] roleIds) {
        return baseMapper.deleteBatch(roleIds);
    }

    @Override
    public List<String> getRolePermission(Long appId, Long roleId) {
        ArrayList<String> list = new ArrayList<>();

        List<String> permissons = baseMapper.getRolePermission(appId, roleId)
                .stream().filter(s -> s != null && !s.isEmpty()).map(String::trim).collect(Collectors.toList());
        permissons.forEach(str -> {
            if (str.contains(",")) {
                String[] strings = str.split(",");
                list.addAll(Arrays.asList(strings));
            } else {
                list.add(str);
            }
        });
        return list.stream().distinct().collect(Collectors.toList());

    }

    /**
     * 查询当前应用 当前角色的菜单权限id
     *
     * @param roleId
     * @param appId
     * @return
     */
    private List<Long> getMenuIdsByAppId(Long roleId, Long appId) {

        if (roleId == null || appId == null) {
            return null;
        }
        List<SysRoleMenuEntity> list = new ArrayList<>();
        QueryWrapper<SysRoleMenuEntity> qw = new QueryWrapper<>();
        qw.eq("a.app_id", appId)
                .eq("b.role_id", roleId);

        list = baseMapper.getByQuery(qw);

        return list.stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toList());
    }

    /**
     * 查询当前应用 当前角色的菜单权限id
     *
     * @param roleId
     * @param appId
     * @return
     */
    private List<Long> getMenuIdsByAppId(Long roleId, Long appId, Long type) {

        if (roleId == null || appId == null) {
            return null;
        }
        //chenwei 2022/10/24 查询添加字段type(功能改造)
        List<SysRoleMenuEntity> list = new ArrayList<>();
        QueryWrapper<SysRoleMenuEntity> qw = new QueryWrapper<>();
        qw.eq("a.app_id", appId)
                .eq("b.role_id", roleId)
                .eq("b.type",type);

        list = baseMapper.getByQuery(qw);

        return list.stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toList());

    }

}
