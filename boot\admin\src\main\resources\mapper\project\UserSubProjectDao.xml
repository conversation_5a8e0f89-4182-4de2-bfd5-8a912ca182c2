<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.UserSubProjectDao">
    <!-- 分页查询子项目用户 -->
    <select id="queryPage" resultType="com.boot.modules.project.vo.UserSubProjectVo">
        SELECT a.*,
            b.username     AS userName,
            b.nickname     AS nickName,
            a.proj_role_id AS roleId,
            c.role_name    AS roleName,
            d.id           AS deptId,
            d.`name`       AS deptName
        FROM
            rp_user_subproject a
        LEFT JOIN
            (SELECT
                f.id,
                f.userName,
                f.nickname,
                substring_index(substring_index(f.dept_id, ',', g.help_topic_id + 1), ',', - 1) dept_id
            FROM
                rp_sys_user f
            JOIN
                mysql.help_topic g
            ON
                g.help_topic_id &lt; (LENGTH(f.dept_id) - LENGTH(REPLACE(f.dept_id, ',', '')) + 1)) b on a.user_id = b.id
        LEFT JOIN
            rp_sys_role c on a.proj_role_id = c.id
        LEFT JOIN
            rp_sys_dept d ON b.dept_id = d.id
        LEFT JOIN
            rp_sub_project e ON e.id = a.sub_project_id
        ${ew.customSqlSegment}
    </select>

    <!-- 按条件查询查询子项目用户 -->
    <select id="getByQuery" resultType="com.boot.modules.project.entity.UserSubProjectEntity">
        SELECT a.*
        FROM `rp_user_subproject` a
                ${ew.customSqlSegment}
    </select>
    <select id="selectByQuery" resultType="com.boot.modules.project.vo.UserSubProjectVo">
        SELECT a.id              as userSubProjectId,
               a.user_id         as userId,
               a.proj_role_id    as roleId,
               a.project_dept_id as projDeptId,
               a.project_dept_id as deptId,
               a.top_dept_id     as topDeptId,
               c.name            as projDeptName,
               c.name            as deptName,
               d.username        as username,
               d.nickname        as nickname,
               e.role_name       as roleName,
               f.name            as parentDeptName
        FROM rp_user_subproject a
                     LEFT JOIN rp_sys_dept c ON a.project_dept_id = c.id
                     LEFT JOIN rp_sys_user d ON a.user_id = d.id
                     LEFT JOIN rp_sys_role e ON a.proj_role_id = e.id
                     LEFT JOIN rp_sys_dept f ON c.pid = f.id
                ${ew.customSqlSegment}
    </select>
<!--    <select id="getProjMember" resultType="com.boot.modules.project.vo.UserSubProjectVo">-->
<!--        SELECT a.id           AS userSubProjectId,-->
<!--               a.user_id      AS userId,-->
<!--               a.proj_role_id AS roleId,-->
<!--               d.username,-->
<!--               d.nickname,-->
<!--               e.role_name     AS roleName-->
<!--        FROM rp_user_subproject a-->
<!--                 LEFT JOIN rp_sys_dept c ON a.project_dept_id = c.id-->
<!--                 right JOIN rp_sys_user d ON a.user_id = d.id-->
<!--                 LEFT JOIN rp_sys_role e ON a.proj_role_id = e.id-->
<!--        WHERE a.sub_project_id = #{subProjectId} and b.project_id = #{projectId}-->
<!--    </select>-->
    <select id="selectByProjectId" resultType="com.boot.modules.project.entity.UserSubProjectEntity">
        SELECT a.*
        FROM rp_user_subproject a
                     RIGHT JOIN rp_sub_project b ON a.sub_project_id = b.id
        WHERE b.project_id = #{projectId}
          and a.id is not NULL
    </select>
    <select id="getUserCenter" resultType="java.lang.Long">
        SELECT a.project_dept_id
        FROM rp_user_subproject a
                ${ew.customSqlSegment}
    </select>
<!--    <select id="getUserCenter" resultType="java.lang.Long">-->
<!--        SELECT b.dept_id-->
<!--        FROM rp_user_subproject a-->
<!--                     LEFT JOIN rp_project_dept b ON a.project_dept_id = b.id-->
<!--                ${ew.customSqlSegment}-->
<!--    </select>-->
<!--    <select id="getAdminUserCenter" resultType="java.lang.Long">-->
<!--        SELECT b.dept_id-->
<!--        FROM rp_project a-->
<!--         JOIN rp_sys_user_dept b ON a.project_admin_id=b.user_id-->
<!--                ${ew.customSqlSegment}-->
<!--    </select>-->
    <select id="listByQuery" resultType="com.boot.modules.project.vo.UserSubProjectVo">
        SELECT
            a.*,
            b.*
        FROM
            rp_user_subproject a
                LEFT JOIN rp_sys_user b ON a.user_id = b.id
            ${ew.customSqlSegment}
    </select>
    <select id="getByMobile" resultType="com.boot.modules.sys.entity.SysUserEntity">
        SELECT
            b.*
        FROM
            rp_user_subproject a
                LEFT JOIN rp_sys_user b ON a.user_id = b.id
            ${ew.customSqlSegment}
    </select>
    <select id="query" resultType="com.boot.modules.sys.vo.UserSubProjectDeptRoleVo">
        SELECT
            a.*,b.role_name as roleName,b.scope as roleScope
        FROM
            rp_user_subproject a
                LEFT JOIN rp_sys_role b ON a.proj_role_id = b.id
            ${ew.customSqlSegment}
    </select>

</mapper>