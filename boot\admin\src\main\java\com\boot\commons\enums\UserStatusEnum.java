package com.boot.commons.enums;

/**
 * <AUTHOR>
 * @create 2020-11-23 16:40
 */
public enum UserStatusEnum {

    /**
     * 账号正常、
     */
    UN_LOCK(1, "账号正常"),

    /**
     * 账号被锁、停用
     */
    LOCK(0, "账号被锁");


    private int type;

    private String desc;

    UserStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static UserStatusEnum getByType(int type) {
        UserStatusEnum[] values = UserStatusEnum.values();
        UserStatusEnum result = null;
        for (UserStatusEnum value : values) {

            if (value.type == type) {
                result = value;
            }
        }
        return result;
    }

}
