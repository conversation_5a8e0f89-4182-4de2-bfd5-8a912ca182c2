<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysRoleMenuDao">
    <cache-ref namespace="com.boot.modules.sys.dao.SysRoleMenuDao"/>
    <delete id="deleteBatch">
        delete from rp_sys_role_menu where role_id in
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <select id="getByQuery" resultType="com.boot.modules.sys.entity.SysRoleMenuEntity">
        SELECT b.*
        FROM rp_sys_menu a
                 LEFT JOIN rp_sys_role_menu b on a.id = b.menu_id
            ${ew.customSqlSegment}
    </select>
    <select id="getRolePermission" resultType="java.lang.String">
        SELECT esm.permissions
        FROM rp_sys_role_menu esrm
                 LEFT JOIN rp_sys_menu esm ON esrm.menu_id = esm.id

        WHERE esm.app_id = #{appId}
          AND esrm.role_id = #{roleId}
        GROUP BY esrm.menu_id
    </select>


</mapper>