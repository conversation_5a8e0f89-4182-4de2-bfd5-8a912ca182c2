package com.boot.commons.socket;


import com.boot.commons.config.WebSocketConfig;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.impl.SysUserServiceImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * @Author: tanleijin
 * @description ()
 * @Date:2018/9/14 17:45
 */
@ServerEndpoint(value = "/userMessageSocket",configurator = WebSocketConfig.class)
@Component
public class UserMessageSocket {

    private static ApplicationContext applicationContext;
    private static int onlineCount = 0;
    private static CopyOnWriteArraySet<UserMessageSocket> webSocketSet = new CopyOnWriteArraySet<>();
    private Session session;

    private SysUserServiceImpl userService;

    //todo 这里需要一个变量来接收shiro中登录的人信息
    private SysUserEntity shiroUser ;
    @OnOpen
    public void onOpen (Session session){
        this.session = session;
        //注入userService
        this.userService = applicationContext.getBean(SysUserServiceImpl.class);

        //设置用户
        this.shiroUser =  (SysUserEntity) session.getUserProperties().get("user");
        webSocketSet.add(this);
        addOnlineCount();
        System.out.println("有新链接加入!当前在线人数为" + getOnlineCount());
    }

    @OnClose
    public void onClose (){
        webSocketSet.remove(this);
        subOnlineCount();
        System.out.println("有一链接关闭!当前在线人数为" + getOnlineCount());
    }

    @OnMessage
    public void onMessage (String message, Session session) throws IOException {
        //todo 1.制造给自己的初始化消息
        String msg = userService.createUserMessage(this.shiroUser);
        //2.发送给自己
        sendMessage (msg);
    }

    public void sendMessage (String message) throws IOException {
        //String m = "1,2,3,4,5";
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 群发自定义消息
     */
    public void sendInfo() throws IOException {
        for (UserMessageSocket item : webSocketSet) {
            try {
                String msg = userService.createUserMessage(item.shiroUser);
                //2.发送消息
                item.sendMessage(msg);
            } catch (IOException e) {
                continue;
            }
        }
    }
    public static synchronized  int getOnlineCount (){
        return UserMessageSocket.onlineCount;
    }
    public static synchronized void addOnlineCount (){
        UserMessageSocket.onlineCount++;
    }
    public static synchronized void subOnlineCount (){
        UserMessageSocket.onlineCount--;
    }
    public static void setApplicationContext(ApplicationContext applicationContext) {
        UserMessageSocket.applicationContext = applicationContext;
    }
}