package com.boot.commons.enums;

/**
 * 业务类型枚举类
 *
 * <AUTHOR>
 */
public enum BusinessEnum {
    /**
     * 子项目创建审核
     */
    PROJECT_AUDIT("A001", "项目创建审核", true),

    EXPORT_AUDIT("A002", "导出审核", true),

    EXPORT_RECORD("A003", "数据导出", true),

    /**
     * 随访窗口期内
     */
    VISIT_INSIDE("A004", "随访窗口内", true),

    /**
     * 随访超期未随访
     */
    VISIT_EXPIRED("A005", "随访超期", true);


    private String code;

    private String desc;

    /**
     * 是否系统待办事项
     */
    private boolean isSystem;

    BusinessEnum(String code, String desc, boolean isSystem) {
        this.code = code;
        this.desc = desc;
        this.isSystem = isSystem;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
