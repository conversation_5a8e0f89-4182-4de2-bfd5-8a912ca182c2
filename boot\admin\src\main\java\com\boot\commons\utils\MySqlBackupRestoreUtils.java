package com.boot.commons.utils;

import com.boot.commons.constant.BackupConstants;
import com.boot.commons.enums.BackupTaskEnums;
import lombok.extern.log4j.Log4j2;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * MySQL备份还原工具类
 */
@Log4j2
public class MySqlBackupRestoreUtils {

    /**
     * 20MB字节流
     */
    private static ByteBuffer byteBuffer = ByteBuffer.allocate(BackupConstants.LENGTH_20MB);


    /**
     * 备份数据库
     *
     * @param backupFolderPath 备份的路径
     * @param fileName         备份的文件名
     * @param database         需要备份的数据库的名称
     * @param mysqlPath        需要备份的数据库的安装位置
     * @return
     * @throws IOException
     */
    public static BackupTaskEnums backup(
                                 String backupFolderPath,
                                 String fileName,
                                 String database,
                                 String mysqlPath
                                 ) {
        File backupFolderFile = new File(backupFolderPath);

        Process process = null;
        boolean b = false;

        if (!backupFolderFile.exists()) {
            // 如果目录不存在则创建
            b = backupFolderFile.mkdirs();
        }
        if (!b) {
            log.error("创建数据库备份目录失败---" + "目录:" + backupFolderFile);
            return BackupTaskEnums.BACKUP_ERROR;
        }
        if (!backupFolderPath.endsWith(File.separator) && !backupFolderPath.endsWith("/")) {
            backupFolderPath = backupFolderPath + File.separator;
        }

        // 拼接命令行的备份命令
        String backupFilePath = backupFolderPath + fileName;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("mysqldump ");
        //指定my.ini文件路径
        stringBuilder.append("--defaults-extra-file=").append(mysqlPath);
        // 生成数据库创建信息
        stringBuilder.append(" --databases ");
        //指定要备份的数据库名称
        stringBuilder.append(" ").append(database);
        //指定备份文件的存放位置与后缀名
        stringBuilder.append(" > ").append(backupFilePath);

        log.info("数据库备份开始:" + stringBuilder.toString());

        String[] command = getCommand(stringBuilder.toString());
        //  调用外部执行 exe 文件的 Java API执行导出
        try {
            process = Runtime.getRuntime().exec(command);
            if (process.waitFor() == 0) {
                log.info("数据库备份成功，保存路径：" + backupFilePath + " 文件中");
            }
        } catch (IOException e) {
            log.error("备份" + database + " 数据库 出现 IO异常 ", e);
        } catch (InterruptedException e) {
            log.error("备份" + database + " 数据库 执行命令出现中断异常 ", e);
        }

        // 压缩备份文件
        try(

                FileInputStream is = new FileInputStream(new File(backupFilePath));
                FileChannel inChannel = is.getChannel();
                ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(backupFilePath + ".zip"))
        ) {
            log.info("开始压缩数据库文件---"+ backupFilePath);
            //  这个是压缩sql文件，利用文件名创建条目sqlname以.sql结尾
            ZipEntry zipEntry = new ZipEntry(fileName);
            //  放进Entry
            zos.putNextEntry(zipEntry);
            while (inChannel.read(byteBuffer) != -1) {
                byteBuffer.clear();
                zos.write(byteBuffer.array());
                zos.flush();
            }
            log.info("压缩数据库文件成功---"+ backupFilePath +".zip");
            return BackupTaskEnums.BACKUP_SUCCESS;

        } catch (IOException e) {
            log.error("压缩" + database + " 数据库文件 出现 IO异常 ", e);

        } catch (Exception e) {
            log.error("压缩" + database +" 数据库文件 出现 其他异常", e);
        }

        return BackupTaskEnums.BACKUP_ZIP_ERROR;
    }

    /**
     * 还原数据库
     *
     * @param restoreFilePath 数据库备份的脚本路径
     * @return
     */
    public static boolean restore(String restoreFilePath)
    {
        File restoreFile = new File(restoreFilePath);
        if (restoreFile.isDirectory()) {
            for (File file : Objects.requireNonNull(restoreFile.listFiles())) {
                if (file.exists() && file.getPath().endsWith(".sql")) {
                    restoreFilePath = file.getAbsolutePath();
                    break;
                }
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("mysql");
        stringBuilder.append(" < ").append(restoreFilePath);
        try {
            Process process = Runtime.getRuntime().exec(getCommand(stringBuilder.toString()));
            if (process.waitFor() == 0) {
                log.info("数据已从 " + restoreFilePath + " 导入到数据库中");
            }
        } catch (Exception e) {
            log.error("---------数据库还原失败---------", e);
            e.printStackTrace();
            return false;
        }
        return true;
    }

    private static String[] getCommand(String command) {
        // 拼接cmd命令  windows下 cmd   Linux下 /bin/sh
        String os = System.getProperty("os.name");
        String shell = "/bin/sh";
        String c = "-c";
        if (os.toLowerCase().startsWith("win")) {
            shell = "cmd";
            c = "/c";
        }
        String[] cmd = {shell, c, command};
        return cmd;
    }

    private static void close(ZipOutputStream zos, OutputStream os, InputStream is) {
        try {
            if (zos != null) {
                zos.close();
            }
            if (os != null) {
                os.close();
            }
            if (is != null) {
                is.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 递归删除
     * 删除某个目录及目录下的所有子目录和文件
     * @param file 文件或目录
     * @return 删除结果
     */
    public static boolean delFiles(File file){
        boolean result = false;
        //目录
        if(file.isDirectory()){
            File[] childrenFiles = file.listFiles();
            for (File childFile:childrenFiles){
                result = delFiles(childFile);
                if(!result){
                    return result;
                }
            }
        }
        //删除 文件、空目录
        result = file.delete();
        return result;
    }

    public static void main(String[] args) throws Exception {
        String host = "localhost";
        String userName = "root";
        String password = "123456";
        String database = "boot-admin";
        String mysqlPath = "";
        System.out.println("开始备份");
        String backupFolderPath = "d:/dev/";
        String fileName = "mdh";
      //  backup(host, userName, password, backupFolderPath, fileName, database, mysqlPath);
        System.out.println("备份成功");

        /*System.out.println("开始还原");
        String restoreFilePath = "d:/dev/mdh.sql";
        restore(restoreFilePath, host, userName, password, database);
        System.out.println("还原成功");*/

    }

}
