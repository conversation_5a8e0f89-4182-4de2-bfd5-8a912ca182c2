package com.boot.commons.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.service.*;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.sync.service.KelinSyncService;
import com.boot.modules.sys.service.SysLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同一个线程串行执行
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SchedulerTask {

    @Resource
    private SysLogService sysLogService;

    @Resource
    private ProjectService projectService;

    @Resource
    private KelinSyncService kelinSyncService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private PreRecommendService preRecommendService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private PatientService patientService;

    @Resource
    private DeptRecommendCountService deptRecommendCountService;
    @Resource
    private RecommendCountService recommendCountService;

    /**
     * 定期删除30天之前的日志数据， 每天晚上两点执行,
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Async("bootExecutor")
    public void deleteLog() {
        sysLogService.deleteBeforeDays(30);
    }

    /**
     * 每天晚上两点半 自动扫描项目是否过期
     */
//    @Scheduled(cron = "0 30 2 * * ?")
//    @Async("bootExecutor")
//    public void updateProjectStatus() {
//        projectService.updateExpireProjects();
//    }

    /**
     * 通知克林写数据
     * 需要定期去通知克林写数据
     * 每天22点执行一次
     */
    @Scheduled(cron = "0 0 22 * * *")
    @Async("bootExecutor")
    public void autoNotice() throws ParseException {
        kelinSyncService.autoNotice();
    }

    /**
     * 检索病例
     * 需要定期去入组条件订阅的数据
     * 每天23点执行一次
     */
    @Scheduled(cron = "0 0 23 * * *")
    @Async("bootExecutor")
    public void autoIntoCase() throws ParseException {
        kelinSyncService.autoIntoCase();
    }

    /**
     * 对过期未签署进行异常退出处理
     * 每天0点执行
     */
    @Scheduled(cron = "0 0 0 * * *")
    @Async("bootExecutor")
    public void expiredPatClean() throws ParseException {
        inboundProcessService.expiredPatClean();
    }

    /**
     * 对移动端推荐需要重新同步的患者进行信息重新同步
     * 每天0点执行
     */
    @Scheduled(cron = "0 0 0 * * *")
    @Async("bootExecutor")
    public void resetInfo() throws ParseException {
        patientService.resetInfo();
    }


    /**
     * 统计预推荐患者中，当天就诊的患者
     * 每天1点执行
     */
    @Scheduled(cron = "0 0 1 * * *")
    @Async("bootExecutor")
    public void todayPat() throws ParseException {
        preRecommendService.todayEmpi();
    }

    /**
     * 受试者列表-推荐时间超过一个月自动退回推荐池
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Async("bootExecutor")
    public void changePatStatus() {
        // 已推荐项目超过30天的，进入回退状态 (状态回退 设置编码为 6)
        QueryWrapper<DoctorRecommendationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.last(" and TIMESTAMPDIFF(DAY, recommend_time, NOW()) > 30");
        List<DoctorRecommendationEntity> list = doctorRecommendationService.list(queryWrapper);

        List<String> empiidList = list.stream().map(DoctorRecommendationEntity::getEmpiid).collect(Collectors.toList());

        if (empiidList.size() > 0) {
            UpdateWrapper<DoctorRecommendationEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("status", 1);
            updateWrapper.in("empiid", empiidList).set("status", 6);
            boolean update = doctorRecommendationService.update(updateWrapper);

            log.info("受试者列表-推荐时间超过一个月自动退回推荐池-定时任务执行完成：" + empiidList.size() + "条数据 ——> " + update);
        } else {
            log.info("受试者列表-推荐时间超过一个月自动退回推荐池-定时任务执行完成：无自动退回患者");
        }

    }

    /**
     * 更新中间表数据
     */
    @Scheduled(cron = "0 0 3 * * *")
    @Async("bootExecutor")
    public void updateRecommendMid() {
        log.info("开始更新推荐数中间表");
        deptRecommendCountService.updateNewDate();
        recommendCountService.updateNewDate();
        log.info("更新推荐数中间表完成");
    }
}

