package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.constants.Const;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.Result;

import com.boot.commons.result.R;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.service.SysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 机构管理
 *
 * <AUTHOR>
 */
@Api(tags = "组织机构管理")
@RestController
@RequestMapping("/sys/dept")
public class SysDeptController extends AbstractController {
    @Resource
    private SysDeptService sysDeptService;

    /**
     * 列表
     */
    @GetMapping("/list")
    @RequiresPermissions("sys:dept:list")
    public Result list() {

        List<SysDeptEntity> deptList = new ArrayList<>();

        deptList = sysDeptService.queryList(new HashMap<String, Object>());

        return R.success(deptList);
    }

    @GetMapping("/parent/code/list")
    @RequiresPermissions("sys:dept:list")
    public Result getParentDeptAndCodeNotNull() {
        List<SysDeptEntity> deptList = sysDeptService.getParentDeptAndCodeNotNull();
        return R.success(deptList);
    }


    /**
     * 设置为研究中心列表
     *
     * @return
     */
    @GetMapping("/research/center/list")
    @RequiresPermissions("sys:dept:list")
    public Result selectResearchCenterDept() {
        List<SysDeptEntity> deptList = new ArrayList<>();

        deptList = sysDeptService.queryListShowResearchCenter();
        //过滤掉医疗机构级别的
        if (!CollectionUtils.isEmpty(deptList)) {
            deptList = deptList.stream().filter(o -> !o.getPid().equals(0L) && !ObjectUtils.isEmpty(o.getPid())).collect(Collectors.toList());
        }
        // 带上第一级机构名称
        List<SysDeptEntity> allDept = sysDeptService.getAll();
        for (SysDeptEntity deptEntity : deptList) {
            SysDeptEntity top = sysDeptService.getTopDeptById(allDept, deptEntity.getId());
            if (!top.getName().equals(deptEntity.getParentName())) {
                deptEntity.setParentName(top.getName() + "-" + deptEntity.getParentName());
            }
        }

        return R.success(deptList);
    }

    /**
     * 选择部门(添加、修改菜单)
     */
    @GetMapping("/select")
    @RequiresPermissions("sys:dept:select")
    public Result select() {
        List<SysDeptEntity> deptList = new ArrayList<>();

        //添加一级部门
        if (getUserId() == Const.SUPER_ADMIN || getUser().getIsSysAdmin().equals(1)) {
            SysDeptEntity root = new SysDeptEntity();
            root.setId(0L);
            root.setName("一级部门");
            root.setPid(-1L);
            root.setOpen(true);
            root.setLevel(0);

            List<SysDeptEntity> childList = sysDeptService.queryList(new HashMap<String, Object>());

            root.setChildren(childList);

            deptList.add(root);
        } else {
            deptList = sysDeptService.queryList(new HashMap<String, Object>());

        }

        return R.success(deptList);
    }

    /**
     * 上级部门Id(管理员则为0)
     */
    @GetMapping("/info")
    @RequiresPermissions("sys:dept:parentid")
    public Result info() {
        long deptId = 0;
        if (!isSuperAdmin()) {
            List<SysDeptEntity> deptList = sysDeptService.queryList(new HashMap<String, Object>());
            Long parentId = null;
            for (SysDeptEntity sysDeptEntity : deptList) {
                if (parentId == null) {
                    parentId = sysDeptEntity.getPid();
                    continue;
                }

                if (parentId > sysDeptEntity.getPid()) {
                    parentId = sysDeptEntity.getPid();
                }
            }
            deptId = parentId;
        }

        return R.success(deptId);
    }

    /**
     * 信息
     */
    @GetMapping("/{id}")
    @RequiresPermissions("sys:dept:info")
    public Result info(@PathVariable("id") Long id) {
        SysDeptEntity dept = sysDeptService.getById(id);
        // 获取上级科室的信息
        if (dept.getPid() != null && dept.getPid() > 0) {
            SysDeptEntity parent = sysDeptService.getById(dept.getPid());
            dept.setParentName(parent.getName());
        } else {
            dept.setParentName("一级部门");
        }

        return R.success(dept);
    }

    /**
     * 根据deptCode获取第一层级的机构
     */
    @GetMapping("/code")
    public Result getParentDeptByCode(@RequestParam("deptCode") String deptCode) {
        List<SysDeptEntity> list = sysDeptService.list(
                new QueryWrapper<SysDeptEntity>()
                        .lambda().eq(SysDeptEntity::getCode, deptCode)
                        .eq(SysDeptEntity::getPid, 0)
        );

        if (!CollectionUtils.isEmpty(list)) {
            return R.success(list.get(0));
        }

        return R.success(null);
    }


    /**
     * 保存
     */
    @SysLog("保存科室")
    @PostMapping()
    @RequiresPermissions("sys:dept:save")
    public Result save(@RequestBody SysDeptEntity dept) {
        ValidatorUtils.validateEntity(dept, AddGroup.class);
        boolean res = sysDeptService.addDept(dept);

        return res ? R.success("新增成功", true) : R.fail("新增失败");
    }

    /**
     * 修改
     */
    @SysLog("修改科室")
    @PutMapping()
    @RequiresPermissions("sys:dept:update")
    public Result update(@RequestBody SysDeptEntity dept) {
        ValidatorUtils.validateEntity(dept, UpdateGroup.class);
        sysDeptService.updateDept(dept);

        return R.success();
    }

    /**
     * 删除
     */
    @SysLog("删除科室")
    @DeleteMapping("/{deptId}")
    @RequiresPermissions("sys:dept:delete")
    public Result delete(@PathVariable("deptId") long deptId) {
        //判断是否有子部门
        List<Long> deptList = sysDeptService.queryDeptIdList(deptId);
        if (deptList != null && deptList.size() > 0) {
            return R.fail("请先删除子部门");
        }

        sysDeptService.removeById(deptId);

        return R.success();
    }

    /**
     * 数据中心的科室
     */
    @GetMapping("/listDataCenter")
    @RequiresPermissions("sys:dept:list")
    public Result listDataCenter() {
        List<SysDeptEntity> deptList = new ArrayList<>();
        deptList = sysDeptService.queryDataCenter();

        return R.success(deptList);
    }

    @ApiOperation(
            value = "获取华西医院的所有科室",
            notes = "获取华西医院的所有科室"
    )
    @GetMapping("/hxDept")
    @RequiresPermissions("proj:project:info")
    public Result hxDept() {
        String[] deptIdArr = BootAdminProperties.deptIdList.split(",");
        List<Long> deptIds = new ArrayList<>();
        for (String deptId : deptIdArr) {
            deptIds.add(Long.parseLong(deptId));
        }
        return R.success(sysDeptService.getSubDeptList(deptIds));
    }
}
