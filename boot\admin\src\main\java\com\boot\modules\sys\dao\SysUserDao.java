package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.sys.entity.SysUserEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统用户
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysUserDao extends BaseMapper<SysUserEntity> {

	/**
	 * 查询用户的所有权限
	 * @param userId  用户ID
	 */
	List<String> queryAllPerms(Long userId);

	/**
	 * 查询用户的所有菜单ID
	 */
	List<Long> queryAllMenuId(Long userId);

	/**
	 * 按内外网查询用户的所有菜单ID
	 */
	List<Long> queryAllMenuIdByType(Long userId, Long type);
	/**
	 * 分页查询用户信息
	 * @param pageFilter
	 * @param qw
	 * @return
	 */
    IPage<SysUserEntity> queryPage(IPage<SysUserEntity> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);

    List<SysUserEntity> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);

	/**
	 * 根据deptId获取对应用户信息
	 * @param qw
	 * @return
	 */
	List<SysUserEntity> listByDeptId(@Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);

	/**
	 * 根据deptId获取对应用户信息
	 * @param qw
	 * @return
	 */
	IPage<SysUserEntity> pageByDeptId(IPage<SysUserEntity> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);

	/**
	 * 获取指定条用户及机构条件的用户信息
	 * @param qw
	 * @return
	 */
	List<SysUserEntity> getUserAndDept( @Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);


	List<ReportDto> listCountDisease(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

	List<ReportDto> listCountProject(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

	List<SysUserEntity> getByDeptId(@Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);

	List<ReportDto> listAll(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);
}
