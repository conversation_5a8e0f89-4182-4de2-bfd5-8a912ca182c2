package com.boot.modules.sys.service;

import com.boot.modules.sync.model.KelinSearchResponse;
import com.boot.modules.sync.vo.KelinRequestVo;
import com.github.lianjiatech.retrofit.spring.boot.annotation.OkHttpClientBuilder;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import okhttp3.OkHttpClient;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.concurrent.TimeUnit;

@RetrofitClient(
        baseUrl = "${boot.external.user-api}",
        poolName = "userApi"
)
public interface UserHttpApiService {
    @OkHttpClientBuilder
    static OkHttpClient.Builder okhttpClientBuilder() {
        return new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS);
    }

    /*
     * 获取token
     */
    @POST("oauth2/accessToken")
    String getAccessToken(@Query("appKey") String appKey, @Query("appSecret") String appSecret);

    /*
     * 柯林动态获取全院用户接口
     */
    @POST("api/metadata/V1.0/object_FC15B801E5F0B499E055000000000001/getList")
    String getList(@Header("Authorization") String Authorization,
                   @Query("limit") Integer limit,
                   @Query("offset") Integer offset);
}
