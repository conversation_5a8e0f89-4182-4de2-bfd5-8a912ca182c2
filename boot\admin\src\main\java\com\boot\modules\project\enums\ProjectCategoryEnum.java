package com.boot.modules.project.enums;

/**
 * <AUTHOR>
 * @desc : 项目分类枚举
 * @create 2021-05-20
 */
public enum ProjectCategoryEnum {

    /**
     * 单中心， 只有一个主中心
     */
    SINGLE_CENTER(1),

    /**
     * 多中心 有多个分中心
     */
    MULTIPLE_CENTER(2),
    ;

    private Integer type;

    ProjectCategoryEnum(int type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static ProjectCategoryEnum getEnumByType(int type) {
        ProjectCategoryEnum[] types = ProjectCategoryEnum.values();
        for (ProjectCategoryEnum enme : types) {
            if (type == enme.getType()) {
                return enme;
            }
        }
        return null;
    }
}
