-- 2024-11-07 chenwei 推荐表添加备注和拒绝原因字段
ALTER TABLE rp_doctor_recommendation ADD refuse_reason varchar(100) NULL COMMENT '驳回理由';
ALTER TABLE rp_doctor_recommendation ADD content varchar(500) NULL COMMENT '备注';


-- 2024-11-07 chenwei 添加患者自定义勾选纳排记录表
DROP TABLE IF EXISTS `rp_pat_custom_record`;
CREATE TABLE `rp_pat_custom_record` (
  `id` char(32) NOT NULL COMMENT '主键ID',
  `empiid` varchar(100) NOT NULL COMMENT '患者唯一ID',
  `group_config_id` bigint NOT NULL COMMENT '配置ID',
  PRIMARY KEY (`id`),
  KEY `rp_pat_custom_record_empi_IDX` (`empiid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 2024-11-08 chenxia 添加推荐医院ID，用于统计医院推荐数
ALTER TABLE rp_doctor_recommendation ADD top_recommend_dept_id bigint NULL COMMENT '推荐医院ID';

-- 2024-11-12 chenwei 患者表添加民族字段存入
ALTER TABLE rp_patient ADD nation varchar(100) NULL COMMENT '民族';

-- 2024-11-25 chenwei 纳排配置表添加是否是医联体配置
ALTER TABLE rp_group_config ADD is_ylt INT DEFAULT 0 NOT NULL COMMENT '是否是医联体配置 0-否 1-是';
-- 2024-11-25 chenwei 患者表添加医院编码字段、是否需要重新同步字段、源empiid
ALTER TABLE rp_patient ADD med_code varchar(100) DEFAULT 'HID0101' NULL COMMENT '医疗机构编码默认华西';
ALTER TABLE rp_patient ADD is_need_sync INT DEFAULT 0 NOT NULL COMMENT '是否需要重新同步数据 0-否 1-是';
ALTER TABLE rp_patient ADD src_empiid varchar(100) NOT NULL COMMENT '归一后的empi';
-- 2024-12-09 患者更新归一empi历史数据
update rp_patient set src_empiid  = empiid;


-- 推送华医通记录表
DROP TABLE IF EXISTS `rp_push_hyt`;
CREATE TABLE `rp_push_hyt` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `empi` varchar(100) NOT NULL COMMENT '患者唯一ID',
  `card_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '患者身份证',
  `recommend_time` varchar(100) NOT NULL COMMENT '首次推荐时间',
  `request_time` varchar(100) NOT NULL COMMENT '请求时间',
  `status` int(11) NOT NULL COMMENT '返回结果 0-失败 1-成功',
  `message` varchar(100) DEFAULT NULL COMMENT '返回结果',
  `type` int(2) NOT NULL COMMENT '请求类型',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `phone` varchar(20) NULL COMMENT '手机号',
  `birthday` varchar(20) NULL COMMENT '出生日期',
  `med_name` varchar(100) DEFAULT NULL COMMENT '推荐医院',
  `med_code` varchar(100) DEFAULT NULL COMMENT '推荐医疗机构编码',
  `doctor_name` varchar(100) DEFAULT NULL COMMENT '推荐医疗机构编码',
  `doctor_code` varchar(100) DEFAULT NULL COMMENT '推荐医生工号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 COMMENT='推送华医通接口执行记录表';

-- 2025-02-24 项目是否医联体可用
ALTER TABLE rp_project ADD enable_med_const int NULL COMMENT '启用医联体 0否；1是';
ALTER TABLE rp_project ADD main_med_id bigint(20) NULL COMMENT '所属机构ID';

ALTER TABLE rp_inbound_process ADD adm_type varchar(10) NULL COMMENT '就诊类型';

CREATE TABLE `rp_dept_recommend_count` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `dept_id` bigint DEFAULT NULL COMMENT '科室ID',
  `recommend_count` int DEFAULT NULL COMMENT '推荐数',
  `adm_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '就诊类型',
  `create_time` varchar(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `rp_dept_recommend_count_dept_id_IDX` (`dept_id`) USING BTREE,
  KEY `rp_dept_recommend_count_adm_type_IDX` (`adm_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb3 COMMENT='科室推荐数统计';

CREATE TABLE `rp_recommend_count` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `recommend_count` int DEFAULT NULL COMMENT '推荐数',
  `adm_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '就诊类型',
  `create_time` varchar(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `rp_recommend_count_adm_type_IDX` (`adm_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb3 COMMENT='推荐数统计';

update rp_inbound_process a join rp_doctor_recommendation b on a.doctor_recommend_id =b.id set a.adm_type =b.adm_type;

CREATE INDEX top_recommend_dept_id_IDX USING BTREE ON rp_doctor_recommendation (top_recommend_dept_id);
CREATE INDEX recommend_dept_id_IDX USING BTREE ON rp_doctor_recommendation (recommend_dept_id);
ALTER TABLE rp_doctor_recommendation
RENAME INDEX rp_doctor_recommendation_adm_type_IDX TO adm_type_IDX;

-- cx 2025-04-08 查询慢问题，增加索引
CREATE INDEX sub_project_id USING BTREE ON rp_doctor_recommendation (sub_project_id);

-- cx 2025-04-08 表达式必填去掉
ALTER TABLE rp_group_config MODIFY COLUMN expression longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '表达式';