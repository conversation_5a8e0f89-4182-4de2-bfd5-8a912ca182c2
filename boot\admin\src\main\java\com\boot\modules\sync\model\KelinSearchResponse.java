package com.boot.modules.sync.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/2 15:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KelinSearchResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * http状态码
     */
    private Integer code;

    /**
     * 响应数据
     */
    private KelinSearchResponseData data;

    /**
     * 响应状态
     */
    private String status;

    /**
     * 组装多页数据
     * @param response 组装后的数据
     */
    public void add(KelinSearchResponse response) {
        this.getData().setCount(
                this.getData().getCount() + response.getData().getCount()
        );
        this.getData().setList(
                this.getData().getList()
                        .stream()
                        .sequential()
                        .collect(
                                Collectors.toCollection(
                                        () -> response.getData().getList()
                                )
                        )
        );
    }
}
