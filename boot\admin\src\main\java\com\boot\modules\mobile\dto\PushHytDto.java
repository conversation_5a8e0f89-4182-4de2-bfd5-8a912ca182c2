package com.boot.modules.mobile.dto;

import com.boot.modules.mobile.entity.PushHytEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "推送华医通DTO")
public class PushHytDto extends PushHytEntity {
    /**
     * 患者性别
     */
    private String gender;

    /**
     * 民族
     */
    private String nation;

    /**
     * 患者姓名
     */
    private String patName;

    /**
     * 入径医院名称
     */
    private Long medId;

    /**
     * 入径日期
     */
    private String joinDatetime;
    /**
     * 项目名称-入径病种
     */
    private String projectName;


}
