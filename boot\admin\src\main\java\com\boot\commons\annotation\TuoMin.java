package com.boot.commons.annotation;


import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @desc : 脱敏注解
 * @create 2021-06-24
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TuoMin {

    /**
     * 字段所属类型  例如"patient"
     *
     * @return
     */
    String fieldType() default "";

    /**
     * 返回结果的类型 可能是PageUtils、List<>、 Object
     *
     * @return
     */
    Class<?> returnDateType();

    /**
     * 返回结果中具体对象的类型
     *
     * @return
     */
    Class<?> returnObjectType();
}
