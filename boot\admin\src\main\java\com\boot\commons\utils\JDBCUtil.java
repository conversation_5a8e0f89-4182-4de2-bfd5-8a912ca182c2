package com.boot.commons.utils;

import com.beust.jcommander.internal.Nullable;

import java.sql.*;

/**
 * jdbc数据库连接工具类
 * <AUTHOR>
 * @createTime 2022年01月11日 15:59:00
 */
public class JDBCUtil {
    //连接对象
    private static Connection connection = null;
    //数据库操作对象
    private static  PreparedStatement ps = null;
    //数据库连接地址
//    private static String url =  "***************************" +
//            "/INFORMATION_SCHEMA?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true";
//    //用户名
//    private static String user = "root";
//    //密码
//    private static String password = "123456";
    //静态代码块 注册驱动
    //类加载的时候，只执行一次
    static{
        try {
            Class.forName("com.mysql.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    //获取连接对象
    public static  Connection getConnection(String url, String user, String password){
        //Connection conn = null;
        try {
            connection = DriverManager.getConnection(url,user,password);
        } catch (SQLException e) {
            e.printStackTrace();
            System.out.println("数据库连接失败....");
        }
        System.out.println("数据库连接成功...");
        return connection;
    }

    //获取数据库操作对象
    public static  PreparedStatement createPreparedStatement(String url, String user, String password, String sql){
        connection = getConnection(url, user, password);
        try {
            ps = connection.prepareStatement(sql);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return ps;
    }

    //释放资源
    public static  void close(){
        //释放连接对象
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        //释放数据库操作对象
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        System.out.println("释放资源成功...");
    }

    //方法的重载
    public static  void close(ResultSet reuslt){
        // 调用释放资源的方法
        close();
        // 释放查询结果集对象
        if (reuslt != null) {
            try {
                reuslt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            //获取连接
//            conn = JDBCUtil.getConnection();
            String url = "***************************" +
                    "/INFORMATION_SCHEMA?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true";
            String user = "root";
            String password = "123456";

            //编写sql
            String sql = "select * from csm_base.t_user";
            //创建语句执行者
//            stmt = conn.prepareStatement(sql);
            stmt = createPreparedStatement(url, user, password, sql);
            rs = stmt.executeQuery();

            while(rs.next()) {
                //输出结果中的第一列和第二列
                System.out.println(rs.getString(1)+"+++"+rs.getString(2));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }finally {
            JDBCUtil.close(rs);
        }

    }

    public static void close(ResultSet rs, Statement sm, Connection conn) {
        try {
            if (rs != null) {
                closeResultSet(rs);
            }

            if (sm != null) {
                closeStatement(sm);
            }

            if (conn != null) {
                closeConnection(conn);
            }
        } catch (Exception var4) {
            var4.printStackTrace();
        }

    }

    public static void closeResultSet(@Nullable ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    public static void closeConnection(@Nullable Connection con) {
        if (con != null) {
            try {
                con.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    public static void closeStatement(@Nullable Statement stmt) {
        if (stmt != null) {
            try {
                stmt.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

}