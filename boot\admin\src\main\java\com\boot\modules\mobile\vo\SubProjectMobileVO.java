package com.boot.modules.mobile.vo;

import com.boot.modules.project.entity.SubProjectEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SubProjectMobileVO extends SubProjectEntity {

    /**
     * 项目PI
     */
    private String projectAdminName;

    /**
     * 项目联系人
     */
    private String contacts;

    /**
     * 项目联系人电话
     */
    private String contactsPhone;

    /**
     * 病种
     */
    private String diseaseName;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 是否是参加的项目
     */
    private Integer isAttend = 0;

}
