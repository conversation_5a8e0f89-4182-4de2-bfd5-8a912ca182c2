package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc :项目外部接口医疗机构权限
 * @create 2022-12-05
 */
@Data
@TableName("rp_project_dept_permission")
public class ProjDeptPermissionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id ;

    /**
     * 子项目id
     */
    @NotNull(message = "项目ID不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long projectId;

    @NotNull(message = "机构不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long deptId;

    private String deptCode;
}
