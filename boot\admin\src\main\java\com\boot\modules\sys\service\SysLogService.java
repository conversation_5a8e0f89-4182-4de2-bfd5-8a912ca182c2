package com.boot.modules.sys.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysLogEntity;

import java.util.List;
import java.util.Map;


/**
 * 系统日志
 *
 * <AUTHOR>
 */
public interface SysLogService extends IService<SysLogEntity> {

    PageUtils queryPage(Map<String, Object> params);

    /**
     * 查询全部满足条件的日志信息
     * @param params 过滤条件
     * @return 日志集合
     */
    List<SysLogEntity> queryAll(Map<String, Object> params);

    /**
     * 删除指定时间之外的日志数据
     *
     * @param days
     */
    boolean deleteBeforeDays(Integer days);
}
