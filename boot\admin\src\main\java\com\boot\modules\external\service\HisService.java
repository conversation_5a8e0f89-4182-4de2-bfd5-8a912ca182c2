package com.boot.modules.external.service;

import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.model.DieaseProjectModel;

import java.util.List;
import java.util.Map;

/**
 * his 相关服务类
 */
public interface HisService {

    /**
     * 通过hisNo获取用户信息
     *
     * @param foreignId
     * @return
     */
    SysUserEntity getUserByForeignId(String foreignId, String deptCode);

    /**
     * 用户参与项目及系统推荐
     * @param userId
     * @param empi
     * @return
     */
    List<SubProjectVo> myproject(Long userId, String empi);

    /**
     * 用户所属科室及系统推荐
     * @param deptId
     * @param empi
     * @return
     */
    List<DieaseProjectModel> deptproject(List<Long> deptIdList, String empi);

    /**
     * 患者是否需要系统推荐
     * @param user
     * @param regNoList
     * @return
     */
    Map<String, Boolean> isRecommend(SysUserEntity user, Long deptId, List<Map<String, String>> regNoList);

    /**
     * 获取his信息
     * @param startDate
     * @param endDate
     * @param deptCode
     * @return
     */
    Map<String, Integer> getDisChgCount(String startDate, String endDate, String deptCode);

    /**
     * 获取非特需结构id列表
     * @param hisDeptCode
     * @return
     */
    List<Long> getDeptList(String hisDeptCode, Long userId);
}
