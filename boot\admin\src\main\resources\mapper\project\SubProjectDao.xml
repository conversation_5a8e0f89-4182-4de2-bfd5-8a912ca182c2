<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.SubProjectDao">

    <resultMap id="subProjectResult" type="com.boot.modules.project.vo.SubProjectVo">
        <id property="id" column="id" />
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="projectId" column="project_id"/>
        <result property="enabled" column="enabled"/>
        <result property="enableAudit" column="enable_audit"/>
        <result property="patCount" column="patCount"/>
        <result property="userId" column="userId"/>
        <result property="projectName" column="projectName"/>
        <!--         角色信息-->
        <association property="roleInfo" column="roleInfo" javaType="com.boot.modules.project.vo.ProjectRoleInfoVo" resultMap="roleResult"/>
    </resultMap>

    <resultMap id="roleResult" type="com.boot.modules.project.vo.ProjectRoleInfoVo">
        <result property="roleId" column="roleId"/>
        <result property="roleName" column="roleName"/>
    </resultMap>
    
    <select id="getByProjectId" resultType="com.boot.modules.project.vo.SubProjectVo">

        SELECT
            a.*, c.id as roleId ,c.role_name as roleName
        FROM
            rp_sub_project a
                LEFT JOIN rp_user_subproject b ON a.id = b.sub_project_id
                LEFT JOIN rp_sys_role c ON c.id = b.proj_role_id
                LEFT JOIN rp_project d ON a.project_id = d.id
            ${ew.customSqlSegment} GROUP BY a.id
    </select>

    <select id="getDetailsByProjectId" resultType="com.boot.modules.project.vo.SubProjectVo">

        SELECT
            a.*,
            (SELECT count(1) FROM rp_inbound_process d where d.sub_project_id=a.id) as patCount,
            (SELECT count(1) FROM rp_doctor_recommendation e where e.sub_project_id=a.id and e.status = 1) as recommendCount,
            (SELECT count(1) FROM rp_inbound_process f where f.sub_project_id=a.id and f.status = 2) as inboundCount,
            (SELECT count(1) FROM rp_inbound_process g where g.sub_project_id=a.id and g.status = 3) as signCount,
            (SELECT count(1) FROM rp_inbound_process h where h.sub_project_id=a.id and h.status in (4,5) ) as exitCount
        FROM
            rp_sub_project a
                LEFT JOIN rp_user_subproject b ON a.id = b.sub_project_id
                LEFT JOIN rp_project d ON a.project_id = d.id
            ${ew.customSqlSegment} GROUP BY a.id
    </select>

    <select id="getAllProjectInfo" resultType="com.boot.modules.project.vo.ProjectInfoVo">
        SELECT
            a.id AS id,
            a.`name` AS projectName,
            b.project_admin_id AS projectAdminId,
            c.nickname AS nickName,
            c.foreign_id AS projectAdminMemberId,
            a.create_time AS createTime,
            a.`status` AS status,
            a.project_id AS parentId
        FROM
            rp_sub_project a
                LEFT JOIN rp_project b ON a.project_id = b.id
                LEFT JOIN rp_sys_user c ON b.project_admin_id = c.id

            ${ew.customSqlSegment}
    </select>

    <select id="getUserSubPrj" resultType="com.boot.modules.project.vo.SubProjectVo">
        SELECT
            a.user_id AS userId,
            b.*,
            c.project_name AS projectName,
            d.role_name AS roleName,
            d.id AS roleId,
            d.is_proj_admin AS isProjectAdmin,
            c.contacts AS contacts,
            c.contacts_phone AS contactsPhone,
            c.project_type
        FROM rp_user_subproject a
                 LEFT JOIN rp_sub_project b ON
            a.sub_project_id = b.id
                 LEFT JOIN rp_project c ON
            c.id = b.project_id
                 LEFT JOIN rp_sys_role d ON
            d.id = a.proj_role_id
            ${ew.customSqlSegment}
    </select>

    <select id="getSubProjManage" resultType="com.boot.modules.project.vo.SubProjectVo">
        SELECT
            a.*,
            b.project_name AS projectName,
            b.project_admin_id AS userId
        FROM rp_sub_project a
                 LEFT JOIN rp_project b ON
            b.id = a.project_id
            ${ew.customSqlSegment}
    </select>

    <select id="getSubProjCRC" resultType="com.boot.modules.project.vo.SubProjectVo">
        SELECT
            e.nickname as crcUserName,
            e.mobile as crcPhoneNumber,
            b.id
        FROM rp_user_subproject a
                 LEFT JOIN rp_sub_project b ON
            a.sub_project_id = b.id
                 LEFT JOIN rp_sys_role d ON
            d.id = a.proj_role_id
            	 LEFT JOIN rp_sys_user e ON
            e.id = a.user_id
            ${ew.customSqlSegment}
    </select>


    <select id="getListByQuery" resultType="com.boot.modules.mobile.vo.SubProjectMobileVO">
        select
        a.*,
        b.contacts,
        b.contacts_phone,
        b.project_type,
        d.disease_name,
        c.nickname as projectAdminName
        from
        rp_sub_project a
        left join rp_project b on
        a.project_id = b.id
        left join rp_sys_user c on
        b.project_admin_id = c.id
        left join rp_sys_disease d on
        b.disease_id = d.id
        ${ew.customSqlSegment}
    </select>

    <select id="getUserSubListByQuery" resultType="com.boot.modules.mobile.vo.SubProjectMobileVO">
        select
        a.*,
        b.contacts,
        b.contacts_phone,
        b.project_type,
        d.disease_name,
        c.nickname as projectAdminName
        from
        rp_user_subproject e
        left join rp_sys_role f on
        e.proj_role_id = f.id
        left join
        rp_sub_project a on
        e.sub_project_id = a.id
        left join rp_project b on
        a.project_id = b.id
        left join rp_sys_user c on
        b.project_admin_id = c.id
        left join rp_sys_disease d on
        b.disease_id = d.id
        ${ew.customSqlSegment}
    </select>
</mapper>