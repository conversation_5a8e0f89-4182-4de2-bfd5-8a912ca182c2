package com.boot.modules.sys.controller;


import com.boot.commons.annotation.SysLog;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysIpFilterEntity;
import com.boot.modules.sys.service.SysIpFilterService;
import io.swagger.annotations.Api;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * IP白名单
 */
@Api(tags = "IP白名单")
@RestController
@RequestMapping("sys/ip/filter")
public class SysIpFilterController {
    @Resource
    private SysIpFilterService ipFilterService;

    @GetMapping()
    @RequiresPermissions("sys:ip:all")
    public Result all() {
        List<SysIpFilterEntity> data = ipFilterService.list();
        if (data != null && data.size() > 0) {
            return R.success(data);
        }
        return R.fail("无数据");
    }

    /**
     * 根据id获取IP
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @RequiresPermissions("sys:ip:info")
    public Result info(@PathVariable("id") Long id) {
        SysIpFilterEntity info = ipFilterService.getById(id);
        return R.success(info);

    }

    /**
     * 列表
     */
    @GetMapping("/list")
    @RequiresPermissions("sys:ip:list")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = ipFilterService.queryPage(params);

        return R.success(page);
    }


    @PostMapping()
    @SysLog("保存IP")
    @RequiresPermissions("sys:ip:save")
    public Result save(@RequestBody SysIpFilterEntity entity) {
        ValidatorUtils.validateEntity(entity, AddGroup.class);
        if (ipFilterService.save(entity)) {
            return R.success("保存成功", true);
        }
        return R.fail("保存失败");

    }


    /**
     * 更新IP
     *
     * @param entity
     * @return
     */
    @PutMapping()
    @SysLog("更新IP")
    @RequiresPermissions("sys:ip:update")
    public Result update(@RequestBody SysIpFilterEntity entity) {
        ValidatorUtils.validateEntity(entity, UpdateGroup.class);
        if (ipFilterService.updateById(entity)) {
            return R.success("更新成功", true);
        }
        return R.fail("更新失败");
    }

    /**
     * 批量删除IP
     *
     * @param ids
     * @return
     */
    @DeleteMapping()
    @SysLog("删除IP")
    @RequiresPermissions("sys:ip:delete")
    public Result delete(@RequestBody List<Long> ids) {

        if (ipFilterService.removeByIds(ids)) {
            return R.success("删除成功", true);
        }
        return R.fail("删除失败");
    }


}
