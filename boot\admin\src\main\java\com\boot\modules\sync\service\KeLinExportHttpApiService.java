package com.boot.modules.sync.service;


import com.alibaba.fastjson.JSONObject;
import com.boot.modules.sync.model.KelinSearchExportResponse;
import com.github.lianjiatech.retrofit.spring.boot.annotation.OkHttpClientBuilder;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import okhttp3.OkHttpClient;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/22 11:48
 */
@RetrofitClient(
        baseUrl = "${boot.external.kelin-export-api}",
        poolName = "kelinExportApi"
)
public interface KeLinExportHttpApiService {

    @OkHttpClientBuilder
    static OkHttpClient.Builder okhttpClientBuilder() {
        return new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.MINUTES)
                .readTimeout(5, TimeUnit.MINUTES)
                .writeTimeout(5, TimeUnit.MINUTES);
    }

//    /**
//     * 导出任务列表状态查询接口
//     *
//     * @param status 本次数据插入状态。-2,失败， 0，未开始，1，进行中，2完成，匹配不同状态，不传查所有状态
//     * @return 接口返回的数据
//     */
//    @POST("exportRecords")
//    KelinSearchResponse exportRecords(String exportId, String expression, String status, PageVo page);

    /**
     * 申请成功任务定时任务执行状态查询接口
     *
     * @param status   定时任务状态 本次数据插入状态。-2,失败， 0，未开始，1，进行中，2完成，匹配不同状态，不传查所有状态
     * @param saveType 保存类型，数据来源，1，检索（默认为0，无意义），2，导出的导入功能，3 检索订阅定时任务执行，4 审批任务回调执行
     * @return 接口返回的数据
     */
    @POST("appliedRecords")
//    KelinSearchExportResponse appliedRecords(String exportId, String expression, String status, String saveType, String platformCode);
    KelinSearchExportResponse appliedRecords(@Body JSONObject jsonObject, @Query("jwt")String jwt);

    /**
     * 数据查询接口
     *
     * @param exportId            导出任务ID
     * @param diseaseExpressionId 对应表达式id
     * @return 接口返回的数据
     */
    @POST("expressionDimension")
//    KelinSearchExportResponse expressionDimension(String exportId, List<String> diseaseExpressionId, String startDate, String endDate, PageVo page, String platformCode);
    KelinSearchExportResponse expressionDimension(@Body JSONObject jsonObject,@Query("jwt")String jwt);
}
