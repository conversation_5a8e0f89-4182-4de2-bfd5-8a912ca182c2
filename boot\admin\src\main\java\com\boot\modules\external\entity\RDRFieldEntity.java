package com.boot.modules.external.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * rdr表结构
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_rdr_field")
public class RDRFieldEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表ID
     */
    @TableId
    private Long id;

    /**
     * 关联rdr表映射信息表的id
     */
    private Long tableId;

    /**
     * 字段中文名
     */
    private String cname ;

    /**
     * 字段英文名
     */
    @NotBlank(message = "表单英文名不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String ename;

    /**
     * 字段说明：患者唯一ID、就诊ID、时间戳、就诊时间、医疗机构编码、医嘱ID、其他（1/2/3/4/5/6/0）
     */
    private Integer fieldExplain ;

    /**
     * 是否为联合主键 1：是；0：否
     */
    private Integer isUnionKey ;

    /**
     * 字段类型
     */
    private String type;

    /**
     * 字段长度
     */
    private Integer length ;

    /**
     * 对照柯林表字段名
     */
    private String klFieldName ;

    /**
     * 排序字段
     */
    private Integer sequenceNo ;

    /**
     * 是否显示  1：显示；0：不显示
     */
    private Integer isShow;

    /**
     * 是否默认导出 0：否，1：是
     */
    private Integer isExport;

    /**
     * 导出表头分类：0：无；1：主表头；2：主表头-需合并；3：副表头
     */
    public Integer exportLevel;

    /**
     * 导出唯一标识：0：无；1：行唯一标识；2：列唯一标识；
     */
    public Integer exportUnique;

    @TableField(exist = false)
    public Long catId;
}
