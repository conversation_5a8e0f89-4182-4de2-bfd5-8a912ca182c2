<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.patient.dao.DoctorRecommendationDao">

    <select id="getEmpiBySubProjectId" resultType="String">
        select distinct(empiid)
        from rp_doctor_recommendation
        where
            sub_project_id = #{subProjectId}
            and (status = 1 or status = 2 or status = 3)
    </select>

    <select id="groupPat" resultType="com.boot.modules.patient.dto.PersonalReportDto">
        select
            temp.doctorId as doctorId,
            c.nickname as doctorName,
            temp.projectId as projectId,
            temp.total as 'sum',
            b.project_name as projectName,
            d.name as department
        from
        (select
            count(distinct (empiid)) as total,
            recommend_id as doctorId,
            project_id as projectId
        from
            rp_doctor_recommendation
        ${ew.customSqlSegment}
        group by
            recommend_id,project_id) temp
        join
            rp_project b
        on temp.projectId = b.id
        join
            rp_sys_user c
        ON
            temp.doctorId = c.id
        join
            rp_sys_dept d
        ON
            c.dept_id = d.id
    </select>

    <select id="groupByDept" resultType="com.boot.modules.patient.dto.DepartmentReportDto">
        SELECT
            COUNT(DISTINCT(empiid)) 'sum',
            COUNT(DISTINCT(recommendId)) joinCount,
            deptId
        FROM
            (SELECT
                a.empiid AS empiid,
                a.recommend_id AS recommendId,
                b.dept_id AS deptId
            FROM
                rp_doctor_recommendation a
            JOIN
                rp_sys_user b
            ON
                a.recommend_id = b.id
                ${ew.customSqlSegment}) temp
        GROUP BY
            deptId
    </select>

    <select id="groupDept" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">
        SELECT
                COUNT(distinct a.empiid, b.disease_id) totalRecommend,
                a.recommend_dept_id deptId
            FROM
                rp_doctor_recommendation a
            JOIN
                rp_project b
            ON
                a.project_id = b.id
            ${ew.customSqlSegment}
        GROUP BY
            a.recommend_dept_id
    </select>

    <select id="groupPer" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.disease_id) totalRecommend,
            a.recommend_id doctorId,
            a.recommend_dept_id deptId
        FROM rp_doctor_recommendation a
            JOIN
                rp_project b
            ON
                a.project_id = b.id
            ${ew.customSqlSegment}
        GROUP BY
            a.recommend_id,a.recommend_dept_id
    </select>


    <select id="totalRecommend" resultType="Integer" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.disease_id) totalRecommend
        FROM
                rp_doctor_recommendation a
            JOIN
                rp_project b
            on
                a.project_id = b.id
        ${ew.customSqlSegment}
    </select>

    <select id="icoTotalRecommend" resultType="Integer" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.disease_id) totalRecommend
        FROM
                rp_doctor_recommendation a
            JOIN
                rp_project b
            on
                a.project_id = b.id
            JOIN rp_sys_dept c on a.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}
    </select>

    <select id="getRecommendProject" resultType="com.boot.modules.patient.vo.DoctorRecommendProjectDiseaseVo">
        SELECT a.*,rp.project_name,rp.project_type ,rsd.disease_name,d.status as patientStatus,rsu.nickname as pi,rip.out_reason,
        d.name as patientName,d.nation as nation,d.gender,inboundUser.nickname AS inboundUserName  FROM rp_doctor_recommendation a
        left join rp_patient d on a.empiid = d.empiid
        left join rp_project rp on a.project_id = rp.id
        left join rp_sys_user rsu on rp.project_admin_id =rsu.id
        left join rp_sys_disease rsd on rp.disease_id = rsd .id
        left join rp_inbound_process rip on rip.doctor_recommend_id=a.id
		left join rp_sys_user inboundUser ON rip.inbound_id = inboundUser.id
        ${ew.customSqlSegment}
    </select>

    <select id="getRecommendPatient" resultType="com.boot.modules.patient.vo.DoctorRecommendPatientVo">
        SELECT
            a.*,
            b.id_card as idCard
        FROM
            rp_doctor_recommendation a
        LEFT JOIN
            rp_patient b
        ON
            a.empiid = b.empiid
        ${ew.customSqlSegment}
    </select>

    <select id="groupByTopDeptId" resultType="com.boot.modules.mobile.model.PercentModel" useCache="false">
        SELECT a.top_recommend_dept_id as hospitalId,
            COUNT(a.empiid) as recommendCount,
            b.name as hospitalName
        from
            rp_doctor_recommendation a
            left join rp_sys_dept b on a.top_recommend_dept_id=b.id
        ${ew.customSqlSegment}
        group by a.top_recommend_dept_id
    </select>

    <select id="statisticPatientByMed" resultType="com.boot.modules.patient.dto.MedPatientDto" useCache="false">
        select
        a.id as medId,
        a.name as medName,
        count(b.id) as totalCount
        from
        rp_sys_dept a
        left join rp_doctor_recommendation b on
        a.id = b.top_recommend_dept_id
        ${ew.customSqlSegment}
    </select>

    <select id="icoProjectCount" resultType="Integer" useCache="false">
        select count(distinct (a.id))
        from
        rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_sys_dept c on b.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}
    </select>

    <select id="groupIcoProject" resultType="com.boot.modules.patient.dto.IcoReportDto" useCache="false">
        SELECT
            COUNT(distinct a.id) totalProject,
            b.top_recommend_dept_id medId,
            c.name medName
        FROM
            rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_sys_dept c on b.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}
        GROUP BY
            b.top_recommend_dept_id
    </select>

    <select id="groupIcoRecommend" resultType="com.boot.modules.patient.dto.IcoReportDto" useCache="false">
        SELECT
            COUNT(distinct b.empiid, a.disease_id) totalRecommend,
            b.top_recommend_dept_id medId,
            c.name medName
        FROM
            rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_sys_dept c on b.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}
        GROUP BY
            b.top_recommend_dept_id
        order by totalRecommend desc
    </select>

    <select id="groupPIRecommend" resultType="com.boot.modules.patient.dto.PIReportDto" useCache="false">
        SELECT
            COUNT(distinct b.empiid, a.disease_id) totalRecommend,
            a.id projectId,
            a.project_name projectName
        FROM
            rp_project a
        left join rp_doctor_recommendation b on a.id = b.project_id
        ${ew.customSqlSegment}
        GROUP BY
            a.id
    </select>

    <select id="getProjectIds" resultType="Long" useCache="false">
        SELECT
            distinct project_id
        FROM
            rp_doctor_recommendation
        ${ew.customSqlSegment}
    </select>


    <select id="groupProject" resultType="com.boot.modules.mobile.dto.ProjectReportDto" useCache="false">
        SELECT
            COUNT(distinct b.empiid, a.disease_id) recommendCount,
            a.id projectId
        FROM
            rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_sys_dept c on b.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}
        GROUP BY
            a.id
    </select>

    <select id="getRecommendDeptId" resultType="Long" useCache="false">
        SELECT
            distinct recommend_dept_id
        FROM
            rp_doctor_recommendation
        ${ew.customSqlSegment}
    </select>

</mapper>