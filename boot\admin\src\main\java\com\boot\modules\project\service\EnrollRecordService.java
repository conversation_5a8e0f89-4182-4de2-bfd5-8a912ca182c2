package com.boot.modules.project.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.EnrollRecordEntity;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface EnrollRecordService extends IService<EnrollRecordEntity> {
    /**
     * 分页查询任务执行记录
     * @param params
     * @return 分页后的病种列表
     */
    PageUtils getAllByPage(Map<String, Object> params);
}
