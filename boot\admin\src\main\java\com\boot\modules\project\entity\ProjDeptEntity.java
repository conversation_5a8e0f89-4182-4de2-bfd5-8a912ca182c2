package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc :项目中心表
 * @create 2021-05-20
 */
@Data
@TableName("rp_project_dept")
public class ProjDeptEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 中心/机构id
     */
    private Long deptId;

    /**
     * 医院id
     */
    private Long topDeptId;

    /**
     * 是否是主中心 1：是主中心 0：否
     */
    private Integer isPrimary;

}
