package com.boot.modules.sys.vo;

import lombok.Data;
import oracle.sql.BLOB;

import java.util.Date;

/**
 * 回收站  将指定数据删除的操作放入回收站， 防止误删除的数据
 * <AUTHOR>
 */
@Data
public class SysRecycleRuleVo {

    private Long id;

    private String tableName;

    /**
     * 删除的数据， bast64加密， 压缩
     */
    private BLOB deleteData;

    /**
     * 删除的操作用户ID
     */
    private Long deleteUserId;

    /**
     * 删除时间
     */
    private Date deleteTime;
}
