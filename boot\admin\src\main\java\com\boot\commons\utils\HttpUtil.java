package com.boot.commons.utils;

import com.boot.commons.exception.BusinessException;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

import static com.boot.commons.constants.Charsets.UTF_8;

public class HttpUtil {

    /**
     * 使用http方式调用webService
     * @param urlString
     * @param soapXML
     * @param headers
     * @return
     */
    public static String webServiceHttp(String urlString, String soapXML, Map<String, String> headers) {
        StringBuilder sb = new StringBuilder();
        try{
            //1：创建服务地址
            URL url = new URL(urlString);
            //2：打开到服务地址的一个连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            //3：设置连接参数
            //3.1设置发送方式：POST必须大写
            connection.setRequestMethod("POST");
            //3.2设置数据格式：Content-type
            connection.setRequestProperty("content-type", "text/xml;charset=utf-8");

            //3.3设置请求头
            for (Map.Entry<String, String> header : headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }

            //3.3设置输入输出，新创建的connection默认是没有读写权限的，
            connection.setDoInput(true);
            connection.setDoOutput(true);
//            connection.setConnectTimeout();
            //4：组织SOAP协议数据，发送给服务端
            OutputStream os = connection.getOutputStream();
            os.write(soapXML.getBytes());
            //5：接收服务端的响应
            int responseCode = connection.getResponseCode();

            if(200 == responseCode){//表示服务端响应成功
                InputStream is = connection.getInputStream();
                InputStreamReader inputStreamReader = new InputStreamReader(is, UTF_8);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                String temp = null;

                while (null != (temp = bufferedReader.readLine())) {
                    sb.append(temp);
                }

                is.close();
                inputStreamReader.close();
                bufferedReader.close();
            } else {
                throw new BusinessException("请求接口" + urlString + "有误，相应码为：" + responseCode + "，错误信息：" + connection.getResponseMessage());
            }
            os.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sb.toString();
    }
}
