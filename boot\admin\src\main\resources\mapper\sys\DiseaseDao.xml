<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.DiseaseDao">

<!--    <select id="getByQuery" resultType="com.boot.modules.sys.vo.DiseaseVo">-->
<!--        select-->
<!--            c.id as id,-->
<!--            c.disease_name as diseaseName,-->
<!--            c.dept_id as deptId,-->
<!--            concat(concat(c.name,'-',w.name), '-',x.name) as department-->
<!--        from-->
<!--            rp_sys_dept w inner join-->
<!--            (-->
<!--            select-->
<!--                a.id,-->
<!--                b.name,-->
<!--                b.pid,-->
<!--                a.disease_name,-->
<!--                a.dept_id-->
<!--            from-->
<!--                rp_sys_disease a-->
<!--            left join rp_sys_dept b on a.dept_id = b.id-->
<!--                ${ew.customSqlSegment}-->
<!--            ) c on w.id =  c.pid-->
<!--        inner join-->
<!--            rp_sys_dept x-->
<!--        on w.pid = x.id-->
<!--    </select>-->
<!--    <select id="pageByQuery" resultType="com.boot.modules.sys.vo.DiseaseVo">-->
<!--        select-->
<!--            c.id as id,-->
<!--            c.disease_name as diseaseName,-->
<!--            c.dept_id as deptId,-->
<!--            concat(concat(c.name,'-',w.name), '-',x.name) as department-->
<!--        from-->
<!--            rp_sys_dept w inner join-->
<!--            (-->
<!--            select-->
<!--                a.id,-->
<!--                b.name,-->
<!--                b.pid,-->
<!--                a.disease_name,-->
<!--                a.dept_id-->
<!--            from-->
<!--                rp_sys_disease a-->
<!--            left join rp_sys_dept b on a.dept_id = b.id-->
<!--                ${ew.customSqlSegment}-->
<!--            ) c on w.id =  c.pid-->
<!--        inner join-->
<!--            rp_sys_dept x-->
<!--        on w.pid = x.id-->
<!--    </select>-->
    <select id="pageQuery" resultType="com.boot.modules.sys.vo.DiseaseVo">
        select
            count(distinct projectId) as projectCount,
            diseaseName,
            id
        from(select
                a.disease_id id,
                e.disease_name diseaseName,
                a.id projectId
            from
                rp_project a
            left join
                rp_sys_disease e
            on
                e.id = a.disease_id
            left join
                (SELECT e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id
                FROM rp_sys_user e
                JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) f
            on
                a.project_admin_id = f.id and a.main_dept_id = f.dept_id
            ${ew.customSqlSegment})temp
        group by id,diseaseName
        order by projectCount desc
    </select>
<!--    <select id="getById" resultType="com.boot.modules.sys.vo.DiseaseVo">-->
<!--        select-->
<!--            c.id as id,-->
<!--            c.disease_name as diseaseName,-->
<!--            c.dept_id as deptId,-->
<!--            concat(c.name,'-',w.name) as department-->
<!--        from-->
<!--            rp_sys_dept w inner join-->
<!--            (-->
<!--                select-->
<!--                    a.id,-->
<!--                    b.name,-->
<!--                    b.pid,-->
<!--                    a.disease_name,-->
<!--                    a.dept_id-->
<!--                from-->
<!--                    rp_sys_disease a-->
<!--                        left join rp_sys_dept b on a.dept_id = b.id-->
<!--                    ${ew.customSqlSegment}-->
<!--            ) c on w.id =  c.pid-->
<!--    </select>-->
<!--    <select id="getDieaseByDept" resultType="com.boot.modules.sys.vo.DiseaseProjectVo">-->
<!--        SELECT a.id as diseaseId,a.disease_name,a.dept_id,b.project_name,c.id,c.project_id,c.name,b.contacts AS contacts,-->
<!--            b.contacts_phone AS contactsPhone-->
<!--        FROM `rp_sys_disease` a-->
<!--            left join rp_project b on a.id=b.disease_id-->
<!--            join rp_sub_project c on b.id = c.project_id-->
<!--            ${ew.customSqlSegment}-->
<!--    </select>-->
    <select id="getDieaseByDept" resultType="com.boot.modules.sys.vo.DiseaseProjectVo">
        SELECT a.id as diseaseId,a.disease_name,d.dept_id,b.project_name,c.id,c.project_id,c.name,b.contacts AS contacts,
            b.status,
            b.contacts_phone AS contactsPhone,
            b.project_type AS projectType
        FROM `rp_sys_disease` a
            left join rp_project b on a.id=b.disease_id
            left join rp_project_dept d on b.id = d.project_id
            join rp_sub_project c on b.id = c.project_id
            ${ew.customSqlSegment}
    </select>
    <select id="pageQueryByDept" resultType="com.boot.modules.sys.vo.DiseaseVo">
        select distinct rsd.*
        from
            (select
                a.id deptId,
                a.name deptName,
                c.disease_id diseaseId
            from
                rp_sys_dept a
            join
                rp_project_dept b
            on
                a.id = b.dept_id
            join
                rp_project c
            on
                b.project_id = c.id
        ) as temp
        left join rp_sys_disease rsd on rsd.id =temp.diseaseId
        ${ew.customSqlSegment}
    </select>
</mapper>