package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 备份任务记录表
 *
 * <AUTHOR>
 * @date 2021/3/23 09:56
 *
 */
@Data
@TableName("edc_backup_task")
public class BackupTaskEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 备份任务ID
     */
    @TableId
    private Long id;

    /**
     * 备份文件名称
     */
    private String fileName;

    /**
     * 备份文件长度
     */
    private Long fileLength;

    /**
     * 备份时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date backupDatetime;

    /**
     * 备份文件路径
     */
    private String filePath;

    /**
     * 备份任务状态
     */
    private int backupStatus;

    /**
     * 备份任务失败原因
     */
    private String backupErrorMessage;

}
