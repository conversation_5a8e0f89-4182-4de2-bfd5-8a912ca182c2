package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserHisEntity;
import com.boot.modules.sys.vo.UserHisForeignVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :
 * @create 2022-05-05
 */
public interface SysUserHisService extends IService<SysUserHisEntity> {

    /**
     * 分页获取
     * @param params
     * @return
     */
    PageUtils queryPage(Map<String, Object> params);

    List<UserHisForeignVo> selectByQuery(Map<String, Object> params);

    List<UserHisForeignVo> selectByQuery(Long userId);

    UserHisForeignVo selectById(Long id);

    List<SysUserEntity> getByForeignAndDept(String foreignId, String deptCode);

    boolean add(SysUserHisEntity entity);

    boolean updateUserHis(SysUserHisEntity entity);

    List<UserHisForeignVo> selectByQuery(Long userId,String deptCode);

}
