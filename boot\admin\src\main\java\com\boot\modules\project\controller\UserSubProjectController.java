package com.boot.modules.project.controller;

import com.boot.commons.annotation.Login;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.UserSubProjectEntity;
import com.boot.modules.project.service.UserSubProjectService;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc : 项目用户关联
 * @create 2021-02-23
 */
@Api(tags = "项目用户关联")
@RestController
@RequestMapping("/proj/subproject/user")
public class UserSubProjectController extends AbstractController {

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private SysUserService userService;

    @Resource
    private SysDeptService deptService;


    @ApiOperation(
            value = "获取项目关联用户列表",
            notes = "获取项目关联用户列表"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型，"
                    + "1:获取项目关联的全部用户，"
                    + "2:获取项目关联机构的全部用户，"
                    + "3:获取项目关联机构的未添加的全部用户 ",
                    dataType = "long", paramType = "query", example = "xingguo"),
    })
    @Login
    @GetMapping("/list/{type}")
    @RequiresPermissions("proj:user:list")
    public Result list(@RequestParam Map<String, Object> params, @PathVariable(value = "type") Long type) {
        SysUserEntity user = getUser();
        params.put("user", user);
        if (type == 1L) {
            return R.success(userSubProjectService.getAllSubProjUser(params));
        } else if (type == 2L) {
            //分页返回当前指定中心的用户
            Long deptId = MapUtils.getValue(params, "deptId", Long.class);
            PageUtils pageUtils = userService.queryByDeptIds(deptService.getSubDeptIdList(deptId), params, true);
            return R.success(pageUtils);
        } else if (type == 3L) {
            return R.success(userSubProjectService.getNotAddUser(params));
        }
        return R.fail("参数错误");
    }

    @ApiOperation(
            value = "获取当前用户所属中心名称",
            notes = "获取当前用户所属中心名称"
    )
    @RequiresPermissions("proj:user:info")
    @GetMapping("/center/{projectId}/{subProjectId}")
    public Result getCurUserCenter(@PathVariable("projectId") Long projectId, @PathVariable("subProjectId") Long subProjectId) {
        return R.success(userSubProjectService.getCurUserCenter(projectId, subProjectId, getUserId()));
    }


    @ApiOperation(
            value = "新增项目关联用户",
            notes = "新增项用关联用户"
    )
    @SysLog("新增项目关联用户")
    @PostMapping()
    @RequiresPermissions("proj:user:save")
    public Result save(@RequestBody UserSubProjectEntity projUser) {

        boolean res = userSubProjectService.add(projUser);

        return res ? R.success("新增成功") : R.fail("新增失败");
    }

    @ApiOperation(
            value = "批量新增项目关联用户",
            notes = "批量新增项目关联用户"
    )
    @SysLog("批量新增项目关联用户")
    @PostMapping("/saveBatch")
    @RequiresPermissions("proj:user:savebatch")
    public Result addBatch(@RequestBody UserSubProjectEntity[] projUsers) {

        boolean res = userSubProjectService.addBatch(Arrays.asList(projUsers));

        return res ? R.success("新增成功") : R.fail("新增失败");
    }


    @ApiOperation(
            value = "修改项目关联用户",
            notes = "修改项目关联用户"
    )
    @SysLog("修改项目关联用户")
    @PutMapping()
    @RequiresPermissions("proj:user:update")
    public Result update(@RequestBody UserSubProjectEntity projectUser) {

        boolean res = true;
        //由于修改只修改用户在对应项目中的角色， 所以不用唯一性验证
        res = userSubProjectService.updateById(projectUser);

        return res ? R.success("修改成功") : R.fail("修改失败");
    }

    @ApiOperation(
            value = "删除项目关联用户",
            notes = "删除项目关联用户"
    )
    @SysLog("删除项目关联用户")
    @DeleteMapping()
    @RequiresPermissions("proj:user:delete")
    public Result delete(@RequestBody Long[] ids) {
        userSubProjectService.removeByIds(Arrays.asList(ids));

        return R.success("删除成功", true);
    }
}
