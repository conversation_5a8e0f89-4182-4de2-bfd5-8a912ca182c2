package com.boot.commons.enums;

/**
 * 监听事件类型
 * <AUTHOR>
 * @desc :
 * @create 2021-06-16
 */
public enum EventTypeEnum {

    /**
     * 新增或修改事件
     */
    SAVE_OR_UPDATE(1, "新增或修改事件"),
    /**
     * 删除文档
     */
    DELETE(2, "删除事件");

    private Integer type;
    private String desc;

    EventTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static EventTypeEnum getByType(Integer type) {
        if (type != null) {
            for (EventTypeEnum eventTypeEnum : EventTypeEnum.values()) {
                if (eventTypeEnum.getType() == type) {
                    return eventTypeEnum;
                }
            }
        }
        return null;
    }


}
