package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.project.dao.EnrollRecordDao;
import com.boot.modules.project.entity.EnrollRecordEntity;
import com.boot.modules.project.service.EnrollRecordService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class EnrollRecordServiceImpl extends ServiceImpl<EnrollRecordDao, EnrollRecordEntity> implements EnrollRecordService {
    @Override
    public PageUtils getAllByPage(Map<String, Object> params) {
        //子项目Id
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        //开始时间
        String startTime = MapUtils.getValue(params, "startTime", String.class);
        //结束时间
        String endTime = MapUtils.getValue(params, "endTime", String.class);
        //状态
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        //类型
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        //拼接条件
        QueryWrapper<EnrollRecordEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(subProjectId != null, EnrollRecordEntity::getSubProjectId, subProjectId)
                .ge(!StringUtils.isEmpty(startTime), EnrollRecordEntity::getStartTime, startTime)
                .le(!StringUtils.isEmpty(endTime), EnrollRecordEntity::getEndTime, endTime)
                .eq(status != null, EnrollRecordEntity::getStatus, status)
                .eq(type != null, EnrollRecordEntity::getType, type);
        //分页查询
        IPage<EnrollRecordEntity> pageFilter = new Query<EnrollRecordEntity>().getPage(params);
        IPage<EnrollRecordEntity> page = this.baseMapper.selectPage(pageFilter,qw);
        return new PageUtils(page);
    }
}
