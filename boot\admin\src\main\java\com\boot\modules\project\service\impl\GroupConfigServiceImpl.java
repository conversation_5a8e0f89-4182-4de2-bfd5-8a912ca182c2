package com.boot.modules.project.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.ObjectUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.mobile.vo.GroupConfigVO;
import com.boot.modules.project.dao.GroupConfigDao;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.project.redis.EnrollDetailRedis;
import com.boot.modules.project.service.GroupConfigService;
import com.boot.modules.sync.dao.KelinConfigDao;
import com.boot.modules.sync.entity.KelinConfigEntity;
import com.boot.modules.sync.service.KeLinHsspHttpApiService;
import com.boot.modules.sync.service.KelinSsoHttpApiService;
import com.boot.modules.sync.utils.JwtForKelin;
import com.boot.modules.sys.entity.SysUserEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GroupConfigServiceImpl
        extends ServiceImpl<GroupConfigDao, GroupConfigEntity>
        implements GroupConfigService {

    @Resource
    private KeLinHsspHttpApiService keLinHsspHttpApiService;

    @Resource
    private KelinSsoHttpApiService kelinSsoHttpApiService;

    @Resource
    private KelinConfigDao kelinConfigDao;

    @Resource
    private EnrollDetailRedis enrollDetailRedis;

    public final String accept = "application/json, text/javascript, */*; q=0.01";

    @Resource
    SSOConfigurationProperties ssoConfig;

    @Override
    public List<GroupConfigEntity> listBySubProjId(long subProjectId, Integer type, Integer isYlt) {
        LambdaQueryWrapper<GroupConfigEntity> ew = new QueryWrapper<GroupConfigEntity>().lambda()
                .eq(GroupConfigEntity::getSubProjectId, subProjectId)
                .eq(type != null, GroupConfigEntity::getType, type)
                .eq(isYlt != null, GroupConfigEntity::getIsYlt, isYlt)
                .orderByAsc(GroupConfigEntity::getSort);

        return this.getBaseMapper().selectList(ew);
    }


    @Override
    public List<GroupConfigVO> listYltVOBySubProjId(long subProjectId, Integer type) {
        List<GroupConfigEntity> result = listBySubProjId(subProjectId, type, 1);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        List<GroupConfigVO> vos = new ArrayList<>();
        for (GroupConfigEntity entity : result) {
            GroupConfigVO vo = new GroupConfigVO();
            BeanUtils.copyProperties(entity, vo);
            vos.add(vo);
        }
        return vos;
    }

    @Override
    public void updateSort(List<GroupConfigEntity> groupConfigEntityList) {
        // 1.获取所有需要调整排序的id
        List<Long> idList = groupConfigEntityList.stream().map(GroupConfigEntity::getId).collect(Collectors.toList());
        // 2.获取id sort 对照对象
        Map<Long, Integer> sortMap = groupConfigEntityList.stream().collect(Collectors.toMap(GroupConfigEntity::getId, GroupConfigEntity::getSort));
        // 2.获取对应所有对应
        List<GroupConfigEntity> groupConfigEntities = this.baseMapper.selectList(new QueryWrapper<GroupConfigEntity>().lambda()
                .in(GroupConfigEntity::getId, idList));

        if (CollectionUtils.isEmpty(groupConfigEntities)) {
            return;
        }

        // 3.批量更新sort字段
        groupConfigEntities.forEach(groupConfigEntity -> {
            groupConfigEntity.setSort(sortMap.get(groupConfigEntity.getId()));
        });

        this.updateBatchById(groupConfigEntities);
    }

    /**
     * 保存纳排条件并验证检索表达式
     */
    @Transactional
    @Override
    public Boolean saveConfig(GroupConfigEntity groupConfigEntity, SysUserEntity user) {
        boolean res;
        if (groupConfigEntity.getIsYlt() == 1) {
            //医联体配置只需要保存
            res = saveWithSort(groupConfigEntity);
        } else {
            // 记录检索表达式验证表
            KelinConfigEntity kelinConfigEntity = new KelinConfigEntity();
            valid(groupConfigEntity, kelinConfigEntity, user);

            //保存数据库并排序
            res = saveWithSort(groupConfigEntity);

            // 保存柯林检索表达式验证记录
            kelinConfigEntity.setGroupConfigId(groupConfigEntity.getId());
            kelinConfigEntity.setProjectId(groupConfigEntity.getProjectId());
            kelinConfigEntity.setSubProjectId(groupConfigEntity.getSubProjectId());
            res = res && kelinConfigDao.insert(kelinConfigEntity) > 0;
        }

        return res;
    }

    private boolean saveWithSort(GroupConfigEntity groupConfigEntity) {
        // 1.保存数据库
        boolean res = save(groupConfigEntity);

        // 2.更新本项目数据库非必要条件sort顺序
        int necessary = groupConfigEntity.getNecessary();

        if (necessary == 1) {
            List<GroupConfigEntity> groupConfigEntityList = this.baseMapper.selectList(new QueryWrapper<GroupConfigEntity>().lambda()
                    .eq(GroupConfigEntity::getNecessary, 0)
                    .eq(GroupConfigEntity::getSubProjectId, groupConfigEntity.getSubProjectId())
                    .orderByAsc(GroupConfigEntity::getSort));

            if (!CollectionUtils.isEmpty(groupConfigEntityList)) {
                // 3.非必要条件增加排序
                groupConfigEntityList.sort(Comparator.comparingInt(GroupConfigEntity::getSort));

                if (groupConfigEntityList.get(0).getSort() <= groupConfigEntity.getSort()) {
                    groupConfigEntityList.forEach(groupConfig -> {
                        groupConfig.setSort(groupConfig.getSort() + 1);
                    });
                }
                res = res && this.updateBatchById(groupConfigEntityList);
            }
        }
        return res;
    }

    /**
     * 保存纳排条件并验证检索表达式
     */
    @Transactional
    @Override
    public Boolean updateConfig(GroupConfigEntity groupConfigEntity, SysUserEntity user) {
        GroupConfigEntity old = getById(groupConfigEntity.getId());
        if (ObjectUtils.isEmpty(old)) {
            throw new BusinessException("未查找到该id的纳排条件");
        }
        boolean res;
        if (groupConfigEntity.getIsYlt() == 1) {
            //医联体配置只需要保存
            res = updateById(groupConfigEntity);
        } else {
            // 检索表达式修改时，需要重新验证
            if (!old.getExpression().equals(groupConfigEntity.getExpression())) {
                KelinConfigEntity kelinConfigEntity = new KelinConfigEntity();
                // 查询检索表达式验证表
                List<KelinConfigEntity> exist = kelinConfigDao.selectList(new QueryWrapper<KelinConfigEntity>().lambda().eq(KelinConfigEntity::getGroupConfigId, groupConfigEntity.getId()));
                if (!CollectionUtils.isEmpty(exist)) {
                    kelinConfigEntity = exist.get(0);
                }
                valid(groupConfigEntity, kelinConfigEntity, user);
                // 更新柯林检索表达式验证记录
                kelinConfigEntity.setGroupConfigId(groupConfigEntity.getId());
                kelinConfigDao.updateById(kelinConfigEntity);
            }
            // 保存数据库
            res = updateById(groupConfigEntity);

        }
        return res;
    }

    private void valid(GroupConfigEntity groupConfigEntity, KelinConfigEntity kelinConfigEntity, SysUserEntity user) {
        // 检索表达式验证
        try {
            String appCode = ssoConfig.getAppCode();
            // 1.获取token
            String code = user.getUsername();
            if (StringUtils.isEmpty(code)) {
                throw new BusinessException("当前用户为空");
            }
            String token = kelinSsoHttpApiService.getHsspToken(code, JwtForKelin.getJwt());
            if (StringUtils.isEmpty(token)) {
                throw new BusinessException("token获取失败");
            }
            Map<String, Object> sqRet = new HashMap<>();
            sqRet.put("expression", groupConfigEntity.getExpression());
            sqRet.put("searchByType", "VKey");
            sqRet.put("platformCode", appCode);
            JSONObject jsonObject = new JSONObject(sqRet);
            // 验证表达式
            sqRet = keLinHsspHttpApiService.solrQure(jsonObject, token, accept, JwtForKelin.getJwt());

            String status = String.valueOf(sqRet.get("responseStatu"));
            kelinConfigEntity.setResponseStatus(status);
            if (!status.equals("0000")) {
                kelinConfigEntity.setRequestId("0");
                String errMsg = StringUtils.isEmpty(String.valueOf(sqRet.get("responseDescription"))) ? "调用柯林检索表达式验证接口失败" : String.valueOf(sqRet.get("responseDescription"));
                kelinConfigEntity.setMessage(errMsg);
                kelinConfigEntity.setStatus(2);
                throw new BusinessException("表达式验证失败:" + errMsg);
            }
            int total = Integer.parseInt(String.valueOf(sqRet.get("totalRecordShow")).replaceAll(",", ""));
            if (total > BootAdminProperties.maxCount) {
                throw new BusinessException("该检索配置数目: " + total + "超过最大限制: " + BootAdminProperties.maxCount);
            }

            kelinConfigEntity.setRequestId(String.valueOf(sqRet.get("requestID")));
            kelinConfigEntity.setSolrQureStartTime(String.valueOf(sqRet.get("solrQureStartTime")));
            kelinConfigEntity.setSolrQureEndTime(String.valueOf(sqRet.get("solrQureEndTime")));
            kelinConfigEntity.setTotalRecord(Long.valueOf(String.valueOf(sqRet.get("totalRecordShow")).replaceAll(",", "")));
        } catch (Exception ex) {
            kelinConfigEntity.setResponseStatus("A001");
            kelinConfigEntity.setMessage(ex.getMessage());
            kelinConfigEntity.setStatus(2);
            throw new BusinessException(ex.getMessage());
        }
        kelinConfigEntity.setSolrCreateTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
    }

    @Async("bootExecutor")
    public Boolean execConfig(Long subProjectId, SysUserEntity user) {
        Boolean res = true;
        String code = user.getUsername();
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException("当前用户为空");
        }
        // 获取子项目下所有纳排条件
        List<GroupConfigEntity> groupConfigEntityList = this.list(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId)
                //只执行华西配置的纳排条件
                .eq(GroupConfigEntity::getIsYlt, 0));
        List<Long> groupConfigIdList = groupConfigEntityList.stream().map(p -> p.getId()).collect(Collectors.toList());
        // 清理原有缓存
        clearRedis(subProjectId, groupConfigIdList);
        List<KelinConfigEntity> kelinConfigEntityList = kelinConfigDao.selectList(new QueryWrapper<KelinConfigEntity>()
                .lambda().eq(KelinConfigEntity::getSubProjectId, subProjectId));
        for (GroupConfigEntity groupConfigEntity : groupConfigEntityList) {
            List<KelinConfigEntity> current = kelinConfigEntityList.stream().filter(p -> p.getGroupConfigId().equals(groupConfigEntity.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(current)) {
                log.error("当检索表达式验证记录未查到：" + JSONObject.toJSONString(groupConfigEntity));
                continue;
            }
            KelinConfigEntity kelinConfigEntity = current.get(0);
            // 创建数据集、保存数据集
            try {
                String appCode = ssoConfig.getAppCode();
                // 数据集名称
                String subCategoryName = UUID.randomUUID().toString().replace("-", "");
                String token = kelinSsoHttpApiService.getHsspToken(code, JwtForKelin.getJwt());
                if (StringUtils.isEmpty(token)) {
                    log.error("token获取失败");
                    throw new BusinessException("token获取失败");
                }
                //记录首次的结束时间
                kelinConfigEntity.setKlStartTime("");
                kelinConfigEntity.setKlEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                Map<String, Object> diseaseCreate = keLinHsspHttpApiService.saveDiseaseDBPlatform(subCategoryName, appCode,
                        groupConfigEntity.getExpression(), subCategoryName,
                        token, accept, JwtForKelin.getJwt());

                if (!diseaseCreate.get("status").equals("succeed")) {
                    kelinConfigEntity.setStatus(2);
                    kelinConfigEntity.setMessage("创建数据集失败" + diseaseCreate.get("message"));
                    log.error("创建数据集失败" + diseaseCreate.get("message"));
                } else {
                    String diseaseIds = String.valueOf(diseaseCreate.get("DiseaseID"));
                    Long diseaseId = Long.valueOf(String.valueOf(diseaseCreate.get("DiseaseID")));
                    kelinConfigEntity.setDiseaseId(diseaseId);
                    // 保存数据集
                    BigInteger requestIds = new BigInteger(kelinConfigEntity.getRequestId());
                    Map<String, Object> diseaseSave = keLinHsspHttpApiService.saveDiseaseDBLocal(Integer.parseInt(diseaseIds), requestIds,
                            0, appCode,
                            token, accept, JwtForKelin.getJwt());

                    if (!diseaseSave.get("status").equals("success")) {
                        kelinConfigEntity.setStatus(2);
                        kelinConfigEntity.setMessage("创建数据集保存任务失败" + diseaseSave.get("message"));
                        log.error("创建数据集保存任务失败" + diseaseSave.get("message"));
                    } else {
                        List<Map<String, Object>> diseaseSaveData = (List<Map<String, Object>>) diseaseSave.get("data");
                        Long expressionId = Long.valueOf(String.valueOf(diseaseSaveData.get(0).get("expressionId")));
                        kelinConfigEntity.setStatus(1);
                        kelinConfigEntity.setExpressionId(expressionId);
                        Long exportId = Long.valueOf(String.valueOf(diseaseSaveData.get(0).get("exportId")));
                        kelinConfigEntity.setExportId(exportId);
                    }
                }
            } catch (Exception ex) {
                kelinConfigEntity.setStatus(2);
                kelinConfigEntity.setMessage("保存并启动入组策略失败" + ex.getMessage());
                log.error("保存并启动入组策略失败" + ex.getMessage());
            }
            // 保存柯林检索表达式验证记录
            kelinConfigEntity.setCreateTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            res = res && kelinConfigDao.updateById(kelinConfigEntity) > 0;
        }
        return res;
    }

    /**
     * 查病例的Redis
     *
     * @param empi
     * @return
     */
    public Map<String, List<String>> getPatientMatchRedis(String empi) {
        Map<String, List<String>> map = new LinkedHashMap<>();
        List<String> keys = enrollDetailRedis.get(enrollDetailRedis.REDIS_PATIENT + empi);
        for (String key : keys) {
            String visitId = key.split("_").length > 1 ? key.split("_")[0] : "";
            List<String> value = enrollDetailRedis.get(enrollDetailRedis.REDIS_EMPI + empi + EnrollDetailRedis.UNDER_SCORE + visitId);
            map.put(visitId, value);
        }
        return map;
    }

    /**
     * 根据Redis判断纳排满足情况
     *
     * @param subProjectId
     * @param patientRedis
     * @return
     */
    public Map<String, List<Long>> getIsMatch(Long subProjectId, Map<String, List<String>> patientRedis) {
        // 患者就诊满足的纳入条件情况
        Map<String, List<Long>> map = new LinkedHashMap<>();
        // 查项目纳排
        List<GroupConfigEntity> groupConfigEntityList = list(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId));
        // 纳入条件
        List<GroupConfigEntity> includeConfig = groupConfigEntityList.stream().filter(p -> p.getType() == 0).collect(Collectors.toList());
        // 必要条件ID
        List<String> necessaryConfig = groupConfigEntityList.stream().filter(p -> p.getType() == 0 && p.getNecessary() == 1).map(p -> p.getId().toString()).collect(Collectors.toList());
        // 排除条件ID
        List<String> excludeConfig = groupConfigEntityList.stream().filter(p -> p.getType() == 1).map(p -> p.getId().toString()).collect(Collectors.toList());
        List<String> fitExclude = new ArrayList<>();    // 满足的排除条件ID
        List<String> fitNecessary = new ArrayList<>();  // 满足的纳入必要条件ID
        for (String key : patientRedis.keySet()) {
            List<String> value = patientRedis.get(key);
            // 满足排除条件，则直接返回空；
            excludeConfig.forEach(id -> {
                if (value.contains(id)) {
                    fitExclude.add(id);
                    return;
                }
            });
            if (!CollectionUtils.isEmpty(fitExclude)) {
                return new LinkedHashMap<>();
            }

            // 满足纳入条件情况
            List<Long> groupConfigIds = new ArrayList<>();
            for (GroupConfigEntity groupConfigEntity : includeConfig) {
                String groupId = groupConfigEntity.getId().toString();
                if (value.contains(groupId) && !groupConfigIds.contains(groupId)) {
                    groupConfigIds.add(groupConfigEntity.getId());
                    // 满足的必要条件情况
                    if (groupConfigEntity.getNecessary() == 1 && !fitNecessary.contains(groupId)) {
                        fitNecessary.add(groupId);
                    }
                }
            }
            if (groupConfigIds.size() > 0) {
                map.put(key, groupConfigIds);
            }
        }
        // 判断必要条件是否都满足，若不满足，返回空
        if (fitNecessary.stream().sorted().equals(necessaryConfig.stream().sorted())) {
            return new LinkedHashMap<>();
        }
        return map;
    }

    /**
     * 批量 保存纳排条件并验证检索表达式
     */
    @Transactional
    @Override
    public Boolean saveConfigs(List<GroupConfigEntity> groupConfigEntitys, SysUserEntity user) {
        for(int i = 0 ; i < groupConfigEntitys.size(); i++){
            GroupConfigEntity groupConfigEntity = groupConfigEntitys.get(i);
            Boolean res = saveConfig(groupConfigEntity, user);
            if(!res) return false; // 如果有一条插入失败，则返回失败。否则，返回成功。
        }
        return true;
    }

    /**
     * 按条件ID清理redis
     */
    private void clearRedis(Long subProjectId, List<Long> groupConfigIdList) {
        List<String> configKeyList = new ArrayList<>();
        for (Long configId : groupConfigIdList) {
            String key = EnrollDetailRedis.REDIS_CONFIG + subProjectId + EnrollDetailRedis.UNDER_SCORE + configId;
            configKeyList.add(key);
        }

        // 查指定条件ID的empi
        List<String> empiList = enrollDetailRedis.uinon(configKeyList);
        // 删除config key
        enrollDetailRedis.deleteBatch(configKeyList);

        for (String empi : empiList) {
            // 查就诊
            List<String> visitList = enrollDetailRedis.get(EnrollDetailRedis.REDIS_PATIENT + empi);
            for (String visit : visitList) {
                String visitId = visit.split("_")[0];
                String empiKey = EnrollDetailRedis.REDIS_EMPI + empi + EnrollDetailRedis.UNDER_SCORE + visitId;
                // 删除empi 指定value
                for (Long configId : groupConfigIdList) {
                    enrollDetailRedis.remove(empiKey, configId.toString());
                }
            }
        }
    }

//    /**
//     * 根据Redis判断纳排满足情况
//     * @param subProjectId
//     * @param patientRedis
//     * @return
//     */
//    public Map<String, List<String>> getIsMatch(Long subProjectId, Map<String, List<String>> patientRedis) {
//        // 患者就诊满足的纳入条件情况
//        Map<String, List<String>> map = new LinkedHashMap<>();
//        // 查项目纳排
//        List<GroupConfigEntity> groupConfigEntityList = list(new QueryWrapper<GroupConfigEntity>()
//                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId));
//
//        for (String key : patientRedis.keySet()) {
//            List<String> value = patientRedis.get(key);
//            // 满足必要条件，不满足排除条件，则标识为系统推荐
//            List<String> groupConfigIds = new ArrayList<>();
//            groupConfigEntityList = groupConfigEntityList.stream().sorted(Comparator.comparing(GroupConfigEntity::getType)).sorted(Comparator.comparing(GroupConfigEntity::getSort)).collect(Collectors.toList());
//            for (GroupConfigEntity groupConfigEntity : groupConfigEntityList) {
//                String groupId = groupConfigEntity.getSubProjectId() + "_" + groupConfigEntity.getId();
//                // 纳入条件，不满足必要条件，退出本循环
//                if (groupConfigEntity.getType() == 0
//                        && groupConfigEntity.getNecessary() == 1
//                        && !value.contains(groupId)) {
//                    groupConfigIds.clear();
//                    break;
//                }
//                // 纳入条件，满足必要条件，记录条件ID
//                if (groupConfigEntity.getType() == 0
//                        && value.contains(groupId)) {
//                    groupConfigIds.add(groupId);
//                }
//                // 满足排除条件，退出本循环
//                if (groupConfigEntity.getType() == 1 && value.contains(groupId)) {
//                    groupConfigIds.clear();
//                    break;
//                }
//            }
//            if (groupConfigIds.size() > 0) {
//                map.put(key, groupConfigIds);
//            }
//        }
//        return map;
//    }
}
