package com.boot.commons.utils;

import com.boot.commons.exception.BusinessException;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

public interface DownloadUtils {

    static HttpServletResponse SplitDownFile(File file, HttpServletResponse response) {
        InputStream fis = null;
        OutputStream toClient = null;
        try {
            int buf_size = 8192;
            int length;
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(file.getPath()), buf_size);
            byte[] buffer = new byte[buf_size];
            // 清空response
            response.reset();
            toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            //如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            while ((length = fis.read(buffer)) != -1) {

                //将文件读入压缩文件内
                toClient.write(buffer,0,length);
                toClient.flush();
            }
        } catch (IOException ex) {
            ex.printStackTrace();
            throw new BusinessException("下载到服务器失败");
        } finally {
            try {
                File f = new File(file.getPath());
                f.delete();
                if (fis != null) {
                    fis.close();
                }
                if (toClient != null) {
                    toClient.close();
                }

            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException("下载到服务器失败");
            }
        }

        return response;
    }
}
