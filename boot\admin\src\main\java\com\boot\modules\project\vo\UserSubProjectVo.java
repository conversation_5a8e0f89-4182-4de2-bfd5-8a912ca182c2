package com.boot.modules.project.vo;

import com.boot.modules.sys.entity.SysUserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @desc : 添加项目用户时的vo对象
 * @create 2021-04-01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserSubProjectVo extends SysUserEntity {

    private Long userSubProjectId;
    /**
     * 用户id
     */
    private Long userId;


    /**
     * 项目id  必填
     */
    private Long projectId;

    /**
     * 子项目id，非必填 若是填了 则是子项目域
     */
    private Long subProjectId;

    /**
     * 角色id
     */
    private Long roleId;


    private String roleName;

    private Integer isPrimaryCenter;

    /**
     * 项目医疗单元id
     */
    private Long projDeptId;

    /**
     * 项目医疗单元名称
     */
    private String projDeptName;

    /**
     * 项目医疗单元父节点名称
     */
    private String parentDeptName;

    /**
     * 项目中心id
     */
    private Long topDeptId;

}
