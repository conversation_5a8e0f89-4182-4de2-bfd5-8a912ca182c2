<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.ProjectDao">
    <!--分页查询-->
    <select id="queryPage" resultType="com.boot.modules.project.vo.ProjectVo">
        SELECT a.*,
               d.nickname as projectAdminName,
               d.username as projectUserName,
               e.disease_name as diseaseName
        FROM rp_project a
                 LEFT JOIN rp_sub_project b ON a.id = b.project_id
                 LEFT JOIN rp_user_subproject c ON c.sub_project_id = b.id
                 LEFT JOIN rp_sys_user d ON d.id = a.project_admin_id
                 LEFT JOIN rp_sys_disease e on a.disease_id = e.id
                ${ew.customSqlSegment}
    </select>

    <!-- 按条件查询 -->
    <select id="getByQuery" resultType="com.boot.modules.project.vo.ProjectVo">
        SELECT a.*,
               d.nickname as projectAdminName,
               d.username as projectUserName,
               e.disease_name as diseaseName
        FROM rp_project a
                     LEFT JOIN rp_sub_project b ON a.id = b.project_id
                     LEFT JOIN rp_user_subproject c ON c.sub_project_id = b.id
                     LEFT JOIN rp_sys_user d ON d.id = a.project_admin_id
                     LEFT JOIN rp_sys_disease e on a.disease_id = e.id
            ${ew.customSqlSegment}
        GROUP BY a.id
        ORDER BY a.create_time
                DESC
    </select>

    <select id="getByUser" resultType="com.boot.modules.project.vo.SubProjectInfoVo">
        SELECT b.id, b.name as subProjectName,
               b.project_id as project_id,
               b.create_time as subProjectCreateTime,
               b.create_user_id as subProjectCreateUserId,
               a.project_name as projectName,
               a.create_time as projectCreateTime,
               a.create_user_id as projectCreateUserId,
               a.start_date as startDate,
               a.end_date as endDate,
               a.type as projectType,
               b.enable_audit as projectEnableAudit,

               d.id as projectAdminId,
               d.nickname as projectAdminName,
               d.username as projectUserName
        FROM  rp_sub_project b
                  LEFT JOIN rp_project a on a.id=b.project_id
                  LEFT JOIN rp_user_subproject c ON c.sub_project_id = b.id
                  LEFT JOIN rp_sys_user d ON d.id = a.project_admin_id
            ${ew.customSqlSegment}
        GROUP BY b.id
        ORDER BY b.create_time
        DESC
    </select>

    <select id="getInfoByQuery" resultType="com.boot.modules.project.vo.ProjectVo">
        select
            a.id as id,
            a.project_name as projectName,
            b.name as mainDeptName
        from
            rp_project a
        join
            rp_sys_dept b
        on
            a.main_dept_id = b.id
        ${ew.customSqlSegment}
    </select>

    <select id="pageByDept" resultType="com.boot.modules.project.vo.ProjectVo">
        select
            distinct a.id,
            a.project_name,
            a.project_type,
            a.status,
            c.nickname projectAdminName,
            a.start_date,
            a.end_date,
            a.enrollments_estimated_count,
            a.create_time,
            (select name from rp_sys_dept rsd where id = a.main_dept_id) dept
        from
            rp_project a
        join
            (SELECT e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id
            FROM rp_sys_user e
            JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) c
        on
            a.project_admin_id = c.id and c.dept_id = a.main_dept_id
        ${ew.customSqlSegment}
    </select>

<!--    <select id="pageByDept" resultType="com.boot.modules.project.vo.ProjectVo">-->
<!--        select-->
<!--            distinct a.id,-->
<!--            a.project_name,-->
<!--            a.project_type,-->
<!--            a.status,-->
<!--            c.nickname projectAdminName,-->
<!--            a.start_date,-->
<!--            a.end_date,-->
<!--            a.enrollments_estimated_count,-->
<!--            a.create_time,-->
<!--            tmp.name as dept-->
<!--        from-->
<!--            rp_project a-->
<!--            join rp_sub_project b on a.id = b.project_id-->
<!--            join rp_user_subproject g on g.sub_project_id = b.id-->
<!--            join rp_sys_dept tmp on tmp.id = g.project_dept_id-->
<!--            join (select e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id-->
<!--                FROM rp_sys_user e-->
<!--                JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) c-->
<!--                on a.project_admin_id = c.id-->
<!--            ${ew.customSqlSegment}-->
<!--    </select>-->

    <select id="allByDept" resultType="com.boot.modules.project.vo.ProjectVo">
        select
        distinct a.id,
        a.project_name,
        a.project_type,
        a.status,
        c.nickname projectAdminName,
        a.start_date,
        a.end_date,
        a.enrollments_estimated_count,
        (select name from rp_sys_dept rsd where id = a.main_dept_id) dept
        from
        rp_project a
        join
        (SELECT e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id
        FROM rp_sys_user e
        JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) c
        on
        a.project_admin_id = c.id and c.dept_id = a.main_dept_id
        ${ew.customSqlSegment}
    </select>

    <select id="getAddPatProjectIdList" resultType="Long">
        select distinct project_id
        from
            rp_inbound_process
        ${ew.customSqlSegment}
    </select>

    <select id="getProjectInfo" resultType="com.boot.modules.mobile.dto.ProjectReportDto">
        select a.id as projectId,a.project_name as projectName,b.name as deptName,c.nickname as adminUserName
        from
            rp_project a
            join rp_sys_dept b on a.main_dept_id = b.id
            join rp_sys_user c on a.project_admin_id = c.id
        ${ew.customSqlSegment}
    </select>
    <select id="groupByMedId" resultType="com.boot.modules.mobile.dto.MedReportDto">
        select a.main_med_id as medId,
            b.name as medName,
            count(a.id) as count
        from
            rp_project a
        join rp_sys_dept b on a.main_med_id = b.id
        ${ew.customSqlSegment}
        group by main_med_id
        order by count desc
    </select>
</mapper>