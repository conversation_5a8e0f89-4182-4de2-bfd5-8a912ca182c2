package com.boot.modules.project.vo;

import com.boot.modules.project.entity.ProjectSettingEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 子项目视图
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubProjectVo extends SubProjectEntity {

    /**
     * 当前用户在子项目中的项目角色信息
     */
    private ProjectRoleInfoVo roleInfo;

    /**
     * 当前子项目名字
     */
    private Integer patCount;

    /**
     * 已推荐人数
     */
    private Integer recommendCount;

    /**
     * 拟入组人数
     */
    private Integer inboundCount;

    /**
     * 已签人数
     */
    private Integer signCount;

    /**
     * 退出人数
     */
    private Integer exitCount;


    /**
     * 用户id
     */
    private Integer userId;

    private String projectName;
    private String refuseMsg;

    private String auditUserNickname;

    /**
     * 是否启用了微信公众号  默认是0  未开启
     */
    private Integer enableWxMp = 0;

    /**
     * 子项目配置属性
     * */
    private ProjectSettingEntity subprojectSetting;

    private Integer remainderType;

    /**
     * 是否符合系统推荐
     */
    private Integer isMatched = 0;

    /**
     * 患者状态：1-拟入组或已入组到干预性项目，患者已锁定；2-拟入组或已入组队列项目；3-已推荐
     */
    private Integer patStatus = 0;

    /**
     * 是否有系统推荐标识
     */
    private Integer isRecommend = 0;

    /**
     * 纳入条件总数
     */
    private Integer includeCount = 0;

    private Map<String, List<Long>> visitConfigMap;

    private String crcUserName;

    private String crcPhoneNumber;

    /**
     * 项目联系人
     */
    private String contacts;

    /**
     * 项目联系人电话
     */
    private String contactsPhone;

    /**
     * 用户子项目成员管理选择的医疗机构
     */
    private Long userSubProjectDeptId;

    /**
     * 项目类型 1：IIT；2：GCP
     */
    private Integer projectType;
}
