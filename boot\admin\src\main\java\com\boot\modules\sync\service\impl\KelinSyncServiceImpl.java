package com.boot.modules.sync.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.sync.entity.KelinConfigEntity;
import com.boot.modules.sync.service.KeLinHsspHttpApiService;
import com.boot.modules.sync.service.KelinConfigService;
import com.boot.modules.sync.service.KelinSsoHttpApiService;
import com.boot.modules.sync.service.KelinSyncService;
import com.boot.modules.sync.utils.JwtForKelin;
import com.boot.modules.sys.dao.SysUserDao;
import com.boot.modules.sys.entity.SysUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KelinSyncServiceImpl implements KelinSyncService {
    @Resource
    private ProjectService projectService;

    @Resource
    private KelinConfigService kelinConfigService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private KeLinHsspHttpApiService keLinHsspHttpApiService;

    @Resource
    private KelinSsoHttpApiService kelinSsoHttpApiService;

    @Resource
    private SysUserDao sysUserDao;

    public final String accept = "application/json, text/javascript, */*; q=0.01";

    @Resource
    SSOConfigurationProperties ssoConfig;
    /**
     * 病例自动入组
     */
    @Async
    @Override
    public void autoIntoCase() throws ParseException {
        // 查所有项目
        QueryWrapper<ProjectEntity> qwPrj = new QueryWrapper<>();
        qwPrj.lambda().eq(ProjectEntity::getStatus, 1);
        List<ProjectEntity> projectEntityList = projectService.list(qwPrj);
        if (projectEntityList == null || projectEntityList.size() == 0) {
            return;
        }
        List<KelinConfigEntity> kelinConfigEntityList = kelinConfigService.list();

        if (CollectionUtils.isEmpty(kelinConfigEntityList)) {
            return;
        }

        List<SubProjectEntity> subProjectEntityList = subProjectService.list();

        if (subProjectEntityList == null || subProjectEntityList.size() == 0) {
            return;
        }
        // 查指定项目下的子项目
        for (ProjectEntity projectEntity : projectEntityList) {
            List<SubProjectEntity> curSubProjects = subProjectEntityList.stream()
                    .filter(f -> f.getProjectId().equals(projectEntity.getId()))
                    .collect(Collectors.toList());

            // 查指定子项目下的所有检索入组记录
            for (SubProjectEntity subProjectEntity : curSubProjects) {
                List<KelinConfigEntity> curList = kelinConfigEntityList.stream()
                        .filter(f -> f.getSubProjectId() != null && f.getSubProjectId().equals(subProjectEntity.getId()))
                        .collect(Collectors.toList());

                for (KelinConfigEntity kelinConfigEntity : curList) {
                    //入组策略停止状态，跳过
                    if (!kelinConfigEntity.getStatus().equals(1)) {
                        continue;
                    }

                    //未通知写入，跳过
                    if (kelinConfigEntity.getNoticeStatus().equals(0)) {
                        continue;
                    }
//
//                    // 下次执行时间未到，不入组
//                    if (DateUtils.getSecondsBetweenTwo(DateUtils.getNowTimeStr(DATE_TIME_PATTERN), dieaseSaveVo.getNextTime()) > 0) {
//                        continue;
//                    }
                    //未通知写入，跳过
                    if (StringUtils.isEmpty(kelinConfigEntity.getKlEndTime())) {
                        continue;
                    }

                    //入组病例
                    kelinConfigService.asyncPatient(kelinConfigEntity, true);
                }
            }
        }
    }
    /**
     * 病例增量数据写入通知
     */
    @Async
    @Override
    public void autoNotice() throws ParseException {
        // 查所有项目
        QueryWrapper<ProjectEntity> qwPrj = new QueryWrapper<>();
        qwPrj.lambda().eq(ProjectEntity::getStatus, 1);
        List<ProjectEntity> projectEntityList = projectService.list(qwPrj);

        // 查指定项目下的子项目
        for (ProjectEntity projectEntity : projectEntityList) {
            QueryWrapper<SubProjectEntity> qwSubPrj = new QueryWrapper<>();
            qwSubPrj.lambda().eq(SubProjectEntity::getProjectId, projectEntity.getId());
            List<SubProjectEntity> subProjectEntityList = subProjectService.list(qwSubPrj);

            // 查指定子项目下的所有检索入组记录
            for (SubProjectEntity subProjectEntity : subProjectEntityList) {
                QueryWrapper<KelinConfigEntity> qwKe = new QueryWrapper<>();
                qwKe.lambda().eq(KelinConfigEntity::getSubProjectId, subProjectEntity.getId());
                List<KelinConfigEntity> kelinConfigEntityList = kelinConfigService.list(qwKe);
                for (KelinConfigEntity kelinConfigEntity : kelinConfigEntityList) {
                    //入组策略停止状态，跳过
                    if (!kelinConfigEntity.getStatus().equals(1)) {
                        continue;
                    }

                    //已通知写入，跳过
                    if (kelinConfigEntity.getNoticeStatus().equals(1)) {
                        continue;
                    }

//                    // 下次执行时间未到，不通知
//                    if (DateUtils.getSecondsBetweenTwo(DateUtils.getNowTimeStr(DATE_TIME_PATTERN), dieaseSaveVo.getNextTime()) > 0) {
//                        continue;
//                    }

                    //通知写入
                    //查詢userid(项目管理员)
                    Long userid = projectEntity.getProjectAdminId();
                    //查詢用戶username
                    SysUserEntity userEntity = sysUserDao.selectById(userid);
                    kelinConfigEntity.setNoticeStatus(0);
                    if (userEntity.getUsername() == null) {
                        kelinConfigEntity.setMessage("获取用戶id" + userid + "失败");
                        kelinConfigService.updateById(kelinConfigEntity);
                        throw new BusinessException("获取用戶id" + userid + "失败");
                    }
                    String token = kelinSsoHttpApiService.getHsspToken(userEntity.getUsername(), JwtForKelin.getJwt());
                    if (StringUtils.isEmpty(token)) {
                        kelinConfigEntity.setMessage("token获取失败");
                        kelinConfigService.updateById(kelinConfigEntity);
                        throw new BusinessException("token获取失败");
                    }
                    //通知前记录结束时间
                    kelinConfigEntity.setKlEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                    Map<String, Object> startDiseaseSaveRes = keLinHsspHttpApiService.startDiseaseSave(String.valueOf(kelinConfigEntity.getExportId()),
                            ssoConfig.getAppCode(), String.valueOf(kelinConfigEntity.getExpressionId()), token, accept, JwtForKelin.getJwt());
                    if (!"succeed".equals(startDiseaseSaveRes.get("status"))) {
                        kelinConfigEntity.setMessage("柯林接口异常（通知柯林导出数据）");
                        kelinConfigService.updateById(kelinConfigEntity);
                        log.error("柯林接口异常（通知柯林导出数据）--HTTP状态码:{}, 异常原因:{}",startDiseaseSaveRes.get("code"), startDiseaseSaveRes.get("message"));
                        continue;
                    }
                    // 更新通知状态为已通知
                    kelinConfigEntity.setNoticeStatus(1);
                    kelinConfigService.updateById(kelinConfigEntity);
                }
            }
        }
    }
}
