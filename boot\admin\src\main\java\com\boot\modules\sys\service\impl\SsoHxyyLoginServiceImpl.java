package com.boot.modules.sys.service.impl;

import com.boot.commons.constants.Const;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.SSOUtil;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import com.boot.modules.sys.service.SSOService;
import com.boot.modules.sys.service.SysUserService;
import com.boot.modules.sys.service.SysUserTokenService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/5 15:29
 */
@Service
public class SsoHxyyLoginServiceImpl implements SSOService {

    private static final Logger log = LogManager.getLogger(SsoHxyyLoginServiceImpl.class);

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysUserTokenService sysUserTokenService;

    private boolean loginType = BootAdminProperties.userLgoin;


    @Override
    public SysUserEntity ssoLogin(HttpServletRequest request, HttpServletResponse response, String code) throws Exception {
        SysUserEntity userInfo = sysUserService.queryByUserName(code);
        if (userInfo == null || StringUtils.isEmpty(userInfo.getUsername())) {
            log.error("用户名" + code + "在受试者入组平台系统中没有维护！");
            throw new BusinessException("用户名" + code + "在受试者入组平台系统中没有维护！");
        }
        log.info("localUserInfo=" + userInfo);
        return userInfo;
    }

    @Override
    public boolean ssoLogout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return false;
    }

    @Override
    public String createTokenByUserName(String userName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        SysUserEntity user;

        //当前登录ip地址和时间
        String loginIp = request.getRemoteAddr();
        //定义token相关信息
        if (BootAdminProperties.adminAccount.equals(userName)) {
            // 超级管理员

            //检验超级管理员的IP登录限制
            SSOUtil.adminLoginIpCheck(request);

            user = new SysUserEntity();
            user.setId(Const.SUPER_ADMIN);
            user.setUsername(userName);
            user.setNickname("超级管理员");
            user.setIsSuperAdmin(1);
            user.setIsSysAdmin(0);
        } else {
            user = sysUserService.queryByUserName(userName);
            if (user == null || user.getId() == null) {
                log.error("用户不存在");
                throw new BusinessException("用户不存在");
            } else {

                if (CollectionUtils.isEmpty(user.getRoleNameList())) {
                    log.error("账号未分配系统角色，无法登录");
                    throw new BusinessException("账号未分配系统角色，无法登录");
                }
            }
        }

        return loginCheck(user, loginIp);
    }

    /**
     * 判断用户是否已登录方法提取
     */
    private String loginCheck(SysUserEntity user, String loginIp) {
        //获取当前时间，定义判断已登录用户code为001
//        SysUserTokenEntity userToken = sysUserTokenService.getById(user.getId());
        String userId = user.getId().toString();
        List<SysUserTokenEntity> userTokenEntityList = sysUserTokenService.getByUserId(userId, null);
        SysUserTokenEntity userToken = CollectionUtils.isEmpty(userTokenEntityList) ? null : userTokenEntityList.get(0);
        if (userToken == null) {
            // 第一次登录
            Result result = sysUserTokenService.createToken(user.getId(), loginIp);
            if (result == null) {
                log.error("生成token失败");
                throw new BusinessException("生成token失败");
            }

            return result.getToken();
        }

        String userIp = userToken.getLoginIp();
        Date expireTime = userToken.getExpireTime();
        Date nowTime = new Date();

        if (loginType) {
            if (!nowTime.before(expireTime)) {
                Result result = sysUserTokenService.createToken(user.getId(), loginIp);
                if (result == null) {
                    log.error("生成token失败");
                    throw new BusinessException("生成token失败");
                }
                return result.getToken();
            }
            return allowLogin(userToken, user, loginIp);
        } else {
            if (!loginIp.equals(userIp)) {
                //生成token，并保存到数据库
                Result result = sysUserTokenService.createToken(user.getId(), loginIp);
                if (result == null) {
                    log.error("生成token失败");
                    throw new BusinessException("生成token失败");
                }
                return result.getToken();
            }
        }
        return allowLogin(userToken, user, loginIp);
    }


    /**
     * 允许多人同时在线登录
     *
     * @param userToken`
     * @param user
     * @param loginIp
     * @return
     */
    private String allowLogin(SysUserTokenEntity userToken, SysUserEntity user, String loginIp) {
        Date nowTime = new Date();
        userToken.setExpireTime(new Date(nowTime.getTime() + (3600 * 12 * 1000)));
        userToken.setLoginIp(loginIp);
        boolean flag = sysUserTokenService.updateToken(userToken);
        if (user.getLoginErrorCount() != null && user.getLoginErrorCount() != 0) {
            user.setLoginErrorCount(0);
            flag = flag && sysUserService.updateById(user);
        }
        if (flag) {
            user.setToken(userToken.getToken());
            Result result = R.success();
            result.setData(user);
            result.setToken(userToken.getToken());
            result.setExpire(3600 * 12);

            return userToken.getToken();
        }

        return null;
    }
}
