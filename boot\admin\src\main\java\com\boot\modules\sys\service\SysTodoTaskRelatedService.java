package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.sys.entity.SysTodoTaskRelatedEntity;

import java.util.List;
import java.util.Map;

/**
 * 待办任务关联
 *
 * <AUTHOR>
 */
public interface SysTodoTaskRelatedService extends IService<SysTodoTaskRelatedEntity> {
    /**
     * 查询代办任务关联
     */
    List<SysTodoTaskRelatedEntity> list(Map<String, Object> params);

    /**
     * 新增代办任务关联
     */
    boolean insert(SysTodoTaskRelatedEntity sysTodoTaskRelatedEntity);

    /**
     * 修改代办任务关联
     */
    boolean update(SysTodoTaskRelatedEntity sysTodoTaskRelatedEntity);

    /**
     * 删除代办任务关联
     */
    boolean delete(Long[] ids);
}
