package com.boot.commons.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boot.commons.annotation.TuoMinTo;
import com.boot.commons.annotation.ResultMap;
import com.boot.commons.enums.TuoMinEnum;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.DesensitiseUtil;
import com.boot.commons.utils.PageUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;

/**
 * 切面
 * 规范返回数据
 * 并进行数据过滤
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(2)
public class ResultMapAspect {
    @Around("@annotation(com.boot.commons.annotation.ResultMap)")
    public Object resultMapping(ProceedingJoinPoint joinPoint) throws Throwable {
        //方法执行前
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        ResultMap resultMap = methodSignature.getMethod().getAnnotation(ResultMap.class);
        //获取了ResultMap的参数,然后遍历，和返回类上的参数做比较；
        Class<?>[] resultMapValues = resultMap.value();
        //执行方法，返回的对象
        Object proceed = joinPoint.proceed();
        if (proceed == null) {
            return R.success();
        }
        return R.success(hasBelongTo(proceed, resultMapValues));
    }

    /**
     * 如果字段上出现belongTo的注解
     */
    private Object hasBelongTo(Object proceed, Class<?>[] resultMapValues) throws ParseException, IllegalAccessException {


        if (proceed instanceof Result) {
            Object data = ((Result) proceed).getData();
            if (data == null) {
                return data;
            }

            if (data instanceof PageUtils) {
                PageUtils page = (PageUtils) data;
                List cur = (List) page.getList();
                if (cur == null || cur.size() == 0) {
                    return page;
                }

                Object curO = cur.get(0);
                // 获取所有public字段,包括父类字段
                List<Field> declaredFields = getAllField(curO.getClass());

                List<String> fields = new ArrayList<>();
                Map<String, TuoMinEnum> fieldsMap = new HashMap<>();
                for (Field field : declaredFields) {
                    if (field.isAnnotationPresent(TuoMinTo.class)) {
                        TuoMinTo anno = field.getAnnotation(TuoMinTo.class);
                        // 重新设置值
                        fields.add(field.getName());
                        fieldsMap.put(field.getName(), anno.tmType());
                    }
                }

                JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(cur));
                for (Object o : jsonArray) {
                    JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
                    for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                        //对没个脱敏设置进行遍历
                        if (fieldsMap.containsKey(entry.getKey())) {
                            //如果字段匹配上了  进行脱敏操作
                            String sourceStr = (String) entry.getValue();
                            // TODO 临时写死
                            entry.setValue(DesensitiseUtil.tuoMin(fieldsMap.get(entry.getKey()).getType(), sourceStr, false));
                        }

                    }
                }
                page.setList(jsonArray.toJavaList(Object.class));
                return page;
            } else if (data instanceof ArrayList) {
                return data;
            } else {
                // 获取所有public字段,包括父类字段
                List<Field> declaredFields = getAllField(data.getClass());

                List<String> fields = new ArrayList<>();
                Map<String, TuoMinEnum> fieldsMap = new HashMap<>();
                for (Field field : declaredFields) {
                    if (field.isAnnotationPresent(TuoMinTo.class)) {
                        TuoMinTo anno = field.getAnnotation(TuoMinTo.class);
                        // 重新设置值
                        fields.add(field.getName());
                        fieldsMap.put(field.getName(), anno.tmType());
                    }
                }
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(data));
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    //对没个脱敏设置进行遍历
                    if (fieldsMap.containsKey(entry.getKey())) {
                        //如果字段匹配上了  进行脱敏操作
                        String sourceStr = (String) entry.getValue();
                        // TODO 临时写死
                        entry.setValue(DesensitiseUtil.tuoMin(fieldsMap.get(entry.getKey()).getType(), sourceStr, false));
                    }

                }
                return jsonObject;
            }
        }

        return null;
    }

    /**
     * 判断classes2里是否有classes1
     *
     * @param classes1
     * @param classes2
     * @return
     */
    private boolean hasSameClass(Class<?>[] classes1, Class<?>[] classes2) {
        for (Class<?> class1 : classes1) {
            for (Class<?> class2 : classes2) {
                if (class1.equals(class2)) {
                    return true;
                }
            }
        }
        return false;
    }

    private List<Field> getAllField(Class clazz) {
        // 保存属性对象数组到列表
        List fieldsList = new ArrayList<Field>();
        while (clazz != null) {  // 遍历所有父类字节码对象
            Field[] declaredFields = clazz.getDeclaredFields();  // 获取字节码对象的属性对象数组
            fieldsList.addAll(Arrays.asList(declaredFields));

            clazz = clazz.getSuperclass();  // 获得父类的字节码对象
        }


        return fieldsList;
    }
}