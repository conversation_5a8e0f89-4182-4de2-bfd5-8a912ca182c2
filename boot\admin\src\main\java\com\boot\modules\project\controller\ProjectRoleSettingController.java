package com.boot.modules.project.controller;


import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.modules.project.entity.ProjectRoleSettingEntity;
import com.boot.modules.project.service.ProjectRoleSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "项目角色配置")
@RestController
@RequestMapping("/project/rolesetting")
public class ProjectRoleSettingController {

    @Resource
    ProjectRoleSettingService roleSettingService;

    @ApiOperation(
            value = "获取指定项目的角色配置",
            notes = "获取指定项目的角色配置"
    )
    @GetMapping("list/{subProjectId}/{projectCategory}")
    @RequiresPermissions("project:setting:list")
    public Result list(@PathVariable("subProjectId") Long subProjectId, @PathVariable("projectCategory") Integer projectCategory) {

        List<ProjectRoleSettingEntity> list = roleSettingService.queryBySubProjectId(subProjectId, projectCategory);

        return R.success(list);
    }

    @ApiOperation(
            value = "获取指定项目的指定角色配置",
            notes = "获取指定项目的指定角色配置"
    )
    @GetMapping("/{subProjectId}/{roleId}")
    @RequiresPermissions("project:setting:list")
    public Result list(@PathVariable("subProjectId") Long subProjectId, @PathVariable("roleId") Long roleId) {
        List<ProjectRoleSettingEntity> list = roleSettingService.query(subProjectId, roleId);

        return R.success(list);
    }

    @ApiOperation(
            value = "保存配置",
            notes = "保存配置"
    )
    @PostMapping("{subProjectId}")
    @RequiresPermissions("project:setting:save")
    public Result saveSetting(@RequestBody List<ProjectRoleSettingEntity> list, @PathVariable("subProjectId") Long subProjectId) {
        boolean ret = roleSettingService.saveSetting(list, subProjectId);

        return ret ? R.success() : R.fail();
    }
}
