package com.boot.modules.project.enums;

/**
 * 项目类型枚举
 *
 * <AUTHOR>
 */
public enum ProjectTypeEnum {

    /**
     * 单病种 ， 项目下直接入组病例  项目管理用户  对应的是项目域-1
     */
    SINGLE_DISEASE(1),

    /**
     * 多病种， 项目下有研究组 有研究组入组病例 研究组管理用户  对应的是研究组域-2
     */
    MULTIPLE_DISEASE(2),

//    /**
//     * 常规的， 能够兼容就的项目，
//     */
//    GENERAL(1),
//
//    /**
//     * 人为的分组研究
//     */
//    MANUAL_GROUPING(2),
//
//    /**
//     * 随机分组
//     */
//    RANDOM_GROUPING(3),
    ;

    private Integer type;

    ProjectTypeEnum(int type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static ProjectTypeEnum getEnumByType(int type) {
        ProjectTypeEnum[] types = ProjectTypeEnum.values();
        for (ProjectTypeEnum enme : types) {
            if (type == enme.getType()) {
                return enme;
            }
        }
        return null;
    }

}
