package com.boot.commons.enums;

/**
 * 进行状态 1，进行中/未完成，2：完成
 * 表示这种进行状态的都可以用这个enum
 *
 * <AUTHOR>
 * @create
 */
public enum VisitStatusEnum {

    /**
     * 随访状态为：进行中
     */
    RUNNING(1, "进行中/未完成"),

    /**
     * 随访状态为：完成
     */
    FINISH(2, "完成"),


    UN_START(3, "未开始"),

    STOP(4, "已停止");

    private int type;

    private String msg;

    public int getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    VisitStatusEnum(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }


}


