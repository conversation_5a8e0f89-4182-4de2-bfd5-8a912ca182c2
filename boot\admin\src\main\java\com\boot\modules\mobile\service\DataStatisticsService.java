package com.boot.modules.mobile.service;

import com.boot.commons.utils.PageUtils;
import com.boot.modules.mobile.dto.MedReportDto;
import com.boot.modules.mobile.dto.ProjectReportDto;
import com.boot.modules.mobile.model.OverviewModel;
import com.boot.modules.mobile.model.PercentModel;
import com.boot.modules.mobile.model.RecommendCountModel;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface DataStatisticsService {
    /**
     * 数据总览
     *
     * @param param
     * @return
     */
    OverviewModel overview(Map<String, Object> param);

    /**
     * 推荐数占比
     *
     * @param param
     * @return
     */
    List<PercentModel> percent(Map<String, Object> param);

    /**
     * 推荐入组数统计
     *
     * @param param
     * @return
     */
    RecommendCountModel count(Map<String, Object> param) throws ParseException;

    /**
     * 通过条件查询记录
     *
     * @param param
     * @return
     */
    PageUtils queryPage(Map<String, Object> param);

    /**
     * 通过条件查询记录
     *
     * @param param
     * @return
     */
    List<ProjectReportDto> project(Map<String, Object> param);

    List<MedReportDto> sort(Map<String, Object> param);
}
