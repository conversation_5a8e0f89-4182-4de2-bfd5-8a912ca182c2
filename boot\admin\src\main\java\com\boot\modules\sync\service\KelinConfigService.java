package com.boot.modules.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.sync.entity.KelinConfigEntity;

import java.text.ParseException;

/**
 * <AUTHOR>
 */
public interface KelinConfigService extends IService<KelinConfigEntity> {
    /**
     * 更新执行任务
     *
     * @param diseaseId
     * @param status
     * @param message
     * @return
     */
    Boolean update(Long diseaseId, Integer status, String message, Integer notice);

    /**
     * 首次检索
     *
     * @param diseaseId
     * @param expressionId
     * @param exportId
     * @return
     * @throws ParseException
     */
    Boolean firstSearch(Long diseaseId, Long expressionId, Long exportId) throws ParseException;


    void asyncPatient(KelinConfigEntity kelinConfigEntity, Boolean isDecreased) throws ParseException;
}