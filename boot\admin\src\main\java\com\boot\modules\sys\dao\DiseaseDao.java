package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.sys.entity.DiseaseEntity;
import com.boot.modules.sys.vo.DiseaseProjectVo;
import com.boot.modules.sys.vo.DiseaseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface DiseaseDao extends BaseMapper<DiseaseEntity> {
//    /**
//     * 获取病种和科室的信息
//     * @param qw
//     * @return
//     */
//    List<DiseaseVo> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<DiseaseVo> qw);

//    IPage<DiseaseVo> pageByQuery(IPage<DiseaseVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<DiseaseVo> qw);

    IPage<DiseaseVo> pageQuery(IPage<DiseaseVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<DiseaseVo> qw);
//    /**
//     * 获取病种和科室的信息
//     * @param qw
//     * @return
//     */
//    DiseaseVo getById(@Param(Constants.WRAPPER) QueryWrapper<DiseaseVo> qw);

    List<DiseaseProjectVo> getDieaseByDept(@Param(Constants.WRAPPER) QueryWrapper<DiseaseProjectVo> qw);

    IPage<DiseaseVo> pageQueryByDept(IPage<DiseaseVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<DiseaseVo> qw);
}
