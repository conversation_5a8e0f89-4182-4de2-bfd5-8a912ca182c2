//package com.boot.modules.sys.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.boot.commons.result.R;
//import com.boot.commons.result.Result;
////import com.boot.modules.sys.entity.SysUserDeptEntity;
////import com.boot.modules.sys.service.SysUserDeptService;
//import com.boot.modules.sys.vo.UserDeptsVo;
//import com.boot.modules.sys.vo.UserPermissionVo;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @desc : 用户机构权限管理
// * @create 2022-04-19
// */
//@Api(value = "用户机构权限管理", tags = "用户机构权限管理")
//@RestController
//@RequestMapping("/user/dept")
//public class SysUserDeptController extends AbstractController {
//
//    @Resource
//    private SysUserDeptService userDeptService;
//
//    /**
//     * 指定用户的权限列表
//     *
//     * @param userId
//     * @return
//     */
//    @ApiOperation(
//            value = "指定用户的权限列表",
//            notes = "指定用户的权限列表"
//    )
//    @GetMapping("/list")
////    @RequiresPermissions("user:dept:list")
//    public Result list(@RequestParam(required = false) Long userId) {
//        List<SysUserDeptEntity> list = userDeptService.list(
//                new QueryWrapper<SysUserDeptEntity>().lambda().eq(userId != null, SysUserDeptEntity::getUserId, userId)
//        );
//        return R.success(list);
//    }
//
//    /**
//     * 指定用户的权限列表
//     *
//     * @param
//     * @return
//     */
//    @ApiOperation(
//            value = "指定用户的权限",
//            notes = "指定用户的权限"
//    )
//    @GetMapping("/permission")
////    @RequiresPermissions("user:dept:list")
//    public Result userPermission() {
//        Long userId = getUserId();
//        List<UserPermissionVo> list = userDeptService.getUserPermission(userId);
//        return R.success(list);
//    }
//
//
//    /**
//     * 获取指定id的数据
//     *
//     * @param id
//     * @return
//     */
//    @ApiOperation(
//            value = "指定Id的数据",
//            notes = "指定Id的数据"
//    )
//    @GetMapping("{id}")
////    @RequiresPermissions("user:dept:info")
//    public Result info(@PathVariable Long id) {
//        SysUserDeptEntity entity = userDeptService.getById(id);
//        return R.success(entity);
//    }
//
//    @ApiOperation(
//            value = "新增或者修改用户的机构权限",
//            notes = "新增或者修改用户的机构权限"
//    )
//    @PostMapping("saveOrUpdate")
////    @RequiresPermissions("user:dept:save")
//    public Result saveOrUpdate(@RequestBody UserDeptsVo vo) {
//
//        userDeptService.saveOrUpdate(vo);
//
//        return R.success("修改成功");
//    }
//}
