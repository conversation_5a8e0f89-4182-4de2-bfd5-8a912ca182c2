<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.EnrollDetailDao">
    <!-- 按条件查询 -->
    <select id="getByQuery" resultType="com.boot.modules.project.vo.EnrollDetailVo">
        SELECT a.*,
               b.*
        FROM rp_enroll_detail a
                     LEFT JOIN rp_patient b ON a.empi = b.empiid
            ${ew.customSqlSegment}
        ORDER BY a.id
                DESC
    </select>

</mapper>