package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.DiseaseEntity;
import com.boot.modules.sys.model.DieaseProjectModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DiseaseService extends IService<DiseaseEntity> {
    /**
     * 分页获取全部病种列表
     * @param params
     * @return 分页后的病种列表
     */
    PageUtils getAllByPage(Map<String, Object> params);

    /**
     * 分页获取指定病种列表
     * @param params
     * @return 分页后的病种列表
     */
    PageUtils pageByDept(Map<String, Object> params);

    /**
     * 分页获取指定病种列表
     * @param params
     * @return 分页后的病种列表
     */
    PageUtils pageByDoctor(Map<String, Object> params);

    /**
//     * 获取指定id的病种信息
//     * @param id 病种id
//     * @return 指定id的病种信息
//     */
//    DiseaseVo getById(Long id);
    /**
     * 添加病种
     * @param diseaseEntity
     * @return
     */
    boolean add(DiseaseEntity diseaseEntity);
    /**
     * 更新病种
     * @param diseaseEntity
     * @return
     */
    boolean updateDisease(DiseaseEntity diseaseEntity);
    /**
     * excel导入病种
     * @param file 文件
     * @return
     */
    boolean excelImport(MultipartFile file);
    
    List<DieaseProjectModel> getDieaseByDept(List<Long> deptIdList);


}
