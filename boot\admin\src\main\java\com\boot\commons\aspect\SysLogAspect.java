package com.boot.commons.aspect;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.enums.LogTypeEnum;
import com.boot.commons.enums.ResponseStatusEnum;
import com.boot.commons.result.Result;
import com.boot.modules.sys.entity.SysLogEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.form.SysLoginForm;
import com.boot.modules.sys.service.SysLogService;
import com.google.gson.Gson;


import com.boot.commons.utils.HttpContextUtils;
import com.boot.commons.utils.IPUtils;

import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;

/**
 * 系统日志，切面处理类
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class SysLogAspect {
    @Resource
    private SysLogService sysLogService;

    @Pointcut("@annotation(com.boot.commons.annotation.SysLog)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        //设置日志
        setSysLog(point, 0L);

        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        sysLogEntity.setTime(time);

        return result;
    }

    @AfterReturning(returning = "ret", pointcut = "logPointCut()")
    public void doAfterReturning(Object ret) throws Throwable {
        //保存日志

        JSONObject retJson = JSONObject.parseObject(JSON.toJSONString(ret));
        if (retJson == null) {
            return;
        }
        if (retJson.getString("code").equals(ResponseStatusEnum.SUCCESS.getCode())) {
            // 成功
            sysLogEntity.setStatus(1);
        } else {
            sysLogEntity.setStatus(0);
        }
        //保存系统日志
        sysLogService.save(sysLogEntity);
    }

    @AfterThrowing(pointcut = "logPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {

        sysLogEntity.setStatus(0);
        sysLogEntity.setOperaType(LogTypeEnum.ERROR.getType());
        sysLogEntity.setErrorInfo(getErrorMsg(e));
        //保存系统日志
        sysLogService.save(sysLogEntity);
    }


    private SysLogEntity sysLogEntity;

    private void setSysLog(ProceedingJoinPoint point, long time) {
        sysLogEntity = new SysLogEntity();
        sysLogEntity.setTime(time);
        sysLogEntity.setCreateDate(new Date());

        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        SysLog syslog = method.getAnnotation(SysLog.class);
        if (syslog != null) {
            //注解上的描述
            sysLogEntity.setOperation(syslog.value());
            // 日志类型
            sysLogEntity.setOperaType(syslog.type().getType());
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        sysLogEntity.setUrl(request.getRequestURL().toString());
        sysLogEntity.setIp(request.getRemoteAddr());
        sysLogEntity.setMethod(request.getMethod());
        sysLogEntity.setParams(Arrays.toString(point.getArgs()));


        assert syslog != null;
        Object user = SecurityUtils.getSubject().getPrincipal();

        if (user == null) {
            // 可能是登录
            if (syslog.type().equals(LogTypeEnum.LOGIN)) {
                // 从参数中获取登录名
                Object[] args = point.getArgs();//2.传参
                String curParams = new Gson().toJson(args[0]);
                SysLoginForm form = new Gson().fromJson(curParams, SysLoginForm.class);
                if (form != null) {
                    sysLogEntity.setUsername(form.getUsername());
                }
            }
        } else {
            //用户名
            String username = ((SysUserEntity) user).getUsername();
            sysLogEntity.setUsername(username);
        }
    }


    public static String getErrorMsg(Throwable thr) {
        StringWriter stringWriter = new StringWriter();
        try (PrintWriter printWriter = new PrintWriter(stringWriter)) {
            thr.printStackTrace(printWriter);
            return stringWriter.toString();

        }
    }
}
