package com.boot.modules.cas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.enums.RoleLevelEnum;
import com.boot.commons.utils.DateUtils;
import com.boot.modules.cas.service.CasUserAndRoleService;
import com.boot.modules.cas.vo.CasUserVo;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.entity.UserSubProjectEntity;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.service.UserSubProjectService;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.dao.SysUserRoleDao;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserHisEntity;
import com.boot.modules.sys.entity.SysUserRoleEntity;
import com.boot.modules.sys.service.SysRoleService;
import com.boot.modules.sys.service.SysUserHisService;
import com.boot.modules.sys.service.SysUserRoleService;
import com.boot.modules.sys.service.SysUserService;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * cas用户角色业务类
 * <AUTHOR>
 * @createTime 2021年11月23日 10:01:00
 */
@Service
public class CasUserAndRoleServiceImpl implements CasUserAndRoleService {
    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private ProjectService projectService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private SysUserRoleDao sysUserRoleDao;

    @Resource
    private SysUserRoleService sysUserRoleService;

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private SysUserHisService sysUserHisService;
    /**
     * 获取所有角色信息,包括系统角色、项目角色及项目、子项目信息
     * @return
     */
    @Override
    public List<CasUserVo.CASRole> getRoles() {
        List<SysRoleEntity> roleEntities = sysRoleService.list();
        List<SubProjectEntity> subProjectEntities = subProjectService.lambdaQuery().eq(SubProjectEntity::getEnabled, 1).list();
        List<ProjectEntity> projectEntities = projectService.lambdaQuery().eq(ProjectEntity::getStatus, 1).list();

        Map<Long, String> projectNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectEntities)) {
            projectNameMap = projectEntities.stream().collect(Collectors.toMap(ProjectEntity::getId, ProjectEntity::getProjectName, (s1, s2) -> s2));
        }

        if (!CollectionUtils.isEmpty(roleEntities)) {
            List<CasUserVo.CASRole> casRoles = new ArrayList<>();
            for (SysRoleEntity roleEntity : roleEntities) {
                CasUserVo.CASRole casRole = new CasUserVo.CASRole();
                Long roleId = roleEntity.getId();
                // 判断是系统角色还是项目角色
                Integer roleLevel = roleEntity.getRoleLevel();
                if (RoleLevelEnum.SYSTEM.getLevel().equals(roleLevel)) {
//                    casRole.setId(roleId.toString() + "^^");
//                    casRole.setLabel(roleEntity.getRoleName() + "^^");
                } else if (RoleLevelEnum.PROJECT.getLevel().equals(roleLevel)) {
                    // 过滤掉项目管理员
                    if (new Integer(1).equals(roleEntity.getIsProjAdmin())) {
                        continue;
                    }

                    if (!CollectionUtils.isEmpty(subProjectEntities)) {
                        for (SubProjectEntity subProjectEntity : subProjectEntities) {
                            Long projectId = subProjectEntity.getProjectId();
                            String projectName = projectNameMap.get(projectId);
                            String id = projectId + "^" + subProjectEntity.getId() + "^" + roleId;
                            String label = projectName + "^" + subProjectEntity.getName() + "^" + roleEntity.getRoleName();

                            casRole.setId(id);
                            casRole.setLabel(label);
                        }
                    }
                }

                casRoles.add(casRole);
            }

            return casRoles;
        }

        return null;
    }

    /**
     * 获取指定用户名的角色信息,包括系统角色、项目角色及项目、子项目信息
     * @param loginName 用户名
     * @return
     */
    @Override
    public List<CasUserVo.CASRole> getByUserName(String loginName) {
        SysUserRoleEntity sysRole = null;
        List<SysUserRoleEntity> sysUserRoleEntities = sysUserRoleDao.getByQuery(
                new QueryWrapper<SysUserRoleEntity>().eq("b.username", loginName));
        if (!CollectionUtils.isEmpty(sysUserRoleEntities)){
            sysRole = sysUserRoleEntities.get(0);
        }else {
            return new ArrayList<CasUserVo.CASRole>();
        }

//        CasUserVo.CASRole casRole;
//        // 1.获取系统角色
//        List<CasUserVo.CASRole> casRoles = new ArrayList<>();
//        casRole = new CasUserVo.CASRole();
//        casRole.setId(sysRole.getId().toString());
//        casRole.setLabel(sysRole.getRoleName());
//        casRoles.add(casRole);

        // 2.遍历子项目及项目角色信息
        List<CasUserVo.CASRole> casRoles = new ArrayList<>();
        Long userId = sysRole == null ? 0L : sysRole.getUserId();
        List<SubProjectVo> subProjectVos = subProjectService.getByUserId(userId);
        if (!CollectionUtils.isEmpty(subProjectVos)){
            for (SubProjectVo subProjectVo : subProjectVos) {
                CasUserVo.CASRole casRole = new CasUserVo.CASRole();
                Long projectId = subProjectVo.getProjectId();
                String projectName = subProjectVo.getProjectName();
                String id = projectId + "^" + subProjectVo.getId() + subProjectVo.getRoleInfo().getRoleId();
                String label = projectName + "^" + subProjectVo.getName() + subProjectVo.getRoleInfo().getRoleName();

                casRole.setId(id);
                casRole.setLabel(label);
                casRoles.add(casRole);
            }
        }

        return casRoles;
    }

    /**
     * cas同步用户信息及用户角色信息
     * @param casUserVos 用户信息及角色信息
     * @param loginUserId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Map<String, Object>> saveUserAndRole(List<CasUserVo> casUserVos, Long loginUserId, Long deptId, String deptCode) {
        if (CollectionUtils.isEmpty(casUserVos)) {
            return null;
        }

        // 获取科研用户角色ID
        QueryWrapper<SysRoleEntity> qwRole = new QueryWrapper<>();
        qwRole.lambda().eq(SysRoleEntity::getRoleLevel, 1)
                .lt(SysRoleEntity::getIsSysAdmin, 1);
        List<SysRoleEntity> sysRoles = sysRoleService.list(qwRole);
        long roleId = CollectionUtils.isEmpty(sysRoles) ? 7 : sysRoles.get(0).getId();

        String userName = "";
        Map<String, Object> curResult;
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            for (CasUserVo casUserVo : casUserVos) {
                Boolean flag = true;
                // 1.同步用户信息
                String loginName = casUserVo.getLogin_name();
                SysUserEntity userEntity = sysUserService.lambdaQuery().eq(SysUserEntity::getUsername, loginName).one();
                if (userEntity == null) {
                    userEntity = new SysUserEntity();
                    userEntity.setCreateUserId(loginUserId);
                    flag = flag && addCasUser(casUserVo, userEntity, deptId);
                } else {
                    userName = userEntity.getUsername();
                    // 用户名不同，则修改用户名
                    userEntity.setNickname(casUserVo.getUser_name());
                    userEntity.setEmail(casUserVo.getEmail());
                    userEntity.setForeignId(casUserVo.getGx_number());
                    userEntity.setMobile(casUserVo.getTelephone());
                    sysUserService.update(userEntity);

                    curResult = new HashMap<>();
                    curResult.put("login_name", userName);
                    curResult.put("result", true);
                    curResult.put("code", "1");
                    curResult.put("message", "新增用户成功");
                    result.add(curResult);
                    continue;
                }


                // 处理系统角色及项目角色信息
                Long userId = userEntity.getId();
                List<CasUserVo.CASRole> partner_roles = casUserVo.getPartner_roles();
                List<Long> sysRoleIds = new ArrayList<>();
                sysRoleIds.add(roleId);
                Map<Long, Long> subPrjRoleIdMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(partner_roles)) {
                    for (CasUserVo.CASRole casRole : partner_roles) {
                        //
                        String id = casRole.getId();
                        String[] split = id.split("\\^");
                        // 项目角色，id的结构为：项目id^子项目id^角色id
                        if (split.length == 3) {
                            // key为子项目id，value为角色id
                            subPrjRoleIdMap.put(Long.valueOf(split[1]), Long.valueOf(split[2]));
                        }
                        //科研用户角色
                    }
                }
                // 2.同步用户系统角色信息
                flag = flag && saveOrUpdateSysRole(sysRoleIds, userId);
                // 3.同步用户项目角色信息
                flag = flag && saveOrUpdatePrjRole(subPrjRoleIdMap, userId, deptId);
                // 同步用户HIS信息
                SysUserHisEntity sysUserHisEntity = new SysUserHisEntity();
                sysUserHisEntity.setUserId(userEntity.getId());
                sysUserHisEntity.setDeptId(deptId);
                sysUserHisEntity.setDeptCode(deptCode);
                QueryWrapper<SysUserHisEntity> countConditionWrapper = new QueryWrapper<>(sysUserHisEntity);
                int count = sysUserHisService.count(countConditionWrapper);
                if (count == 0) {
                    //新增HIS表记录
                    sysUserHisEntity.setHisForeignId(casUserVo.getGx_number());
                    flag = flag && sysUserHisService.save(sysUserHisEntity);
                } else {
                    //更新HIS记录
                    SysUserHisEntity whereEntity = new SysUserHisEntity();
                    whereEntity.setUserId(userEntity.getId());
                    whereEntity.setDeptId(deptId);
                    //根据条件 whereWrapper 指定要筛选的更新记录
                    QueryWrapper<SysUserHisEntity> whereWrapper = new QueryWrapper<>(whereEntity);
                    sysUserHisEntity.setHisForeignId(casUserVo.getGx_number());
                    flag = flag && sysUserHisService.update(sysUserHisEntity, whereWrapper);
                }
                curResult = new HashMap<>();
                curResult.put("login_name", userName);
                if (flag) {
                    curResult.put("result", true);
                    curResult.put("code", "1");
                    curResult.put("message", "新增用户成功");
                } else {
                    curResult.put("result", false);
                    curResult.put("code", "-1");
                    curResult.put("message", "新增用户失败");
                }
                result.add(curResult);
            }
        } catch (Exception e) {
            curResult = new HashMap<>();
            curResult.put("login_name", userName);
            curResult.put("result", false);
            curResult.put("code", "-1");
            curResult.put("message", e.getMessage());
            result.add(curResult);
        }

        return result;
    }

    /**
     * 批量新增指定用户项目角色
     * @param subPrjRoleIdMap
     * @param userId
     * @return
     */
    private Boolean saveOrUpdatePrjRole(Map<Long, Long> subPrjRoleIdMap, Long userId, Long dept) {
        if (!CollectionUtils.isEmpty(subPrjRoleIdMap)){
            // 先删除项目角色
            // 暂时写死华西二院 todo
//            Long dept = 0L;
            Set<Long> subPrjIds = subPrjRoleIdMap.keySet();
            QueryWrapper<UserSubProjectEntity> qw = new QueryWrapper<UserSubProjectEntity>();
            qw.lambda()
                    .eq(UserSubProjectEntity::getUserId, userId)
                    .eq(UserSubProjectEntity::getProjectDeptId, dept)
                    .in(UserSubProjectEntity::getSubProjectId, subPrjIds);
            if (userSubProjectService.count(qw) > 0){
                userSubProjectService.remove(qw);
            }

            // 再新增项目角色
            List<UserSubProjectEntity> userSubProjectEntities = new ArrayList<>();
            Iterator<Map.Entry<Long, Long>> iterator = subPrjRoleIdMap.entrySet().iterator();
            while (iterator.hasNext()){
                Map.Entry<Long, Long> next = iterator.next();
                UserSubProjectEntity userSubProjectEntity = new UserSubProjectEntity();
                userSubProjectEntity.setUserId(userId);
                userSubProjectEntity.setSubProjectId(next.getKey());
                userSubProjectEntity.setProjectDeptId(dept);
                userSubProjectEntity.setProjRoleId(next.getValue());
                userSubProjectEntities.add(userSubProjectEntity);
            }
            return userSubProjectService.saveBatch(userSubProjectEntities);
        }

        return true;
    }

    /**
     * 批量新增指定用户系统角色
     * @param sysRoleIds
     * @param userId
     * @return
     */
    private Boolean saveOrUpdateSysRole(List<Long> sysRoleIds, Long userId) {
        if (!CollectionUtils.isEmpty(sysRoleIds)){
            // 先删除系统角色
            QueryWrapper<SysUserRoleEntity> qw = new QueryWrapper<SysUserRoleEntity>();
            qw.lambda()
                    .eq(SysUserRoleEntity::getUserId, userId)
                    .in(SysUserRoleEntity::getRoleId, sysRoleIds);
            if (sysUserRoleService.count(qw) > 0){
                sysUserRoleService.remove(qw);
            }

            // 再新增系统角色
            List<SysUserRoleEntity> sysUserRoleEntities = new ArrayList<>();
            for (Long sysRoleId : sysRoleIds) {
                SysUserRoleEntity sysUserRoleEntity = new SysUserRoleEntity();
                sysUserRoleEntity.setUserId(userId);
                sysUserRoleEntity.setRoleId(sysRoleId);
                sysUserRoleEntities.add(sysUserRoleEntity);
            }
            return sysUserRoleService.saveBatch(sysUserRoleEntities);
        }

        return false;
    }

    /**
     * 新增用户信息
     * @param casUserVo
     * @param userEntity
     * @return
     */
    private boolean addCasUser(CasUserVo casUserVo, SysUserEntity userEntity, Long deptId) {
        userEntity.setUsername(casUserVo.getLogin_name());
        userEntity.setDeptId(deptId.toString());
        userEntity.setEmail(casUserVo.getEmail());
        Date date = DateUtils.stringToDate("2099-01-01", DateUtils.DATE_PATTERN);
        userEntity.setExpiredDate(date);
        userEntity.setForeignId(casUserVo.getGx_number());
        userEntity.setMobile(casUserVo.getTelephone());
        userEntity.setNickname(casUserVo.getUser_name());
        userEntity.setStatus(1);
        userEntity.setCreateTime(new Date());
        // 设置初始密码
        String password = "dhccsm_" + casUserVo.getGx_number();
        //sha256加密
        String salt = RandomStringUtils.randomAlphanumeric(20);
        userEntity.setPassword(new Sha256Hash(password, salt).toHex());
        userEntity.setSalt(salt);

        return sysUserService.save(userEntity);
    }

}
