package com.boot.modules.sys.service.impl;

import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.EhCacheUtil;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.sys.dto.PassWordChangeDto;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.MailService;
import com.boot.modules.sys.service.SysUserService;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class MailServiceImpl implements MailService {
//    todo 邮箱验证码服务屏蔽
//    @Resource
//    private JavaMailSender mailSender;

    @Resource
    private SysUserService userService;

    @Value("${spring.mail.username:{null}}")
    private String from;

    /**
     * 用户登录验证码邮箱发送
     *
     * @param wordChangeDto
     * @return
     */
    @Override
    public Boolean sendMail(PassWordChangeDto wordChangeDto) {
        if (StringUtils.isBlank(from)) {
            return true;
        }
        String toAddress = wordChangeDto.getToAddress();
        String username = wordChangeDto.getUsername();

        SysUserEntity user = userService.lambdaQuery().eq(SysUserEntity::getUsername, username).one();
        if (user == null) {
            throw new BusinessException("该账号不存在！");
        }
        String email = user.getEmail();
        if (!toAddress.equals(email)) {
            throw new BusinessException("与账号绑定邮箱不一致，或账号未绑定邮箱，请联系管理员！");
        }
        SimpleMailMessage message = new SimpleMailMessage();
        String verifyCode = RandomStringUtils.random(6, false, true);

        EhCacheUtil.getInstance().put("codeCache", username, verifyCode);


//        String text = "你的邮箱验证码为 " + verifyCode + " 该验证码 10 分钟内有效。为了保障您的账户安全，请勿向他人泄漏验证码信息。";
//        message.setFrom(from);
//        message.setTo(toAddress);
//        message.setSubject("邮箱验证码");
//        message.setText(text);
//        mailSender.send(message);

        return true;

    }
}
