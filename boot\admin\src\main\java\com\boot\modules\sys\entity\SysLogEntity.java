package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 系统日志
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_log")
public class SysLogEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId
	private Long id;
	/**
	 * 用户名
	 */
	private String username;
	/**
	 * 用户操作
	 */
	private String operation;

	/**
	 * 操作类型
	 */
	private Integer operaType;

	/**
	 * 请求方法
	 */
	private String method;
	/**
	 * 请求参数
	 */
	private String params;

	/**
	 * IP地址
	 */
	private String ip;

	/**
	 * url
	 */
	private String url;

	/**
	 * 执行状态 1： 成功 0：失败
	 */
	private Integer status;

	/**
	 * 错误描述
	 */
	private String errorInfo;

	/**
	 * 执行时长(毫秒)
	 */
	private Long time;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

}
