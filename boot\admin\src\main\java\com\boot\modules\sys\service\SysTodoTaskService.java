package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.enums.BusinessEnum;
import com.boot.modules.sys.entity.SysTodoTaskEntity;
import com.boot.modules.sys.vo.SysTodoTaskVo;
import com.boot.modules.sys.vo.TodoTaskListVo;

import java.util.List;
import java.util.Map;

/**
 * 待办任务
 *
 * <AUTHOR>
 */
public interface SysTodoTaskService extends IService<SysTodoTaskEntity> {
    /**
     * 查询代办任务
     */
    List<SysTodoTaskEntity> list(Map<String, Object> params);

    List<SysTodoTaskEntity> getSysTodoByUser(Long userId);

    /**
     * 代办业务以及代办业务数量返回前端
     */
    List<SysTodoTaskVo> getTaskAndCount();

//    /**
//     * 获取待办业务列表，返回每种业务的code和业务解释和每种待办业务的数量，和具体的待办业务
//     *
//     * @return
//     */
//    List<TodoTaskListVo> getTodoTaskList(Map<String, Object> params);


    /**
     * 新增代办任务
     */
    boolean insert(SysTodoTaskEntity sysTodoTaskEntity);

    boolean add(BusinessEnum businessEnum, Long businessId);

    /**
     * 修改代办任务
     */
    boolean update(SysTodoTaskEntity sysTodoTaskEntity);

    /**
     * 删除代办任务
     */
    boolean delete(Long[] ids);
}
