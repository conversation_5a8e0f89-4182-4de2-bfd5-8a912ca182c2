package com.boot.modules.sys.controller;


import com.boot.commons.annotation.Login;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.validator.Assert;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.project.vo.ImportVo;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.form.PasswordForm;
import com.boot.modules.sys.service.*;
import com.boot.modules.sys.vo.UserHisForeignVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.ArrayUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统用户
 *
 * <AUTHOR>
 */
@Api(tags = "系统用户")
@RestController
@RequestMapping("/sys/user")
public class SysUserController extends AbstractController {
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysDeptService deptService;
    @Resource
    private SysUserHisService userHisService;
    @Resource
    private SysRoleService roleService;
    /**
     * 所有用户列表
     * get sys/user/list
     */
    @ApiOperation(
            value = "用户列表",
            notes = "所有用户列表")
    @Login
    @GetMapping("/list")
    @RequiresPermissions("sys:user:list")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysUserService.queryPage(params);

        return R.success(page);
    }

    /**
     * 获取登录的用户信息
     */
    @ApiOperation(
            value = "获取登录用户信息",
            notes = "获取登录用户信息")
    @GetMapping("/info")
    public Result info() {
        SysUserEntity user = getUser();
        if (!this.isSuperAdmin()) {
            user = sysUserService.queryByUserName(user.getUsername());
        }
        user.setEnableWechat(BootAdminProperties.enableWechat);

        user.setEnableExportAudit(BootAdminProperties.enableExportAudit);
        user.setEnableSample(BootAdminProperties.enableSample);

        return R.success(user);
    }

    @ApiOperation(
            value = "分页获取指定机构中的用户",
            notes = "分页获取指定机构中的用户")
    @Login
    @RequiresPermissions("sys:user:all")
    @GetMapping("/all")
    public Result listByDeptIds(@RequestParam Map<String, Object> params,
                                @RequestBody Long[] deptIds) {
        PageUtils page = sysUserService.queryByDeptIds(Arrays.asList(deptIds), params);

        return R.success(page);

    }

    /**
     * 修改登录用户密码
     */
    @ApiOperation(value = "修改密码")
    @SysLog("修改密码")
    @PutMapping("/password")
    public Result password(@RequestBody PasswordForm form) {
        Assert.isBlank(form.getNewPassword(), "新密码不为能空");

        Long userId = null;
        if (form.getUserId() != null) {
            userId = form.getUserId();
        } else {
            userId = getUserId();
        }
        //更新密码
        sysUserService.updatePassword(userId, form.getPassword(), form.getNewPassword());
        return R.success("修改成功");
    }


    /**
     * 用户信息
     * get sys/user/1
     */
    @ApiOperation(
            value = "获取指定ID用户信息",
            notes = "获取指定ID用户信息")
    @GetMapping("/{userId}")
    @RequiresPermissions("sys:user:info")
    public Result info(@PathVariable("userId") Long userId) {
        SysUserEntity user = sysUserService.getById(userId);

        //获取用户所属的角色列表
        List<Long> roleIdList = sysUserRoleService.queryRoleIdList(userId);
        user.setRoleIdList(roleIdList);

        // todo 获取用户所属科室信息
        List<SysDeptEntity> dept = deptService.listByIds(Arrays.asList(user.getDeptId().split(",")));
        List<String> deptNameList = dept.stream().map(SysDeptEntity::getName).collect(Collectors.toList());

        if (dept != null) {
            user.setDeptName(deptNameList.toString());
        }
        List<UserHisForeignVo> userHisForeignVos = userHisService.selectByQuery(userId);
        user.setUserHisList(userHisForeignVos.toArray(new UserHisForeignVo[userHisForeignVos.size()]));

        return R.success(user);
    }


    /**
     * 保存用户
     * type: post
     * url: sys/user/
     */
    @ApiOperation(
            value = "新增用户",
            notes = "新增用户")
    @SysLog("保存用户")
    @PostMapping()
    @RequiresPermissions("sys:user:save")
    public Result save(@RequestBody SysUserEntity user) {
        ValidatorUtils.validateEntity(user, AddGroup.class);
        user.setCreateUserId(getUserId());

        sysUserService.saveUser(user);

        return R.success();
    }

    @ApiOperation(
            value = "修改用户",
            notes = "修改用户")
    @SysLog("修改用户")
    @PutMapping()
    @RequiresPermissions("sys:user:update")
    public Result update(@RequestBody SysUserEntity user) {
        ValidatorUtils.validateEntity(user, UpdateGroup.class);

        sysUserService.update(user);

        return R.success();
    }

    @ApiOperation(
            value = "删除用户",
            notes = "删除用户")
    @SysLog("删除用户")
    @DeleteMapping()
    @RequiresPermissions("sys:user:delete")
    public Result delete(@RequestBody Long[] userIds) {
        if (ArrayUtils.contains(userIds, 1L)) {
            return R.fail("系统管理员不能删除");
        }

        if (ArrayUtils.contains(userIds, getUserId())) {
            return R.fail("当前用户不能删除");
        }

        sysUserService.removeByIds(Arrays.asList(userIds));

        return R.success();
    }

    @ApiOperation(value = "用户excel导入", notes = "用户excel导入")
    @PostMapping("/excel/import")
    @RequiresPermissions("sys:user:excelimport")
    public Result excelImport(@RequestParam("file") MultipartFile file) {

        boolean res = true;
        if (file == null) {
            throw new BusinessException("上传的文件为空");
        }
        Long userId = getUserId();
        res = sysUserService.excelImport(file, userId);

        return res ? R.success("导入成功") : R.fail("有重复的用户名，或者excel格式错误，请检查！");
    }

    @ApiOperation(value = "批量添加用户excel模板下载", notes = "批量添加用户excel模板下载")
    @PostMapping("/excel/download")
    @RequiresPermissions("sys:user:exceldownload")
    public Result templateDowload(HttpServletResponse response) throws NoSuchFieldException {
        String fileName = "usertemplate.xlsx";
        List<List<String>> heads = new ArrayList<>();

        // 设置一级表头信息
        heads.add(Collections.singletonList("用户名"));
        heads.add(Collections.singletonList("昵称"));
        heads.add(Collections.singletonList("密码"));
        heads.add(Collections.singletonList("所属机构ID（多个医疗机构用英文逗号分割）"));
        heads.add(Collections.singletonList("手机号"));
        heads.add(Collections.singletonList("角色ID"));
        heads.add(Collections.singletonList("过期时间"));
        heads.add(Collections.singletonList("HIS工号"));
        heads.add(Collections.singletonList("导入失败信息"));

        // 设置sheet2一级表头信息
        List<List<String>> heads1 = new ArrayList<>();
        heads1.add(Collections.singletonList("中心ID"));
        heads1.add(Collections.singletonList("中心名称"));
        //获取所有科室信息
        List<SysDeptEntity> sysDeptEntityList = deptService.queryListShowResearchCenter();
        List<ImportVo> deptVoList = new ArrayList<>();
        //构造返回的结构
        for (SysDeptEntity sysDeptEntity : sysDeptEntityList) {
            ImportVo deptVo = new ImportVo();
            deptVo.setFirst(sysDeptEntity.getId());
            deptVo.setSecond(sysDeptEntity.getName());
            deptVoList.add(deptVo);
        }

        // 设置sheet3一级表头信息
        List<List<String>> heads2 = new ArrayList<>();
        heads2.add(Collections.singletonList("角色ID"));
        heads2.add(Collections.singletonList("角色名称"));
        //获取所有角色信息
        List<SysRoleEntity> sysRoleEntityList = roleService.list();
        List<ImportVo> roleVoList = new ArrayList<>();
        //构造返回的结构
        for (SysRoleEntity sysRoleEntity : sysRoleEntityList) {
            ImportVo roleVo = new ImportVo();
            roleVo.setFirst(sysRoleEntity.getId());
            roleVo.setSecond(sysRoleEntity.getRoleName());
            roleVoList.add(roleVo);
        }

        // 下载用户批量导入excel模板
        Map<String, List<List<String>>> headList = new LinkedHashMap<>();
        headList.put("导入用户", heads);
        headList.put("中心信息", heads1);
        headList.put("角色信息", heads2);
        Map<String, List<ImportVo>> dataList = new LinkedHashMap<>();
        dataList.put("中心信息", deptVoList);
        dataList.put("角色信息", roleVoList);
        ExcelUtils.wirteExcel(response, fileName, headList, dataList);

        return R.success("下载成功");
    }

    @ApiOperation(value = "获取水印信息", notes = "获取水印信息")
    @PostMapping("/getWatermark")
    public Result getWatermark() {
        if (BootAdminProperties.enableWatermark) {
            return sysUserService.getWatermark(getUser());
        } else {
            return R.success();
        }
    }

    @ApiOperation(value = "同步用户", notes = "同步用户")
    @PostMapping("/asynUser")
    public Result asynUser() {
        sysUserService.asynUser();
        return R.success();
    }
}
