package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.mobile.dto.MedReportDto;
import com.boot.modules.mobile.dto.ProjectReportDto;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.project.vo.SubProjectInfoVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc : 项目dao
 * @create 2021-02-23
 */
@Mapper
@CacheNamespace
public interface ProjectDao extends BaseMapper<ProjectEntity> {

    /**
     * 分页查询
     *
     * @param pageFilter
     * @param qw
     * @return
     */
    IPage<ProjectVo> queryPage(IPage<ProjectVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<ProjectVo> qw);

    IPage<ProjectVo> pageByDept(IPage<ProjectVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<ProjectVo> qw);

    List<ProjectVo> allByDept(@Param(Constants.WRAPPER) QueryWrapper<ProjectVo> qw);

    /**
     * 按条件查询
     *
     * @param qw
     * @return
     */
    List<ProjectVo> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<ProjectVo> qw);

    List<SubProjectInfoVo> getByUser(@Param(Constants.WRAPPER) QueryWrapper<SubProjectInfoVo> qw);

    List<ProjectVo> getInfoByQuery(@Param(Constants.WRAPPER) QueryWrapper<ProjectEntity> qw);

    /**
     * 查有入组病例的项目ID
     * @param qw
     * @return
     */
    List<Long> getAddPatProjectIdList(@Param(Constants.WRAPPER) QueryWrapper<InboundProcessEntity> qw);

    List<ProjectReportDto> getProjectInfo(@Param(Constants.WRAPPER) QueryWrapper<ProjectReportDto> qw);

    /**
     * 按机构计算、排序
     * @param qw
     * @return
     */
    List<MedReportDto> groupByMedId(@Param(Constants.WRAPPER) QueryWrapper<MedReportDto> qw);
}
