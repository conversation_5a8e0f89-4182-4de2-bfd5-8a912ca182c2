package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.Past;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * 菜单管理
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_menu")
public class SysMenuEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @TableId
    private Long id;

    /**
     * 父菜单ID，一级菜单为0
     */
    private Long pid;

    /**
     * 父菜单名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 菜单名称
     */
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "名称不能包含特殊字符", groups = {AddGroup.class, UpdateGroup.class})
    private String name;

    /**
     * 菜单URL
     */
    private String url;

    /**
     * 授权(多个用逗号分隔，如：user:list,user:create)
     */
    private String permissions;

    /**
     * 类型     0：目录   1：菜单   2：按钮
     */
    private Integer type;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 是否启用， 0: 不启用， 1：启用，默认启用
     */
    private Integer enabled;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 应用id
     */
    private Long appId;

    /**
     * ztree属性
     */
    @TableField(exist = false)
    private Boolean open;

    @TableField(exist = false)
    private Boolean checked;

    @TableField(exist = false)
    private Integer level;

    @TableField(exist = false)
    private List<SysMenuEntity> children;

    /**
     * 角色id
     */
    @TableField(exist = false)
    private Long roleId;
}
