package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.excel.model.ReadExcelListener;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.WordToPinYin;
import com.boot.modules.sys.dao.SysDictCateDao;
import com.boot.modules.sys.entity.SysDictCateEntity;
import com.boot.modules.sys.entity.SysDictItemEntity;
import com.boot.modules.sys.service.SysDictCateService;
import com.boot.modules.sys.service.SysDictItemService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @desc :
 * @create 2021-02-25
 */
@Service
public class SysDictCateServiceImpl extends ServiceImpl<SysDictCateDao, SysDictCateEntity> implements SysDictCateService {

    private final static String CODE_PATTERN = "^[a-zA-Z0-9:_@|^%&',;=?$]*$";

    @Resource
    private SysDictItemService dictItemService;

    /**
     * 分页查询字典分类
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        String dictCateName = (String) params.get("dictCateName");
        QueryWrapper<SysDictCateEntity> queryWrapper = MapUtils.getWrapperByParams(params, "sort", "rp_sys_dict_cate", SysDictCateEntity.class);
        queryWrapper
                .lambda()
                .like(StringUtils.isNotBlank(dictCateName), SysDictCateEntity::getCateName, dictCateName);
        IPage<SysDictCateEntity> page = this.page(
                new Query<SysDictCateEntity>().getPage(params),
                queryWrapper
        );

        return new PageUtils(page);
    }


    /**
     * 根据分类id，查分类信息，并返回此分类对应的所有字典信息
     *
     * @param id
     * @return
     */
    @Override
    public SysDictCateEntity selectById(Long id) {

        SysDictCateEntity dictCate = getById(id);
        //查此分类下的字典项
        if (dictCate != null) {
            List<SysDictItemEntity> dictItemList = dictItemService.list(
                    new QueryWrapper<SysDictItemEntity>()
                            .lambda().eq(SysDictItemEntity::getDictCateId, id)
            );
            dictCate.setDictItemList(dictItemList);
        }

        return dictCate;
    }

    /**
     * 批量删除字典分类，并关联删除此分类下的字典项
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(List<Long> ids) {
        //删除字典分类
        int dictCateRemove = baseMapper.deleteBatchIds(ids);

        LambdaQueryWrapper<SysDictItemEntity> dictItemQw = new QueryWrapper<SysDictItemEntity>()
                .lambda().in(SysDictItemEntity::getDictCateId, ids);
        //删除相关字典项
        boolean dictItemRemove = dictItemService.remove(dictItemQw);

        return dictCateRemove >= 0 && dictItemRemove;
    }

    /**
     * 字典分类
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean excelImport(MultipartFile file) {
        try {
            ReadExcelListener readExcelListener = new ReadExcelListener(SysDictCateEntity.class);

            List<SysDictCateEntity> list = ExcelUtils.readExcel(file, SysDictCateEntity.class, readExcelListener);

            for (SysDictCateEntity cate : list) {
                // 验证编码格式
                boolean inspect = Pattern.matches(CODE_PATTERN, cate.getCateCode());

                if (!inspect) {
                    throw new BusinessException("code只能包含英文字母、数字、_@|^$&',;=?这些字符");
                }

                if (cate.getSort() == null) {
                    cate.setSort(1);
                }
            }

            return this.saveBatch(list);
        } catch (
                Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }
}
