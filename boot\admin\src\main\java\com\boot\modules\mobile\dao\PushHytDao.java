package com.boot.modules.mobile.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.modules.mobile.dto.PushHytDto;
import com.boot.modules.mobile.entity.PushHytEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface PushHytDao extends BaseMapper<PushHytEntity> {
    /**
     * 获取分页数据
     * @param pageFilter 分页参数
     * @param queryWrapper 查询条件
     * @return
     */
    Page<PushHytDto> getPage(IPage<PushHytDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<PushHytDto> queryWrapper);


    PushHytDto getPatientById(Long id);
}
