package com.boot.modules.sys.controller;

import com.alibaba.fastjson.JSONObject;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.SystemInfoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * 系统相关方法
 */
@Log4j2
@Api(tags = "系统服务")
@RestController
@RequestMapping()
public class SystemController extends AbstractController {


    /**
     * 获取系统服务器信息
     */
    @ApiOperation(
            value = "获取服务器信息",
            notes = "获取服务器信息")
    @GetMapping("/sys/system")
    @RequiresPermissions("sys:system:info")
    public Result getSystemInfo() throws UnknownHostException {
        JSONObject object = SystemInfoUtils.getInfo();

        return R.success(object);
    }

}
