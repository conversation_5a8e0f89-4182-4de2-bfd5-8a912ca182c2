package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.mobile.vo.SubProjectMobileVO;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.project.vo.ProjectInfoVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc : 子项目 dao
 * @create 2021-03-18
 */
@Mapper
@CacheNamespace
public interface SubProjectDao extends BaseMapper<SubProjectEntity> {
    /**
     * 查询当前用户在指定项目的所有子项目
     *
     * @param qw
     * @return
     */
    List<SubProjectVo> getByProjectId(@Param(Constants.WRAPPER) QueryWrapper<SubProjectVo> qw);


    /**
     * 获取子项目明细
     * @param qw
     * @return
     */
    List<SubProjectVo> getDetailsByProjectId(@Param(Constants.WRAPPER) QueryWrapper<SubProjectVo> qw);

    /**
     * 获取所有的zi项目
     *
     * @param qw
     * @return
     */
    List<ProjectInfoVo> getAllProjectInfo(@Param(Constants.WRAPPER) QueryWrapper<ProjectInfoVo> qw);


    /**
     * 获取子项目信息、用户信息及角色信息
     * @param qw
     * @return
     */
    List<SubProjectVo> getUserSubPrj(@Param(Constants.WRAPPER) QueryWrapper<SubProjectVo> qw);

    /**
     * 获取角色为项目管理员的子项目信息
     * @param qw
     * @return
     */
    List<SubProjectVo> getSubProjManage(@Param(Constants.WRAPPER) QueryWrapper<SubProjectVo> qw);

    /**
     * 获取子项目的CRC用户信息
     * @param qw
     * @return
     */
    List<SubProjectVo> getSubProjCRC(@Param(Constants.WRAPPER) QueryWrapper<SubProjectVo> qw);

    /**
     * 获取子项目列表
     * @param qw
     * @return
     */
    IPage<SubProjectMobileVO> getUserSubListByQuery(IPage<Object> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<Object> qw);

    /**
     * 获取子项目列表
     * @param qw
     * @return
     */
    IPage<SubProjectMobileVO> getListByQuery(IPage<Object> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<Object> qw);
}
