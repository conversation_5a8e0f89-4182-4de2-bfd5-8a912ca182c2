package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysApplicationEntity;
import com.boot.modules.sys.service.SysApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用管理
 *
 * <AUTHOR>
 */
@Api(tags = "应用管理")
@RestController
@RequestMapping("/sys/app")
public class SysApplicationController extends AbstractController {

    @Resource
    private SysApplicationService sysApplicationService;

    /**
     * 应用列表
     */
    @GetMapping("/list")
    @RequiresPermissions("sys:app:list")
    @ApiOperation("获取应用列表")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysApplicationService.queryPage(params);

        return R.success(page);
    }

    /**
     * 应用信息
     */
    @GetMapping("/{id}")
    @RequiresPermissions("sys:app:info")
    @ApiOperation("应用信息")
    public Result info(@PathVariable("id") Long id) {
        SysApplicationEntity app = sysApplicationService.getById(id);

        return R.success(app);
    }

    /**
     * 保存应用
     */
    @SysLog("保存应用")
    @PostMapping()
    @RequiresPermissions("sys:app:save")
    @ApiOperation("保存应用")
    public Result save(@RequestBody SysApplicationEntity app) {
        ValidatorUtils.validateEntity(app, AddGroup.class);

        List<SysApplicationEntity> apps = sysApplicationService.list(
                new QueryWrapper<SysApplicationEntity>().lambda().eq(SysApplicationEntity::getIsDefault, 1));
        if (!CollectionUtils.isEmpty(apps) && app.getIsDefault() == 1) {
            return R.fail("只能设置一个默认应用");
        }

        sysApplicationService.save(app);

        return R.success();
    }

    /**
     * 修改应用
     */
    @SysLog("修改应用")
    @PutMapping()
    @RequiresPermissions("sys:app:update")
    @ApiOperation("修改应用")
    public Result update(@RequestBody SysApplicationEntity app) {
        ValidatorUtils.validateEntity(app, UpdateGroup.class);

        List<SysApplicationEntity> apps = sysApplicationService.list(
                new QueryWrapper<SysApplicationEntity>().lambda().eq(SysApplicationEntity::getIsDefault, 1));
        if (!CollectionUtils.isEmpty(apps)) {
            List<Long> appIds = apps.stream().map(SysApplicationEntity::getId).collect(Collectors.toList());
            if (!appIds.contains(app.getId()) && app.getIsDefault() == 1) {
                return R.fail("只能设置一个默认应用");
            }
        }
        sysApplicationService.updateById(app);

        return R.success();
    }

    /**
     * 删除应用
     */
    @SysLog("删除应用")
    @DeleteMapping()
    @RequiresPermissions("sys:app:delete")
    @ApiOperation("删除应用")
    public Result delete(@RequestBody Long[] roleIds) {
        sysApplicationService.removeByIds(Arrays.asList(roleIds));

        return R.success();
    }
}
