package com.boot.commons.constant;

/**
 * <AUTHOR>
 * @desc : 华西接口需要的常量值
 * @create 2021-11-17
 */
public class HxConst {
    /**
     * 基础配置
     */
    /**
     * 成功状态码
     */
    public final static int RESPONSE_SUCCESS = 200;
    /**
     * 失败状态码
     */
    public final static int RESPONSE_FAIL = 500;

    // 是否走华西OA导出审核流程
    public final static String IS_OA_EXPORT = "isOaExport";

    // 导出跳转OA传参配置
    // 跳转导出申请页面url
    public final static String EXPORT_APPLY_URL = "exportApplyUrl";
    // 导出任务ID
    public final static String TASK_ID = "taskId";
    public final static String EXPORT_TASK_ID = "exportTaskId";//test-oa "exportTaskId";
    // 来源， 1：柯林 2：东华病种库
    public final static String EXPORT_KELIN = "kelin";

    // 导出获取用户基本信返回字段参数配置
    public final static String EXPORT_USER_NAME = "userName";
    // 工号
    public final static String EXPORT_JOB_NUMBER = "jobNumber";
    // 搜索表达式
    public final static String EXPORT_EXPRESSION = "expression";
    // 导出的字段
    public final static String EXPORT_EXPORT_FIELD = "exportField";
    // 状态， 1:取文件，0：不取文件
    public final static String EXPORT_STATUS = "status";

    // 文件生成要传的数据、关闭任务的接口传参配置
    // 导出关闭任务接口路径url
    public final static String UPLOAD_EXPORT_INFO_URL = "uploadExportInfoUrl";
    // 是否关闭任务  0/1
    public final static String EXPORT_IS_CLOSE = "isClose";
    // 文件位置
    public final static String EXPORT_PATH = "exportPath";
    // 导出敏感字段
    public final static String EXPORT_SENSITIVE_FIELD = "exportSensitiveField";
    // 导出文件总记录数
    public final static String EXPORT_FILE_ROW_COUNT = "fileRowCount";
    // 导出文件名称
    public final static String EXPORT_FILE_NAME = "fileName";
    // 就诊人数
    public final static String EXPORT_VISIT_COUNT = "visitCount";
    // 就诊人次
    public final static String EXPORT_VISIT_PERSON = "visitPerson";
    // 隐私数据
    public final static String EXPORT_PRIVACY_FIELD = "exportPrivacyField";

    //平台服務地址
    public final static String PT_URL = "pt_url";
    //平台水印服務地址
    public final static String WATER_MARK_URL = "water_mark_url";
    // 讯飞AI电话相关配置
    // 企业的客户端id 名字
    public final static String AI_CLIENT_ID_NAME = "client_id";
    // 企业的客户端密钥 名字
    public final static String AI_CLIENT_SECRET_NAME = "client_secret";
    // 企业类型
    public final static String AI_GRANT_TYPE_NAME = "grant_type";
    // 企业的客户端id
    public final static String AI_CLIENT_ID = "ai_client_id";
    // 企业的客户端密钥
    public final static String AI_CLIENT_SECRET = "ai_client_secret";
    // 企业类型
    public final static String AI_GRANT_TYPE = "ai_grant_type";
    // 获取讯飞AI令牌请求url
    public final static String AI_ACCESS_TOKEN_URL = "ai_access_token_url";
    // 执行电话随访url
    public final static String AI_OPEN_CALL_SPEECH_URL = "open_call_speech_url";
    // 居民对象
    public final static String AI_DWELLERS = "dwellers";
    // 电话模版ID
    public final static String AI_SPEECH_ID = "speechId";
    // 呼叫类型 时间类型(1：即呼，2：距上次时间段，3：指定日期)
    public final static String AI_Type = "type";
    // 外呼当天起始时间
    public final static String AI_Start = "start";
    // 电话模版变量内容,采用JSON String 提交
    public final static String AI_VARS = "vars";
    // 访问令牌
    public final static String AI_ACCESS_TOKEN = "access_token";
    // 讯飞外呼回推参数名配置
    // 第三方ID，可用来做数据对应
    public final static String AI_RELATION_ID = "relationId";
    /**
     * 任务状态枚举值说明：0：成 2：通话中 3：无法接通 4：关机 5：用户正忙 6：空号 7：号码错误 9：停机 15：客户接听后并主动挂机 20：用户未接
     * 22：来电提醒 22：转来电提醒 23：呼入限制 26：网络故障 28：线路故障 30：呼叫失败 300-399 ：任务执行失败
     */
    public final static String AI_RESULT_CODE = "resultCode";
    /**
     * 接通状态枚举值说明：1：正常回答 2：自动留言 3：接听不便 4：居民已死亡 5：家属不能代答 6：不愿配合 7：号码错误 8：中断
     */
    public final static String AI_END_NODE = "endNode";
    // 电话任务状态描述
    public final static String AI_CALL_RESULT = "callResult";
    // 记录id
    public final static String AI_RECORD_ID = "recordId";
    //录音路径
    public final static String AI_VOICE_URL = "recordUrl";
    // 电话拨打时间
    public final static String AI_CALL_TIME = "callTime";
    // 拨打次数
    public final static String AI_ACTUAL_CALL_COUNT = "actualCallCount";
    // 通话时长（秒）
    public final static String AI_CALL_TIME_LENGTH = "callTimeLength";
    // 接通状态说明
    public final static String AI_CENNECT_STATUS = "connectStatus";
    // 居民姓名
    public final static String AI_DWELLER_NAME = "dwellerName";
    // 智能随访状态
    public static final String AI_STATUS = "AIStatus";

    // AES解密密钥
    public final static String AI_ASE_KEY = "aseKey";

    // 平台访问令牌
    public final static String PT_ACCESS_KEY = "pt_access_key";
    // 平台访问密钥
    public final static String PT_SECRET_KEY = "pt_secret_key";

    // 平台访问令牌
    public final static String HIS_ACCESS_KEY = "his_access_key";
    // 平台访问密钥
    public final static String HIS_SECRET_KEY = "his_secret_key";

    // 联影GCP图片下载相关
    // 通知下载指定检查号
    public final static String GCP_URL = "gcp_url";

    // GCP相关配置
    // 企业的客户端id 名字
    public final static String GCP_CLIENT_ID_NAME = "client_id";
    // 企业的客户端密钥 名字
    public final static String GCP_CLIENT_SECRET_NAME = "client_secret";
    // 企业类型
    public final static String GCP_GRANT_TYPE_NAME = "grant_type";
    // 企业的客户端id
    public final static String GCP_CLIENT_ID = "gcp_client_id";
    // 企业的客户端密钥
    public final static String GCP_CLIENT_SECRET = "gcp_client_secret";
    // 企业类型
    public final static String GCP_GRANT_TYPE = "gcp_grant_type";
    // 获取GCP令牌请求url
    public final static String GCP_ACCESS_TOKEN_URL = "gcp_access_token_url";

    // RDR默认字段配置
    // 就诊表
    public final static String RDR_ADM_TABLE = "visit_info";
    // 病人唯一字段
    public final static String RDR_EMPIID_FIELD = "empiid";

    // 360视图
    public final static String VIEW_FOR_360_URL = "viewFor360Url";

    // 检索入组
    //柯林外部接口url
//    public final static String KELIN_EXTERNAL_URL = "kelin_external_url";
    // 柯林mysq数据库url
    public final static String KELIN_DATABASE_URL = "kelin_database_url";
    //柯林数据库用户名
    public final static String KELIN_DATABASE_USERNAME = "kelin_database_username";
    //柯林数据库密码
    public final static String KELIN_DATABASE_PASSWORD = "kelin_database_name";

    /**
     * 病人基本信息表
     */
    public final static String KELIN_PAT_TABLE = "pers_basicinfo";
    // 就诊表
    public final static String KELIN_ADM_TABLE = "visit_info";
    // 超声心动
    public final static String KELIN_EXAMREPORT_TABLE = "report_exammeasuresub";

    // 检查报告表就诊ID字段
    public final static String KELIN_EXAMREPORT_VISIT_ID_FIELD = "report_exammeasuresub_visitid";
    // 检查报告表报告号字段
    public final static String KELIN_EXAMREPORT_APPLYNO_FIELD = "report_exammeasuresub_applyno";
    // 检查报告表检查名称字段
    public final static String KELIN_EXAMREPORT_NAME_FIELD = "report_exammeasuresub_examsitemcnname";

    // 检验报告表
    public final static String KELIN_LAB_TABLE = "report_labmeasuresub";
    // 检验报告表报告号字段
    public final static String KELIN_LAB_APPLYNO_FIELD = "report_labmeasuresub_applyno";

    // 诊断表
    public final static String KELIN_DIAG_TABLE = "diaginfo";
    // 诊断id字段
    public final static String KELIN_DIAG_SERIALNO_FIELD = "diaginfo_serialno";

    // 医嘱表
    public final static String KELIN_ORDER_TABLE = "order_order";
    // 医嘱表唯一id字段
    public final static String KELIN_ORDER_SERIALNO_FIELD = "order_order_serialno";

    // 医嘱表病人唯一id字段
    public final static String KELIN_ORDER_EMPIID_FIELD = "order_order_empiid";

    //医嘱表医嘱时间
    public final static String KELIN_ORDER_ORDER_TIME_FIELD = "order_order_orderdttm";

    // 报告
    public final static String KELIN_MTREPORT_TABLE = "mt_report_main";

    // 护理病历
    public final static String KELIN_NURSINGDET_TABLE = "case_nursingdet";

    // 护理病历创建时间字段
    public final static String KELIN_NURSINGDET_CREATE_TIME_FIELD = "case_nursingdet_createdttm";

    // 报告号字段
    public final static String KELIN_MTREPORT_REPORTNO_FIELD = "report_main_reportno";

    // 检查报告表就诊ID字段
    public final static String KELIN_MTREPORT_VISIT_ID_FIELD = "report_main_visitid";

    // 检查对照医嘱表ID字段
    public final static String KELIN_MTREPORT_APPLY_ID_FIELD = "report_main_applyid";

    // 检查报告表就诊号字段
    public final static String KELIN_MTREPORT_VISIT_NO_FIELD = "report_main_visitno";

    // 检查报告表检查时间字段
    public final static String KELIN_MTREPORT_EXAM_TIME_FIELD = "report_main_examenddttm";

    // 检查影像编号
    public final static String KELIN_MTREPORT_IMAGENO_FIELD = "report_main_imageno";
    //    // 检查报告ID
//    public final static String KELIN_MTREPORT_REPORTID_FIELD = "report_main_reportid";
    // 检查名称
    public final static String KELIN_MTREPORT_CLASSNAME_FIELD = "report_main_classname";

    // 具体检查名称
    public final static String KELIN_MTREPORT_APPLYITEMNAME_FIELD = "report_main_applyitemname";

    // 检验明细表
    public final static String KELIN_LAB_REPORT_TABLE = "mt_lab_report";

//
//    // 手麻手术
//    public final static String KELIN_OPSN_TABLE = "opsn_opsncolumn";
//    // 手麻手术id字段
//    public final static String KELIN_OPSN_SEQNO_FIELD = "opsn_opsevent_seqno";

    // 就诊表就诊号字段
    public final static String KELIN_VISIT_ID_FIELD = "visit_info_visitid";
    // 就诊表empiid字段
    public final static String KELIN_EMPI_ID_FIELD = "visit_info_empiid";

    // 就诊表归一前就诊号字段
    public final static String KELIN_VISIT_SRC_ID_FIELD = "visit_info_exstr3";
    // 就诊表就诊时间字段
    public final static String KELIN_VISIT_TIME_FIELD = "visit_info_visitdttm";
    // 就诊表主治医生字段
    public final static String KELIN_ATTEND_DOCTOR_FIELD = "visit_info_attendingdoctname";
    // 就诊表就诊类型字段
    public final static String KELIN_VISIT_TYPE_FIELD = "visit_info_visittype";
    // 就诊表就诊科室字段-当前科室
    public final static String KELIN_VISIT_DEPT_FIELD = "visit_info_currentdeptname";
    // 就诊表就诊科室字段-出院科室
    public final static String KELIN_VISIT_OUT_DEPT_FIELD = "visit_info_outhospdept";
    // 就诊表就诊科室字段-入院科室
    public final static String KELIN_VISIT_IN_DEPT_FIELD = "visit_info_inhospdept";
    // 就诊表医疗机构名称字段
    public final static String KELIN_VISIT_MED_NAME_FIELD = "visit_info_medorgname";
    // 就诊表医疗机构编码字段
    public final static String KELIN_VISIT_MED_CODE_FIELD = "visit_info_medorgcode";
    // 就诊表医疗访问状态
    public final static String KELIN_VISIT_MED_STATUS_NAME = "visit_info_statusname";
    // 就诊表医疗访问编码
    public final static String KELIN_VISIT_MED_STATUS_CODE = "visit_info_statuscode";
    // 就诊表就诊时间字段
    public final static String KELIN_VISIT_PERSNO_FIELD = "visit_info_persno";

    // 病人表的empiid字段名字
    public final static String KELIN_PAT_EMPI_ID_FIELD = "pers_basicinfo_empiid";
    // 病人表登记号
    public final static String KELIN_PAT_REGNO_FIELD = "pers_basicinfo_persno";
    // 柯林医疗机构字段名称
    public final static String KELIN_PAT_MEDORG_CODE_FIELD = "pers_basicinfo_medorgcode";
    // 出生日期
    public final static String KELIN_PAT_BIRTHDAY_FIELD = "pers_basicinfo_birthday";
    // 性别
    public final static String KELIN_PAT_GENDER_FIELD = "pers_basicinfo_sexname";
    // 身份证号
    public final static String KELIN_PAT_ID_CARD_FIELD = "pers_basicinfo_certno";
    // 姓名
    public final static String KELIN_PAT_NAME_FIELD = "pers_basicinfo_persname";
    // 手机号码
    public final static String KELIN_PAT_MOBILE_NO_FIELD = "pers_basicinfo_phoneno";
    //民族
    public final static String KELIN_PAT_NATION_FIELD = "pers_basicinfo_nationname";

    //rdr同步empiid接口配置
    public final static String RDR_SYNC_DATA_URL = "rdr_sync_data_url";

    //获取手机验证码接口配置
    public final static String PHONE_VERIFY_CODE_URL = "phone_verify_code_url";

    /**
     * 柯林 电子病历表的表名
     */
    public final static String KELIN_EMR_TABLE_NAME = "case_cdadet";

    public final static String KELIN_EMR_VISIT_ID = "case_cdadet_visitid";

    public final static String KELIN_EMR_DATASETCODE = "case_cdadet_datasetcode";

    public final static String KELIN_EMR_DATASETNAME = "case_cdadet_datasetname";

    public final static String KELIN_EMR_DATASETVALUE = "case_cdadet_datasetvalue";

    public final static String KELIN_EMR_MEDORGCODE = "case_cdadet_medorgcode";

    public final static String KELIN_EMR_MEDORGNAME = "case_cdadet_medorgname";

    /**
     * 柯林 电子病历表 datasetcode 能取的值
     */
    public final static String KELIN_EMR_DATASETCODE_QUERY = "V001_V002_I0169,V001_V002_I0170,V001_V002_I0171,V001_V002_I0172,V001_V002_I0173,V001_V002_I0174,V001_V002_I0175,V001_V002_I0176,V001_V002_D0153,V001_V002_D0154,V001_V002_D0155,V001_V002_D0156,V001_V002_D0157,V001_V002_D0158,V001_V002_D0159,V001_V002_D0160,V001_V002_I0161,V001_V002_I0162,V001_V002_I0163,V001_V002_I0164,V001_V002_I0165,V001_V002_I0166,V001_V002_I0167,V001_V002_I0168,V001_V002_I0209,V001_V002_I0210,V001_V002_I0211,V001_V002_I0212,V001_V002_I0213,V001_V002_I0214,V001_V002_I0215,V001_V002_I0216";

    /**
     * 通过登记号与医疗机构查询RDR的登记号批量上限
     */
    public final static int KELIN_PATIENT_FETCH_SIZE = 10000;

    /**
     * 通过Empid查就诊，Empid批量上限
     */
    public final static int KELIN_ADM_FETCH_SIZE = 10000;

    /**
     * 柯林接口页宽
     */
    public final static int KELIN_LIMIT_SIZE = 100000;

    public final static String RDR_QUERY_EQ = "=";

    /**
     * 左括号
     */
    public static final String LEFT = "left";

    /**
     * 右括号
     */
    public static final String RIGHT = "right";

    public static final String ALL = "all";

    public static final String AND = "and";

    public static final String OR = "or";

    /**
     * RDR2 就诊表的就诊号
     */
    public static final String RDR2_ADM_ID = "adm_id";

    /**
     * 华西医院编码
     */
    public static final String HX_DEPT_CODE = "HID0101";

    /**
     * 华西上锦医院编码
     */
    public static final String HXSJ_DEPT_CODE = "HID0103";

    /**
     * CA 认证
     */
    public static final String HX = "hx";
    public static final String MSSPID = "msspId";
    public static final String SIGN_DATA_ID = "signDataId";
    public static final String SIGN_JOB_ID = "signJobId";
    public static final String STATUS = "status";
    public static final String JOB_STATUS = "jobStatus";
    public static final String SIGN_RESULT = "signResult";
    public static final String SIGN_CERT = "signCert";
    public static final String QR_CODE = "qrCode";

    /**
     * OA 审核
     */
    //OA文件审核通过
    public static final String OA_FILE_SUCCESS = "303";
    //OA文件审核不通过
    public static final String OA_FILE_FAIL = "302";
    //OA申请单提交成功
    public static final String OA_FORM_SUBMIT_SUCCESS = "307";
    //OA申请单提交失败
    public static final String OA_FORM_SUBMIT_FAIL = "306";
    //OA申请单审核通过
    public static final String OA_FORM_SUCCESS = "305";
    //OA申请单审核不通过
    public static final String OA_FORM_FAIL = "304";

    public static final String OA_CODE = "code";
    public static final String OA_MESSAGE = "message";

    /**
     * modify by cx PAD端标识
     */
    public final static String HTTP_APP_CODE = "X-HTTP-MOBILE-APP";
    public final static String APP_CODE = "app";
    public final static String UNI_APP_CODE = "uni-app";
    //HIS入组时就诊ID
    public final static String HIS_INTO_GRP_ADMID = "hisAdmId";

    public static final String CRF_EXTERNAL_STATUS = "crf_status";
    public static final String CRF_EXTERNAL_FILL_DATA = "crf_external_data";

    /**
     * 删除空列-常量属性
     */
    public static final String LOAD_FILE = "loadFile";
    public static final String DELETE_COLUMN = "deleteColumn";
    public static final String SAVE_FILE = "save_File";

    public static final String REGNO = "regNo";
    public static final String DEPT_CODE = "deptCode";
    public static final String MED_CODE = "medCode";

     public static final String TOTAL_KEY = "total";
     public static final String LIST_KEY = "list";

     public static final String TYPE = "type";
}
