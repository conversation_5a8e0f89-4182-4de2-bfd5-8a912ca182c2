package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.modules.sys.dao.SysRecycleRuleDao;
import com.boot.modules.sys.vo.SysRecycleRuleVo;

import java.util.ArrayList;
import java.util.List;

public class SysRecycleRuleServiceImpl extends ServiceImpl<SysRecycleRuleDao, SysRecycleRuleVo> {

    /**
     * 添加到回收站
     * @param ids
     * @param classz
     * @param service
     * @param <A>
     */
    public <A> void backup(Long[] ids, Class<A> classz, IService<A> service) {
        List<SysRecycleRuleVo> ruleEntityList = new ArrayList<>();
        for (Long id : ids) {
            A a = service.getById(id);

            SysRecycleRuleVo entity = new SysRecycleRuleVo();
//            entity.setDeleteData();
//            entity.setTableName(classz.);
        }

    }
}
