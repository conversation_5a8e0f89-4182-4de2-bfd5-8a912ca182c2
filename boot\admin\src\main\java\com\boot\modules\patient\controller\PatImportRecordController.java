package com.boot.modules.patient.controller;

import com.boot.commons.annotation.Login;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.file.FileHelper;
import com.boot.modules.patient.entity.PatImportRecordEntity;
import com.boot.modules.patient.service.PatImportRecordService;
import com.boot.modules.sys.controller.AbstractController;
import com.j256.simplemagic.ContentInfo;
import com.j256.simplemagic.ContentInfoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Api(tags = "受试者导入记录")
@RestController
@RequestMapping("/pat/import/record")
public class PatImportRecordController extends AbstractController {

    @Resource
    private PatImportRecordService patImportRecordService;

    @ApiOperation(
            value = "导入记录列表",
            notes = "导入记录列表"
    )
    @Login
    @GetMapping("/list")
//    @RequiresPermissions("export:record:list")
    public Result list(@RequestParam Map<String, Object> params) {

        Long userId = getUserId();

        if (isSuperAdmin()) {
            userId = 0L;
        }
        params.put("userId", userId);

        PageUtils page = patImportRecordService.queryPage(params);

        return R.success(page);
    }

    @ApiOperation(
            value = "获取指定导入记录的信息",
            notes = "获取指定导入记录的信息"
    )
    @GetMapping("/{id}")
//    @RequiresPermissions("export:record:info")
    public Result info(@PathVariable("id") Long recordId) {

        return R.success(patImportRecordService.getById(recordId));
    }

    @ApiOperation("导人数据下载")
    @GetMapping("/download")
//    @RequiresPermissions("export:record:download")
    public Result download(HttpServletRequest request, HttpServletResponse response, @RequestParam Long recordId) throws IOException {
        //验证导出数据是否审核
        PatImportRecordEntity recordEntity = patImportRecordService.getById(recordId);

        String path = FileHelper.getImportErrorInfoPath() + recordEntity.getErrorInfoUrl();

        String userAgent = request.getHeader("User-Agent");
        String fileName = "错误信息.xls";
        //解决乱码
        if (
            //IE 8 至 IE 10 or  IE 11
                userAgent.toUpperCase().contains("MSIE")
                        || userAgent.contains("Trident/7.0")) {
            fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
        } else {
            fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        ContentInfo contentInfo = ContentInfoUtil.findExtensionMatch(path);
        if (contentInfo != null) {
            response.setContentType(contentInfo.getMimeType());
        }

        response.addHeader("Content-disposition", "attachment;filename=" + fileName);
        response.setCharacterEncoding("UTF-8");
        InputStream inputStream = FileHelper.getInputStream(path);
        ServletOutputStream outputStream = response.getOutputStream();
        IOUtils.copy(FileHelper.getInputStream(path), outputStream);

        response.flushBuffer();
        inputStream.close();
        outputStream.close();
        return R.success("下载成功");
    }
}
