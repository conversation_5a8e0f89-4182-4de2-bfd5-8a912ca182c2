package com.boot.modules.patient.service;

import com.boot.modules.patient.dto.InboundProcessDto;

import java.util.List;
import java.util.Map;

public interface ReportService {

    /**
     * 临管部管理员统计
     * @param params
     * @return
     */
    Map<String, Object> proManagement(Map<String, Object> params);

    /**
     * 临管部管理员统计
     * @param params
     * @return
     */
    Map<String, Object> deptManagement(Map<String, Object> params, boolean isPro);

    /**
     * 设置科室管理者导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setDeptManageData(Map<String, List<List<String>>> headMap,
                       Map<String, List<List<Object>>> dataMap,
                       String key,
                       List<Map<String, Object>> list);

    /**
     * 设置医生导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setDoctorData(Map<String, List<List<String>>> headMap,
                       Map<String, List<List<Object>>> dataMap,
                       String key,
                       List<Map<String, Object>> list);

    /**
     * 设置科室表头导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setDoctorHeaderData(Map<String, List<List<String>>> headMap,
                     Map<String, List<List<Object>>> dataMap,
                     String key,
                     List<Map<String, Object>> list);

    /**
     * 设置科室导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setDeptData(Map<String, List<List<String>>> headMap,
                     Map<String, List<List<Object>>> dataMap,
                     String key,
                     List<Map<String, Object>> list);

    /**
     * 设置科室导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setDeptManageHeaderData(Map<String, List<List<String>>> headMap,
                     Map<String, List<List<Object>>> dataMap,
                     String key,
                     List<Map<String, Object>> list);

    /**
     * 设置医联体导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setIcoData(Map<String, List<List<String>>> headMap,
                     Map<String, List<List<Object>>> dataMap,
                     String key,
                     List<Map<String, Object>> list);

    /**
     * 设置PI报表导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setPIData(Map<String, List<List<String>>> headMap,
                    Map<String, List<List<Object>>> dataMap,
                    String key,
                    List<Map<String, Object>> list);
    /**
     * 医联体报表
     * @param params
     * @return
     */
    Map<String, Object> icoManagement(Map<String, Object> params);

    /**
     * PI报表
     * @param params
     * @return
     */
    Map<String, Object> pi(Map<String, Object> params);

    /**
     * 设置受试者详情导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setPatientData(Map<String, List<List<String>>> headMap,
                           Map<String, List<List<Object>>> dataMap,
                           String key,
                           List<InboundProcessDto> list);

    /**
     * 设置受试者详情导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setYltPatientData(Map<String, List<List<String>>> headMap,
                        Map<String, List<List<Object>>> dataMap,
                        String key,
                        List<InboundProcessDto> list);

    /**
     * 设置受试者详情导出为excel数据
     * @param headMap
     * @param dataMap
     */
    void setPIPatientData(Map<String, List<List<String>>> headMap,
                        Map<String, List<List<Object>>> dataMap,
                        String key,
                        List<InboundProcessDto> list);
}
