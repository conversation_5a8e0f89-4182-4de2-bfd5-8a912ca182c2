<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysRoleDao">


    <select id="getByQuery" resultType="com.boot.modules.sys.entity.SysRoleEntity">
        SELECT
            c.*
        FROM
            rp_sys_user a
                LEFT JOIN rp_sys_user_role b ON a.id = b.user_id
                LEFT JOIN rp_sys_role c ON b.role_id = c.id
            ${ew.customSqlSegment}

    </select>
</mapper>