package com.boot.modules.sys.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录表单
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description= "登录表单")
public class SysLoginForm {
    @ApiModelProperty(value = "用户名")
    private String username;
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty(value = "验证码")
    private String captcha;
    private String uuid;
    //是否从his登录 1his登录 0或空为非his登录
    private String isHis;
}
