package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.project.entity.ProjDeptPermissionEntity;
import com.boot.modules.project.vo.ProjDeptPermissionVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

//import com.boot.modules.sys.entity.SysUserDeptEntity;

/**
 * <AUTHOR>
 * @desc :
 * @create 2022-12-05
 */
@Mapper
@CacheNamespace
public interface ProjDeptPermissionDao extends BaseMapper<ProjDeptPermissionEntity> {
    List<ProjDeptPermissionVo> getProjectPermission(@Param(Constants.WRAPPER) QueryWrapper<ProjDeptPermissionVo> qw);
}
