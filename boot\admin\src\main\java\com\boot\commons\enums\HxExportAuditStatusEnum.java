package com.boot.commons.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc : 导出审核状态枚举类    审核状态。
 * @create 2022-06-16
 */
public enum HxExportAuditStatusEnum {
    /**
     * CA认证中
     */
    CA_AWAIT(0, "ca_await", "CA认证中", ""),
    /**
     * CA认证通过
     */
    CA_PASS(1, "ca_pass", "CA认证通过，申请单待提交", ""),
    /**
     * CA认证不通过
     */
    CA_NO_PASS(2, "ca_no_pass", "CA认证不通过", ""),
    /**
     * OA申请单审核中
     */
    OA_FORM_AWAIT(3, "oa_form_await", "OA申请单审核中", ""),
    /**
     * OA申请单审核通过
     */
    OA_FORM_PASS(4, "oa_form_pass", "OA申请单审核通过", ""),
    /**
     * OA审核单审核不通过
     */
    OA_FORM_NO_PASS(5, "oa_form_no_pass", "OA审核单审核不通过", ""),
    /**
     * OA导出文件审核中
     */
    OA_FILE_AWAIT(6, "oa_file_await", "OA文件审核中", ""),
    /**
     * OA导出文件审核通过
     */
    OA_FILE_PASS(7, "oa_file_pass", "OA文件审核通过", ""),
    /**
     * OA导出文件审核不通过
     */
    OA_FILE_NO_PASS(8, "oa_file_no_pass", "OA文件审核不通过", ""),
    ;
    private int type;
    private String code;
    private String name;
    private String desc;

    HxExportAuditStatusEnum(int type, String code, String name, String desc) {
        this.type = type;
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<String, Object> typeMap = new HashMap<>();
    public static Map<String, HxExportAuditStatusEnum> typeEnumMap = new HashMap<>();

    static {
        HxExportAuditStatusEnum[] types = HxExportAuditStatusEnum.values();
        for (HxExportAuditStatusEnum type : types) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", type.type);
            map.put("code", type.code);
            map.put("name", type.name);
            map.put("desc", type.desc);
            typeMap.put(String.valueOf(type.type), map);
            typeEnumMap.put(String.valueOf(type.type), type);
        }
    }

}
