package com.boot.commons.enums;

public enum DataPermissionEnum {
    All_DATA(1, "查询所有数据"),
    OWNER_DEPT(2, "查询指定中心数据"),
    OWENER(3, "查询自己相关数据");

    private Integer code;

    private String desc;

    DataPermissionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static DataPermissionEnum getByCode(Integer code){
        if (code != null) {
            for (DataPermissionEnum dataPermissionEnum : DataPermissionEnum.values()) {
                if (dataPermissionEnum.getCode().equals(code)) {
                    return dataPermissionEnum;
                }
            }
        }
        return null;
    }
}
