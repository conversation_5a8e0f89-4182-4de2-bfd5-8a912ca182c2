package com.boot.commons.utils;

import com.boot.commons.constant.HxConst;
import org.apache.batik.svggen.font.table.Device;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import sun.net.util.IPAddressUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * 客户端信息工具类
 */
public class ClientInfoUtil {
    private static final String LOCAL_IP = "127.0.0.1";

    /**
     * 判断内外网
     */
    public static Long netType() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String ip = IPUtils.getIpAddr(request);
        return internalIp(ip) ? 1L : 2L;
    }

    private static boolean internalIp(String ip) {
        if (LOCAL_IP.equals(ip)) {
            return true;
        }
        byte[] addr = IPAddressUtil.textToNumericFormatV4(ip);
        if (addr == null) {
            return true;
        }
        return internalIp(addr);
    }

    private static boolean internalIp(byte[] addr) {
        final byte b0 = addr[0];
        final byte b1 = addr[1];
        //10.x.x.x/8
        final byte SECTION_1 = 0x0A;
        //172.16.x.x/12
        final byte SECTION_2 = (byte) 0xAC;
        final byte SECTION_3 = (byte) 0x10;
        final byte SECTION_4 = (byte) 0x1F;
        //192.168.x.x/16
        final byte SECTION_5 = (byte) 0xC0;
        final byte SECTION_6 = (byte) 0xA8;
        switch (b0) {
            case SECTION_1:
                return true;
            case SECTION_2:
                if (b1 >= SECTION_3 && b1 <= SECTION_4) {
                    return true;
                }
            case SECTION_5:
                switch (b1) {
                    case SECTION_6:
                        return true;
                }
            default:
                return false;
        }
    }

    /**
     * modify by cx PAD端标识
     */
    public static String mobileType() {
        String type = "";
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        // 表单填写界面单独处理
        if(request.getRequestURI().contains("/boot-admin/form-maker/form-fill-mobile/data/")
                ||request.getRequestURI().contains("/boot-admin/form-maker/form-fill-mobile-disauditable/data/")){
            return HxConst.APP_CODE;
        }

        return request.getHeader(HxConst.HTTP_APP_CODE) == null || !request.getHeader(HxConst.HTTP_APP_CODE).equals("true") ? "" : HxConst.APP_CODE;
//        return request.getHeader("user-agent").contains(HxConst.UNI_APP_CODE) ? HxConst.APP_CODE : "";
    }
}
