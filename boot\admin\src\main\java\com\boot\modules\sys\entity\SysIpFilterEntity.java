package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@TableName("rp_sys_ip_filter")
public class SysIpFilterEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * IP地址
     */
    @NotBlank(message="IP不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String ip;


    /**
     * 标记：0：白名单 1：黑名单， 默认添加的是白名单
     */
    private Integer mark;

}
