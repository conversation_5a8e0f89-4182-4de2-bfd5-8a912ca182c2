package com.boot.modules.sys.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 角色
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_role")
public class SysRoleEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @TableId
    private Long id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 20, message = "角色名称最多为20位", groups = {AddGroup.class, UpdateGroup.class})
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "名称不能包含特殊字符", groups = {AddGroup.class, UpdateGroup.class})
    private String roleName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 职能级别 1.系统级  2. 项目级
     */
    @NotNull(message = "职能级别不能为空")
    private Integer roleLevel;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 角色首页路由，目前只需对系统级角色设置，可为空
     */
    private String indexUrl;

    /**
     * 是否下项目管理员
     */
    private Integer isProjAdmin;

    /**
     * 是否是系统管理员
     */
    private Integer isSysAdmin;

    /**
     * 是否为录入人员
     */
    private Integer isInput;

    /**
     * 角色使用范围，字符串数组,可为单中心、多中心（数字）,
     * 单中心：1，多中心2
     * 如："1,2"
     */
    private String scope;

    /**
     * 部门名称
     */
//	@TableField(exist=false)
//	private String deptName;

    @TableField(exist = false)
    private List<Long> menuIdList;

    @TableField(exist = false)
    private List<Long> deptIdList;

}
