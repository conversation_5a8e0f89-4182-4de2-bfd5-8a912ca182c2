package com.boot.modules.patient.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("rp_patient")
public class PatientEntity implements Serializable {

    private String empiid;

    private String regno;

    private String name;
    // 0- 男 1-女 2-其他
    private int gender;

    private String birthday;

    private String idCard;

    private String initials;

    private String tel;

    /**
     * 患者状态：0-未锁定，1-锁定，默认值为0
     */
    private int status;

    /**
     * 患者类型：0-院内，1-院外
     */
    private int type;

    /**
     * 民族
     */
    private String nation;
    /**
     * 是否需要重新同步0 -否 1-是，移动端默认入组后为1
     */
    private Integer isNeedSync = 0;
    /**
     * 归一后的empiid，一般是和empi一致的，但是在某些情况下，例如移动端，可能会有差异，empiid是随机生成的，srcEmpiid是真实归一后的
     */
    private String srcEmpiid;

    /**
     * 医疗机构编码
     */
    private String medCode;
}
