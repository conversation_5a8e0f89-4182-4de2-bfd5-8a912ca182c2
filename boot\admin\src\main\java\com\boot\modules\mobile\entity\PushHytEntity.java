package com.boot.modules.mobile.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 推送华医通记录
 */
@Data
@TableName(value = "rp_push_hyt")
public class PushHytEntity implements Serializable {
    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 证件号
     */
    private String cardInfo;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 请求时间
     */
    private String requestTime;
    /**
     * 首次推荐时间
     */
    private String recommendTime;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 信息
     */
    private String message;
    /**
     * 类型 1新增；2删除
     */
    private Integer type;
    /**
     * 请求用户ID
     */
    private Long userId;

    /**
     * 病例ID
     */
    private String empi;
    /**
     * 入径医院名称
     */
    private String medName;
    /**
     * 入径医院编码
     */
    private String medCode;
    /**
     * 入径医生姓名
     */
    private String doctorName;
    /**
     * 入径医生工号
     */
    private String doctorCode;
}
