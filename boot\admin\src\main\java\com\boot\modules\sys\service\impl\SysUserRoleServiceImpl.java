package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.ListUtils;
import com.boot.modules.sys.service.SysUserRoleService;
import com.boot.modules.sys.dao.SysUserRoleDao;
import com.boot.modules.sys.entity.SysUserRoleEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 用户与角色对应关系
 *
 * <AUTHOR>
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleDao, SysUserRoleEntity> implements SysUserRoleService {
    @Override
    public void saveOrUpdate(Long userId, List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return;
        }
        List<Long> roleIds = queryRoleIdList(userId);
        //增加的元素组成的列表
        List<Long> aList = ListUtils.getAddListThanList(roleIdList, roleIds);
        List<SysUserRoleEntity> sysUserRoleEntities = new ArrayList<>();
        for (Long id : aList) {
            SysUserRoleEntity entity = new SysUserRoleEntity();
            entity.setRoleId(id);
            entity.setUserId(userId);
            sysUserRoleEntities.add(entity);
        }

        if(!CollectionUtils.isEmpty(sysUserRoleEntities)){
            this.saveBatch(sysUserRoleEntities);
        }

        //减少的元素组成的列表
        List<Long> bList = ListUtils.getReduceListThanList(roleIdList, roleIds);

        if(!CollectionUtils.isEmpty(bList)){
            //删除用户与角色关系
            this.remove(
                    new QueryWrapper<SysUserRoleEntity>()
                            .lambda()
                            .eq(SysUserRoleEntity::getUserId, userId)
                            .in(SysUserRoleEntity::getRoleId, bList)
            );
        }

    }

    @Override
    public List<Long> queryRoleIdList(Long userId) {
        List<SysUserRoleEntity> list = baseMapper.selectList(
                new QueryWrapper<SysUserRoleEntity>().lambda().eq(SysUserRoleEntity::getUserId, userId)
        );

        return list.stream().map(SysUserRoleEntity::getRoleId).collect(Collectors.toList());
    }

    @Override
    public int deleteBatch(Long[] roleIds) {
        return baseMapper.deleteBatch(roleIds);
    }
}
