<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysMenuDao">
	<cache-ref namespace="com.boot.modules.sys.dao.SysMenuDao"/>
    <select id="queryAllPerms" resultType="java.lang.String" flushCache="true" useCache="false">

        SELECT
            m.permissions
        FROM
            rp_sys_menu m
                LEFT JOIN rp_sys_role_menu rm ON rm.menu_id = m.id
                LEFT JOIN rp_sys_user_role ur ON ur.role_id = rm.role_id
        where ur.user_id = #{userId}

    </select>

    <!-- 区分内外网查询用户的所有权限 -->
    <select id="queryAllPermsByType" resultType="string" flushCache="true" useCache="false">
        SELECT
            m.permissions
        FROM
            rp_sys_menu m
                LEFT JOIN rp_sys_role_menu rm ON rm.menu_id = m.id
                LEFT JOIN rp_sys_user_role ur ON ur.role_id = rm.role_id
        where ur.user_id = #{userId} and rm.type = #{type}
    </select>

    <select id="getByQuery" resultType="com.boot.modules.sys.entity.SysMenuEntity" flushCache="true" useCache="false">
        select a.*,b.role_id as roleId  from rp_sys_menu a
            left join rp_sys_role_menu b on a.id =  b.menu_id
            ${ew.customSqlSegment}
    </select>

</mapper>