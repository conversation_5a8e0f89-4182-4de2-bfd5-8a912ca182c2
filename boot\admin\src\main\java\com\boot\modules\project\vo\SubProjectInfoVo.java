package com.boot.modules.project.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 子项目信息视图-数据集市
 * <AUTHOR>
 */
@Data
public class SubProjectInfoVo {

    private Long id;
    /**
     * 子项目病例数
     */
    private Integer count;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 子项目ID
     */
    private Long subProjectId;

    /**
     * 项目-子项目名称
     */
    private String projectName;

    /**
     * 项目所属机构
     */
    private String deptName;

    /**
     * 子项目创建时间
     */
    private String createDate;

    /**
     * 项目开始时间
     */
    private String startDate;

    /**
     * 项目结束时间
     */
    private String endDate;

    /**
     * 项目或子项目描述
     */
    private String description;

    /**
     * 项目类型-1单病种、2多病种
     */
    private Integer type;

    /**
     * 管理员
     */
    private String projectAdmin;


    /**
     * 项目-主中心
     */
    private String primaryDeptName;

    private String subProjectName;

    private String projectAdminName;

    private Long projectAdminId;

    private String projectUserName;

    private Date subProjectCreateTime;

    private Long subProjectCreateUserId;

    private Date projectCreateTime;

    private Long projectCreateUserId;

    private Integer projectType;

    private Integer projectEnableAudit;

    private Long wxUserId;

    private Long subPatientId;
    /**
     * 是否私密(功能改造)
     * chenwei 2022/10/24
     * 数据集市调用此类型
     */
    private Boolean isPrivate;
}
