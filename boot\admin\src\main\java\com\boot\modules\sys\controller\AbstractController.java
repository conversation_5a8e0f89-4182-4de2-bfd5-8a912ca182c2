package com.boot.modules.sys.controller;

import com.boot.commons.constants.Const;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.ObjectUtils;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import com.boot.modules.sys.service.ShiroService;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Controller公共组件
 *
 * <AUTHOR>
 */
public abstract class AbstractController {
    @Autowired
    HttpServletRequest req;

    @Resource
    private ShiroService shiroService;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected SysUserEntity getUser() {
        if (SecurityUtils.getSubject().getPrincipal() == null) {
            String token = req.getHeader(Const.TOKEN);
            if (!StringUtils.isEmpty(token)) {
                SysUserTokenEntity sysUserTokenEntity = shiroService.queryByToken(token);
                if (!ObjectUtils.isEmpty(sysUserTokenEntity)) {
                    String curUserId = shiroService.queryByToken(token).getUserId();
                    return shiroService.queryUser(Long.parseLong(curUserId));
                }
            }
            return null;
        } else {
            return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        }
    }

    protected Long getUserId() {
        // 从header中获取token
        String token = req.getHeader(Const.TOKEN);
        if (!StringUtils.isEmpty(token)) {
            String curUserId = shiroService.queryByToken(token).getUserId();
            return Long.parseLong(curUserId);
        } else {
            return null;
        }
    }

    protected boolean isSuperAdmin() {
        return getUser().getUsername().equals(BootAdminProperties.adminAccount);
    }

    protected boolean isSysAdmin() {
        SysUserEntity user = getUser();

        if (user == null) {
            return false;
        }

        return (user.getIsSysAdmin() != null && user.getIsSysAdmin() == 1) || user.getId() == 0L;
    }

    /**
     * 当前用户所在科室
     *
     * @return
     */
    protected List<Long> getDeptId() {
        List<String> deptIdStringList = Arrays.asList(getUser().getDeptId().split(","));
        List<Long> deptIdList = new ArrayList<>();
        for (String deptIdString : deptIdStringList) {
            if (StringUtils.isEmpty(deptIdString)) {
                continue;
            }
            try {
                deptIdList.add(Long.parseLong(deptIdString));
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
        return deptIdList;
    }

    /**
     * 当前用户为科室管理者的科室
     *
     * @return
     */
    protected List<Long> getAdminDeptId() {
        String[] split = getUser().getAdminDeptId().split(",");
        List<Long> deptIdList = new ArrayList<>();
        if(split.length>0){
            List<String> deptIdStringList = Arrays.asList(split);
            for (String deptIdString : deptIdStringList) {
                if (StringUtils.isEmpty(deptIdString)) {
                    continue;
                }
                try {
                    deptIdList.add(Long.parseLong(deptIdString));
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
            }
        }
        return deptIdList;

    }
}
