package com.boot.modules.patient.service;

import com.boot.commons.utils.PageUtils;

import java.util.Map;

public interface PreRecommendService {
    /**
     * 获取漏斗条数，type 1-今日，2-全部
     *
     * @param subProjectId
     * @param type
     * @return
     */
    Map<Long, Integer> getCount(Long projectId, Long subProjectId, Integer type, Map<String, Object> params);

    /**
     * 分页获取推荐病例
     *
     * @param subProjectId
     * @param type
     * @param params
     * @return
     */
    PageUtils getByPage(Long projectId, Long subProjectId, Integer type, Map<String, Object> params);

    /**
     * 统计今日就诊的患者
     */
    void todayEmpi();

    /**
     * 判断患者是否为系统推荐
     * @param projectId
     * @param subProjectId
     * @param empi
     * @return
     */
    Boolean isMatch(Long projectId, Long subProjectId, String empi);
}
