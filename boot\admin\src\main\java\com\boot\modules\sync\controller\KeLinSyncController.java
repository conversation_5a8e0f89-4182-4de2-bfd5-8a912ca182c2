package com.boot.modules.sync.controller;

import com.boot.commons.result.KeLinResult;
import com.boot.commons.utils.MapUtils;
import com.boot.modules.sync.service.KelinConfigService;
import com.boot.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 病例检索入组相关接口管理
 * <AUTHOR>
 * @createTime 2022年01月04日 11:11:00
 */
@Slf4j
@Api(tags = "纳排检索")
@RestController
@RequestMapping("/keLin/search")
public class KeLinSyncController extends AbstractController {
    @Resource
    private KelinConfigService kelinConfigService;
    @ApiOperation(
            value = "数据集首次保存完成后回调接口",
            notes = "数据集首次保存完成后回调接口"
    )
    @PostMapping("activate")
    public KeLinResult activate(@RequestBody Map<String, Object> params) {
        Boolean res;
        try {
            String status = MapUtils.getValue(params, "status", String.class);
            List<Map<String, Object>> diseaseSaveData = (List<Map<String, Object>>) params.get("data");
            Long expressionId = Long.parseLong(diseaseSaveData.get(0).get("expressionId").toString());
            Long exportId = Long.parseLong(diseaseSaveData.get(0).get("exportId").toString());
            Long diseaseId = Long.parseLong(diseaseSaveData.get(0).get("diseaseID").toString());
            String message = MapUtils.getValue(params, "message", String.class);
            if (!"success".equals(status)) {
                //失败更新记录信息
                res = kelinConfigService.update(diseaseId, 2, message,0);
                return KeLinResult.fail("检索回调失败"+ message);
            }
            res = kelinConfigService.firstSearch(diseaseId, expressionId, exportId);
        } catch (Exception ex) {
            return KeLinResult.fail("检索回调失败:" + ex.getMessage());
        }
        return res ? KeLinResult.ok("检索回调成功") : KeLinResult.fail("检索回调失败");
    }
}
