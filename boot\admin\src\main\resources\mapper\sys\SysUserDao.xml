<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysUserDao">
    <cache-ref namespace="com.boot.modules.sys.dao.SysUserDao"/>
    <!-- 查询用户的所有权限 -->
    <select id="queryAllPerms" resultType="string">
        SELECT
            m.permissions
        FROM
            rp_sys_menu m
                LEFT JOIN rp_sys_role_menu rm ON rm.menu_id = m.id
                LEFT JOIN rp_sys_user_role ur ON ur.role_id = rm.role_id
        where ur.user_id = #{userId}
    </select>

    <!-- 查询用户的所有菜单ID -->
    <select id="queryAllMenuId" resultType="long">
        select distinct rm.menu_id
        from rp_sys_user_role ur
                 LEFT JOIN rp_sys_role_menu rm on ur.role_id = rm.role_id
        where ur.user_id = #{userId}
    </select>

    <select id="queryAllMenuIdByType" resultType="long">
        select distinct rm.menu_id
        from rp_sys_user_role ur
                 LEFT JOIN rp_sys_role_menu rm on ur.role_id = rm.role_id
        where ur.user_id = #{userId} and rm.type = #{type}
    </select>

    <select id="queryPage" resultType="com.boot.modules.sys.entity.SysUserEntity">
        SELECT distinct a.*
        FROM (
            select
                d.*
            FROM
                rp_sys_user d
            JOIN
                mysql.help_topic e
            ON
                e.help_topic_id &lt; (LENGTH(d.dept_id) - LENGTH(REPLACE(d.dept_id, ',', '')) + 1)) a
         LEFT JOIN rp_sys_user_role b on a.id = b.user_id
         LEFT JOIN rp_sys_dept c on a.dept_id = c.id
            ${ew.customSqlSegment}
    </select>

    <select id="getByQuery" resultType="com.boot.modules.sys.entity.SysUserEntity">
        SELECT
            a.id,
            a.username AS userName,
            c.is_sys_admin
        FROM
            `rp_sys_user` a
                LEFT JOIN rp_sys_user_role b ON a.id = b.user_id
                LEFT JOIN rp_sys_role c ON b.role_id = c.id
            ${ew.customSqlSegment}
    </select>

    <select id="listByDeptId" resultType="com.boot.modules.sys.entity.SysUserEntity">
        select *
        from (
            select
                a.*,
                substring_index(substring_index(a.dept_id, ',', b.help_topic_id + 1), ',', - 1) deptId
            FROM
                rp_sys_user a
            JOIN
                mysql.help_topic b
            ON
                b.help_topic_id &lt; (LENGTH(a.dept_id) - LENGTH(REPLACE(a.dept_id, ',', '')) + 1)) temp
        ${ew.customSqlSegment}
    </select>

    <select id="pageByDeptId" resultType="com.boot.modules.sys.entity.SysUserEntity">
        select *
        from (
            select
                a.*,
                substring_index(substring_index(a.dept_id, ',', b.help_topic_id + 1), ',', - 1) deptId
            FROM
                rp_sys_user a
            JOIN
                mysql.help_topic b
            ON
                b.help_topic_id &lt; (LENGTH(a.dept_id) - LENGTH(REPLACE(a.dept_id, ',', '')) + 1)) temp
        ${ew.customSqlSegment}
    </select>

    <select id="getUserAndDept" resultType="com.boot.modules.sys.entity.SysUserEntity">
        SELECT a.*
        FROM `rp_sys_user` a
                 LEFT JOIN rp_sys_dept b on b.id = a.dept_id
            ${ew.customSqlSegment}
    </select>

    <select id="listCountDisease" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">

        select
            temp.doctorId,
            temp.doctorName,
            count(distinct(temp.diseaseId)) totalDisease,
            count(distinct(temp.projectId)) totalProject
        from
            (select
                a.id doctorId,
                a.nickname doctorName,
                d.id projectId,
                d.disease_id diseaseId
            from
                rp_sys_user a
            join
                rp_user_subproject b
            on
                a.id = b.user_id
            join
                rp_sub_project c
            on
                b.sub_project_id = c.id
            join
                rp_project d
            on
                c.project_id = d.id
            join
                rp_sys_disease e
            on
                d.disease_id = e.id
            ${ew.customSqlSegment}) temp
        group by
            temp.doctorId,temp.doctorName
    </select>

    <select id="listCountProject" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">
        select
            f.id doctorId,
            f.nickname doctorName,
            f.dept_id deptId,
            temp1.totalDisease,
            temp1.totalProject
        from (SELECT e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id
                    FROM rp_sys_user e
                    JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) f
        left join (
            select
                temp.doctorId,
                temp.doctorName,
                temp.deptId,
                count(distinct(temp.diseaseId)) totalDisease,
                count(distinct(temp.projectId)) totalProject
            from
                (select
                    a.id doctorId,
                    a.nickname doctorName,
                    d.id projectId,
                    d.disease_id diseaseId,
                    a.dept_id as deptId
                from
                    (SELECT e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id
                    FROM rp_sys_user e
                    JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) a
                join
                    rp_project d
                on
                    a.id = d.project_admin_id and a.dept_id = d.main_dept_id
                ${ew.customSqlSegment}) temp
            group by
                temp.doctorId,temp.doctorName,temp.deptId
        ) temp1 on f.id = temp1.doctorId and f.dept_id=temp1.deptId
    </select>

    <select id="getByDeptId" resultType="com.boot.modules.sys.entity.SysUserEntity">
        SELECT c.*
        FROM
            (SELECT a.*,substring_index(substring_index(a.dept_id, ',', b.help_topic_id + 1), ',', - 1) deptIdStr
            FROM rp_sys_user a
            JOIN mysql.help_topic b ON b.help_topic_id  &lt; (LENGTH(a.dept_id) - LENGTH(REPLACE(a.dept_id, ',', '')) + 1)) as c
            ${ew.customSqlSegment}
    </select>

    <select id="listAll" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">

        select
        temp.doctorId,
        temp.doctorName,
        temp.deptId
        from
        (select
        a.id doctorId,
        a.nickname doctorName,
        a.dept_id as deptId
        from
        (SELECT e.id,e.nickname,substring_index(substring_index(e.dept_id, ',', f.help_topic_id + 1), ',', - 1) dept_id
        FROM rp_sys_user e
        JOIN mysql.help_topic f ON f.help_topic_id  &lt; (LENGTH(e.dept_id) - LENGTH(REPLACE(e.dept_id, ',', '')) + 1)) a
        ${ew.customSqlSegment}) temp
    </select>

</mapper>
