package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统用户Token
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_user_token")
public class SysUserTokenEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	// modify by cx PAD端userId拼接上app
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;
    /**
     * token
     */
    private String token;
    /**
     * 过期时间
     */
    private Date expireTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 登录ip地址
     */
    private String loginIp;

    /**
     *    针对病人登入的时候模拟生成token ,加入IDcard 来判断病例
     */
    private String idCard;

    /**
     *    针对病人登入的时候模拟生成token ,加入IDcard 来判断病例
     */
    private Long subProjPatientId;
}
