package com.boot.modules.sync.service;

import com.boot.modules.sync.model.KelinSearchResponse;
import com.boot.modules.sync.vo.KelinRequestVo;
import com.github.lianjiatech.retrofit.spring.boot.annotation.OkHttpClientBuilder;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import okhttp3.OkHttpClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/5/11 16:14
 */
@RetrofitClient(
        baseUrl = "${boot.external.rdr-web-service}",
        poolName = "rdrApi"
)
public interface Rdr2HttpApiService {

    @OkHttpClientBuilder
    static OkHttpClient.Builder okhttpClientBuilder() {
        return new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS);
    }
    /**
     * rdr直连克林数据库
     * @param requestBody
     * @return
     */
    @POST("queryByTable")
    KelinSearchResponse getKelinDatasAuto(@Body KelinRequestVo requestBody);
}
