package com.boot.modules.sys.shiro;

import com.boot.commons.constant.HxConst;
import com.boot.commons.constants.Const;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.ClientInfoUtil;
import com.boot.commons.utils.SpringContextUtils;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import com.boot.modules.sys.service.ShiroService;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 认证
 *
 * <AUTHOR>
 */
@Component
public class OAuth2Realm extends AuthorizingRealm {
    private ShiroService shiroService;

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof OAuth2Token;
    }

    /**
     * 授权(验证权限时调用)
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        SysUserEntity user = (SysUserEntity) principals.getPrimaryPrincipal();
        Long userId = user.getId();
        if (shiroService == null) {
            shiroService = SpringContextUtils.getBean(ShiroService.class);
        }

        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        if (userId.equals(Const.SUPER_ADMIN)) {
            List<String> permissions = getAllPerm();
            info.addStringPermissions(permissions);
        } else {
            // 用户权限列表
            Set<String> permsSet = shiroService.getUserPermissions(userId);
            info.setStringPermissions(permsSet);
        }

        return info;
    }

    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        String accessToken = (String) token.getPrincipal();

        if (shiroService == null) {
            shiroService = SpringContextUtils.getBean(ShiroService.class);
        }

        // 根据accessToken，查询用户信息
        SysUserTokenEntity tokenEntity = shiroService.queryByToken(accessToken);
        // token失效
        if (tokenEntity == null || tokenEntity.getExpireTime().getTime() < System.currentTimeMillis()) {
            throw new IncorrectCredentialsException("token失效，请重新登录");
        }

        SysUserEntity user = new SysUserEntity();

        // PAD端需要去掉userId中的app标识
        Long userId = 0L;
        if (ClientInfoUtil.mobileType().equals(HxConst.APP_CODE) && tokenEntity.getUserId().contains(HxConst.APP_CODE)) {
            userId = Long.parseLong(tokenEntity.getUserId().split(HxConst.APP_CODE)[1]);
        } else {
            userId = Long.parseLong(tokenEntity.getUserId());
        }

        // 加上条件限制， 避免每一次权限验证都查询全部信息
        if (user == null
                || !userId.equals(user.getId())
                || userId.equals(Const.SUPER_ADMIN)
        ) {

            if (userId.equals(Const.SUPER_ADMIN)) {
                user = new SysUserEntity();
                user.setUsername(BootAdminProperties.adminAccount);
                user.setId(0L);
                user.setNickname("超级管理员");
                user.setIsSuperAdmin(1);
                user.setIsSysAdmin(0);
            } else {
                //查询用户信息,以及用户的角色和权限
                user = shiroService.queryUser(userId);
                user.setIsSuperAdmin(0);
                //账号锁定
                if (user.getStatus() == 0) {
                    throw new LockedAccountException("账号已被锁定,请联系管理员");
                }

            }
        }

        SimpleAuthenticationInfo info = new SimpleAuthenticationInfo(user, accessToken, getName());

        return info;
    }

    private List<String> getAllPerm() {
        List<String> permissions = new ArrayList<String>();

        // 获取所有的RequestMapping
        Map<String, HandlerMapping> allRequestMappings =
                BeanFactoryUtils.beansOfTypeIncludingAncestors(SpringContextUtils.applicationContext,
                        HandlerMapping.class, true, false);

        for (HandlerMapping handlerMapping : allRequestMappings.values()) {
            // 只需要RequestMappingHandlerMapping中的URL映射
            if (handlerMapping instanceof RequestMappingHandlerMapping) {
                RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping) handlerMapping;
                Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
                for (Map.Entry<RequestMappingInfo, HandlerMethod> requestMappingInfoHandlerMethodEntry : handlerMethods.entrySet()) {
                    HandlerMethod mappingInfoValue = requestMappingInfoHandlerMethodEntry.getValue();
                    //选择有RequiresPermissions注解的
                    if (mappingInfoValue.getMethod().isAnnotationPresent(RequiresPermissions.class)) {
                        RequiresPermissions requiresPermissions = mappingInfoValue.getMethod().getAnnotation(RequiresPermissions.class);

                        if (requiresPermissions != null) {
                            //获取注解中的值
                            String[] values = requiresPermissions.value();
                            permissions.addAll(Arrays.asList(values));
                        }
                    }
                }
            }
        }
        permissions = permissions.stream().distinct().collect(Collectors.toList());

        return permissions;
    }
}
