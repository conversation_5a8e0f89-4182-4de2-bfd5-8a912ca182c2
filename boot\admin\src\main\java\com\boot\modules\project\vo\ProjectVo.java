package com.boot.modules.project.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boot.modules.project.entity.ProjectEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 项目VO对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectVo extends ProjectEntity {
    /**
     * 子项目列表
     */
    private List<SubProjectVo> subProjList;

    private String dept;

    private String projectAdminName;

    private String projectUserName;

    /**
     * 项目病例数
     */
    private Integer patCount;


    /**
     * 已推荐人数
     */
    private int recommendCount;

    /**
     * 入组人数
     */
    private int inboundCount;

    /**
     * 新增入组人数
     */
    private int addInboundCount;

    /**
     * 已签人数
     */
    private int signCount;

    /**
     * 退出人数
     */
    private int exitCount;

    /**
     * 当前用户在项目中的角色信息
     */
    private ProjectRoleInfoVo roleInfo;


    /**
     * 主中心id，对应部门机构id
     */
    private Long primaryCenter;

    /**
     * 分中心id，对应部门机构id，分中心可以有多个，只有多中心项目有此
     */
    private String branchCenter;

    private String primaryCenterName;

    private List<String> branchCenterNames;

    /**
     * 微信关联的用户id
     */
    private Long wxUserId;

    /**
     * 微信关联的subPatientId
     */
    private Long subPatientId;

    private String mainDeptName;

    /**
     * 是否开启API，0：不开启， 1：开启 默认是0
     */
    private Integer enableApi;
    /**
     * 病种名称
     */
    private String diseaseName;

    /**
     * 开启API后的唯一ID
     */
    @TableField(exist = false)
    private String guid;

    private Date createTime;

    /**
     * 项目来源，2:GCP，3:IIT，4:专病队列，5:前瞻性研究
     */
    private Integer source;

    /**
     * 是否是导入
     */
    @TableField(exist = false)
    private Boolean isImport;

    /**
     * 累计Iit项目实际入组人数
     */
    private Integer totalIitInbound;

    /**
     * 累计GCP项目实际入组人数
     */
    private Integer totalGcpInbound;

    /**
     * 累计前瞻项目实际入组人数
     */
    private Integer totalPreObsInbound;

    /**
     * 累计专病队列实际入组人数
     */
    private Integer totalSpecialInbound;

    /**
     * 累计实际加权入组人数
     */
    private Integer totalWeightInbound = 0;
}
