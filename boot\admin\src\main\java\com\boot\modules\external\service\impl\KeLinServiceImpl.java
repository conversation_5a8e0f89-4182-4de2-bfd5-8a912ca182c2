package com.boot.modules.external.service.impl;

import com.boot.commons.constant.HxConst;
import com.boot.commons.enums.RDRFieldExplainEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.modules.external.entity.RDRFieldEntity;
import com.boot.modules.external.model.RdrResponseModel;
import com.boot.modules.external.service.KeLinService;
import com.boot.modules.external.service.RDRFieldService;
import com.boot.modules.sync.model.KelinSearchResponse;
import com.boot.modules.sync.service.Rdr2HttpApiService;
import com.boot.modules.sync.vo.KelinRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KeLinServiceImpl implements KeLinService {

    @Resource
    private RDRFieldService rdrFieldService;

    @Resource
    private Rdr2HttpApiService rdr2HttpApiService;

    /**
     * 获取病例就诊信息
     *
     * @param empi 患者唯一id
     * @return
     */
    @Override
    public RdrResponseModel getAdmInfo(String empi) {
        RdrResponseModel ret = new RdrResponseModel();
        KelinSearchResponse admInfo;
        try {
            // 根据表名查字段名
            List<RDRFieldEntity> rdrFieldEntities = rdrFieldService.getByTableName(HxConst.RDR_ADM_TABLE);
            //患者唯一ID字段
            RDRFieldEntity empidRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.EMPID.getType())).collect(Collectors.toList()).get(0);
            if (empidRDRField == null) {
                return ret;
            }

            //医疗机构编码字段
            RDRFieldEntity medRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.MED_ORG.getType())).collect(Collectors.toList()).get(0);
            if (medRDRField == null) {
                return ret;
            }

            //所有字段
            List<String> fields = rdrFieldEntities.stream().map(RDRFieldEntity::getKlFieldName).collect(Collectors.toList());

            //拼查询条件
            Map<String, List<Object>> params = new HashMap<>(2);
            //转换类型
            params.put(empidRDRField.getKlFieldName(), Arrays.asList(Long.parseLong(empi)));


            //接口参数
            KelinRequestVo body = new KelinRequestVo();
            Map<String, String> sort = new HashMap<>();
            sort.put(HxConst.KELIN_VISIT_TIME_FIELD, "desc");
            body.setTableName(HxConst.KELIN_ADM_TABLE)
                    .setLimit(HxConst.KELIN_LIMIT_SIZE)
                    .setOffset(1)
                    .setColumns(fields)
                    .setParams(params)
                    .setSortFields(sort);

            admInfo = rdr2HttpApiService.getKelinDatasAuto(body);
            if (admInfo == null) {
                log.error("查询柯林接口异常,返回为空");
                return ret;
            }
            if (admInfo.getCode() == null || admInfo.getCode() != HxConst.RESPONSE_SUCCESS) {
                log.error("查询柯林接口异常--HTTP状态码:{}, 异常原因:{}", admInfo.getCode(), admInfo.getData());
                return ret;
            }
            //获取就诊信息数据列表
            Map<String, String> keyMap = rdrFieldEntities.stream().collect(Collectors.toMap(RDRFieldEntity::getKlFieldName, RDRFieldEntity::getEname));

            List<Map<String, Object>> admList = admInfo.getData().getList();

            List<Map<String, Object>> admListAfter = new ArrayList<>();
            if (!CollectionUtils.isEmpty(admList)) {
                //按就时间倒序
                admList = admList.stream().sorted(Collections.reverseOrder(Comparator.comparing(p -> String.valueOf(p.get(HxConst.KELIN_VISIT_TIME_FIELD))))).collect(Collectors.toList());
                for (Map<String, Object> adm : admList) {
                    Map<String, Object> admAfter = new LinkedHashMap<>();
                    for (String key : adm.keySet()) {

                        //转成String解决超长传值问题
                        if (key.endsWith("visitid")) {
                            admAfter.put(keyMap.get(key), adm.get(key).toString());
                        } else {
                            admAfter.put(keyMap.get(key), adm.get(key));
                        }

                    }
                    admListAfter.add(admAfter);
                }
            }
            admInfo.getData().setList(admListAfter);
            RdrResponseModel.RdrResponseModelData rdrResponseModelData = new RdrResponseModel.RdrResponseModelData();
            rdrResponseModelData.setCurrent(1);
            rdrResponseModelData.setEndItems(admInfo.getData().getCount());
            rdrResponseModelData.setStartItems(1);
            rdrResponseModelData.setPageSize(HxConst.KELIN_LIMIT_SIZE);
            rdrResponseModelData.setTotalPage(1);
            rdrResponseModelData.setTotal(admInfo.getData().getCount());
            rdrResponseModelData.setList(admInfo.getData().getList());
            ret.setData(rdrResponseModelData);
        } catch (Exception ex) {
            log.error("获取empi{}病例就诊信息失败", empi, ex);
            return ret;
        }

        return ret;
    }

    /**
     * 获取病例基本信息
     *
     * @param regNo
     * @param medorgcode
     * @return
     */
    @Override
    public RdrResponseModel getPatientInfo(String regNo, String medorgcode) {
        RdrResponseModel ret = new RdrResponseModel();
        try {
            if (StringUtils.isEmpty(regNo) || StringUtils.isEmpty(medorgcode)) {
                throw new BusinessException("登记号和医院编码不能为空!");
            }
            String[] regnos = {regNo};
            KelinSearchResponse patientRes = this.getPatientByRegNoAndMed(regnos, medorgcode);
            if (patientRes == null) {
                log.error("查询柯林接口异常返回为空");
                return null;
            }
            if (patientRes.getCode() == null || patientRes.getCode() != HxConst.RESPONSE_SUCCESS) {
                log.error("查询柯林接口异常--HTTP状态码:{}, 异常原因:{}", patientRes.getCode(), patientRes.getData());
                //获取不到基本信息，无Empiid
                return null;
            }
            RdrResponseModel.RdrResponseModelData rdrResponseModelData = new RdrResponseModel.RdrResponseModelData();
            rdrResponseModelData.setCurrent(1);
            rdrResponseModelData.setEndItems(patientRes.getData().getCount());
            rdrResponseModelData.setStartItems(1);
            rdrResponseModelData.setPageSize(HxConst.KELIN_LIMIT_SIZE);
            rdrResponseModelData.setTotalPage(1);
            rdrResponseModelData.setTotal(patientRes.getData().getCount());
            rdrResponseModelData.setList(patientRes.getData().getList());
            ret.setData(rdrResponseModelData);
        } catch (Exception ex) {
            log.error("获取患者登记号{}，机构{}基本信息错误", regNo, medorgcode, ex);
            return ret;
        }
        return ret;
    }

    /**
     * 批量获取指定登记号和医院编码的病例基本信息
     *
     * @param regnos 登记号列表
     * @param medorg 医疗机构编码
     * @return 基本信息列表
     */
    @Override
    public KelinSearchResponse getPatientByRegNoAndMed(String[] regnos, String medorg) {
        KelinSearchResponse res = new KelinSearchResponse();
        try {

            KelinRequestVo body = new KelinRequestVo();
            Map<String, List<Object>> params = new HashMap<>(2);

            //限制请求的登记号上限
            if (regnos.length > HxConst.KELIN_PATIENT_FETCH_SIZE) {
                throw new BusinessException("单次查询柯林接口的患者表的登记号批量上限最大为10000!");
            }

            // 根据表名查字段名
            List<RDRFieldEntity> rdrFieldEntities = rdrFieldService.getByTableName(HxConst.KELIN_PAT_TABLE);
            //医疗机构编码字段
            RDRFieldEntity medRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.MED_ORG.getType())).collect(Collectors.toList()).get(0);
            if (medRDRField == null) {
                return null;
            }
            List<String> fields = rdrFieldEntities.stream().map(RDRFieldEntity::getKlFieldName).collect(Collectors.toList());

            //构造条件
            // 医院编码
            params.put(medRDRField.getKlFieldName(), Collections.singletonList(medorg));
            // 登记号集合
            params.put(HxConst.KELIN_PAT_REGNO_FIELD, Arrays.asList(regnos));

            body.setColumns(fields)
                    .setLimit(HxConst.KELIN_LIMIT_SIZE)
                    .setOffset(1)
                    .setTableName(HxConst.KELIN_PAT_TABLE)
                    .setParams(params);

            res = rdr2HttpApiService.getKelinDatasAuto(body);
        } catch (Exception ex) {
            log.error("获取指定登记号和医院编码的病例基本信息失败{}，机构{}基本信息错误", regnos, medorg, ex);
            res.setCode(HxConst.RESPONSE_FAIL);
            res.setStatus("FAIL");
        }
        return res;
    }

    /**
     * 获取指定admIds的就诊信息
     *
     * @param admIds
     * @return
     */
    @Override
    public KelinSearchResponse getAdmByVisitId(String[] admIds) {
        KelinSearchResponse res = new KelinSearchResponse();
        try {
            KelinRequestVo body = new KelinRequestVo();
            Map<String, List<Object>> params = new HashMap<>(2);

            //限制请求的admIds上限
            if (admIds.length > HxConst.KELIN_ADM_FETCH_SIZE) {
                throw new BusinessException("单次查询柯林接口的就诊表的admIds批量上限最大为1000!");
            }
            // 根据表名查字段名
            List<RDRFieldEntity> rdrFieldEntities = rdrFieldService.getByTableName(HxConst.KELIN_ADM_TABLE);
            //患者唯一ID字段
            RDRFieldEntity admIdRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.ADM_ID.getType())).collect(Collectors.toList()).get(0);
            if (admIdRDRField == null) {
                return null;
            }
            List<String> fields = rdrFieldEntities.stream().map(RDRFieldEntity::getKlFieldName).collect(Collectors.toList());

            //构造条件
            // empiid集合
            Long[] admList = new Long[admIds.length];
            for (int i = 0; i < admIds.length; i++) {
                admList[i] = Long.valueOf(admIds[i]);
            }
            params.put(admIdRDRField.getKlFieldName(), Arrays.asList(admList));

            body.setColumns(fields)
                    .setLimit(HxConst.KELIN_LIMIT_SIZE)
                    .setOffset(1)
                    .setTableName(HxConst.KELIN_ADM_TABLE)
                    .setParams(params);

            res = rdr2HttpApiService.getKelinDatasAuto(body);
        } catch (Exception ex) {
            res.setCode(HxConst.RESPONSE_FAIL);
            res.setStatus("FAIL");
        }
        return res;
    }

    /**
     * 批量获取指定empiid的病例基本信息
     *
     * @param empiids
     * @return
     */
    @Override
    public KelinSearchResponse getPatientByEmpiids(String[] empiids, List<Object> medCodeList) {
        KelinSearchResponse res = new KelinSearchResponse();
        if (empiids == null || empiids.length == 0) {
            return res;
        }
        try {
            KelinRequestVo body = new KelinRequestVo();
            Map<String, List<Object>> params = new HashMap<>(2);

            //限制请求的empiid上限
            if (empiids.length > HxConst.KELIN_PATIENT_FETCH_SIZE) {
                throw new BusinessException("单次查询柯林接口的患者表的empiids批量上限最大为10000!");
            }
            // 根据表名查字段名
            List<RDRFieldEntity> rdrFieldEntities = rdrFieldService.getByTableName(HxConst.KELIN_PAT_TABLE);
            //患者唯一ID字段
            RDRFieldEntity empidRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.EMPID.getType())).collect(Collectors.toList()).get(0);
            if (empidRDRField == null) {
                return null;
            }
            RDRFieldEntity medRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.MED_ORG.getType())).collect(Collectors.toList()).get(0);
            if (medRDRField == null) {
                return null;
            }
            List<String> fields = rdrFieldEntities.stream().map(RDRFieldEntity::getKlFieldName).collect(Collectors.toList());

            //构造条件
            // empiid集合
            // 转换类型
            Long[] empiid = new Long[empiids.length];
            for (int i = 0; i < empiids.length; i++) {
                empiid[i] = Long.parseLong(empiids[i]);
            }
            params.put(empidRDRField.getKlFieldName(), Arrays.asList(empiid));
            params.put(medRDRField.getKlFieldName(), medCodeList);
            Map<String, String> sort = new HashMap<>();
            body.setColumns(fields)
                    .setLimit(HxConst.KELIN_LIMIT_SIZE)
                    .setOffset(1)
                    .setTableName(HxConst.KELIN_PAT_TABLE)
                    .setParams(params);

            res = rdr2HttpApiService.getKelinDatasAuto(body);
        } catch (Exception ex) {
            res.setCode(HxConst.RESPONSE_FAIL);
            res.setStatus("FAIL");
        }
        return res;
    }

    @Override
    public RdrResponseModel getTodayEmpiList(List<Object> medCodeList) {
        RdrResponseModel ret = new RdrResponseModel();
        KelinSearchResponse admInfo;
        try {
            // 根据表名查字段名
            List<RDRFieldEntity> rdrFieldEntities = rdrFieldService.getByTableName(HxConst.RDR_ADM_TABLE);
            //患者唯一ID字段
            RDRFieldEntity empidRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.EMPID.getType())).collect(Collectors.toList()).get(0);
            if (empidRDRField == null) {
                return ret;
            }

            //医疗机构编码字段
            RDRFieldEntity medRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.MED_ORG.getType())).collect(Collectors.toList()).get(0);
            if (medRDRField == null) {
                return ret;
            }

            //就诊时间字段
            RDRFieldEntity admDateRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.ADM_TIME.getType())).collect(Collectors.toList()).get(0);
            if (admDateRDRField == null) {
                return ret;
            }

            //所有字段
            List<String> fields = Arrays.asList(empidRDRField.getKlFieldName());

            //拼查询条件
            Map<String, List<Object>> params = new HashMap<>(2);
            // 华西
            params.put(medRDRField.getKlFieldName(), medCodeList);
            // todo 今日就诊，需要按起止时间过滤
            Map<String, Object> ge = new HashMap<>();
            Map<String, Object> lt = new HashMap<>();
            ge.put(admDateRDRField.getKlFieldName(), DateUtils.getNowTimeStr());
            lt.put(admDateRDRField.getKlFieldName(), DateUtils.getAfterDay(DateUtils.getNowTimeStr(), "1"));

            //接口参数
            KelinRequestVo body = new KelinRequestVo();
            Map<String, String> sort = new HashMap<>();
            sort.put(HxConst.KELIN_VISIT_TIME_FIELD, "desc");
            body.setTableName(HxConst.KELIN_ADM_TABLE)
                    .setLimit(HxConst.KELIN_LIMIT_SIZE)
                    .setOffset(1)
                    .setColumns(fields)
                    .setParams(params)
                    .setGe(ge)
                    .setLt(lt)
                    .setSortFields(sort);

            admInfo = rdr2HttpApiService.getKelinDatasAuto(body);
            if (admInfo == null) {
                log.error("查询柯林接口异常,返回为空");
                return ret;
            }
            if (admInfo.getCode() == null || admInfo.getCode() != HxConst.RESPONSE_SUCCESS) {
                log.error("查询柯林接口异常--HTTP状态码:{}, 异常原因:{}", admInfo.getCode(), admInfo.getData());
                return ret;
            }
            RdrResponseModel.RdrResponseModelData rdrResponseModelData = new RdrResponseModel.RdrResponseModelData();
            rdrResponseModelData.setCurrent(1);
            rdrResponseModelData.setEndItems(admInfo.getData().getCount());
            rdrResponseModelData.setStartItems(1);
            rdrResponseModelData.setPageSize(HxConst.KELIN_LIMIT_SIZE);
            rdrResponseModelData.setTotalPage(1);
            rdrResponseModelData.setTotal(admInfo.getData().getCount());
            rdrResponseModelData.setList(admInfo.getData().getList());
            ret.setData(rdrResponseModelData);
        } catch (Exception ex) {
            log.error("获取机构列表{}今日empi信息失败", medCodeList, ex);
            return ret;
        }

        return ret;
    }

    /**
     * 批量获取指定身份证号的病例基本信息
     *
     * @param idCards 身份证号
     * @return 基本信息列表
     */
    public KelinSearchResponse getPatientInfoByIdCard(String[] idCards, String[] medCodes) {
        KelinSearchResponse res = new KelinSearchResponse();
        try {
            if (idCards == null || idCards.length == 0) {
                return null;
            }
            if (medCodes == null || medCodes.length == 0) {
                return null;
            }
            KelinRequestVo body = new KelinRequestVo();
            Map<String, List<Object>> params = new HashMap<>(2);

            //限制请求的登记号上限
            if (idCards.length > HxConst.KELIN_PATIENT_FETCH_SIZE) {
                throw new BusinessException("单次查询柯林接口的患者表的身份证号批量上限最大为10000!");
            }

            // 根据表名查字段名
            List<RDRFieldEntity> rdrFieldEntities = rdrFieldService.getByTableName(HxConst.KELIN_PAT_TABLE);
            //医疗机构编码字段
            RDRFieldEntity medRDRField = rdrFieldEntities.stream().filter(a -> a.getFieldExplain().equals(RDRFieldExplainEnum.MED_ORG.getType())).collect(Collectors.toList()).get(0);
            if (medRDRField == null) {
                return null;
            }
            List<String> fields = rdrFieldEntities.stream().map(RDRFieldEntity::getKlFieldName).collect(Collectors.toList());

            //构造条件
            // 身份证号集合
            params.put(HxConst.KELIN_PAT_ID_CARD_FIELD, Arrays.asList(idCards));
            //机构
            params.put(medRDRField.getKlFieldName(), Arrays.asList(medCodes));

            body.setColumns(fields)
                    .setLimit(HxConst.KELIN_LIMIT_SIZE)
                    .setOffset(1)
                    .setTableName(HxConst.KELIN_PAT_TABLE)
                    .setParams(params);

            res = rdr2HttpApiService.getKelinDatasAuto(body);
        } catch (Exception ex) {
            res.setCode(HxConst.RESPONSE_FAIL);
            res.setStatus("FAIL");
        }
        return res;
    }

}
