package com.boot.modules.cas.vo;


import lombok.Data;

/**
 * 推送用户信息时包含的对象
 *
 * <AUTHOR>
 */
@Data
public class MultiGroup {
    /**
     * 所属医院名称
     */
    private String hospital;
    /**
     * 所属医院编码
     */
    private String hospital_code;
    /**
     * 所属科室名称
     */
    private String department;
    /**
     * 所属科室编码
     */
    private String department_code;
    /**
     * 关联信息
     */
    private String group_chain_info;
    /**
     * 工号
     */
    private String gx_number;
    /**
     * 职位
     */
    private String position;
    /**
     * 合作伙伴角色
     */
    private PartinerRoles partiner_roles;

    @Data
    public static class PartinerRoles {
        /**
         * 角色id
         */
        private String id;
        /**
         * 角色标签
         */
        private String label;
    }
}
