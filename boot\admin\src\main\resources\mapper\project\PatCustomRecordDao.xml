<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.PatCustomRecordDao">

    <select id="getDetailInfo" resultType="com.boot.modules.mobile.model.PatCustomDetailModel">
        select
        a.group_config_id,
        f.name as patient<PERSON>ame,
        f.gender,
        e.name as medName,
        c.name as subProjectName,
        d.nickname as doctor<PERSON><PERSON>,
        b.recommend_time,
        f.tel as phone,
        f.empiid,
        f.src_empiid,
        f.regno,
        f.birthday,
        b.content
        from
        rp_pat_custom_record a
        left join rp_doctor_recommendation b on
        a.empiid = b.empiid
        left join rp_sub_project c on
        b.sub_project_id = c.id
        left join rp_sys_user d on
        b.recommend_id = d.id
        left join rp_sys_dept e on
        b.recommend_dept_id = e.id
        left join rp_patient f on
        a.empiid = f.empiid
        ${ew.customSqlSegment}
    </select>
</mapper>