package com.boot.modules.external.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 返回结构
 * <AUTHOR>
 */
@Data
public class RdrResponseModel {

    private static final long serialVersionUID = 1L;

    /**
     * http状态码
     */
    private Integer code;

    /**
     * 响应数据
     */
    private RdrResponseModelData data;

    /**
     * 请求小写
     */
    private String message;

    /**
     * 数据
     */
    @Data
    public static class RdrResponseModelData {
        /**
         * 返回的数据列表总数
         */
        private Integer total;
        /**
         * 返回的数据列表总数
         */
        private Integer pageSize;
        /**
         * 返回的数据列表总数
         */
        private Integer totalPage;
        /**
         * 返回的数据列表总数
         */
        private Integer startItems;
        /**
         * 返回的数据列表总数
         */
        private Integer endItems;
        /**
         * 返回的数据列表总数
         */
        private Integer current;

        /**
         * 返回的数据列表
         */
        private List<Map<String, Object>> list;
    }
}
