package com.boot.commons.enums;

/**
 * <AUTHOR>
 * @desc : 角色级别枚举
 * @create 2021-03-26
 */
public enum RoleLevelEnum {

    /**
     * 系统级
     */
    SYSTEM(1,"系统级"),

    /**
     * 项目级
     */
    PROJECT(2,"项目级")

    ;
    private Integer level;

    private String desc;

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    RoleLevelEnum(Integer level, String desc) {
        this.level = level;
        this.desc = desc;
    }

    public static RoleLevelEnum getByLevel(Integer level) {
        if (level != null) {
            for (RoleLevelEnum roleLevelEnum : RoleLevelEnum.values()) {
                if (roleLevelEnum.getLevel().equals(level) ) {
                    return roleLevelEnum;
                }
            }
        }
        return null;
    }


}
