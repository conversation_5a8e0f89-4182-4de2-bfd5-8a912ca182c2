package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.*;
import com.boot.modules.sys.dao.SysIpFilterDao;
import com.boot.modules.sys.entity.SysIpFilterEntity;
import com.boot.modules.sys.service.SysIpFilterService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 白名单过滤服务
 *
 * <AUTHOR>
 */
@Service
public class SysIpFilterServiceImpl extends ServiceImpl<SysIpFilterDao, SysIpFilterEntity> implements SysIpFilterService {
    /**
     * 判断是否白名单
     *
     * @param ip
     * @return
     */
    @Override
    public Boolean isWhiteList(String ip) {

        List<SysIpFilterEntity> list = this.list(new QueryWrapper<SysIpFilterEntity>()
                .lambda().eq(SysIpFilterEntity::getMark, 0)
        );

        if (!CollectionUtils.isEmpty(list)) {
            List<String> ips = list.stream().map(SysIpFilterEntity::getIp).collect(Collectors.toList());
            if(ips.size()>0){
                return IPUtils.IPMatch(ips, ip);
            }
            return false;
        }

        return false;
    }


    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String condition = MapUtils.getValue(params, "condition", String.class);
        IPage<SysIpFilterEntity> page = this.page(
                new Query<SysIpFilterEntity>().getPage(params),
                new QueryWrapper<SysIpFilterEntity>()
                        .lambda()
                        .like(StringUtils.isNotBlank(condition), SysIpFilterEntity::getIp, condition)
        );

        return new PageUtils(page);
    }
}
