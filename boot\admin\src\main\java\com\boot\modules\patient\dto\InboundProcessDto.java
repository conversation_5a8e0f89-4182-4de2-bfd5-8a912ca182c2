package com.boot.modules.patient.dto;


import com.boot.modules.patient.entity.InboundProcessEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 前端获取受试者信息返回
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InboundProcessDto extends InboundProcessEntity {

    /**
     * '医生推荐表id'
     */
    private Long doctorRecommendId;

    /**
     * '推荐医生id'
     */
    private Long recommendId;

    /**
     * '推荐医生名'
     */
    private String recommendName;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 病种名
     */
    private String diseaseName;

    /**
     * 推荐状态 :0:已锁定；1已推荐；2退出
     */
    private Integer recommendStatus;
    /**
     * '推荐理由'
     */
    private String recommendReason;

    /**
     * '推荐时间'
     */
    private String recommendTime;

    /**
     * 已读未读
     */
    private Integer isRead;

    /**
     * 签署知情同意书审核用户名
     */
    private String signingAuditName;

    /**
     * 入职医生名
     */
    private String inboundName;

    private String name;

    private String regno;

    private int gender;

    private String birthday;

    private String idCard;

    private String initials;

    private String tel;

    /**
     * 推荐类型 0-系统推荐 1-手动推荐
     */
    private Integer type;

    /**
     * 是否同步 1-未同步 2-同步成功 3-同步失败
     */
    private int syncStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 推荐用户的医疗机构Id
     */
    private Long recommendDeptId;

    /**
     * 推荐用户的医疗机构名称
     */
    private String recommendHospitalName;

    /**
     * 子项目名
     */
    private String subProjectName;

    /**
     * 民族
     */
    private String nation;

    //归一后empi
    private String srcEmpiid;

    /**
     * CRC信息列 (需展示推荐项目对应的CRC的姓名和电话号码，多个CRC用分隔符展示)
     */
    private String crc;

    /**
     * PI
     */
    private String projectAdminId;

    /**
     * PI名称
     */
    private String projectAdminName;

    /**
     * 推荐医生科室
     */
    private String recommendDeptName;

    /**
     * 临床科室，多中心时只展示主中心
     */
    private String projectDeptName;
}
