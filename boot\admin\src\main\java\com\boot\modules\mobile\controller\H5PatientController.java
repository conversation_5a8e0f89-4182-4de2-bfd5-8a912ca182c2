package com.boot.modules.mobile.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.SexMapUtil;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.mobile.dto.PushHytDto;
import com.boot.modules.mobile.service.PushHytService;
import com.boot.modules.mobile.vo.PatientGroupDetailVO;
import com.boot.modules.patient.dto.ExitDto;
import com.boot.modules.patient.dto.RecommendDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.service.PatCustomRecordService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysUserDeptSpecialEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserDeptSpecialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.boot.commons.enums.PatTypeEnum.REFUSE;

/**
 * 移动端患者调用接口
 */
@Api(tags = "移动端患者调用接口")
@RestController
@RequestMapping("/h5/patient")
@Slf4j
public class H5PatientController extends AbstractController {

    @Resource
    private PatCustomRecordService patCustomRecordService;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysUserDeptSpecialService sysUserDeptSpecialService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private PushHytService pushHytService;

    @ApiOperation(value = "患者纳排明细情况", notes = "患者纳排明细情况")
    @GetMapping({"/detail"})
    public Result detail(@RequestParam String empi, @RequestParam Long subProjectId) {

        PatientGroupDetailVO vo = patCustomRecordService.getDetail(empi, subProjectId);
        vo.setGender(SexMapUtil.getIcoSex(vo.getGender()));
        return R.success(vo);
    }

    @ApiOperation(value = "受试者移动端推荐", notes = "受试者移动端推荐")
    @PostMapping({"/recommend"})
    public Result recommend(@RequestBody MobilePatientDTO mobilePatientDTO) {

        //1.校验前端参数
        Long projectId = validAndReturnProjectId(mobilePatientDTO);

        RecommendDto recommendDto = mobilePatientDTO.getRecommendDto();
        Long userId = this.getUserId();
        Long subProjectId = mobilePatientDTO.getSubProjectId();

        //校验参数判断是否能推送
        PushHytDto pushHytDto = pushHytService.valid(mobilePatientDTO, userId, projectId);

        if (!StringUtils.isEmpty(recommendDto.getHisDeptCode())) {
            Long deptId = 0L;
            // 特殊处理-胰腺炎医疗中心门诊医生工作量归入胰腺炎医疗中心
            List<SysUserDeptSpecialEntity> specialEntityList = sysUserDeptSpecialService.list(new QueryWrapper<SysUserDeptSpecialEntity>().lambda()
                    .eq(SysUserDeptSpecialEntity::getUserId, userId)
                    .eq(SysUserDeptSpecialEntity::getHisDeptCode, recommendDto.getHisDeptCode()));
            if (!CollectionUtils.isEmpty(specialEntityList)) {
                deptId = specialEntityList.get(0).getDeptId();
            } else {
                deptId = sysDeptService.getIdByCode(recommendDto.getHisDeptCode());
            }
            recommendDto.setRecommendDeptId(deptId);
        }
        //移动端后端拼上dto参数
        //拼上子项目参数信息
        List<RecommendDto.SubProjectInfo> subProjectInfos = new ArrayList<>();
        RecommendDto.SubProjectInfo subProjectInfo = new RecommendDto.SubProjectInfo();
        subProjectInfo.setSubProjectId(subProjectId);
        subProjectInfo.setProjectId(projectId);
        subProjectInfo.setSort(0);
        //移动端推荐
        subProjectInfo.setType(2);
        subProjectInfos.add(subProjectInfo);
        recommendDto.setSubProjectList(subProjectInfos);

        //重新转下性别，医联体前端和受试者后端用的不一致,医联体 1-男 2-女 其他-其他值
        Integer gender = mobilePatientDTO.getGender();
        Integer rpGender = SexMapUtil.getRpSex(gender);
        mobilePatientDTO.setGender(rpGender);

        //推荐
        String empiid = doctorRecommendationService.recommendationByMobile(mobilePatientDTO, this.getUserId(), 2);

        //设置empi
        if (StringUtils.isEmpty(empiid)) {
            throw new BusinessException("患者推荐失败");
        }
        pushHytDto.setEmpi(empiid);
        //异步执行通知华易通
        pushHytService.savePathwayPatient(pushHytDto, mobilePatientDTO);

        return R.success();
    }

    private Long validAndReturnProjectId(MobilePatientDTO mobilePatientDTO) {
        if (mobilePatientDTO == null) {
            throw new BusinessException("读取患者参数为空");
        }

        if (StringUtils.isEmpty(mobilePatientDTO.getMedCode())) {
            throw new BusinessException("所属医疗机构为空");
        }

        if (StringUtils.isEmpty(mobilePatientDTO.getName())) {
            throw new BusinessException("姓名为空");
        }

        if (StringUtils.isEmpty(mobilePatientDTO.getIdCard())) {
            throw new BusinessException("身份证为空");
        }

        if (mobilePatientDTO.getGender() == null) {
            throw new BusinessException("性别为空");
        }

        if (StringUtils.isEmpty(mobilePatientDTO.getNation())) {
            throw new BusinessException("所属民族为空");
        }

        if (StringUtils.isEmpty(mobilePatientDTO.getPhone())) {
            throw new BusinessException("联系方式为空");
        }

        RecommendDto recommendDto = mobilePatientDTO.getRecommendDto();

        if (recommendDto == null) {
            throw new BusinessException("推荐参数为空");
        }

        Long userId = this.getUserId();
        if (userId == null) {
            throw new BusinessException("当前用户为空");
        }

        Long subProjectId = mobilePatientDTO.getSubProjectId();
        if (subProjectId == null) {
            throw new BusinessException("子项目不能为空");
        }

        SubProjectEntity subProject = subProjectService.getById(subProjectId);
        if (subProject == null) {
            throw new BusinessException("子项目不存在");
        }

        if (CollectionUtils.isEmpty(recommendDto.getGroupConfigIds())) {
            throw new BusinessException("命中的纳排情况不能为空");
        }
        return subProject.getProjectId();
    }

    @ApiOperation(value = "接收受试者", notes = "接收受试者")
    @PostMapping("/input/{id}/{empi}/{projectId}/{subProjectId}")
    public Result inbound(@PathVariable Long id,
                          @PathVariable String empi,
                          @PathVariable Long projectId,
                          @PathVariable Long subProjectId) {
        Long userId = this.getUserId();
        if (userId == null) {
            throw new BusinessException("当前用户为空");
        }
        inboundProcessService.inbound(id, empi, projectId, subProjectId, userId);
        return R.success();
    }

    @ApiOperation(value = "入组受试者", notes = "入组受试者")
    @PostMapping("/sign/{id}/{empi}/{subProjectId}")
    public Result sign(@PathVariable Long id,
                       @PathVariable String empi,
                       @PathVariable Long subProjectId,
                       @RequestBody Map<String, Object> params) {
        String date = MapUtils.getValue(params, "signingTime", String.class);
        inboundProcessService.sign(id, empi, subProjectId, date, this.getUserId());
        return R.success();
    }


    @ApiOperation(value = "拟入组-受试者驳回", notes = "拟入组-受试者驳回")
    @PostMapping("/inbound-refuse")
    public Result inboundRefuse(@RequestBody ExitDto exitDto) {
        inboundProcessService.refuse(exitDto);
        return R.success();
    }

    @ApiOperation(value = "已推荐-受试者驳回", notes = "已推荐-受试者驳回")
    @PostMapping("/recommend-refuse")
    public Result recommendRefuse(@RequestBody ExitDto exitDto) {
        // 更改推荐表中所有相关推荐的状态
        doctorRecommendationService.update(new UpdateWrapper<DoctorRecommendationEntity>().lambda()
                .eq(DoctorRecommendationEntity::getEmpiid, exitDto.getEmpi())
                .eq(DoctorRecommendationEntity::getSubProjectId, exitDto.getSubProjectId())
                .set(DoctorRecommendationEntity::getStatus, REFUSE.getCode())
                .set(DoctorRecommendationEntity::getIsRead, 0)
                .set(DoctorRecommendationEntity::getRefuseReason, exitDto.getReason()));
        return R.success();
    }

}
