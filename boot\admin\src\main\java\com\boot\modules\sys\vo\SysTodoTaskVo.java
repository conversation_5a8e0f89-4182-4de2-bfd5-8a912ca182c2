package com.boot.modules.sys.vo;

import com.boot.modules.sys.entity.SysTodoTaskEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 代办业务VO
 *
 * <AUTHOR>
 * @desc : 代办业务以及代办业务数量VO对象
 * @create 2021-05-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SysTodoTaskVo extends SysTodoTaskEntity {
    /**
     * 代办业务
     */
    private String busCodeDesc;
    /**
     * 代办业务数量
     */
    private Long countBusCode;

}
