package com.boot.modules.openApi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.openApi.constants.EdcConst;
import com.boot.modules.openApi.entity.EdcReqLogEntity;
import com.boot.modules.openApi.model.EdcResModel;
import com.boot.modules.openApi.service.EdcReqLogService;
import com.boot.modules.openApi.service.EdcService;
import com.boot.modules.openApi.utils.AesUtil;
import com.boot.modules.openApi.utils.PooledHttpApiClient;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.sys.entity.SysUserHisEntity;
import com.boot.modules.sys.service.SysUserHisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EdcServiceImpl implements EdcService {
    private final static Logger log = LoggerFactory.getLogger(EdcService.class);

    private static volatile PooledHttpApiClient client;

    @Resource
    private SysUserHisService userHisService;

    @Resource
    private EdcReqLogService edcReqLogService;

    @Resource
    private ProjectService projectService;

    @Value("${boot.external.edc-url:http://127.0.0.1:8085/boot-admin}")
    private String edcBaseUrl;

    private static Map<String, String> urlNameMap = new HashMap<>();

    static {
        urlNameMap.put(EdcConst.INTO_GROUP_URL, "入组病例");
        urlNameMap.put(EdcConst.ADD_PROJECT_URL, "新增项目");
    }

    public EdcServiceImpl() {
        if (client == null) {
            synchronized (PooledHttpApiClient.class) {
                if (client == null) {
                    client = new PooledHttpApiClient();
                }
            }
        }
    }


    @Override
    public void intoPatient(String empiId, Long projectId, Long edcProjectId, Long userId) {
        intoPatient(empiId, null, projectId, edcProjectId, userId);
    }

    @Override
    public void intoPatient(String empiId, String regno, Long projectId, Long edcProjectId, Long userId) {
        // 1.获取用户信息
        List<SysUserHisEntity> list = userHisService.list(new QueryWrapper<SysUserHisEntity>().lambda().eq(SysUserHisEntity::getUserId, userId));

        EdcReqLogEntity logEntity = new EdcReqLogEntity();
        logEntity.setProjectId(projectId);
        logEntity.setUrl(EdcConst.INTO_GROUP_URL);
        logEntity.setName(urlNameMap.get(EdcConst.INTO_GROUP_URL));
        logEntity.setStatus(-1);
        logEntity.setType(2);
        logEntity.setEmpiid(empiId);
        if (CollectionUtils.isEmpty(list)) {
            logEntity.setStatus(0);
            logEntity.setErrMessage("用户" + userId + "未绑定his编码");
            edcReqLogService.save(logEntity);
            return;
        } else {
            edcReqLogService.save(logEntity);
        }

        // 2.设置请求参数
        SysUserHisEntity sysUserHisEntity = list.get(0);
        Map<String, Object> params = new HashMap<>();
        params.put("requestId", logEntity.getId());
        params.put("intoUser", sysUserHisEntity.getHisForeignId());
        params.put("empiId", empiId);
        params.put("regno", regno);
        params.put("projectId", edcProjectId);
        Object data = getData(EdcConst.INTO_GROUP_URL, params, null);

        // 3.记录日志
        if (data instanceof String && !StringUtils.isEmpty((String) data)) {
            logEntity.setStatus(0);
            logEntity.setErrMessage((String) data);
        }

        edcReqLogService.updateById(logEntity);
    }

    @Override
    public void addProject(ProjectVo projectVo) {
        // 1.设置项目基本信息
        projectVo.setType(4);
        projectVo.setStatus(1);
        Integer projectType = projectVo.getProjectType();

        if (projectType != null) {
            switch (ProjectStudyTypeEnum.getByCode(projectType)) {
                case GCP:
                    projectVo.setSource(2);
                    break;
                case INTERVENTION_IIT:
                    projectVo.setSource(3);
                    break;
                case SPECIALIZED_DISEASE_QUEUE:
                    projectVo.setSource(4);
                    break;
                case PROSPECTIVE_OBSERVATION:
                    projectVo.setSource(5);
                    break;
                default:
                    break;
            }
        }
        // 2.获取用户信息
        Long projectAdminId = projectVo.getProjectAdminId();
        List<SysUserHisEntity> list = userHisService.list(new QueryWrapper<SysUserHisEntity>().lambda().eq(SysUserHisEntity::getUserId, projectAdminId));

        EdcReqLogEntity logEntity = new EdcReqLogEntity();
        logEntity.setProjectId(projectVo.getId());
        logEntity.setUrl(EdcConst.ADD_PROJECT_URL);
        logEntity.setName(urlNameMap.get(EdcConst.ADD_PROJECT_URL));
        logEntity.setStatus(-1);
        logEntity.setType(1);

        if (CollectionUtils.isEmpty(list)) {
            logEntity.setStatus(0);
            logEntity.setErrMessage("项目管理员" + projectVo.getProjectAdminName() + "用户未绑定his编码");
            edcReqLogService.save(logEntity);
            return;
        } else {
            edcReqLogService.save(logEntity);
        }

        // 3.设置请求参数
        SysUserHisEntity sysUserHisEntity = list.get(0);
        Map<String, Object> params = new HashMap<>();
        params.put("requestId", logEntity.getId());
        params.put("adminUser", sysUserHisEntity.getHisForeignId());
        params.put("project", projectVo);
        Object data = getData(EdcConst.ADD_PROJECT_URL, params, null);

        // 4.记录日志
        if (data instanceof String && !StringUtils.isEmpty((String) data)) {
            logEntity.setStatus(0);
            logEntity.setErrMessage((String) data);
            edcReqLogService.save(logEntity);
        }
    }

    /**
     * 获取请求结果
     *
     * @param url    请求地址
     * @param params
     * @return
     */
    private Object getData(String url, Map<String, Object> params, List<String> pathList) {
        // 1.设置验证sign: 时间戳 appId
        String unEncryptSign = System.currentTimeMillis() + BootAdminProperties.appId;
        String sign = AesUtil.encrypt(unEncryptSign);
        params.put("sign", sign);
        // 2.设置路径参数
        if (!CollectionUtils.isEmpty(pathList)) {
            for (String path : pathList) {
                url += "/" + path;
            }
        }
        // 3.发送请求
        String resultString = client.doPost(edcBaseUrl + url, JSON.toJSONString(params));

        // 4.获取请求结果，打印错误信息
        if (!StringUtils.isEmpty(resultString)) {
            EdcResModel result;

            try {
                result = JSONObject.parseObject(resultString, EdcResModel.class);
            } catch (Exception e) {
                return resultString;
            }

            if (result != null && "00000".equals(result.getCode())) {
                return result.getData();
            } else {
                log.error(urlNameMap.get(url) + "请求失败，错误信息：" + result.getMessage());
                return result.getMessage();
            }
        } else {
            return resultString;
        }
    }
}
