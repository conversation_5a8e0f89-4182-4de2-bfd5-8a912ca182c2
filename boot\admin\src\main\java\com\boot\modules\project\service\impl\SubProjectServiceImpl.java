package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.annotation.TuoMin;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.mobile.dao.PushHytDao;
import com.boot.modules.mobile.entity.PushHytEntity;
import com.boot.modules.mobile.vo.SubProjectMobileVO;
import com.boot.modules.project.dao.SubProjectDao;
import com.boot.modules.project.entity.*;
import com.boot.modules.project.enums.ProjectTypeEnum;
import com.boot.modules.project.service.*;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysRoleService;
import com.boot.modules.sys.service.SysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc :子项目接口实现类
 * @create 2021-03-18
 */
@Service
public class SubProjectServiceImpl extends ServiceImpl<SubProjectDao, SubProjectEntity> implements SubProjectService {

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectCenterConfigService centerConfigService;

    @Resource
    private ProjDeptService projDeptService;

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private ProjectSettingService projectSettingService;

    @Resource
    private ProjectRoleSettingService projectRoleSettingService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private PushHytDao pushHytDao;

    /**
     * 查询当前用户在指定项目的所有子项目，如果是项目管理员，获取当前项目下全部子项目
     *
     * @param projectId
     * @param userId
     * @return
     */
    @Override
    public List<SubProjectVo> getByProjectId(Long projectId, Long userId) {

        List<SubProjectVo> res;

        // 获取当前项目信息
        ProjectEntity curProject = null;
        if (projectId != null) {
            // 获取当前项目信息
            curProject = projectService.getById(projectId);
        }
        QueryWrapper<SubProjectVo> qw = new QueryWrapper<>();
        if (userId != null && curProject != null && userId.equals(curProject.getProjectAdminId())) {
            // 项目管理员,获取全部子项目
            qw.lambda().eq(SubProjectEntity::getProjectId, projectId);
        } else {
            // 自己有权限的项目
            qw.eq(projectId != null, "a.project_id", projectId)
                    .and(userId != null && userId != 0L, q ->
                            q.eq("b.user_id", userId)
                                    .or()
                                    .eq("d.create_user_id", userId)
                                    .or()
                                    .eq("d.project_admin_id", userId)
                    );
        }
        res = this.baseMapper.getDetailsByProjectId(qw);
//        for (SubProjectVo re : res) {
//            List<ProjectPatientVo> patSubProjectList = patSubProjectService.getBySubProjectId(re.getId(), re.getProjectId(), userId);
//            if (!CollectionUtils.isEmpty(patSubProjectList)) {
//                re.setPatCount(patSubProjectList.size());
//            }
//        }
        return res;
    }

    @Override
    public List<SubProjectVo> getByProjectId(Long projectId, Long userId, QueryWrapper<SubProjectVo> qw) {
        if (qw == null) {
            return getByProjectId(projectId, userId);
        }
        List<SubProjectVo> res;

        // 获取当前项目信息
        ProjectEntity curProject = null;
        if (projectId != null) {
            // 获取当前项目信息
            curProject = projectService.getById(projectId);
        }

        if (userId != null && curProject != null && userId.equals(curProject.getProjectAdminId())) {
            // 项目管理员,获取全部子项目
            qw.lambda().eq(SubProjectEntity::getProjectId, projectId);
        } else {
            // 自己有权限的项目
            qw.eq(projectId != null, "a.project_id", projectId)
                    .and(userId != null && userId != 0L, q ->
                            q.eq("b.user_id", userId)
                                    .or()
                                    .eq("d.create_user_id", userId)
                                    .or()
                                    .eq("d.project_admin_id", userId)
                    );
        }
        res = this.baseMapper.getDetailsByProjectId(qw);
        return res;
    }

    /**
     * 查询当前项目的所有子项目
     *
     * @param userId
     * @return
     */
    @Override
    public List<SubProjectVo> getByQuery(Long projectId, Long userId) {
        List<SubProjectVo> res;
        QueryWrapper<SubProjectVo> qw = new QueryWrapper<>();

        //超级管理员返回当前项目下的全部子项目
        if (userId != null && userId != 0L) {
            qw.eq("c.user_id", userId);
        }
        qw.eq("a.project_id", projectId);
        //当是项目管理员的时候，返回所有的子项目

        res = baseMapper.getByProjectId(qw);
        res = res.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(SubProjectEntity::getId))), ArrayList::new)
        );

        return res;
    }

    /**
     * 获取当前登录用户的所有enabled为1的子项目信息,也获取该用户角色为项目管理员的子项目信息
     *
     * @param userId
     * @return
     */
    @Override
    public List<SubProjectVo> getByUserId(Long userId) {
        QueryWrapper<SubProjectVo> qw1 = new QueryWrapper<>();
        qw1.eq("a.user_id", userId)
                .eq("b.enabled", 1);
        // 1.获取指定用户的子项目信息
        List<SubProjectVo> subProjectVos = this.baseMapper.getUserSubPrj(qw1);
        // cx 项目管理员也记录到成员管理表
//        // 2.获取指定用户为项目管理员的子项目信息
//        QueryWrapper<SubProjectVo> qw2 = new QueryWrapper<>();
//        qw2.eq("b.project_admin_id", userId)
//                .eq("b.status", 1);
//        // 合并子项目信息
//        ProjectRoleInfoVo projectRoleInfoVo;
//        SysRoleEntity prjManagerRole = sysRoleService.lambdaQuery().eq(SysRoleEntity::getIsProjAdmin, 1).one();
//        List<SubProjectVo> subProjManages = this.baseMapper.getSubProjManage(qw2);
//        if (!CollectionUtils.isEmpty(subProjManages)) {
//            for (SubProjectVo subProjManage : subProjManages) {
//                projectRoleInfoVo = new ProjectRoleInfoVo();
//                projectRoleInfoVo.setProjectAdmin(true);
//                projectRoleInfoVo.setRoleName(prjManagerRole.getRoleName());
//                projectRoleInfoVo.setRoleId(prjManagerRole.getId());
//                subProjManage.setRoleInfo(projectRoleInfoVo);
//            }
//        }
//        subProjectVos.addAll(subProjManages);
        return subProjectVos;
    }

    /**
     * 新增子项目 也往项目业务表中加数据
     *
     * @param researchGroup
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(SubProjectEntity researchGroup, ProjectEntity project) {

        if (researchGroup != null) {
            //1.判断是否超过了项目的子项目个数
            if (project == null) {
                project = projectService.getById(researchGroup.getProjectId());
            }
            //判断是否是多病钟 如果是多病例 需判断是否超过数量
            if (project != null && project.getId() != null) {
                // 默认1
                researchGroup.setEnabled(1);
                if (project.getType().equals(ProjectTypeEnum.MULTIPLE_DISEASE.getType())) {
                    //如果是多病种
                    Integer subProjCount = project.getSubProjectCount();

                    int size = this.count(
                            new QueryWrapper<SubProjectEntity>()
                                    .lambda().eq(SubProjectEntity::getProjectId, project.getId())
                    );

                    if (size + 1 > subProjCount) {
                        //审核中
                        researchGroup.setEnabled(0);
                    }
                }

                boolean save = this.save(researchGroup);

                //新增项目设置数据
                ProjectSettingEntity projectSettingEntity = new ProjectSettingEntity();
                projectSettingEntity.setSubProjectId(researchGroup.getId());
                projectSettingEntity.setStatus(0);
                projectSettingEntity.setSetting("");
                save = save && projectSettingService.add(projectSettingEntity);
                //新增中心默认字段
                List<ProjDeptEntity> projDeptEntityList = projDeptService.getByProjectId(researchGroup.getProjectId());
                save = save && centerConfigService.autoSaveDefaultFields(researchGroup.getId(), projDeptEntityList);
                if (!save) {
                    throw new BusinessException("新增失败");
                }
            }
        }

        return true;
    }

    /**
     * 批量删除指定id子项目
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteBatch(List<Long> ids) {
        boolean ret = true;
        if (CollectionUtils.isEmpty(ids)) {
            return ret;
        }

        // 2、删除子项目
        ret = ret && removeByIds(ids);

        // 删除子项目配置
        QueryWrapper<ProjectRoleSettingEntity> roleSettingEntityQueryWrapper = new QueryWrapper<>();
        roleSettingEntityQueryWrapper.lambda().in(ProjectRoleSettingEntity::getSubProjectId, ids);
        projectRoleSettingService.remove(roleSettingEntityQueryWrapper);

        if (!ret) {
            throw new BusinessException("删除子项目失败");
        }
        return ret;
    }

    @TuoMin(fieldType = "searchAndExport", returnDateType = List.class, returnObjectType = Map.class)
    @Override
    public List<Map<String, Object>> tuominForSearch(Map<String, Object> paramMap, List<Map<String, Object>> patientData) {
        return patientData;
    }

    /**
     * 修改私密公开(功能改造)
     * chenwei 2022/10/24
     */
    @Override
    public boolean updateVisual(Long subProjectId) {
        QueryWrapper<SubProjectEntity> qw = new QueryWrapper<>();
        qw.eq("id", subProjectId);
        List<SubProjectEntity> subProjectEntityList = this.list(qw);
        if (!CollectionUtils.isEmpty(subProjectEntityList)) {
            SubProjectEntity subProjectEntity = subProjectEntityList.get(0);
            boolean isPrivate = !subProjectEntity.getIsPrivate();
            subProjectEntity.setIsPrivate(isPrivate);
            return updateById(subProjectEntity);
        }
        return true;
    }

    @Override
    public List<SysUserEntity> getUserBySubProjectId(Long projectId, Long subProjectId) {
        List<Long> userIdList = new ArrayList<>();
        // 项目管理员
//        userIdList.add(projectService.getById(projectId).getProjectAdminId());
        // 项目组成员
        List<UserSubProjectEntity> list = userSubProjectService.list(new QueryWrapper<UserSubProjectEntity>().lambda()
                .eq(UserSubProjectEntity::getSubProjectId, subProjectId));
        userIdList.addAll(list.stream().map(UserSubProjectEntity::getUserId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return sysUserService.list(new QueryWrapper<SysUserEntity>().lambda().in(SysUserEntity::getId, userIdList));
    }

    @Override
    public PageUtils getSubjectListByQuery(Map<String, Object> param, Integer projectType) {
        IPage<SubProjectMobileVO> result = null;
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        String query = MapUtils.getValue(param, "query", String.class);
        List<Long> projectIds = MapUtils.getValue(param, "projectIds", ArrayList.class);
        QueryWrapper<Object> qw = new QueryWrapper<>();
        // 开启了医联体的项目
        qw.eq("b.enable_med_const", 1);
        //开启且未停用的项目
        qw.eq("a.enabled", 1);
        qw.ne("a.status", 2);
        //项目在研
        qw.eq("b.status", 1);

        if (StringUtils.isNotBlank(query)) {
            qw.and(andQw -> {
                andQw.like("a.name", query) //子项目名称
                        .or()
                        .like("c.nickname", query)//PI名称
                        .or()
                        .like("d.disease_name", query); //病种名
            });
        }
        //过滤项目类型
        qw.eq(projectType != null, "b.project_type", projectType);
        IPage<Object> pageFilter = new Query<>().getPage(param);
        Integer isHis = MapUtils.getValue(param, "isHis", Integer.class);
        if (isHis != null && isHis == 1) {
            // 过滤历史项目
            if (CollectionUtils.isEmpty(projectIds)) {
                return null;
            }
            qw.in("b.id", projectIds);
            //历史推荐
            result = this.getBaseMapper().getListByQuery(pageFilter, qw);
            List<SubProjectMobileVO> list = result.getRecords();
            //查询参与的
            qw.eq("e.user_id", userId);
            IPage<SubProjectMobileVO> attend = this.getBaseMapper().getUserSubListByQuery(pageFilter, qw);
            List<SubProjectMobileVO> attendRecords = attend.getRecords();
            for (SubProjectMobileVO one : list) {
                for (SubProjectMobileVO attendOne : attendRecords) {
                    if (one.getId().equals(attendOne.getId())) {
                        one.setIsAttend(1);
                        break;
                    }
                }
            }
            result.setRecords(list);
            return new PageUtils(result);
        }

        if (userId == null) {
            //查询全部的
            result = this.getBaseMapper().getListByQuery(pageFilter, qw);
        } else {
            //查询参与的
            qw.eq("e.user_id", userId);
            result = this.getBaseMapper().getUserSubListByQuery(pageFilter, qw);
        }
        return new PageUtils(result);
    }

    @Override
    public PageUtils getHisRecProject(Map<String, Object> param, Integer projectType) {
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        QueryWrapper<PushHytEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(PushHytEntity::getUserId, userId)
                .select(PushHytEntity::getProjectId);
        List<PushHytEntity> list = pushHytDao.selectList(qw);
        List<Long> projectIds = list.stream().map(p -> p.getProjectId()).distinct().collect(Collectors.toList());
        param.put("projectIds", projectIds);
        return getSubjectListByQuery(param, projectType);
    }
}
