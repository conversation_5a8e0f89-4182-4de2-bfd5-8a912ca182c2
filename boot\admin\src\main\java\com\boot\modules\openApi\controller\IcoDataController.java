package com.boot.modules.openApi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.openApi.utils.AesUtil;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dto.MedPatientDto;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.service.SysDeptService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Api(tags = "医联体请求受试者外部接口")
@RestController
@RequestMapping("/open/api/ico")
public class IcoDataController {

    @Resource
    private DoctorRecommendationDao doctorRecommendationDao;

    @Resource
    private SysDeptService sysDeptService;

    @GetMapping("/get-mobile-pat")
    public Result getMobilePat(HttpServletRequest request,  @RequestParam Map<String, Object> params) {
        String sign = request.getHeader("sign");
        if (!AesUtil.checkSignForIco(sign)) {
            return R.fail("校验失败");
        }

        String medName = MapUtils.getValue(params, "medName", String.class);
        if (StringUtils.isNotBlank(medName)) {
            //先查询机构是否存在
            int count = sysDeptService.count(new LambdaQueryWrapper<SysDeptEntity>()
                    .eq(SysDeptEntity::getName, medName)
                    .eq(SysDeptEntity::getPid, 0));
            if (count == 0) {
                //为空直接返回避免错误机构数据
                return R.success(new ArrayList<>());
            }
        }

        QueryWrapper<Object> qw = new QueryWrapper<>();
        qw.eq("b.type", 2);
        qw.ne("b.status", PatTypeEnum.REFUSE.getCode());
        if (StringUtils.isNotBlank(medName)) {
            qw.eq("a.name", medName);
        }

        List<MedPatientDto> result = doctorRecommendationDao.statisticPatientByMed(qw);
        return R.success(result);
    }
}
