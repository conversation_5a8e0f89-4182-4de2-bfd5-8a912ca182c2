package com.boot.modules.patient.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.mobile.dto.ProjectReportDto;
import com.boot.modules.patient.dto.*;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.vo.InboundPatientVo;
import com.boot.modules.patient.vo.InboundProjectDieaseVo;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.project.vo.TotalProjectVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@CacheNamespace
public interface InboundProcessDao extends BaseMapper<InboundProcessEntity> {
    List<String> getEmpi();

    List<String> getEmpiByProjectId(Long subProjectId);

    List<PersonalReportDto> groupByDoctor(@Param(Constants.WRAPPER) QueryWrapper<InboundProcessEntity> qw);

    List<DepartmentReportDto> groupByDept(@Param(Constants.WRAPPER) QueryWrapper<InboundProcessEntity> qw);

    List<InboundProjectDieaseVo> getInboundProject(@Param(Constants.WRAPPER) QueryWrapper<InboundProjectDieaseVo> qw);

    List<ReportDto> groupDept(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    Integer totalInbound(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    Integer icoTotalInbound(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    List<ReportDto> groupPer(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    List<ProjectVo> groupProject(@Param(Constants.WRAPPER) QueryWrapper<ProjectVo> qw);

    List<TotalProjectVo> groupProjectType(@Param(Constants.WRAPPER) QueryWrapper<ProjectVo> qw);

    List<InboundPatientVo> getInboundPatient(@Param(Constants.WRAPPER) QueryWrapper<InboundPatientVo> qw);

    List<IcoReportDto> groupIcoInbound(@Param(Constants.WRAPPER) QueryWrapper<IcoReportDto> qw);

    List<PIReportDto> groupPIInbound(@Param(Constants.WRAPPER) QueryWrapper<PIReportDto> qw);

    IPage<ProjectReportDto> groupInbProject(IPage<ProjectReportDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<ProjectReportDto> qw);

    List<ProjectReportDto> groupInbProject(@Param(Constants.WRAPPER) QueryWrapper<ProjectReportDto> qw);
}
