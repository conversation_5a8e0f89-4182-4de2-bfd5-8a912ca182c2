!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.FormMakingGF=t():e.FormMakingGF=t()}("undefined"!=typeof self?self:this,(function(){return function(e){var t={};function i(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,i),o.l=!0,o.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)i.d(n,o,function(t){return e[t]}.bind(null,o));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s="112a")}({"0353":function(e,t,i){"use strict";var n,o,a=i("6bf8"),r=RegExp.prototype.exec,l=String.prototype.replace,s=r,u=(n=/a/,o=/b*/g,r.call(n,"a"),r.call(o,"a"),0!==n.lastIndex||0!==o.lastIndex),c=void 0!==/()??/.exec("")[1];(u||c)&&(s=function(e){var t,i,n,o,s=this;return c&&(i=new RegExp("^"+s.source+"$(?!\\s)",a.call(s))),u&&(t=s.lastIndex),n=r.call(s,e),u&&n&&(s.lastIndex=s.global?n.index+n[0].length:t),c&&n&&n.length>1&&l.call(n[0],i,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(n[o]=void 0)})),n}),e.exports=s},"0474":function(e,t,i){"use strict";i.d(t,"r",(function(){return d})),i.d(t,"b",(function(){return f})),i.d(t,"o",(function(){return m})),i.d(t,"k",(function(){return g})),i.d(t,"d",(function(){return y})),i.d(t,"e",(function(){return v})),i.d(t,"g",(function(){return w})),i.d(t,"i",(function(){return O})),i.d(t,"f",(function(){return k})),i.d(t,"h",(function(){return x})),i.d(t,"a",(function(){return M})),i.d(t,"j",(function(){return E})),i.d(t,"c",(function(){return j})),i.d(t,"l",(function(){return D})),i.d(t,"n",(function(){return C})),i.d(t,"p",(function(){return S})),i.d(t,"q",(function(){return P})),i.d(t,"m",(function(){return A}));i("ac67"),i("9f60"),i("94f0"),i("0c84"),i("2843"),i("8dee"),i("3269"),i("aa18"),i("982e"),i("d0f2"),i("25ba"),i("32ea");var n=i("44a4"),o=(i("1bc7"),i("a450"),i("4057"),i("3ce5")),a=(i("fc02"),i("6c36")),r=i("34a9");function l(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function s(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?l(Object(i),!0).forEach((function(t){Object(o.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function u(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return c(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return c(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function d(e,t){if(void 0===e)return"";var i=e;if(!t)return e;if(!e&&0!==e)return e;if("autocomplete"==t.type)return"object"==Object(a.a)(e)?e.label:e;if("radio"!=t.type&&("select"!=t.type&&"dictionary"!=t.type||t.options.multiple))if("checkbox"==t.type||("select"==t.type||"dictionary"==t.type)&&t.options.multiple)if(e&&e.length)if("object"==Object(a.a)(e[0])){var n,o=[],r=u(e);try{for(r.s();!(n=r.n()).done;){var l=n.value;"1"==l.type?(o.push("other_option"),t.options.otherOptionValue=l.value):o.push(l.value)}}catch(e){r.e(e)}finally{r.f()}i=o}else i=e;else i=[];else if("date"==t.type){if(t.options.timestamp)return e;var s=e.split(" "),c=s[0],d=s.length>1?s[1]:"12:00:00",f=c.split(/[-\/]/),p=e;switch(t.options.format){case"yyyy-MM-dd":p=f.join("-");break;case"yyyy/MM/dd":p=f.join("/");break;case"yyyy-MM-dd hh:mm:ss":p=f.join("-")+" "+d;break;case"yyyy/MM/dd hh:mm:ss":p=f.join("/")+" "+d;break;default:p=e}i=p==e?e:p}else i=e;else e?e.value||"1"==e.type?"1"==e.type?(i="other_option",t.options.otherOptionValue=e.value):i=e.value:i=e:i="";return i}function f(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e;if("autocomplete"==t.type){if(!e)return"";if("string"==typeof n){var o=t.options&&t.options.length>0?t.options:t.options.options,a=o.filter((function(e){return e.label==n}));return a.length>0?s(s({},a[0]),{},{type:"0"}):{label:n,value:"other_option",type:"1"}}return n}if("radio"!=t.type&&("select"!=t.type&&"dictionary"!=t.type||t.options.multiple))if("checkbox"==t.type||"selectMulti"==t.type||("select"==t.type||"dictionary"==t.type)&&t.options.multiple)if(e&&e.length){var r,l=[],c=u(e);try{var d=function(){var e=r.value;if("[object Object]"===Object.prototype.toString.call(e))return l.push(e),"continue";var n=(t.options&&t.options.length>0?t.options:t.options.options).filter((function(t){return i?t.label==e:t.value==e}));if(n.length>0)l.push(s(s({},n[0]),{},{type:"0"}));else if("other_option"==e){var o={value:t.options.otherOptionValue,label:t.options.otherOptionLabel,type:"1"};t.options.otherOptionScore&&(o.score=+t.options.otherOptionScore),l.push(o)}};for(c.s();!(r=c.n()).done;)d()}catch(e){c.e(e)}finally{c.f()}n=l.length>0?l:e}else n=[];else n=e;else{if("[object Object]"===Object.prototype.toString.call(e))return e;if(e)if("other_option"==e){var f={value:t.options.otherOptionValue,label:t.options.otherOptionLabel,type:"1"};t.options.otherOptionScore&&(f.score=+t.options.otherOptionScore),n=f}else{var p=t.options&&t.options.length>0?t.options:t.options.options,m=p.filter((function(t){return i?t.label==e:t.value==e}));n=m.length>0?s(s({},m[0]),{},{type:"0"}):e}else n=""}return n}function p(e){return e?"string"==typeof e?e:"1"==e.type?e.label+":"+e.value:e.label:e}function m(e,t){var i=e[t.key];if(!i)return"";t.name;var o=t.type,r=t.options;if(r||(r={}),"radio"==o||"t_nation"==o||"autocomplete"==o||"select"==o&&!r.multiple)return"string"==typeof i?i:p(i);if("checkbox"==o||"selectMulti"==o||"select"==o&&r.multiple)return"string"==typeof i?i:i.map((function(e){return p(e)})).join(",");if("object"==o){var l=s({},i),u=[];return t.properties.forEach((function(e){var i=e.key,n=e.name;void 0===l[i]&&(l[i]=""),u.push({key:"".concat(t.key,".").concat(i),name:"".concat(t.name,":").concat(n),value:l[i]})})),JSON.stringify(u)}if("array"==o){var c=[],d=Object(n.a)(i);return t.columns.forEach((function(e){var i=e.key,n=e.name,o=d.map((function(e){return e[i]||""})).join(",");c.push({key:"".concat(t.key,".").concat(i),name:"".concat(t.name,":").concat(n),value:o})})),JSON.stringify(c)}return"object"===Object(a.a)(i)?JSON.stringify(i):i}function h(e,t){return e=_(e)?"":e,t=_(t)?"":t,JSON.stringify(e)===JSON.stringify(t)||""!==e&&""!==t&&(Object(a.a)(e)===Object(a.a)(t)&&(Array.isArray(e)&&Array.isArray(t)||g(e)&&g(t)?!b.isObjectChanged(e,t):JSON.stringify(e)===JSON.stringify(t)))}function g(e){return"[object Object]"===Object.prototype.toString.call(e)}function _(e){return""===e||null==e||Array.isArray(e)&&0===e.length||"{}"===JSON.stringify(e)}var b={getDataType:function(e){var t=Object.prototype.toString.call(e).match(/\b\w+\b/g);return t.length<2?"Undefined":t[1]},iterable:function(e){return["Object","Array"].includes(this.getDataType(e))},isObjectChangedSimple:function(e,t){return JSON.stringify(e)!==JSON.stringify(s(s({},e),t))},isObjectChanged:function(e,t){var i=this;if(!this.iterable(e))throw new Error("source should be a Object or Array , but got ".concat(this.getDataType(e)));if(this.getDataType(e)!==this.getDataType(t))return!0;var n=Object.keys(e),o=Object.keys(s(s({},e),t));return n.length!==o.length||o.some((function(n){return i.iterable(e[n])?i.isObjectChanged(e[n],t[n]):e[n]!==t[n]}))}};function y(e,t,i){var o=[];return(Object.keys(e).length>0&&Object.keys(e).length>Object.keys(t).length?Object.keys(e):Object.keys(t)).forEach((function(a){var r=e[a];r||0===r||(r="");var l=t[a];l||0===l||(l="");var u=i[a];if("groups"!=u.type){if(u.isTool&&u.fields.filter((function(e){return e.key==a})).length>0){var c=u.type;(u=u.fields.filter((function(e){return e.key==a}))[0]).type=c}if(u&&!h(r,l)){var d=u,f=d.name,m=d.type,g=d.options,_=void 0===g?{}:g,b=JSON.stringify(l),v=JSON.stringify(r),w=!1;if("radio"==m||"t_nation"==m||"autocomplete"==m||"select"==m&&!_.multiple)l="string"==typeof l?l:p(l),r="string"==typeof r?r:p(r);else if("checkbox"==m||"selectMulti"==m||"select"==m&&_.multiple)Array.isArray(l)&&(l=l.map((function(e){return p(e)})).join(",")),Array.isArray(r)&&(r=r.map((function(e){return p(e)})).join(","));else if("object"==m){var O=l?s({},l):{},k=r?s({},r):{};u.properties.forEach((function(e){var t=e.key,i=e.name;void 0===O[t]&&(O[t]=""),void 0===k[t]&&(k[t]=""),O[t]!=k[t]&&o.push({key:"".concat(a,".").concat(t),name:"".concat(f,":").concat(i),newValue:O[t],oldValue:k[t],newValueRaw:b,oldValueRaw:v})})),w=!0}else if("array"==m){var x=l?Object(n.a)(l):[],M=r?Object(n.a)(r):[];u.columns.forEach((function(e){var t=e.key,i=e.name,n=x.map((function(e){return e[t]||""})).join(","),r=M.map((function(e){return e[t]||""})).join(",");n!=r&&o.push({key:"".concat(a,".").concat(t),name:"".concat(f,":").concat(i),newValue:n,oldValue:r,newValueRaw:b,oldValueRaw:v})})),w=!0}w||h(r,l)||o.push({key:a,name:f,newValue:l,oldValue:r,newValueRaw:b,oldValueRaw:v})}}else{var E=function(e,t,i,n){if(0==e.length&&0==t.length)return[];var o=i[n],a={};if(e.length>0&&t.length>0)for(var r=Math.min(e.length,t.length),l=function(n){var o=t[n];y(e[n],o,i).forEach((function(e){a[e.key]||(a[e.key]=[]),a[e.key].push(s(s({},e),{},{index:n}))}))},u=0;u<r;u++)l(u);if(t.length>e.length)for(var c=function(n){var o=t[n];if(!e[n]){y({},o,i).forEach((function(e){a[e.key]||(a[e.key]=[]),a[e.key].push(s(s({},e),{},{index:n}))}))}},d=e.length;d<t.length;d++)c(d);if(t.length<e.length)for(var f=function(n){var o=e[n];if(!t[n]){y(o,{},i).forEach((function(e){a[e.key]||(a[e.key]=[]),a[e.key].push(s(s({},e),{},{index:n}))}))}},p=t.length;p<e.length;p++)f(p);var m=[];return Object.keys(a).forEach((function(e){a[e].forEach((function(e){var t={key:n+"."+e.key+"^"+e.index,name:o.name+"."+e.name+"(第".concat(e.index+1,"次)"),newValue:e.newValue||0===e.newValue?e.newValue:"",oldValue:e.oldValue||0===e.oldValue?e.oldValue:"",newValueRaw:e.newValueRaw,oldValueRaw:e.oldValueRaw};m.push(t)}))})),m}(r||[],l||[],i,u.key);o.push.apply(o,Object(n.a)(E))}})),o}function v(e){var t=s({},e);for(var i in t)if(Object.hasOwnProperty.call(t,i)){var n=t[i];!n||"group"!=n.type&&"grid"!=n.type&&"matrix"!=n.type&&"table"!=n.type?n&&(n.options&&!n.options.show||n.isDelete)&&(n.isTool&&n.fields.forEach((function(e){delete t[e.key]})),delete t[i]):n.options.show&&!n.isDelete||Object(r.b)({list:[n]},(function(e){t[e.key]&&delete t[e.key]}),!0)}return t}function w(e){return 0<e&&e<=20?"#f56c6c":20<e&&e<=50?"#e6a23c":50<e&&e<=80?"#409eff":"#67c23a"}function O(e,t){var i=0,n=e.type,o=e.options,a=o.scoreType,l=o.scoreValue;if(l=l?+l:0,"input"==n){if(t&&a==r.a.NOT_EMPTY.value)i=l;else if(t&&a==r.a.RIGHT_ANSWER.value&&e.options.scoreAnswer){var s=e.options.scoreAnswer.trim().split("\n");(s=s.map((function(e){var t=e.trim().split(/\s+/);return{str:t[0],score:t.length>1&&t[1]?+t[1]:1}}))).forEach((function(e){new RegExp(e.str).test(t)&&(i+=e.score)}))}}else"radio"==n?t&&a==r.a.NOT_EMPTY.value?i=l:t&&a==r.a.RIGHT_ANSWER.value&&(i=t.score?+t.score:0):"checkbox"==n&&(t&&t.length>0&&a==r.a.NOT_EMPTY.value?i=l:t&&a==r.a.RIGHT_ANSWER.value&&t.forEach((function(e){i+=e.score?+e.score:0})));return i}function k(e,t){var i=0,n=0,o=e,a=0;for(var r in t)if(Object.hasOwnProperty.call(t,r)){var l=t[r];l.options&&l.options.isFillRate&&a++}if(0===a)return-1;var s=function(e){var t=v(e);for(var i in t)if(Object.hasOwnProperty.call(t,i)){var n=t[i];if(n.isTool)n.options&&n.fields&&n.options.isFillRate||(n.fields.forEach((function(e){delete t[e.key]})),delete t[i]);else if(n.isToolSubField){var o=e[n.toolKey];o.options&&o.options.isFillRate||delete t[i]}else n.options&&n.options.isFillRate||delete t[i]}return t}(t);for(var u in s){var c=s[u];c.isDelete||"text"==c.type||"link"==c.type||"matrix"==c.type||"divider"==c.type||"group"==c.type||"grid"==c.type||c.isTool||(i+=1,!o[u]&&0!==o[u]||0==o[u].length||"{}"==JSON.stringify(o[u])||(n+=1))}return 0==i?100:parseInt(n/i*100)}function x(e,t){var i={},n=v(e),o=function(){var e=n[r],o={cname:"",label:"",value:""};o.cname=[r],o.label=n[r].name;var l=t,s="#EBEEF5";if("string"==typeof l[r]||"number"==typeof l[r])o.value=l?l[r]:"";else if("object"==Object(a.a)(l[r]))if(Array.isArray(l[r]))if("array"==e.type)if(l){var u=[],c=[];e.columns.forEach((function(e){u.push('<th style="padding:3px 5px;border-right: 1px solid '.concat(s,";border-bottom: 1px solid ").concat(s,';">').concat(e.name,"</th>"))})),l[r].forEach((function(t){var i=[];e.columns.forEach((function(e){i.push('<td style="padding:3px 5px;border-right: 1px solid '.concat(s,";border-bottom: 1px solid ").concat(s,';">').concat(t[e.key]?t[e.key]:"","</td>"))})),c.push("<tr>".concat(i.join(""),"</tr>"))})),o.value='\n                            <table cellspacing="0" cellpadding="0" class="field-table">\n                            <thead>\n                                <tr>'.concat(u.join(""),"</tr>\n                            </thead>\n                            <tbody> ").concat(c.join("")," </tbody>\n                            </table>")}else o.value="[]";else{var d="";l[r].forEach((function(e){""==d?d+=e.label:d=d+" 、"+e.label})),o.value=d||""}else if("object"==e.type){var f=[];e.properties.forEach((function(e){f.push("<br><lable>".concat(e.name,": </lable>").concat(l&&l[r][e.key]?l[r][e.key]:""," "))})),o.value=" ".concat(f.join(" "))}else o.value=l?l[r].label:"";i[o.cname]=o};for(var r in n)o();return i}function M(e,t){var i="";return(e.list?e.list:[]).forEach((function(e,n){var o=e.type;if(e.isDelete)return!1;if("grid"===o){var a=e.columns?e.columns:[],r=0;a.forEach((function(e){e&&e.list.length>=r&&(r=e.list.length)}));var l="";a.forEach((function(e){l+='<td rowspan="'+r+'">'+M(e,t)+"</td>"})),i+='<table class="field-table"><tr>'+l+"</tr></table>"}else if("group"===o){var s="";(e.columns?e.columns:[]).forEach((function(e){s+=M(e,t)})),i+="<form><fieldset><legend>"+e.name+"</legend>"+s+"</fieldset></form>"}else i+='<p class="field-box">',i+=function(e,t){if(!e)return"";var i="";return t[e.key]&&(i=t[e.key].value),'<span class="span-font field-label" > '+e.name+': </span><span class="span-font field-value" > '+i+" </span>"}(e,t),i+="</p>"})),i}function E(e,t,i){if(!e)return!1;var n=e[t.key];if(!n||!n.length)return!1;var o=[];if(n&&n.length>0){var a=null;if(t.externaldataInfo.ItemProp){a=t.externaldataInfo.ItemProp;for(var r=0;r<n.length;r++)o.push(j(n[r][a],t))}}if(0==o.length||!o[0])return!1;for(var l=0;l<o.length;l++){var s=o[l];if(Array.isArray(i)){if(Array.isArray(s)){if(i.length!=s.length)continue;if(I(s,i))return!1;continue}}else if(s==i)return!1}return{now:i||0===i?JSON.stringify(i):'"-"',external:o[0]||0===o?JSON.stringify(o[0]):'"-"'}}function j(e,t){if("radio"!=t.type&&("select"!=t.type&&"dictionary"!=t.type||t.options&&t.options.multiple))if("checkbox"==t.type||"selectMulti"==t.type||("select"==t.type||"dictionary"==t.type)&&t.options&&t.options.multiple)e="0"==t.externaldataInfo.DEGetValue?d(f(e=e.split("、"),t,!0),t):e.split("、");else{if("inputnumber"===t.type)return isNaN(+e)?"":+e;if("input"===t.type||"textarea"===t.type)return _(e)?"":e+"";if(!e)return""}else"0"==t.externaldataInfo.DEGetValue&&(e=d(f(e,t,!0),t));return e}function D(e,t){return"radio"!=t.type&&("select"!=t.type&&"dictionary"!=t.type||t.options&&t.options.multiple)?e:0==(t.options&&t.options.length>0?t.options:t.options.options).filter((function(t){return t.value==e}))?t.options.hasOtherOption?"other_option":"":e}function I(e,t){if(e.length!=t.length)return!1;for(var i=0;i<e.length;i++){var n=e[i];if(t.indexOf(n)<0)return!1}return!0}function C(e){return e&&Array.isArray(e)?e.map((function(e){return{label:e.name,value:e.id+"",type:"0",data:e}})):[]}function S(e,t){var i={};return e&&0!=Object.keys(e).length?(Object.keys(e).forEach((function(n){var o=e[n];if(n.indexOf("groups")>=0)o&&Array.isArray(o)&&o.length>0&&o.forEach((function(e,o){Object.keys(e).length>0&&Object.keys(e).forEach((function(a){var r=e[a],l="".concat(n,".").concat(a,"^").concat(o),s=t[a];i[l]=s?B(r,s):r}))}));else{var a=t[n];i[n]=a?B(o,a):o}})),i):{}}function B(e,t){if("radio"!==t.type||!e&&0!==e||void 0!==e.label||void 0!==e.value)return f(e,t);var i=f(e,t);return i==e&&"[object Object]"!==Object.prototype.toString.call(i)?"":i}function P(e,t,i){var n=Object.keys(t).filter((function(e){return e.indexOf("groups")>=0})),o=Object.keys(e).filter((function(e){return e.indexOf("groups")>=0})),a=function(e){for(var t=[],i=0,n=e.length;i<n;i++)-1===t.indexOf(e[i])&&t.push(e[i]);return t}(o.map((function(e){return e.split(".")[0]})));n.forEach((function(n){i[n]&&i[n].groupItems.filter((function(e){return!e.isDelete})).length>0&&void 0===e[n]&&a.indexOf(n)>=0&&function(){t[n]=[];for(var a=i[n].groupItems.filter((function(e){return!e.isDelete&&"divider"!==e.type})),r=a[0].key,l=0,s=function(e,t){var i=t.mgk,n=t.key,o=t.i;return e.filter((function(e){return e.indexOf("".concat(i,"."))>=0&&e.indexOf("".concat(n))>=0&&e.indexOf("^".concat(o))>=0})).length>0},u=function(){var r={};a.forEach((function(a){if(a.isTool)a.fields.forEach((function(a){var s="".concat(n,".").concat(a.key,"^").concat(l);o.indexOf(s)>=0?(r[a.key]=e[s],delete t[s]):r[a.key]=i[a.key]&&i[a.key].defaultValue?i[a.key].defaultValue:""}));else{var s="".concat(n,".").concat(a.key,"^").concat(l);o.indexOf(s)>=0?(r[a.key]=e[s],delete t[s]):r[a.key]=i[a.key].options.defaultValue||""}})),t[n].push(r),l++};s(o,{mgk:n,key:r,i:l});)u()}()}))}function A(e,t){if(0===arguments.length)return null;var i,n=t||"{y}-{m}-{d}";if(null==e||"null"===e)return"";"object"===Object(a.a)(e)?i=e:("string"==typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"==typeof e&&10===e.toString().length&&(e*=1e3),i=new Date(e));var o={y:i.getFullYear(),m:i.getMonth()+1,d:i.getDate(),h:i.getHours(),i:i.getMinutes(),s:i.getSeconds(),a:i.getDay()},r=n.replace(/{(y|m|d|h|i|s|a)+}/g,(function(e,t){var i=o[t];return"a"===t?["日","一","二","三","四","五","六"][i]:(e.length>0&&i<10&&(i="0"+i),i||0)}));return r}},"05fd":function(e,t,i){e.exports=i("baa7")("native-function-to-string",Function.toString)},"05fe":function(e,t,i){"use strict";i("097e")},"063b":function(e,t,i){"use strict";var n=i("7286"),o={props:{value:{},field:{}},data:function(){return{dataModel:Object(n.b)(this.value,this.field)}},watch:{value:{deep:!0,handler:function(e){this.dataModel=Object(n.b)(e,this.field)}},dataModel:function(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.$emit("input",Object(n.a)(e,this.field))}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-select",e._b({staticClass:"option-select",model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},"el-select",e.$attrs,!1),e._l(e.field.options,(function(e){return i("el-option",{key:e.value,attrs:{value:e.value,label:e.label}})})),1)}),[],!1,null,null,null);t.a=r.exports},"065d":function(e,t,i){var n=i("bb8b"),o=i("5edc");e.exports=i("26df")?function(e,t,i){return n.f(e,t,o(1,i))}:function(e,t,i){return e[t]=i,e}},"065e":function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},"078c":function(e,t,i){var n=i("0b34"),o=i("76e3"),a=i("3d8a"),r=i("1a58"),l=i("bb8b").f;e.exports=function(e){var t=o.Symbol||(o.Symbol=a?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||l(t,e,{value:r.f(e)})}},"0926":function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},"097e":function(e,t,i){},"09e1":function(e,t,i){},"0b28":function(e,t,i){var n=i("9cff");e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},"0b34":function(e,t){var i=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=i)},"0bca":function(e,t,i){"use strict";var n=i("0b34"),o=i("e99b"),a=i("84e8"),r=i("6f45"),l=i("49f2"),s=i("2b37"),u=i("8b5a"),c=i("9cff"),d=i("0926"),f=i("1a9a"),p=i("bac3"),m=i("a83a");e.exports=function(e,t,i,h,g,_){var b=n[e],y=b,v=g?"set":"add",w=y&&y.prototype,O={},k=function(e){var t=w[e];a(w,e,"delete"==e||"has"==e?function(e){return!(_&&!c(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return _&&!c(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,i){return t.call(this,0===e?0:e,i),this})};if("function"==typeof y&&(_||w.forEach&&!d((function(){(new y).entries().next()})))){var x=new y,M=x[v](_?{}:-0,1)!=x,E=d((function(){x.has(1)})),j=f((function(e){new y(e)})),D=!_&&d((function(){for(var e=new y,t=5;t--;)e[v](t,t);return!e.has(-0)}));j||((y=t((function(t,i){u(t,y,e);var n=m(new b,t,y);return null!=i&&s(i,g,n[v],n),n}))).prototype=w,w.constructor=y),(E||D)&&(k("delete"),k("has"),g&&k("get")),(D||M)&&k(v),_&&w.clear&&delete w.clear}else y=h.getConstructor(t,e,g,v),r(y.prototype,i),l.NEED=!0;return p(y,e),O[e]=y,o(o.G+o.W+o.F*(y!=b),O),_||h.setStrong(y,e,g),y}},"0c29":function(e,t){t.f=Object.getOwnPropertySymbols},"0c84":function(e,t,i){"use strict";var n=i("1663")(!0);i("120f")(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,i=this._i;return i>=t.length?{value:void 0,done:!0}:(e=n(t,i),this._i+=e.length,{value:e,done:!1})}))},"112a":function(e,t,i){"use strict";var n;(i.r(t),i.d(t,"install",(function(){return u})),i.d(t,"GenerateForm",(function(){return r})),i.d(t,"version",(function(){return l})),"undefined"!=typeof window)&&(i("e67d"),(n=window.document.currentScript)&&(n=n.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(i.p=n[1]));i("1bc7"),i("a450");var o=i("e9bb").a,a=(i("05fe"),i("cba8")),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fm-gf",class:e.getThemeClass(),staticStyle:{"min-height":"560px"}},[e.componentsLoadedMap?[i("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[i("div",{staticStyle:{display:"inline-block",width:"200px"}}),i("div",[e._t("header")],2)]),e.showFillInRate?i("div",{staticClass:"gf-form-rate"},[i("div",{staticStyle:{display:"inline-block",width:"200px"}},[i("el-progress",{directives:[{name:"show",rawName:"v-show",value:e.fillInRate>=0,expression:"fillInRate>=0"}],attrs:{percentage:e.fillInRate,color:e.progressColor,"stroke-width":8}})],1)]):e._e(),e.data.config.title&&e.data.config.isTitleShow?i("h4",{staticClass:"text-center fm-gf-title",staticStyle:{"font-size":"18px"}},[e._v("\n            "+e._s(e.data.config.title)+"\n        ")]):e._e()]:e._e(),"score"===e.data.config.mode?[e.data.config.scoreConfig.desc?i("div",{staticClass:"form-score-desc"},[e._v(e._s(e.data.config.scoreConfig.desc))]):e._e(),e.data.config.scoreConfig.metaFieldList.length>0?i("gf-meta-field",{ref:"GfMetaField",class:{"add-mode":e.isAddMode||e.formDisabled,"mobile-form":"lg"!==e.containerSize},staticStyle:{"margin-bottom":"20px"},attrs:{fields:e.data.config.scoreConfig.metaFieldList,data:e.data,model:e.metaValue}}):e._e()]:e._e(),e.componentsLoadedMap?i("el-form",{ref:"generateForm",staticClass:"gf-form",class:{"add-mode":e.isAddMode||e.formDisabled,"mobile-form":"lg"!==e.containerSize},staticStyle:{padding:"10px 30px 10px 20px"},attrs:{"label-suffix":":",size:e.data.config.size,model:e.formModel,rules:e.rules,"label-position":e.data.config.labelPosition,"label-width":e.data.config.labelWidth+"px"}},[e._l(e.data.list,(function(t,n){return[e.componentsLoadedMap[n]?[t.isDelete||"grid"!=t.type&&"group"!=t.type?t.isDelete?e._e():[i("generate-form-item",{directives:[{name:"show",rawName:"v-show",value:t.options.show&&(!e.curPageShowRange||n<e.curPageShowRange.end&&n>e.curPageShowRange.start),expression:"\n                        item.options.show &&\n                            (!curPageShowRange || (index < curPageShowRange.end && index > curPageShowRange.start))\n                    "}],key:t.key,ref:"item",refInFor:!0,staticClass:"gf-form-row",attrs:{value:e.models,models:e.formModel,rules:e.rules,widget:t,widgetForm:e.data,remote:e.remote,externaldata:e.externaldata,"de-settings":e.DESettings},on:{"update:value":function(t){e.models=t},"update:models":function(t){e.formModel=t},"input-change":e.onInputChange,"field-value-change":e.fieldValueChange,event:e.eventHandler,mounted:function(t){return e.componentMounted(n)},destroyed:function(t){return e.componentDestroyed(n)}}})]:i("generate-form-grid",{directives:[{name:"show",rawName:"v-show",value:t.options.show&&(!e.curPageShowRange||n<e.curPageShowRange.end&&n>e.curPageShowRange.start),expression:"\n                    item.options.show &&\n                        (!curPageShowRange || (index < curPageShowRange.end && index > curPageShowRange.start))\n                "}],key:t.key,ref:"grid",refInFor:!0,staticClass:"gf-form-row",attrs:{value:e.models,models:e.formModel,rules:e.rules,item:t,widgetForm:e.data,remote:e.remote,externaldata:e.externaldata,"de-settings":e.DESettings},on:{"update:value":function(t){e.models=t},"update:models":function(t){e.formModel=t},"input-change":e.onInputChange,"field-value-change":e.fieldValueChange,event:e.eventHandler,mounted:function(t){return e.componentMounted(n)},destroyed:function(t){return e.componentDestroyed(n)}}})]:e._e()]}))],2):e._e(),i("div",{staticClass:"text-center",staticStyle:{padding:"10px 0","margin-bottom":"20px"}},[e.allPagination.length>0?i("el-pagination",{staticClass:"fm-pagination",attrs:{background:"",layout:"prev, next","prev-text":"< 上一页","next-text":"下一页 >","page-count":e.allPagination.length,"current-page":e.curPageIndex},on:{"update:currentPage":function(t){e.curPageIndex=t},"update:current-page":function(t){e.curPageIndex=t}}}):e._e()],1),e._t("footer")],2)}),[],!1,null,"387ad504",null).exports,l=(i("eb0d"),i("b20f"),i("09e1"),i("9224").version);r.install=function(e){e.component(r.name,r)};var s=[r];s.forEach((function(e){e.version=l}));var u=function(e){s.forEach((function(t){e.component(t.name,t)}))};"undefined"!=typeof window&&window.Vue&&u(window.Vue);var c={install:u,GenerateForm:r,version:l};t.default=c},"118b":function(e,t,i){"use strict";var n=i("7286"),o={mixins:[i("495d").a],props:{value:{},field:{},disabled:{type:Boolean,default:function(){return!1}}},data:function(){return{dataModel:Object(n.b)(this.value,this.field)}},created:function(){var e=this;this.field.hasOtherOption&&(void 0===this.field.otherOptionValue?this.$set(this.field,"otherOptionValue",""):this.$set(this.field,"otherOptionValue",this.field.otherOptionValue)),this.$watch("field.otherOptionValue",(function(){var t=Object(n.a)(e.dataModel,e.field);e.$emit("input",t)}))},methods:{radioClick:function(e,t){if(!this.disabled){if("INPUT"==e.target.tagName)return;e.preventDefault(),t===this.dataModel?this.dataModel="":this.dataModel=t}}},watch:{value:{deep:!0,handler:function(e){this.dataModel=Object(n.b)(e,this.field)}},dataModel:function(e,t){if(JSON.stringify(e)!=JSON.stringify(t)){var i=Object(n.a)(e,this.field);this.$emit("input",i)}}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-radio-group",e._b({staticClass:"option-radio",attrs:{disabled:e.disabled},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},"el-radio-group",e.$attrs,!1),[e._l(e.field.options,(function(t,n){return i("el-radio",{key:n,attrs:{label:t.value},nativeOn:{click:function(i){return e.radioClick(i,t.value)}}},[e._v(e._s(t.label))])})),e.field.hasOtherOption?[i("el-radio",{attrs:{label:"other_option"},nativeOn:{click:function(t){return e.radioClick(t,"other_option")}}},[e._v(e._s(e.field.otherOptionLabel)),"other_option"===e.dataModel?i("el-input",{staticClass:"fm-option__extend-input",attrs:{disabled:e.disabled||"other_option"!=e.dataModel,size:"mini"},model:{value:e.field.otherOptionValue,callback:function(t){e.$set(e.field,"otherOptionValue",t)},expression:"field.otherOptionValue"}}):e._e()],1)]:e._e()],2)}),[],!1,null,null,null);t.a=r.exports},"120f":function(e,t,i){"use strict";var n=i("3d8a"),o=i("e99b"),a=i("84e8"),r=i("065d"),l=i("953d"),s=i("3460"),u=i("bac3"),c=i("addc"),d=i("839a")("iterator"),f=!([].keys&&"next"in[].keys()),p=function(){return this};e.exports=function(e,t,i,m,h,g,_){s(i,t,m);var b,y,v,w=function(e){if(!f&&e in M)return M[e];switch(e){case"keys":case"values":return function(){return new i(this,e)}}return function(){return new i(this,e)}},O=t+" Iterator",k="values"==h,x=!1,M=e.prototype,E=M[d]||M["@@iterator"]||h&&M[h],j=E||w(h),D=h?k?w("entries"):j:void 0,I="Array"==t&&M.entries||E;if(I&&(v=c(I.call(new e)))!==Object.prototype&&v.next&&(u(v,O,!0),n||"function"==typeof v[d]||r(v,d,p)),k&&E&&"values"!==E.name&&(x=!0,j=function(){return E.call(this)}),n&&!_||!f&&!x&&M[d]||r(M,d,j),l[t]=j,l[O]=p,h)if(b={values:k?j:w("values"),keys:g?j:w("keys"),entries:D},_)for(y in b)y in M||a(M,y,b[y]);else o(o.P+o.F*(f||x),t,b);return b}},1374:function(e,t,i){"use strict";var n=i("bb8b"),o=i("5edc");e.exports=function(e,t,i){t in e?n.f(e,t,o(0,i)):e[t]=i}},1663:function(e,t,i){var n=i("212e"),o=i("3ab0");e.exports=function(e){return function(t,i){var a,r,l=String(o(t)),s=n(i),u=l.length;return s<0||s>=u?e?"":void 0:(a=l.charCodeAt(s))<55296||a>56319||s+1===u||(r=l.charCodeAt(s+1))<56320||r>57343?e?l.charAt(s):a:e?l.slice(s,s+2):r-56320+(a-55296<<10)+65536}}},"1a58":function(e,t,i){t.f=i("839a")},"1a9a":function(e,t,i){var n=i("839a")("iterator"),o=!1;try{var a=[7][n]();a.return=function(){o=!0},Array.from(a,(function(){throw 2}))}catch(e){}e.exports=function(e,t){if(!t&&!o)return!1;var i=!1;try{var a=[7],r=a[n]();r.next=function(){return{done:i=!0}},a[n]=function(){return r},e(a)}catch(e){}return i}},"1b0b":function(e,t,i){var n=i("a86f"),o=i("3250"),a=i("839a")("species");e.exports=function(e,t){var i,r=n(e).constructor;return void 0===r||null==(i=n(r)[a])?t:o(i)}},"1b96":function(e,t,i){var n=i("cea2");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},"1bc7":function(e,t,i){for(var n=i("25ba"),o=i("93ca"),a=i("84e8"),r=i("0b34"),l=i("065d"),s=i("953d"),u=i("839a"),c=u("iterator"),d=u("toStringTag"),f=s.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},m=o(p),h=0;h<m.length;h++){var g,_=m[h],b=p[_],y=r[_],v=y&&y.prototype;if(v&&(v[c]||l(v,c,f),v[d]||l(v,d,_),s[_]=f,b))for(g in n)v[g]||a(v,g,n[g],!0)}},"1e4d":function(e,t,i){var n=i("3250");e.exports=function(e,t,i){if(n(e),void 0===t)return e;switch(i){case 1:return function(i){return e.call(t,i)};case 2:return function(i,n){return e.call(t,i,n)};case 3:return function(i,n,o){return e.call(t,i,n,o)}}return function(){return e.apply(t,arguments)}}},"201c":function(e,t,i){var n=i("212e"),o=Math.min;e.exports=function(e){return e>0?o(n(e),9007199254740991):0}},"20b3":function(e,t,i){"use strict"},"212e":function(e,t){var i=Math.ceil,n=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?n:i)(e)}},"21c3":function(e,t,i){"use strict";i("b07f")},"21c6":function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));i("25ba"),i("32ea"),i("1bc7");var n=i("565b"),o=i("4260");function a(){return{beforeCreate:function(){this.$options.components.ToolField=n.a},directives:{emoji:o.a},inject:{all$vm:{from:"all$vm",default:function(){}},eventBus:{from:"eventBus",default:function(){}},gfItemBus:{from:"gfItemBus",default:function(){}},widget:{from:"widget",default:function(){}},injected_dataModel:{from:"dataModel",default:function(){}}},provide:function(){return{toolModel:this.toolModel}},data:function(){return{toolModel:this.injected_dataModel,isModelsLocking:!1}},watch:{"gfItemBus.models":{handler:function(e,t){var i=this;this.fields.forEach((function(t){JSON.stringify(i.toolModel[t.key])!==JSON.stringify(e[t.key])&&(i.toolModel[t.key]=e[t.key])})),Object.keys(e).forEach((function(t){t.indexOf(i.widget.key+"_")>=0&&void 0===i.toolModel[t]&&i.$set(i.toolModel,t,e[t])}))},deep:!0},toolModel:{handler:function(e,t){var i=this;if(!this.isModelsLocking&&e){var n=!1;Object.keys(e).forEach((function(t){i.gfItemBus.models&&(i.gfItemBus.models[t]=e[t],i.gfItemBus.value[t]=e[t],n=!0)})),n&&(this.isModelsLocking=!0,this.gfItemBus.$emit("update:models",this.gfItemBus.models),this.gfItemBus.$emit("update:value",this.gfItemBus.value),this.$nextTick((function(){i.isModelsLocking=!1})))}},deep:!0}},computed:{fields:function(){return this.widget.fields&&this.widget.fields.length>0?this.widget.fields:[]}},methods:{emitEvent:function(e,t,i,n){this.gfItemBus.$emit(e,t,i,this.gfItemBus)}}}}},"21d9":function(e,t,i){var n=i("3a4c"),o=i("065e").concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,o)}},"24f6":function(e,t,i){"use strict";i.r(t);var n=i("21c6"),o={mixins:[Object(n.a)()],type:"t_birthday",data:function(){return{}},methods:{handleFocus:function(){this.widget.options.disabled&&this.$refs.datePicker.hidePicker()},disabledDate:function(e){return e>Date.now()}},watch:{"widget.options.disabled":function(e){e&&this.$refs.datePicker.hidePicker()}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("tool-field",{staticClass:"gf-tool-birthday",attrs:{field:e.widget.fields[0]},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-date-picker",{ref:"datePicker",staticStyle:{width:"100%"},attrs:{type:"date",disabled:n,placeholder:e.widget.options.placeholder,"picker-options":{disabledDate:e.disabledDate},"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd"},on:{focus:e.handleFocus},model:{value:e.toolModel[e.widget.fields[0].key],callback:function(t){e.$set(e.toolModel,e.widget.fields[0].key,t)},expression:"toolModel[widget.fields[0].key]"}})]}}])})}),[],!1,null,null,null);t.default=r.exports},"25ba":function(e,t,i){"use strict";var n=i("87b2"),o=i("6fef"),a=i("953d"),r=i("3471");e.exports=i("120f")(Array,"Array",(function(e,t){this._t=r(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,i=this._i++;return!e||i>=e.length?(this._t=void 0,o(1)):o(0,"keys"==t?i:"values"==t?e[i]:[i,e[i]])}),"values"),a.Arguments=a.Array,n("keys"),n("values"),n("entries")},"26df":function(e,t,i){e.exports=!i("0926")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},2843:function(e,t,i){"use strict";var n=i("1e4d"),o=i("e99b"),a=i("8078"),r=i("b1d4"),l=i("dcea"),s=i("201c"),u=i("1374"),c=i("e3bb");o(o.S+o.F*!i("1a9a")((function(e){Array.from(e)})),"Array",{from:function(e){var t,i,o,d,f=a(e),p="function"==typeof this?this:Array,m=arguments.length,h=m>1?arguments[1]:void 0,g=void 0!==h,_=0,b=c(f);if(g&&(h=n(h,m>2?arguments[2]:void 0,2)),null==b||p==Array&&l(b))for(i=new p(t=s(f.length));t>_;_++)u(i,_,g?h(f[_],_):f[_]);else for(d=b.call(f),i=new p;!(o=d.next()).done;_++)u(i,_,g?r(d,h,[o.value,_],!0):o.value);return i.length=_,i}})},"285b":function(e,t,i){var n=i("35d4"),o=i("5edc"),a=i("3471"),r=i("5d10"),l=i("4fd4"),s=i("83d3"),u=Object.getOwnPropertyDescriptor;t.f=i("26df")?u:function(e,t){if(e=a(e),t=r(t,!0),s)try{return u(e,t)}catch(e){}if(l(e,t))return o(!n.f.call(e,t),e[t])}},"2b37":function(e,t,i){var n=i("1e4d"),o=i("b1d4"),a=i("dcea"),r=i("a86f"),l=i("201c"),s=i("e3bb"),u={},c={};(t=e.exports=function(e,t,i,d,f){var p,m,h,g,_=f?function(){return e}:s(e),b=n(i,d,t?2:1),y=0;if("function"!=typeof _)throw TypeError(e+" is not iterable!");if(a(_)){for(p=l(e.length);p>y;y++)if((g=t?b(r(m=e[y])[0],m[1]):b(e[y]))===u||g===c)return g}else for(h=_.call(e);!(m=h.next()).done;)if((g=o(h,b,m.value,t))===u||g===c)return g}).BREAK=u,t.RETURN=c},"2d39":function(e,t,i){var n=i("0b34"),o=i("edec").set,a=n.MutationObserver||n.WebKitMutationObserver,r=n.process,l=n.Promise,s="process"==i("cea2")(r);e.exports=function(){var e,t,i,u=function(){var n,o;for(s&&(n=r.domain)&&n.exit();e;){o=e.fn,e=e.next;try{o()}catch(n){throw e?i():t=void 0,n}}t=void 0,n&&n.enter()};if(s)i=function(){r.nextTick(u)};else if(!a||n.navigator&&n.navigator.standalone)if(l&&l.resolve){var c=l.resolve(void 0);i=function(){c.then(u)}}else i=function(){o.call(n,u)};else{var d=!0,f=document.createTextNode("");new a(u).observe(f,{characterData:!0}),i=function(){f.data=d=!d}}return function(n){var o={fn:n,next:void 0};t&&(t.next=o),e||(e=o,i()),t=o}}},3076:function(e,t,i){"use strict";var n=i("fed1"),o=i("f962"),a=i.n(o),r={components:{CusDialog:n.a},props:{visible:{type:Boolean,default:!1},dataTemplate:String},data:function(){return{jsonClipboard:null}},watch:{visible:function(e){var t=this;e&&this.$nextTick((function(){ace.edit("dataeditor").session.setMode("ace/mode/json"),t.jsonClipboard||(t.jsonClipboard=new a.a(".json-btn"),t.jsonClipboard.on("success",(function(e){t.$message.success(t.$t("fm.message.copySuccess"))})))}))}},mounted:function(){}},l=i("cba8"),s=Object(l.a)(r,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("cus-dialog",{ref:"dataPreview",attrs:{visible:e.visible,title:"数据预览",width:"800px",form:""},on:{"on-close":function(t){return e.$emit("close")}}},[i("div",{staticStyle:{height:"400px",width:"100%"},attrs:{id:"dataeditor"}},[e._v(e._s(e.dataTemplate))]),i("template",{slot:"action"},[i("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v(e._s(e.$t("fm.actions.cancel")))]),i("el-button",{staticClass:"json-btn",attrs:{type:"primary","data-clipboard-text":e.dataTemplate}},[e._v("\n            "+e._s(e.$t("fm.actions.copyData"))+"\n        ")])],1)],2)}),[],!1,null,null,null);t.a=s.exports},3080:function(e,t,i){"use strict";i("4abe")},3250:function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},3269:function(e,t,i){var n=i("0b34"),o=i("a83a"),a=i("bb8b").f,r=i("21d9").f,l=i("804d"),s=i("6bf8"),u=n.RegExp,c=u,d=u.prototype,f=/a/g,p=/a/g,m=new u(f)!==f;if(i("26df")&&(!m||i("0926")((function(){return p[i("839a")("match")]=!1,u(f)!=f||u(p)==p||"/a/i"!=u(f,"i")})))){u=function(e,t){var i=this instanceof u,n=l(e),a=void 0===t;return!i&&n&&e.constructor===u&&a?e:o(m?new c(n&&!a?e.source:e,t):c((n=e instanceof u)?e.source:e,n&&a?s.call(e):t),i?this:d,u)};for(var h=function(e){e in u||a(u,e,{configurable:!0,get:function(){return c[e]},set:function(t){c[e]=t}})},g=r(c),_=0;g.length>_;)h(g[_++]);d.constructor=u,u.prototype=d,i("84e8")(n,"RegExp",u)}i("f966")("RegExp")},"32e5":function(e,t,i){"use strict";i("fc02");var n=i("adf4"),o={data:function(){return{}}},a=i("cba8"),r={data:function(){return{}}},l={data:function(){return{}}},s={data:function(){return{}}},u={data:function(){return{}}},c={data:function(){return{}}},d={inject:{eventBus:{from:"eventBus",default:function(){return null}},widget:{from:"widget",default:function(){}},groupsWidget:{from:"groupsWidget",default:function(){return null}},groupsIndex:{from:"groupsIndex",default:function(){return null}},gfItemBus:{from:"gfItemBus",default:function(){return null}},toolFieldBus:{from:"toolFieldBus",default:function(){return null}}},components:{AuditLocked:Object(a.a)(o,(function(){var e=this.$createElement,t=this._self._c||e;return t("el-tooltip",{attrs:{effect:"dark",content:"已锁定",placement:"top-start"}},[t("i",{staticClass:"audit-status audit-locked el-icon-lock"})])}),[],!1,null,null,null).exports,AuditUnlocked:Object(a.a)(r,(function(){var e=this.$createElement,t=this._self._c||e;return t("el-tooltip",{attrs:{effect:"dark",content:"已解锁",placement:"top-start"}},[t("i",{staticClass:"audit-status audit-unlocked el-icon-unlock"})])}),[],!1,null,null,null).exports,AuditQuestioned:Object(a.a)(l,(function(){var e=this.$createElement,t=this._self._c||e;return t("el-tooltip",{attrs:{effect:"dark",content:"有质疑",placement:"top-start"}},[t("i",{staticClass:"audit-status audit-questioned iconfont2 icon-yiwen"})])}),[],!1,null,null,null).exports,AuditReplied:Object(a.a)(s,(function(){var e=this.$createElement,t=this._self._c||e;return t("el-tooltip",{attrs:{effect:"dark",content:"已回复",placement:"top-start"}},[t("i",{staticClass:"audit-status audit-replied iconfont2 icon-huifuxinxi"})])}),[],!1,null,null,null).exports,AuditBackApplied:Object(a.a)(u,(function(){var e=this.$createElement,t=this._self._c||e;return t("el-tooltip",{attrs:{effect:"dark",content:"撤回中",placement:"top-start"}},[t("i",{staticClass:"audit-status audit-back-applied el-icon-refresh-left"})])}),[],!1,null,null,null).exports,AuditCompleted:Object(a.a)(c,(function(){var e=this.$createElement,t=this._self._c||e;return t("el-tooltip",{attrs:{effect:"dark",content:"已通过",placement:"top-start"}},[t("i",{staticClass:"audit-status audit-completed el-icon-check"})])}),[],!1,null,null,null).exports},props:{field:{required:!1}},data:function(){return{}},methods:{openAuditDialog:function(e){this.field?this.toolFieldBus.$refs.outter.openAuditDialog(e):this.gfItemBus.openAuditDialog(e)}},computed:{isInFormBox:function(){return!!this.eventBus},fieldKey:function(){var e="";return e=this.field?this.field.key:this.widget.key,this.groupsWidget&&(e=this.groupsWidget.key+"."+e),void 0!==this.groupsIndex&&null!==this.groupsIndex&&(e+="^"+this.groupsIndex),e},fieldKeyWithOutIndex:function(){return this.fieldKey.split("^")[0]},roleType:function(){return this.eventBus.audit.role},isAuditor:function(){return this.roleType==n.a.Auditor},isAuditMode:function(){return!!this.isInFormBox&&this.eventBus.isAuditMode},isQuestioned:function(){return this.eventBus.audit.doubt.indexOf(this.fieldKey)>=0||this.eventBus.audit.doubt.indexOf(this.fieldKeyWithOutIndex)>=0},isReplied:function(){return this.eventBus.audit.reply.indexOf(this.fieldKey)>=0||this.eventBus.audit.reply.indexOf(this.fieldKeyWithOutIndex)>=0},isLocked:function(){return this.eventBus.audit.lock.indexOf(this.fieldKey)>=0||this.eventBus.audit.lock.indexOf(this.fieldKeyWithOutIndex)>=0},isSubmit:function(){return this.eventBus.audit.submit.indexOf(this.fieldKey)>=0||this.eventBus.audit.submit.indexOf(this.fieldKeyWithOutIndex)>=0},isBackApply:function(){return this.eventBus.audit.backApply.indexOf(this.fieldKey)>=0||this.eventBus.audit.backApply.indexOf(this.fieldKeyWithOutIndex)>=0},isAgreeBackApply:function(){return this.eventBus.audit.agreeBackApply.indexOf(this.fieldKey)>=0||this.eventBus.audit.agreeBackApply.indexOf(this.fieldKeyWithOutIndex)>=0}}},f=Object(a.a)(d,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.isAuditMode?i("span",[e.isSubmit&&!e.isAuditor?i("audit-locked"):e.isAgreeBackApply?i("audit-unlocked",{staticStyle:{cursor:"pointer"},nativeOn:{click:function(t){return e.openAuditDialog({})}}}):e.isQuestioned?i("audit-questioned",{staticStyle:{cursor:"pointer"},nativeOn:{click:function(t){return e.openAuditDialog({})}}}):e.isReplied?i("audit-replied",{staticStyle:{cursor:"pointer"},nativeOn:{click:function(t){return e.openAuditDialog({})}}}):e.isBackApply?i("audit-back-applied",{staticStyle:{cursor:"pointer"},nativeOn:{click:function(t){return e.openAuditDialog({})}}}):e.isLocked?i("audit-completed"):e._e()],1):e._e()}),[],!1,null,null,null);t.a=f.exports},"32ea":function(e,t,i){var n=i("8078"),o=i("93ca");i("b2be")("keys",(function(){return function(e){return o(n(e))}}))},3460:function(e,t,i){"use strict";var n=i("7ee3"),o=i("5edc"),a=i("bac3"),r={};i("065d")(r,i("839a")("iterator"),(function(){return this})),e.exports=function(e,t,i){e.prototype=n(r,{next:o(1,i)}),a(e,t+" Iterator")}},3471:function(e,t,i){var n=i("1b96"),o=i("3ab0");e.exports=function(e){return n(o(e))}},"34a9":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"a",(function(){return ScoreType})),__webpack_require__.d(__webpack_exports__,"c",(function(){return allWidgets})),__webpack_require__.d(__webpack_exports__,"e",(function(){return getAllWidgetConfigObj})),__webpack_require__.d(__webpack_exports__,"b",(function(){return WidgetIterator})),__webpack_require__.d(__webpack_exports__,"d",(function(){return defaultWidgetFormConfig})),__webpack_require__.d(__webpack_exports__,"f",(function(){return getDefaultExternaldataInfo})),__webpack_require__.d(__webpack_exports__,"g",(function(){return mergeJsonData}));var core_js_modules_es7_object_get_own_property_descriptors__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("ac67"),core_js_modules_es7_object_get_own_property_descriptors__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es7_object_get_own_property_descriptors__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es6_object_keys__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("32ea"),core_js_modules_es6_object_keys__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es6_object_keys__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es7_symbol_async_iterator__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("9f60"),core_js_modules_es7_symbol_async_iterator__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es7_symbol_async_iterator__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es6_symbol__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("94f0"),core_js_modules_es6_symbol__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es6_symbol__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es6_regexp_to_string__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("4057"),core_js_modules_es6_regexp_to_string__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es6_regexp_to_string__WEBPACK_IMPORTED_MODULE_4__),D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("3ce5"),core_js_modules_es6_array_iterator__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("25ba"),core_js_modules_es6_array_iterator__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(core_js_modules_es6_array_iterator__WEBPACK_IMPORTED_MODULE_6__),core_js_modules_es6_set__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("c5cb"),core_js_modules_es6_set__WEBPACK_IMPORTED_MODULE_7___default=__webpack_require__.n(core_js_modules_es6_set__WEBPACK_IMPORTED_MODULE_7__),core_js_modules_es6_string_iterator__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("0c84"),core_js_modules_es6_string_iterator__WEBPACK_IMPORTED_MODULE_8___default=__webpack_require__.n(core_js_modules_es6_string_iterator__WEBPACK_IMPORTED_MODULE_8__),core_js_modules_es6_array_from__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("2843"),core_js_modules_es6_array_from__WEBPACK_IMPORTED_MODULE_9___default=__webpack_require__.n(core_js_modules_es6_array_from__WEBPACK_IMPORTED_MODULE_9__),D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("44a4"),core_js_modules_es6_function_name__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__("a450"),core_js_modules_es6_function_name__WEBPACK_IMPORTED_MODULE_11___default=__webpack_require__.n(core_js_modules_es6_function_name__WEBPACK_IMPORTED_MODULE_11__),core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__("1bc7"),core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_12___default=__webpack_require__.n(core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_12__),_components_makingForm_layout_toolWidgets__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__("7a97");function ownKeys(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(i),!0).forEach((function(t){Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_5__.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function _createForOfIteratorHelper(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var basicWidgets=[{type:"input",name:"单行文本",valueName:"",label:"单行文本",icon:"icon-danhangwenben1",phoneCode:"",isForDbMode:!0,options:{width:100,isStatistical:!1,isFillRate:!0,code:"",show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",compareable:!1,unit:"",defaultValue:"",required:!1,dataType:"string",pattern:"",placeholder:"",disabled:!1,remark:""},event:{}},{type:"inputnumber",name:"数值输入框",valueName:"",label:"数值输入框",icon:"icon-shuzhishurukuangbeifen",phoneCode:"",isForDbMode:!0,options:{width:100,code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",unit:"",defaultValue:"",required:!1,max:"",min:"",dataType:"number",pattern:"",placeholder:"",disabled:!1,remark:""},event:{}},{type:"textarea",icon:"icon-duohangwenben",name:"多行文本",valueName:"",label:"多行文本",phoneCode:"",isForDbMode:!0,options:{width:100,code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",rows:5,defaultValue:"",required:!1,disabled:!1,pattern:"",placeholder:"",remark:""},event:{}},{type:"radio",icon:"icon-danxuankuangzu",name:"单选框组",valueName:"",label:"单选框组",phoneCode:"",options:{width:100,code:"",isStatistical:!1,isFillRate:!0,inline:!0,defaultValue:"",isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",show:!0,showLabel:!0,hasOtherOption:!1,otherOptionLabel:"其他",otherOptionValue:"",options:[{value:"1",label:"选项1"},{value:"2",label:"选项2"}],dictionaryType:"",dictionaryOptions:{},required:!1,remote:!1,props:{value:"value",label:"label"},remoteFunc:"",disabled:!1,cancelable:!0,remark:""},event:{}},{type:"checkbox",name:"多选框组",valueName:"",label:"多选框组",icon:"icon-duoxuankuang1",phoneCode:"",options:{inline:!0,code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",defaultValue:[],showLabel:!0,hasOtherOption:!1,otherOptionLabel:"其他",otherOptionValue:"",options:[{value:"1",label:"选项1"},{value:"2",label:"选项2"}],dictionaryType:"",dictionaryOptions:{},required:!1,width:100,remote:!1,props:{value:"value",label:"label"},remoteFunc:"",disabled:!1,remark:""},event:{}},{type:"select",name:"下拉选择框",valueName:"",label:"下拉选择框",icon:"icon-xialaxuanze",phoneCode:"",options:{defaultValue:"",code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",multiple:!1,disabled:!1,clearable:!1,placeholder:"",required:!1,showLabel:!0,width:100,options:[{value:"1",label:"选项1"},{value:"2",label:"选项2"}],dictionaryType:"",dictionaryOptions:{},remote:!1,filterable:!1,props:{value:"value",label:"label"},remoteFunc:"",remark:""},event:{}},{type:"autocomplete",name:"自动补全框",valueName:"",label:"自动补全框",icon:"icon-shurubuquankuang",phoneCode:"",options:{defaultValue:"",code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",disabled:!1,clearable:!1,placeholder:"",required:!1,showLabel:!0,width:100,options:[{value:"1",label:"选项1"},{value:"2",label:"选项2"}],dictionaryType:"",dictionaryOptions:{},remote:!1,props:{value:"value",label:"label"},remoteFunc:"",remark:""},event:{}},{type:"dictionary",name:"字典",valueName:"",label:"字典",icon:"icon-shujuzidian",phoneCode:"",options:{width:100,code:"",isStatistical:!1,isFillRate:!0,defaultValue:"",isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",show:!0,multiple:!1,disabled:!1,clearable:!1,placeholder:"请选择",required:!1,showLabel:!0,options:[],dictionaryType:"",dictionaryOptions:{},remote:!0,filterable:!0,props:{value:"value",label:"label"},remoteFunc:"",remark:""},event:{}},{type:"time",name:"时间选择器",valueName:"",label:"时间选择器",icon:"icon-shijian",phoneCode:"",options:{defaultValue:"00:00:00",code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",disabled:!1,editable:!0,clearable:!0,placeholder:"",startPlaceholder:"",endPlaceholder:"",isRange:!1,arrowControl:!0,format:"HH:mm:ss",required:!1,width:100,remark:""},event:{}},{type:"date",name:"日期选择器",valueName:"",label:"日期选择器",icon:"icon-riqi",phoneCode:"",options:{defaultValue:"",code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",disabled:!1,editable:!0,clearable:!0,placeholder:"",startPlaceholder:"",endPlaceholder:"",format:"yyyy-MM-dd",timestamp:!1,required:!1,width:100,remark:""},event:{}},{type:"object",name:"对象",valueName:"",label:"对象",icon:"icon-json",isOnlyForPc:!0,options:{show:!0,code:"",disabled:!1,width:100,layout:1,remark:"",defaultValue:{}},properties:[{name:"属性1",key:"key1",type:"string"}],event:{}},{type:"array",name:"数组",valueName:"",label:"数组",icon:"icon-shuzu",isOnlyForPc:!0,options:{show:!0,code:"",disabled:!1,width:100,remark:"",defaultValue:[],isLabelHide:!1},columns:[{name:"列1",key:"key1",type:"string"},{name:"列2",key:"key2",type:"string"}],event:{}},{type:"text",name:"文字",valueName:"",label:"文字",icon:"icon-24gl-textAround",options:{defaultValue:"This is a text",show:!0,isLabelTop:!1,isLabelHide:!1,width:100,customClass:"",lineHeight:"28px",color:"#606060",nameFamily:"",nameSize:"",fontWeight:"normal",fontStyle:"normal",fontFamily:"Arial",remark:""},event:{}},{type:"link",name:"链接",label:"链接",icon:"icon-lianjie_o",options:{show:!0,href:"",underline:!0,type:"",marginleft:30,width:100,target:"_blank",linkType:"default",linkFormGuid:"",remark:""},event:{}},{type:"divider",name:"分割线",label:"分割线",icon:"icon-fengexian",options:{contentPosition:"left",show:!0,remark:""},event:{}},{type:"uploadImg",label:"图片",name:"图片",valueName:"",icon:"icon-icons01",options:{width:100,imgWidth:100,imgHeight:100,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",disabled:!1,isMultipleImg:!1,defaultValue:[]},event:{}},{type:"uploadFile",label:"文件上传",name:"文件上传",valueName:"",icon:"icon-wenjianshangchuan",options:{show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",disabled:!1,isMultipleFile:!1,fileTypesAccept:"",defaultValue:[]},event:{}}],customGrid={type:"grid",name:"自定义布局",label:"自定义布局",key:"grid_0",icon:"icon-tianjiaduolie",isForDbMode:!0,columns:[{span:12,list:[]},{span:12,list:[]}],options:{show:!0,addColumnAble:!0,bgColor:"",gutter:0,justify:"start",align:"top"},event:{}},advanceWidgets=[{type:"group",name:"分组",label:"分组",icon:"icon-caidan",isOnlyForPc:!0,isForDbMode:!0,columns:[{span:24,list:[]}],options:{group_style:"fieldset",bgColor:"",gutter:0,justify:"start",align:"top",show:!0,isLabelHide:!1},event:{}},{type:"group",name:"面板",label:"面板",icon:"icon-m-mianban",isOnlyForPc:!0,isForDbMode:!0,columns:[{span:24,list:[]}],options:{group_style:"panel",bgColor:"",gutter:0,justify:"start",align:"top",show:!0,isLabelHide:!1},event:{}},{type:"group",name:"分组(多列)",label:"分组(多列)",icon:"icon-gengduoliebiao",isOnlyForPc:!0,isForDbMode:!0,columns:[{span:24,list:[customGrid]}],options:{group_style:"fieldset",hasGrid:!0,bgColor:"",gutter:0,justify:"start",align:"top",show:!0,isLabelHide:!1},event:{}},{type:"group",name:"面板(多列)",label:"面板(多列)",icon:"icon-__zongkongmianban",isOnlyForPc:!0,isForDbMode:!0,columns:[{span:24,list:[customGrid]}],options:{group_style:"panel",bgColor:"",hasGrid:!0,gutter:0,justify:"start",align:"top",show:!0,isLabelHide:!1},event:{}},{type:"matrix",name:"矩阵",label:"矩阵",icon:"icon-juzhen",isOnlyForPc:!0,isForDbMode:!0,options:{width:100,bgColor:"",show:!0,isLabelHide:!1},matrixRow:["第1行","第2行","第3行"],matrixColumnsFirst:{label:"#",width:""},matrixColumns:[{label:"第1列",width:""},{label:"第2列",width:""},{label:"第3列",width:""}],matrixData:[[[],[],[]],[[],[],[]],[[],[],[]]],event:{}},{type:"groups",name:"动态分组",label:"动态分组",icon:"icon-charubiaoge",isOnlyForPc:!0,options:{show:!0,groupItemsCount:1,disabled:!1},groupItems:[],event:{}}],layoutWidgets=[{type:"grid",name:"两列布局",label:"两列布局",icon:"icon-lianglie",isForDbMode:!0,columns:[{span:12,list:[]},{span:12,list:[]}],options:{bgColor:"",show:!0,gutter:0,justify:"start",align:"top"},event:{}},{type:"grid",name:"三列布局",label:"三列布局",icon:"icon-sanlie",isForDbMode:!0,columns:[{span:12,list:[]},{span:12,list:[]},{span:12,list:[]}],options:{bgColor:"",gutter:0,show:!0,justify:"start",align:"top"},event:{}},customGrid],outDataProps=[{label:"姓名",value:"name"},{label:"出生日期",value:"birthday"},{label:"性别",value:"gender"}],ScoreType={NOT_EMPTY:{label:"不为空时得分",value:"1"},RIGHT_ANSWER:{label:"指定答案得分",value:"2"}},ModeType={pc:{name:"pc端",code:"pc",icon:"icon-diannao",desc:"表单主要在pc端使用，可兼容一些简单的控件在移动端"},mobile:{name:"移动端",code:"mobile",icon:"icon-shouji",desc:"表单主要在移动端使用，一些复杂的控件在移动端模式下不可使用"},score:{name:"量表",code:"score",icon:"icon-celiangbiao",desc:"用于计算一些量表字段得分，一些复杂的控件在量表模式下不可使用"}},scoreWidgets=[{type:"input",icon:"icon-wenda",name:"请填写本项内容？",label:"问答题",phoneCode:"",options:{width:100,code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!0,isLabelHide:!1,isNameBold:!1,isScore:!0,scoreValue:"",scoreType:ScoreType.NOT_EMPTY.value,scoreAnswer:"",isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",compareable:!1,unit:"",defaultValue:"",required:!1,dataType:"string",pattern:"",placeholder:"",disabled:!1,remark:""},event:{}},{type:"radio",icon:"icon-danxuankuangzu",name:"请选择一个答案？",label:"单选题",phoneCode:"",options:{width:100,code:"",isStatistical:!1,isFillRate:!0,inline:!0,isScore:!0,scoreValue:"",scoreType:ScoreType.RIGHT_ANSWER.value,defaultValue:"",isLabelTop:!0,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",show:!0,showLabel:!0,hasOtherOption:!1,otherOptionLabel:"其他",otherOptionValue:"",otherOptionScore:"",options:[{value:"1",label:"选项1"},{value:"2",label:"选项2"}],required:!1,remote:!1,props:{value:"value",label:"label"},remoteFunc:"",disabled:!1,cancelable:!0,remark:""},event:{}},{type:"checkbox",icon:"icon-duoxuankuang1",name:"请选择多个答案？",label:"多选题",phoneCode:"",options:{code:"",isStatistical:!1,isFillRate:!0,inline:!0,isScore:!0,scoreValue:"",scoreType:ScoreType.RIGHT_ANSWER.value,show:!0,isLabelTop:!0,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",defaultValue:[],showLabel:!0,hasOtherOption:!1,otherOptionLabel:"其他",otherOptionValue:"",otherOptionScore:"",options:[{value:"1",label:"选项1"},{value:"2",label:"选项2"}],required:!1,width:100,remote:!1,props:{value:"value",label:"label"},remoteFunc:"",disabled:!1,remark:""},event:{}},{type:"score",icon:"icon-zongfen",name:"分数:",label:"分数",options:{width:100,code:"",isStatistical:!1,isFillRate:!0,show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isScore:!0,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",unit:"",required:!1,placeholder:"",remark:""},event:{},scoreExpression:getDefaultScoreExpression()}];function getDefaultScoreExpression(){return JSON.parse(JSON.stringify({isIncludeOtherScore:!1,decimalPoint:2,items:[]}))}function generateScore(e){e.scoreExpression=getDefaultScoreExpression()}function getScoreExpressionDesc(e){var t=[];return e.items.forEach((function(e){var i="<span>".concat(e.name,"(").concat(e.key,")</span>");1!=e.multiple&&(i="(".concat(i,"*<span>").concat(e.multiple,"</span>)")),1!=e.divisor&&e.divisor&&(i="(".concat(i,"/<span>").concat(e.divisor,"</span>)")),t.push(i)})),t.join(" + ")}var allWidgets=[].concat(advanceWidgets,layoutWidgets,scoreWidgets,Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__.a)(_components_makingForm_layout_toolWidgets__WEBPACK_IMPORTED_MODULE_13__.b),Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__.a)(_components_makingForm_layout_toolWidgets__WEBPACK_IMPORTED_MODULE_13__.a),basicWidgets);function getWidgetTypes(){var e=allWidgets.map((function(e){return e.type}));return Array.from(new Set(e))}function getAllWidgetConfigObj(e){var t={};return e.forEach((function(e){var i=JSON.parse(JSON.stringify(e));t[e.label]=i,t[e.type]=i})),t}function widget2tree(e,t,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=n.isRepeatEmptyContainer,a=void 0!==o&&o,r=n.isIncludeToolContainer,l=void 0!==r&&r,s=arguments.length>4?arguments[4]:void 0;if(!e.isTool||(e.fields.forEach((function(e){widget2tree(e,t,i,a,s)})),l)){var u={label:e.name+"("+e.key+")",value:e.key,path:i?[].concat(Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__.a)(i),[e.key]):[e.key],obj:e};if("grid"!=e.type||e.isDelete)if("group"!=e.type||e.isDelete)if("matrix"!=e.type||e.isDelete)e.isDelete||(s?s(e)&&t.push(u):t.push(u));else{u.children=[];var c,d=_createForOfIteratorHelper(e.matrixData);try{for(d.s();!(c=d.n()).done;){var f,p=c.value,m=_createForOfIteratorHelper(p);try{for(m.s();!(f=m.n()).done;){var h,g=f.value,_=_createForOfIteratorHelper(g);try{for(_.s();!(h=_.n()).done;){var b=h.value;widget2tree(b,u.children,u.path,a)}}catch(e){_.e(e)}finally{_.f()}}}catch(e){m.e(e)}finally{m.f()}}}catch(e){d.e(e)}finally{d.f()}a?(t.push(_objectSpread(_objectSpread({},u),{},{label:u.label+"-(控件)",children:[]})),t.push(_objectSpread(_objectSpread({},u),{},{label:u.label+"-(容器)",value:u.value+"_c",path:i?[].concat(Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__.a)(i),[e.key+"_c"]):[e.key+"_c"]}))):t.push(u)}else{u.children=[];var y,v=_createForOfIteratorHelper(e.columns[0].list);try{for(v.s();!(y=v.n()).done;){var w=y.value;s?s(w)&&widget2tree(w,u.children,u.path,a):widget2tree(w,u.children,u.path,a)}}catch(e){v.e(e)}finally{v.f()}a?(t.push(_objectSpread(_objectSpread({},u),{},{label:u.label+"-(控件)",children:[]})),t.push(_objectSpread(_objectSpread({},u),{},{label:u.label+"-(容器)",value:u.value+"_c",path:i?[].concat(Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_10__.a)(i),[e.key+"_c"]):[e.key+"_c"]}))):t.push(u)}else{var O,k=_createForOfIteratorHelper(e.columns);try{for(k.s();!(O=k.n()).done;){var x,M=O.value,E=_createForOfIteratorHelper(M.list);try{for(E.s();!(x=E.n()).done;){var j=x.value;s?s(j)&&widget2tree(j,t,i,a):widget2tree(j,t,i,a)}}catch(e){E.e(e)}finally{E.f()}}}catch(e){k.e(e)}finally{k.f()}}}}function widgetList2Map(e){var t,i={},n=_createForOfIteratorHelper(e);try{for(n.s();!(t=n.n()).done;){o(t.value,i)}}catch(e){n.e(e)}finally{n.f()}return i;function o(e,t){var i=e.type;if("group"!=i&&"grid"!=i||e.isDelete)if("matrix"!=i||e.isDelete)if("table"!=i||e.isDelete)e.isDelete||(t[e.key]=e);else{t[e.key]=e;var n,a=_createForOfIteratorHelper(e.tableColumns);try{for(a.s();!(n=a.n()).done;){o(n.value,t)}}catch(e){a.e(e)}finally{a.f()}}else{t[e.key]=e;var r,l=_createForOfIteratorHelper(e.matrixData);try{for(l.s();!(r=l.n()).done;){var s,u=_createForOfIteratorHelper(r.value);try{for(u.s();!(s=u.n()).done;){var c,d=_createForOfIteratorHelper(s.value);try{for(d.s();!(c=d.n()).done;){o(c.value,t)}}catch(e){d.e(e)}finally{d.f()}}}catch(e){u.e(e)}finally{u.f()}}}catch(e){l.e(e)}finally{l.f()}}else{"group"==i&&(t[e.key]=e);var f,p=_createForOfIteratorHelper(e.columns);try{for(p.s();!(f=p.n()).done;){var m,h=_createForOfIteratorHelper(f.value.list);try{for(h.s();!(m=h.n()).done;){o(m.value,t)}}catch(e){h.e(e)}finally{h.f()}}}catch(e){p.e(e)}finally{p.f()}}}}var valtorMap={a:{pattern:"/^[A-Za-z]+$/",message:"请输入英文字母",label:"英文字母"},myDate:{pattern:"/^[1-9]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/",message:"请输入日期格式，例如：2014-05-21",label:"日期YYYY-mm-dd"},d100:{pattern:"/^(?:[1-9]?\\d|100)$/",message:"请输入0-100之间的整数",label:"0-100整数"}};function WidgetIterator(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!e.list||!t)return!0;var n,o=_createForOfIteratorHelper(e.list);try{for(o.s();!(n=o.n()).done;){var a=n.value,r=a.type;if("grid"==r||"group"==r){if(i&&!1===t(a))return!1;if(a.columns){var l,s=_createForOfIteratorHelper(a.columns);try{for(s.s();!(l=s.n()).done;){var u=l.value;WidgetIterator({list:u.list},t,i)}}catch(e){s.e(e)}finally{s.f()}}}else if("table"==r){if(i&&!1===t(a))return!1;if(a.tableColumns){var c,d=_createForOfIteratorHelper(a.tableColumns);try{for(d.s();!(c=d.n()).done;){var f=c.value;if(!1===t(f))return!1}}catch(e){d.e(e)}finally{d.f()}}}else if("groups"==r){if(i&&!1===t(a))return!1;if(a.groupItems){var p,m=_createForOfIteratorHelper(a.groupItems);try{for(m.s();!(p=m.n()).done;){var h=p.value;if(!1===t(h))return!1}}catch(e){m.e(e)}finally{m.f()}}}else if("matrix"==r){if(i&&!1===t(a))return!1;if(a.matrixData){var g,_=_createForOfIteratorHelper(a.matrixData);try{for(_.s();!(g=_.n()).done;){var b,y=g.value,v=_createForOfIteratorHelper(y);try{for(v.s();!(b=v.n()).done;){var w,O=b.value,k=_createForOfIteratorHelper(O);try{for(k.s();!(w=k.n()).done;){var x=w.value;if(!1===t(x))return!1}}catch(e){k.e(e)}finally{k.f()}}}catch(e){v.e(e)}finally{v.f()}}}catch(e){_.e(e)}finally{_.f()}}}else if(!1===t(a))return!1}}catch(e){o.e(e)}finally{o.f()}return!0}function WidgetFilter(e,t){e.list=e.list.filter(t);var i,n=_createForOfIteratorHelper(e.list);try{for(n.s();!(i=n.n()).done;){var o=i.value,a=o.type;if("grid"==a||"group"==a){var r,l=_createForOfIteratorHelper(o.columns);try{for(l.s();!(r=l.n()).done;){WidgetFilter(r.value,t)}}catch(e){l.e(e)}finally{l.f()}}else if("table"==a)o.tableColumns=o.tableColumns.filter(t);else if("matrix"==a){var s,u=_createForOfIteratorHelper(o.matrixData);try{for(u.s();!(s=u.n()).done;)for(var c=s.value,d=0;d<c.length;d++){var f=c[d];c[d]=f.filter(t)}}catch(e){u.e(e)}finally{u.f()}}}}catch(e){n.e(e)}finally{n.f()}return!0}var defaultWidgetFormConfig={labelWidth:130,title:"",isTitleShow:!0,labelPosition:"right",size:"small",mode:"pc",theme:{name:"default",formBackgroundColor:"#ffffff",whBackgroundColor:"",inputBorderColor:""},event:{key:"form",types:{formOpenBefore:[],formDataLoaded:[],formOpenAfter:[]}},observationConfig:{SpecialVal:""},scripts:{list:[]},outDataProps:{list:[]},dbConfig:{list:[]},scoreConfig:{desc:"",metaFieldList:[],scoreFieldList:[]}},defaultFeatureConfig={isExternalDataCanUse:!0,isPcMobileChangeCanUse:!0,isObservePeriodCanUse:!0,isThemeConfigCanUse:!0,isPhoneCodeShow:!1};function getDefaultExternaldataInfo(){return{CatID:"",ItemCategoryCode:"",ItemCode:"",ItemProp:"",SpecialValueCode:"",ClassName:"",WebServiceUrl:"",others:[]}}function mergeJsonData(jsonData){return!jsonData||"{}"==jsonData||jsonData instanceof Object&&"{}"==JSON.stringify(jsonData)?jsonData={list:[],config:JSON.parse(JSON.stringify(defaultWidgetFormConfig))}:"string"==typeof jsonData?(jsonData=eval("("+jsonData+")"),jsonData.list||(jsonData.list=[]),jsonData.config?jsonData.config=_objectSpread(_objectSpread({},defaultWidgetFormConfig),jsonData.config):jsonData.config=JSON.parse(JSON.stringify(defaultWidgetFormConfig))):(jsonData.list||(jsonData.list=[]),jsonData.config?jsonData.config=_objectSpread(_objectSpread({},defaultWidgetFormConfig),jsonData.config):jsonData.config=JSON.parse(JSON.stringify(defaultWidgetFormConfig))),jsonData}},"35d4":function(e,t){t.f={}.propertyIsEnumerable},"3a0d":function(e,t,i){var n=i("baa7")("keys"),o=i("d8b3");e.exports=function(e){return n[e]||(n[e]=o(e))}},"3a4c":function(e,t,i){var n=i("4fd4"),o=i("3471"),a=i("52a4")(!1),r=i("3a0d")("IE_PROTO");e.exports=function(e,t){var i,l=o(e),s=0,u=[];for(i in l)i!=r&&n(l,i)&&u.push(i);for(;t.length>s;)n(l,i=t[s++])&&(~a(u,i)||u.push(i));return u}},"3ab0":function(e,t){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},"3c56":function(e,t,i){var n=i("93ca"),o=i("0c29"),a=i("35d4");e.exports=function(e){var t=n(e),i=o.f;if(i)for(var r,l=i(e),s=a.f,u=0;l.length>u;)s.call(e,r=l[u++])&&t.push(r);return t}},"3ce5":function(e,t,i){"use strict";function n(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}i.d(t,"a",(function(){return n}))},"3d8a":function(e,t){e.exports=!1},"3e4e":function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));i("9f60"),i("94f0"),i("0c84"),i("2843"),i("a450"),i("4057");function n(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return o(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return o(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,l=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return l=e.done,e},e:function(e){s=!0,r=e},f:function(){try{l||null==i.return||i.return()}finally{if(s)throw r}}}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var a=[{label:"默认风格",children:[{code:"default_light",label:"亮色主题",name:"default",formBackgroundColor:"#DFE4EB",whBackgroundColor:"#ACE2EA",inputBorderColor:"#767DF4"},{code:"default_dark",label:"暗色主题",name:"default",formBackgroundColor:"#969696",whBackgroundColor:"#809F9C",inputBorderColor:"#3D5799"},{code:"default_warm",label:"暖色主题",name:"default",formBackgroundColor:"#D3928B",whBackgroundColor:"#D7A379",inputBorderColor:"#E0B668"},{code:"default_cool",label:"冷色主题",name:"default",formBackgroundColor:"#B4B9D2",whBackgroundColor:"#9EACCF",inputBorderColor:"#68B2E0"}]},{label:"表单风格",children:[{code:"table_light",label:"亮色主题",name:"table",formBackgroundColor:"#DFE4EB",whBackgroundColor:"#ACE2EA",inputBorderColor:"#767DF4"},{code:"table_dark",label:"暗色主题",name:"table",formBackgroundColor:"#969696",whBackgroundColor:"#809F9C",inputBorderColor:"#3D5799"},{code:"table_warm",label:"暖色主题",name:"table",formBackgroundColor:"#D3928B",whBackgroundColor:"#D7A379",inputBorderColor:"#E0B668"},{code:"table_cool",label:"冷色主题",name:"table",formBackgroundColor:"#B4B9D2",whBackgroundColor:"#9EACCF",inputBorderColor:"#68B2E0"}]},{label:"卡片风格",children:[{code:"card_light",label:"亮色主题",name:"card",formBackgroundColor:"#DFE4EB",whBackgroundColor:"#ACE2EA",inputBorderColor:"#767DF4"},{code:"card_dark",label:"暗色主题",name:"card",formBackgroundColor:"#969696",whBackgroundColor:"#809F9C",inputBorderColor:"#3D5799"},{code:"card_warm",label:"暖色主题",name:"card",formBackgroundColor:"#D3928B",whBackgroundColor:"#D7A379",inputBorderColor:"#E0B668"},{code:"card_cool",label:"冷色主题",name:"card",formBackgroundColor:"#B4B9D2",whBackgroundColor:"#9EACCF",inputBorderColor:"#68B2E0"}]},{label:"HOS-UI风格",children:[{code:"hos_light",label:"亮色主题",name:"card",formBackgroundColor:"#DFE4EB",whBackgroundColor:"#ACE2EA",inputBorderColor:"#767DF4"},{code:"hos_dark",label:"暗色主题",name:"card",formBackgroundColor:"#969696",whBackgroundColor:"#809F9C",inputBorderColor:"#3D5799"},{code:"hos_warm",label:"暖色主题",name:"card",formBackgroundColor:"#D3928B",whBackgroundColor:"#D7A379",inputBorderColor:"#E0B668"},{code:"card_cool",label:"冷色主题",name:"card",formBackgroundColor:"#B4B9D2",whBackgroundColor:"#9EACCF",inputBorderColor:"#68B2E0"}]},{label:"自定义主题",children:[{code:"hxc021",label:"自定义主题1",name:"card",formBackgroundColor:"#EEEEEE",whBackgroundColor:"#CCCCCC",inputBorderColor:null}]}];function r(e){var t,i=n(a);try{for(i.s();!(t=i.n()).done;){var o,r=n(t.value.children);try{for(r.s();!(o=r.n()).done;){var l=o.value;if(l.code==e)return l}}catch(e){r.e(e)}finally{r.f()}}}catch(e){i.e(e)}finally{i.f()}return null}},"3f9e":function(e,t,i){var n=i("bb8b"),o=i("a86f"),a=i("93ca");e.exports=i("26df")?Object.defineProperties:function(e,t){o(e);for(var i,r=a(t),l=r.length,s=0;l>s;)n.f(e,i=r[s++],t[i]);return e}},4057:function(e,t,i){"use strict";i("de49");var n=i("a86f"),o=i("6bf8"),a=i("26df"),r=/./.toString,l=function(e){i("84e8")(RegExp.prototype,"toString",e,!0)};i("0926")((function(){return"/a/b"!=r.call({source:"a",flags:"b"})}))?l((function(){var e=n(this);return"/".concat(e.source,"/","flags"in e?e.flags:!a&&e instanceof RegExp?o.call(e):void 0)})):"toString"!=r.name&&l((function(){return r.call(this)}))},4260:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));i("0c84"),i("2843"),i("4057"),i("8dee"),i("5f1c");var n=function(e,t){return e.tagName.toLowerCase()===t?e:e.querySelector(t)},o={bind:function(e,t,i){var o=n(e,"input")||n(e,"textarea");e.$inp=o,"number"!==o.type&&(o.handle=function(){var e,t=o.value;o.value=(e=t,Array.from(e).filter((function(e){return!(e.length>1)})).join("")),function(e,t){var i=document.createEvent("HTMLEvents");i.initEvent(t,!0,!0),e.dispatchEvent(i)}(o,"input")},o.addEventListener("keyup",o.handle),o.addEventListener("blur",o.handle))},unbind:function(e){e.$inp.removeEventListener("keyup",e.$inp.handle),e.$inp.removeEventListener("blur",e.$inp.handle)}}},"43ec":function(e,t,i){"use strict";var n=i("1663")(!0);e.exports=function(e,t,i){return t+(i?n(e,t).length:1)}},4441:function(e,t,i){var n=i("3471"),o=i("21d9").f,a={}.toString,r="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return r&&"[object Window]"==a.call(e)?function(e){try{return o(e)}catch(e){return r.slice()}}(e):o(n(e))}},"44a4":function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));var n=i("484e");var o=i("edb4");function a(e){return function(e){if(Array.isArray(e))return Object(n.a)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Object(o.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},4836:function(e,t,i){var n=i("a86f"),o=i("9cff"),a=i("d4c9");e.exports=function(e,t){if(n(e),o(t)&&t.constructor===e)return t;var i=a.f(e);return(0,i.resolve)(t),i.promise}},"484e":function(e,t,i){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}i.d(t,"a",(function(){return n}))},"495d":function(e,t,i){"use strict";var n=i("4260");t.a={directives:{emoji:n.a}}},"49f2":function(e,t,i){var n=i("d8b3")("meta"),o=i("9cff"),a=i("4fd4"),r=i("bb8b").f,l=0,s=Object.isExtensible||function(){return!0},u=!i("0926")((function(){return s(Object.preventExtensions({}))})),c=function(e){r(e,n,{value:{i:"O"+ ++l,w:{}}})},d=e.exports={KEY:n,NEED:!1,fastKey:function(e,t){if(!o(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!a(e,n)){if(!s(e))return"F";if(!t)return"E";c(e)}return e[n].i},getWeak:function(e,t){if(!a(e,n)){if(!s(e))return!0;if(!t)return!1;c(e)}return e[n].w},onFreeze:function(e){return u&&d.NEED&&s(e)&&!a(e,n)&&c(e),e}}},"4abe":function(e,t,i){},"4b80":function(e,t,i){},"4fd4":function(e,t){var i={}.hasOwnProperty;e.exports=function(e,t){return i.call(e,t)}},"52a4":function(e,t,i){var n=i("3471"),o=i("201c"),a=i("732b");e.exports=function(e){return function(t,i,r){var l,s=n(t),u=o(s.length),c=a(r,u);if(e&&i!=i){for(;u>c;)if((l=s[c++])!=l)return!0}else for(;u>c;c++)if((e||c in s)&&s[c]===i)return e||c||0;return!e&&-1}}},"53ea":function(e,t,i){},"565b":function(e,t,i){"use strict";var n=i("44a4"),o=(i("a450"),i("1bc7"),i("25ba"),i("32ea"),i("fc02"),i("7286"));var a={mixins:[{inject:{all$vm:{from:"all$vm",default:function(){}},eventBus:{from:"eventBus",default:function(){}},gfItemBus:{from:"gfItemBus",default:function(){}},widget:{from:"widget",default:function(){}},toolModel:{from:"toolModel",default:function(){}}},data:function(){return{}},watch:{},computed:{externaldata:function(){return this.gfItemBus.externaldata},deSettings:function(){return this.gfItemBus.deSettings}},methods:{externalData2Value:function(e){return"radio"==this.field.type||"select"==this.field.type?e="0"==this.field.externaldataInfo.DEGetValue?Object(o.a)(e,this.field,!0):Object(o.a)(e,this.field):"checkbox"!=this.field.type&&"selectMulti"!=this.field.type||("0"==this.field.externaldataInfo.DEGetValue?(e=e.split("、"),e=Object(o.a)(e,this.field,!0)):(e=e.split("、"),e=Object(o.a)(e,this.field))),e}}}],created:function(){var e=this;this.gfItemBus.$on("widget-tool-fill-externaldata",(function(){e.fillExternalData()})),this.gfItemBus.$on("widget-tool-fill-other-externaldata",(function(t,i){e.fillOtherExternalData(t,i)}))},props:{field:{required:!0}},inject:{disabled:{from:"disabled",default:function(){return!1}}},data:function(){return{externalVisible:!1}},methods:{externalItemClick:function(e,t){this.toolModel[this.field.key]=this.externalData2Value(e[t]),this.externalVisible=!1},fillExternalData:function(){var e=this.externaldata[this.field.key];if(e&&e.length>0){var t=null;this.field.externaldataInfo.ItemProp&&(t=this.field.externaldataInfo.ItemProp),t&&!this.toolModel[this.field.key]&&0!==this.toolModel[this.field.key]&&(this.toolModel[this.field.key]=this.externalData2Value(e[0][t]))}},fillOtherExternalData:function(e,t){Object.keys(this.toolModel).indexOf(e)>=0&&(this.toolModel[e]=this.externalData2Value(t))}},computed:{externalColums:function(){for(var e=this,t=this.deSettings.deItemProp.filter((function(t){return("1"==t.displayInOutInterface||t.code==e.field.externaldataInfo.ItemProp)&&t.categoryId==e.field.externaldataInfo.CatID})),i=null,n=0;n<t.length;n++)if(t[n].code==this.field.externaldataInfo.ItemProp){i=t.splice(n,1)[0];break}return i&&t.unshift(i),t}}},r=i("cba8"),l=Object(r.a)(a,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"suffix-btns-inner"},[i("div",{staticClass:"gf-btn-box-inner"},[!e.disabled&&e.deSettings&&e.externaldata[e.field.key]&&e.field.externaldataInfo.ItemProp?i("el-popover",{staticClass:"ex-button",attrs:{"popper-class":"de-settings-popover",trigger:"click"},model:{value:e.externalVisible,callback:function(t){e.externalVisible=t},expression:"externalVisible"}},[i("el-table",{staticStyle:{width:"100%"},attrs:{"max-height":"400",data:e.externaldata[e.field.key],border:"",stripe:!0}},[i("el-table-column",{attrs:{label:"操作",width:"100",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"primary"},on:{click:function(i){e.externalItemClick(t.row,e.field.externaldataInfo.ItemProp.toLocaleLowerCase())}}},[e._v("选择")])]}}],null,!1,1285861)}),e._l(e.externalColums,(function(e){return i("el-table-column",{key:e.id,attrs:{"show-overflow-tooltip":!0,width:e.displayInOutInterfaceWidth>100?e.displayInOutInterfaceWidth:100,prop:e.code.toLocaleLowerCase(),label:e.name}})}))],2),i("el-button",{staticClass:"ex-button__inner",attrs:{slot:"reference",type:"default",size:"mini",title:"外部数据"},slot:"reference"},[e._v("外\r\n                ")])],1):e._e()],1)])}),[],!1,null,null,null).exports,s=i("bff0"),u=i("f1af"),c=i("0474"),d={mixins:[u.a],components:{AuditDialog:s.a},provide:function(){return{field:this.field,toolModel:this.toolModel}},inject:{all$vm:{from:"all$vm",default:function(){}},eventBus:{from:"eventBus",default:function(){}},gfItemBus:{from:"gfItemBus",default:function(){}},widget:{from:"widget",default:function(){}},toolModel:{from:"toolModel",default:function(){}}},props:{field:{required:!0}},data:function(){return{auditDialogVisible:!1}},computed:{isAuditMode:function(){return this.gfItemBus.isAuditMode},isAuditor:function(){return this.gfItemBus.isAuditor},widgetForm:function(){return this.gfItemBus.widgetForm},isExternalDataAndDataModelNotEqual:function(){return Object(c.j)(this.gfItemBus.externaldata,this.field,this.toolModel[this.field.key])}}},f={components:{SuffixBtnsInner:l,SuffixBtnsOutter:Object(r.a)(d,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"suffix-btns-outter"},[e.isExternalDataAndDataModelNotEqual?i("el-tooltip",{staticClass:"ex-button-tooltip",attrs:{effect:"dark",content:"当前字段的值与外部数据的取值不一致 (当前值:"+e.isExternalDataAndDataModelNotEqual.now+",外部接口值:"+e.isExternalDataAndDataModelNotEqual.external+")",placement:"bottom"}},[i("el-button",{attrs:{size:e.widgetForm.config.size,icon:"el-icon-warning"}})],1):e._e(),e.gfItemBus.isInGroupsItemNew?e._e():i("div",{staticClass:"gf-btn-box"},[e.optionBtnPermission(2)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"通过"},on:{click:e.toLockField}},[e._v("通过")]):e._e(),e.optionBtnPermission(4)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"解锁"},on:{click:e.toUnlockField}},[e._v("解锁")]):e._e(),e.optionBtnPermission(3)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"回复"},on:{click:function(t){return e.openAuditDialog({})}}},[e._v("回复")]):e._e(),e.optionBtnPermission(1)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"审核"},on:{click:function(t){return e.openAuditDialog({})}}},[e._v("审核")]):e._e(),e.optionBtnPermission(5)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"编辑"},on:{click:e.doEditing}},[e._v("编辑")]):e._e(),e.optionBtnPermission(6)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"保存"},on:{click:e.saveField}},[e._v("保存")]):e._e(),e.optionBtnPermission(7)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"撤回"},on:{click:e.toApplyBack}},[e._v("撤回")]):e._e(),i("apply-back-reason-dialog",{attrs:{visible:e.ApplyBackReasonDialogVisible},on:{"update:visible":function(t){e.ApplyBackReasonDialogVisible=t},confirm:e.applyBackConfirm}})],1),e.auditDialogVisible&&e.isAuditMode?i("audit-dialog",{attrs:{visible:e.auditDialogVisible,"open-params":e.openParams},on:{"update:visible":function(t){e.auditDialogVisible=t}}}):e._e()],1)}),[],!1,null,null,null).exports,AuditStatus:i("32e5").a},inject:{eventBus:{from:"eventBus",default:function(){}},gfItemBus:{from:"gfItemBus",default:function(){}},all$vm:{from:"all$vm",default:function(){}},widget:{from:"widget",default:function(){}},toolModel:{from:"toolModel",default:function(){}},groupsWidget:{from:"groupsWidget",default:function(){return null}},groupsIndex:{from:"groupsIndex",default:function(){return null}}},provide:function(){return{disabled:this.disabled,toolFieldBus:this}},props:{field:{required:!0},label:{type:String,default:function(){return this.field.name}},showLabel:{type:Boolean,default:function(){return!0}},isTableItem:{type:Boolean,default:function(){return!1}},rules:{type:Array,default:function(){return[]}}},created:function(){var e=this;this.all$vm[this.field.key]=this,this.all$vm[this.fieldKey]=this,this.$watch("toolModel."+this.field.key,(function(t,i){e.gfItemBus.$emit("field-value-change",e.fieldKey,t,i,e.gfItemBus)}))},data:function(){return{}},computed:{required:function(){return this.fullRules.filter((function(e){return e.required})).length>0},fieldKey:function(){var e=this.field.key;return this.groupsWidget&&(e=this.groupsWidget.key+"."+e),void 0!==this.groupsIndex&&null!==this.groupsIndex&&(e+="^"+this.groupsIndex),e},disabled:function(){return!this.eventBus.editing[this.fieldKey]&&(!this.gfItemBus.isInGroupsItemNew&&(!!this.eventBus.formDisabled||(!!(this.eventBus.audit&&this.eventBus.audit.lock&&this.eventBus.audit.lock.length>0&&this.eventBus.audit.lock.indexOf(this.fieldKey)>=0)||(this.eventBus.disabledList.indexOf(this.fieldKey)>=0||this.widget.options.disabled))))},isAuditMode:function(){return this.eventBus.isAuditMode},fullRules:function(){return[].concat(Object(n.a)(this.widget.rules),Object(n.a)(this.rules))},labelComputed:function(){return this.widget.isLabelHide?"":this.label+":"},dataModel:{get:function(){return this.toolModel[this.field.key]},set:function(e){this.$set(this.toolModel,this.field.key,e)}}},methods:{fillOutData:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.dataModel&&!e||this.widget.options.outDataKey&&this.eventBus.outData&&void 0!==this.eventBus.outData[this.widget.options.outDataKey]&&(this.dataModel=this.eventBus.outData[this.widget.options.outDataKey])},openAuditDialog:function(e){this.$refs.outter&&this.$refs.outter.openAuditDialog(e)},closeAuditDialog:function(e){this.$refs.outter&&this.$refs.outter.closeAuditDialog(e)}}},p=Object(r.a)(f,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form-item",e._b({ref:"ElFormItem",staticClass:"tool-field",class:{"widget-label-top":e.widget.options.isLabelTop,"form-item__table-cell":e.isTableItem,"widget-audit":e.isAuditMode},attrs:{prop:e.field.key,rules:e.fullRules},scopedSlots:e._u([{key:"label",fn:function(){return[i("span",{style:{color:e.widget.options.nameColor||"#606266","font-style":e.widget.options.isNameItalic?"italic":"normal","font-weight":e.widget.options.isNameBold?"bold":"normal","font-family":e.widget.options.nameFamily?e.widget.options.nameFamily:"Arial","font-size":e.widget.options.nameSize?e.widget.options.nameSize:"14px"}},[e.required?i("i",{staticClass:"required-x"}):e._e(),e.gfItemBus.isInGroupsItemNew?e._e():i("audit-status",{attrs:{field:e.field}}),e.showLabel?i("span",[e.$slots.label?[e._t("label",(function(){return[e._v("\r\n                            "+e._s(e.labelComputed)+"\r\n                        ")]}))]:[e._v("\r\n                        "+e._s(e.labelComputed)+"\r\n                    ")]],2):e._e(),e.widget.options.remark&&e.widget.options.remark.trim()?i("el-popover",{attrs:{placement:"right","popper-class":"gf-remark-popover",title:"",width:"200",trigger:"hover",content:e.widget.options.remark}},[i("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"14px",color:"#868180"},attrs:{slot:"reference"},slot:"reference"})]):e._e()],1)]},proxy:!0}],null,!0)},"el-form-item",e.$attrs,!1),[e._t("default",null,{disabled:e.disabled}),i("suffix-btns-inner",{attrs:{field:e.field}}),i("suffix-btns-outter",{ref:"outter",attrs:{field:e.field}})],2)}),[],!1,null,null,null);t.a=p.exports},"581c":function(e,t,i){var n=i("839a")("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(i){try{return t[n]=!1,!"/./"[e](t)}catch(e){}}return!0}},"5b87":function(e,t,i){"use strict";var n=i("7286"),o={mixins:[i("495d").a],props:{value:{},field:{},disabled:{type:Boolean,default:function(){return!1}}},data:function(){return{dataModel:Object(n.b)(this.value,this.field)||[]}},created:function(){var e=this;this.field.hasOtherOption&&(void 0===this.field.otherOptionValue?this.$set(this.field,"otherOptionValue",""):this.$set(this.field,"otherOptionValue",this.field.otherOptionValue)),this.$watch("field.otherOptionValue",(function(){var t=Object(n.a)(e.dataModel,e.field);e.$emit("input",t)}))},watch:{value:{deep:!0,handler:function(e){this.dataModel=Object(n.b)(e,this.field)}},dataModel:function(e,t){if(JSON.stringify(e)!=JSON.stringify(t)){var i=Object(n.a)(e,this.field);this.$emit("input",i)}}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-checkbox-group",e._b({staticClass:"option-checkbox",model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},"el-checkbox-group",e.$attrs,!1),[e._l(e.field.options,(function(t,n){return i("el-checkbox",{key:n,attrs:{label:t.value}},[e._v(e._s(t.label))])})),e.field.hasOtherOption?[i("el-checkbox",{attrs:{label:"other_option"}},[e._v(e._s(e.field.otherOptionLabel)),e.dataModel&&e.dataModel.length>0&&e.dataModel.indexOf("other_option")>=0?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],staticClass:"fm-option__extend-input",attrs:{disabled:e.disabled||!e.dataModel||e.dataModel.indexOf("other_option")<0,size:"mini"},model:{value:e.field.otherOptionValue,callback:function(t){e.$set(e.field,"otherOptionValue",t)},expression:"field.otherOptionValue"}}):e._e()],1)]:e._e()],2)}),[],!1,null,null,null);t.a=r.exports},"5d10":function(e,t,i){var n=i("9cff");e.exports=function(e,t){if(!n(e))return e;var i,o;if(t&&"function"==typeof(i=e.toString)&&!n(o=i.call(e)))return o;if("function"==typeof(i=e.valueOf)&&!n(o=i.call(e)))return o;if(!t&&"function"==typeof(i=e.toString)&&!n(o=i.call(e)))return o;throw TypeError("Can't convert object to primitive value")}},"5dc3":function(e,t){e.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},"5edc":function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"5f1c":function(e,t,i){"use strict";var n,o,a,r,l=i("3d8a"),s=i("0b34"),u=i("1e4d"),c=i("d445"),d=i("e99b"),f=i("9cff"),p=i("3250"),m=i("8b5a"),h=i("2b37"),g=i("1b0b"),_=i("edec").set,b=i("2d39")(),y=i("d4c9"),v=i("fb49"),w=i("aeb8"),O=i("4836"),k=s.TypeError,x=s.process,M=x&&x.versions,E=M&&M.v8||"",j=s.Promise,D="process"==c(x),I=function(){},C=o=y.f,S=!!function(){try{var e=j.resolve(1),t=(e.constructor={})[i("839a")("species")]=function(e){e(I,I)};return(D||"function"==typeof PromiseRejectionEvent)&&e.then(I)instanceof t&&0!==E.indexOf("6.6")&&-1===w.indexOf("Chrome/66")}catch(e){}}(),B=function(e){var t;return!(!f(e)||"function"!=typeof(t=e.then))&&t},P=function(e,t){if(!e._n){e._n=!0;var i=e._c;b((function(){for(var n=e._v,o=1==e._s,a=0,r=function(t){var i,a,r,l=o?t.ok:t.fail,s=t.resolve,u=t.reject,c=t.domain;try{l?(o||(2==e._h&&F(e),e._h=1),!0===l?i=n:(c&&c.enter(),i=l(n),c&&(c.exit(),r=!0)),i===t.promise?u(k("Promise-chain cycle")):(a=B(i))?a.call(i,s,u):s(i)):u(n)}catch(e){c&&!r&&c.exit(),u(e)}};i.length>a;)r(i[a++]);e._c=[],e._n=!1,t&&!e._h&&A(e)}))}},A=function(e){_.call(s,(function(){var t,i,n,o=e._v,a=T(e);if(a&&(t=v((function(){D?x.emit("unhandledRejection",o,e):(i=s.onunhandledrejection)?i({promise:e,reason:o}):(n=s.console)&&n.error&&n.error("Unhandled promise rejection",o)})),e._h=D||T(e)?2:1),e._a=void 0,a&&t.e)throw t.v}))},T=function(e){return 1!==e._h&&0===(e._a||e._c).length},F=function(e){_.call(s,(function(){var t;D?x.emit("rejectionHandled",e):(t=s.onrejectionhandled)&&t({promise:e,reason:e._v})}))},$=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),P(t,!0))},L=function(e){var t,i=this;if(!i._d){i._d=!0,i=i._w||i;try{if(i===e)throw k("Promise can't be resolved itself");(t=B(e))?b((function(){var n={_w:i,_d:!1};try{t.call(e,u(L,n,1),u($,n,1))}catch(e){$.call(n,e)}})):(i._v=e,i._s=1,P(i,!1))}catch(e){$.call({_w:i,_d:!1},e)}}};S||(j=function(e){m(this,j,"Promise","_h"),p(e),n.call(this);try{e(u(L,this,1),u($,this,1))}catch(e){$.call(this,e)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=i("6f45")(j.prototype,{then:function(e,t){var i=C(g(this,j));return i.ok="function"!=typeof e||e,i.fail="function"==typeof t&&t,i.domain=D?x.domain:void 0,this._c.push(i),this._a&&this._a.push(i),this._s&&P(this,!1),i.promise},catch:function(e){return this.then(void 0,e)}}),a=function(){var e=new n;this.promise=e,this.resolve=u(L,e,1),this.reject=u($,e,1)},y.f=C=function(e){return e===j||e===r?new a(e):o(e)}),d(d.G+d.W+d.F*!S,{Promise:j}),i("bac3")(j,"Promise"),i("f966")("Promise"),r=i("76e3").Promise,d(d.S+d.F*!S,"Promise",{reject:function(e){var t=C(this);return(0,t.reject)(e),t.promise}}),d(d.S+d.F*(l||!S),"Promise",{resolve:function(e){return O(l&&this===r?j:this,e)}}),d(d.S+d.F*!(S&&i("1a9a")((function(e){j.all(e).catch(I)}))),"Promise",{all:function(e){var t=this,i=C(t),n=i.resolve,o=i.reject,a=v((function(){var i=[],a=0,r=1;h(e,!1,(function(e){var l=a++,s=!1;i.push(void 0),r++,t.resolve(e).then((function(e){s||(s=!0,i[l]=e,--r||n(i))}),o)})),--r||n(i)}));return a.e&&o(a.v),i.promise},race:function(e){var t=this,i=C(t),n=i.reject,o=v((function(){h(e,!1,(function(e){t.resolve(e).then(i.resolve,n)}))}));return o.e&&n(o.v),i.promise}})},"636a":function(e,t,i){"use strict";i("53ea")},"6ba0":function(e,t,i){var n=i("e99b");n(n.S+n.F,"Object",{assign:i("9f15")})},"6bf8":function(e,t,i){"use strict";var n=i("a86f");e.exports=function(){var e=n(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},"6c16":function(e,t,i){"use strict";var n=i("495d"),o=i("5b87"),a=i("118b"),r=i("063b"),l=i("d419"),s={mixins:[n.a],components:{OptionCheckbox:o.a,OptionRadio:a.a,OptionSelect:r.a,OptionSelectMulti:l.a},props:{fields:{type:Array,default:function(){return[]}},data:{},model:{}},inject:{eventBus:{from:"eventBus",default:function(){}}},data:function(){return{}},computed:{disabled:function(){return this.eventBus.formDisabled}},methods:{numberInputBlur:function(e){if(this.model[e]||0===this.model[e]){var t=+this.model[e];t!==this.model[e]&&(this.model[e]=t)}}}},u=i("cba8"),c=Object(u.a)(s,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-form",{ref:"form",staticClass:"gf-form",staticStyle:{padding:"10px 30px 0px 20px"},attrs:{model:e.model,size:e.data.config.size,"label-position":e.data.config.labelPosition,"label-width":e.data.config.labelWidth+"px"}},[i("div",{staticClass:"gf-form-row"},e._l(e.fields,(function(t,n){return i("el-form-item",{key:n,attrs:{rules:t.required?[{required:!0,trigger:"blur",message:t.name+"不能为空"}]:[],prop:t.code},scopedSlots:e._u([{key:"label",fn:function(){return[i("span",[e._v(e._s(t.name?t.name+":":""))])]},proxy:!0}],null,!0)},["input"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled,placeholder:""},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"textarea"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled,type:"textarea",rows:5,placeholder:""},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"inputnumber"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled,type:"number",placeholder:""},on:{blur:function(i){return e.numberInputBlur(t.code)}},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"radio"==t.type?i("option-radio",{attrs:{disabled:e.disabled,field:t},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"checkbox"==t.type?i("option-checkbox",{attrs:{disabled:e.disabled,field:t},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"select"==t.type?i("option-select",{staticStyle:{width:"100%"},attrs:{disabled:e.disabled,field:t},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"selectMulti"==t.type?i("option-select-multi",{staticStyle:{width:"100%"},attrs:{disabled:e.disabled,field:t},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):"date"==t.type?i("el-date-picker",{staticStyle:{width:"100%"},attrs:{editable:!0,clearable:!0,disabled:e.disabled,type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择日期"},model:{value:e.model[t.code],callback:function(i){e.$set(e.model,t.code,i)},expression:"model[field.code]"}}):e._e()],1)})),1)])}),[],!1,null,null,null);t.a=c.exports},"6c36":function(e,t,i){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}i.d(t,"a",(function(){return n}))},"6f45":function(e,t,i){var n=i("84e8");e.exports=function(e,t,i){for(var o in t)n(e,o,t[o],i);return e}},"6fef":function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},7286:function(e,t,i){"use strict";i.d(t,"b",(function(){return u})),i.d(t,"a",(function(){return c}));i("ac67"),i("1bc7"),i("25ba"),i("32ea"),i("9f60"),i("94f0"),i("0c84"),i("2843"),i("a450");var n=i("3ce5"),o=(i("4057"),i("6c36"));function a(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function r(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?a(Object(i),!0).forEach((function(t){Object(n.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):a(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function l(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return s(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function u(e,t){if(void 0===e)return"";var i=e;if(!t)return e;if(!e&&0!==e)return e;if("radio"==t.type||"select"==t.type)e?e.value||"1"==e.type?"1"==e.type?(i="other_option",t.otherOptionValue=e.value):i=e.value:i=e:i="";else if("checkbox"==t.type||"selectMulti"==t.type)if(e&&Array.isArray(e)&&e.length)if("object"==Object(o.a)(e[0])){var n,a=[],r=l(e);try{for(r.s();!(n=r.n()).done;){var s=n.value;"1"==s.type?(a.push("other_option"),t.otherOptionValue=s.value):a.push(s.value)}}catch(e){r.e(e)}finally{r.f()}i=a}else i=e;else i=[];else i=e;return i}function c(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e;if("radio"==t.type||"select"==t.type){if("[object Object]"===Object.prototype.toString.call(e))return e;if(e)if("other_option"==e){var o={value:t.otherOptionValue,label:t.otherOptionLabel,type:"1"};t.otherOptionScore&&(o.score=+t.otherOptionScore),n=o}else{var a=t.options.filter((function(t){return i?t.label==e:t.value==e}));n=a.length>0?r(r({},a[0]),{},{type:"0"}):""}else n=""}else if("checkbox"==t.type||"selectMulti"==t.type)if(e&&Array.isArray(e)&&e.length){var s,u=[],c=l(e);try{var d=function(){var e=s.value;if("[object Object]"===Object.prototype.toString.call(e))return u.push(e),"continue";var n=t.options.filter((function(t){return i?t.label==e:t.value==e}));if(n.length>0)u.push(r(r({},n[0]),{},{type:"0"}));else if("other_option"==e){var o={value:t.otherOptionValue,label:t.otherOptionLabel,type:"1"};t.otherOptionScore&&(o.score=+t.otherOptionScore),u.push(o)}};for(c.s();!(s=c.n()).done;)d()}catch(e){c.e(e)}finally{c.f()}n=u}else n=[];else n=e;return n}},"732b":function(e,t,i){var n=i("212e"),o=Math.max,a=Math.min;e.exports=function(e,t){return(e=n(e))<0?o(e+t,0):a(e,t)}},"76e3":function(e,t){var i=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=i)},"7a53":function(e,t,i){"use strict";i.r(t);i("fc02"),i("1bc7");var n=i("5b87"),o=i("118b"),a=i("063b"),r=i("d419"),l=i("21c6"),s=i("565b"),u={components:{OptionRadio:o.a,OptionCheckbox:n.a,OptionSelect:a.a,OptionSelectMulti:r.a,ToolField:s.a},mixins:[Object(l.a)()],type:"t_table",data:function(){return{}},methods:{},created:function(){var e=this,t={};this.widget.toolTableCols.forEach((function(e){t[e.key]=e})),this.widget.fields.forEach((function(i){var n=i.key,o=n.split("_c_")[n.split("_c_").length-1];t[o]&&t[o].options&&e.$set(i,"options",t[o].options)}))},computed:{tableData:function(){for(var e=[],t=0;t<this.widget.toolTableRows.length;t++){e.push({})}return e}}},c=i("cba8"),d=Object(c.a)(u,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-tool-table"},[!e.widget.options.isLabelHide&&e.widget.name?i("div",{staticStyle:{padding:"8px","text-align":"center","font-size":"18px",color:"#909399","font-weight":"bold"}},[i("span",[e._v(e._s(e.widget.name))])]):e._e(),i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,border:""}},[i("el-table-column",{attrs:{align:"center","header-align":"center",label:"#"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n              "+e._s(e.widget.toolTableRows[t.$index].name)+"\n          ")]}}])}),e._l(e.widget.toolTableCols,(function(t,n){return i("el-table-column",{key:t.key,attrs:{align:"center","header-align":"center",label:t.name},scopedSlots:e._u([{key:"default",fn:function(o){return[i("tool-field",{attrs:{field:e.widget.fields[o.$index*e.widget.toolTableCols.length+n],"show-label":!1,"is-table-item":!0},scopedSlots:e._u([{key:"default",fn:function(n){var a=n.disabled;return["input"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:a,placeholder:""},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"textarea"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:a,type:"textarea",rows:5,placeholder:""},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"inputnumber"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:a,type:"number",placeholder:""},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"radio"==t.type?i("option-radio",{attrs:{disabled:a,field:t},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"checkbox"==t.type?i("option-checkbox",{attrs:{disabled:a,field:t},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"select"==t.type?i("option-select",{attrs:{disabled:a,field:t},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"selectMulti"==t.type?i("option-select-multi",{attrs:{disabled:a,field:t},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):"date"==t.type?i("el-date-picker",{attrs:{disabled:a,editable:!0,clearable:!0,type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd"},model:{value:e.toolModel[e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key],callback:function(i){e.$set(e.toolModel,e.widget.key+"_r_"+e.widget.toolTableRows[o.$index].key+"_c_"+t.key,i)},expression:"toolModel[`${widget.key}_r_${widget.toolTableRows[scope.$index].key}_c_${col.key}`]"}}):e._e()]}}],null,!0)})]}}],null,!0)})}))],2)],1)}),[],!1,null,null,null);t.default=d.exports},"7a97":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"a",(function(){return o}));i("a450"),i("1bc7"),i("34a9");var n=[{type:"t_bmi",isTool:!0,icon:"icon-danhangwenben1",label:"BMI计算器",name:"BMI计算器",options:{show:!0,disabled:!1,isFillRate:!0,show_BMI_name:!1},fields:[{key:"",code:"h",name:"身高",unit:"cm"},{key:"",code:"w",name:"体重",unit:"kg"},{key:"",code:"bmi",name:"BMI",decimalPoint:2}],event:{}},{type:"t_table",isTool:!0,isOnlyForPc:!0,name:"输入表",label:"输入表",icon:"icon-juzhen",options:{show:!0,isLabelHide:!1,disabled:!1},fields:[],toolTableRows:[{key:"",name:"第1行"},{key:"",name:"第2行"}],toolTableCols:[{key:"",name:"第1列",type:"input"},{key:"",name:"第2列",type:"textarea"}],event:{}},{type:"t_birthday",name:"出生日期",valueName:"",label:"出生日期",isTool:!0,icon:"icon-riqi",fields:[{key:"",code:"b",name:"出生日期"}],phoneCode:"",nameCode:"",options:{show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",required:!1,disabled:!1,placeholder:"",remark:"",isFillRate:!0},event:{}},{type:"t_float",name:"浮点数",valueName:"",label:"浮点数",isTool:!0,icon:"icon-shuzhishurukuangbeifen",fields:[{key:"",code:"f",name:"浮点数"}],phoneCode:"",nameCode:"",options:{show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",defaultValue:"",required:!1,disabled:!1,placeholder:"",remark:"",decimalPoint:2,isFillRate:!0},event:{}},{type:"t_nation",name:"民族",valueName:"",label:"民族",isTool:!0,icon:"icon-xialaxuanze",fields:[{key:"",code:"nation",name:"民族"}],phoneCode:"",nameCode:"",options:{show:!0,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",required:!1,disabled:!1,placeholder:"",remark:"",isFillRate:!0},event:{}},{type:"t_medical_image",name:"医学影像",valueName:"",label:"医学影像",isTool:!0,isOnlyForPc:!0,icon:"icon-icons01",fields:[{key:"",code:"i",name:"医学影像"}],nameCode:"",options:{show:!0,width:100,isLabelTop:!1,isLabelHide:!1,isNameBold:!1,isNameItalic:!1,nameColor:"#606266",nameFamily:"",nameSize:"",isMultipleImg:!1,defaultValue:[],required:!1,disabled:!1,remark:"",script:"/**\n  * 点击执行函数\n  * @description 点击图片上传按钮后执行的函数\n  * @params {Function} setData 上传图片后的回调函数\n  * @params {*} widget 当前控件元数据   \n  * @params {*} widget 当前控件的vue实例对象\n  * @params {*} EventLibs 当前可用的函数库 \n  */\n function fm_medical_image_fun(setData,widget,$vm,EventLibs){\n    // 上传医学影像\n    if(window.openMedicalImage){\n      window.openMedicalImage(setData,widget,$vm,EventLibs)\n    }\n }"},event:{}}],o=[{type:"t_as",isTool:!0,icon:"icon-gengduoliebiao",label:"术后跟踪",name:"术后跟踪",options:{show:!0,isScore:!0,disabled:!1,isLabelTop:!0},fields:[{key:"",code:"date_s",name:"您的手术时间"},{key:"",code:"1001_b1",name:"请问在手术之前的一年内（${dateRange}），有几天是有发作的"},{key:"",code:"1003_a1",name:"术后第一年（${dateRange}）是否服药",type:"radio",options:[{label:"否",value:"0"},{label:"是",value:"1"}]},{key:"",code:"1004_a1",name:"术后第1个月（${dateRange}）内是否发作",type:"radio",options:[{label:"无发作",value:"0"}],hasOtherOption:!0,otherOptionLabel:"有发作，有几个发作日？（24小时内的多次发作算作1个发作日）",otherOptionValue:""},{key:"",code:"1005_a1",name:"术后第2-12月（${dateRange}）内是否发作",type:"radio",options:[{label:"无发作",value:"0"},{label:"无发作，但有数次明确的先兆发作",value:"1"}],hasOtherOption:!0,otherOptionLabel:"有发作，有几个发作日？（24小时内的多次发作算作1个发作日）",otherOptionValue:""}],afterSurgeryOfYear_n:{fields:[{code:"1001_an",name:"术后第n年（${dateRange}）有发作吗",type:"radio",options:[{label:"无发作",value:"0"},{label:"无发作，但有数次明确的先兆发作",value:"1"}],hasOtherOption:!0,otherOptionLabel:"有发作，有几个发作日？（24小时内的多次发作算作1个发作日）",otherOptionValue:""},{code:"1002_an",name:"术后第n年（${dateRange}）是否服药",type:"radio",options:[{label:"否",value:"0"},{label:"是",value:"1"}]}]},event:{}}]},"7e36":function(e,t,i){"use strict";i("a26f")},"7ee3":function(e,t,i){var n=i("a86f"),o=i("3f9e"),a=i("065e"),r=i("3a0d")("IE_PROTO"),l=function(){},s=function(){var e,t=i("e8d7")("iframe"),n=a.length;for(t.style.display="none",i("bbcc").appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),s=e.F;n--;)delete s.prototype[a[n]];return s()};e.exports=Object.create||function(e,t){var i;return null!==e?(l.prototype=n(e),i=new l,l.prototype=null,i[r]=e):i=s(),void 0===t?i:o(i,t)}},"7f89":function(e,t,i){"use strict";i.r(t);i("3269"),i("fc02");var n=i("21c6"),o={components:{toolField:i("565b").a},mixins:[Object(n.a)()],type:"t_float",data:function(){return{}},computed:{placeholder:function(){var e=this.widget.options.placeholder;if(e&&e.trim())return e.trim();var t=this.widget.options.decimalPoint;return"请输入小数点".concat(t,"位的小数")}},methods:{disabledDate:function(e){return e>Date.now()},completeFloat:function(){var e=this.widget.options.decimalPoint,t=this.toolModel[this.widget.fields[0].key];if((t||"0"===t||0===t)&&"number"==typeof+t){var i=t.split(".")[1],n=i?i.length:0;if(e>n){var o=new Array(e-n+1).join("0");t.indexOf(".")<0&&(o="."+o),this.toolModel[this.widget.fields[0].key]=t+o}}},getRule:function(){var e=this.widget.options.decimalPoint;return{trigger:"blur",message:"请输入小数点".concat(e,"位的小数"),validator:function(t,i,n){if(""===i)return n();return new RegExp("^\\d{1,9}\\.\\d{".concat(e,"}$")).test(i)?n():n(new Error("请输入小数点".concat(e,"位的小数")))}}}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("tool-field",{staticClass:"gf-tool-float",attrs:{rules:[e.getRule()],field:e.widget.fields[0]},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:n,placeholder:e.placeholder},on:{blur:e.completeFloat},model:{value:e.toolModel[e.widget.fields[0].key],callback:function(t){e.$set(e.toolModel,e.widget.fields[0].key,t)},expression:"toolModel[widget.fields[0].key]"}})]}}])})}),[],!1,null,null,null);t.default=r.exports},"804d":function(e,t,i){var n=i("9cff"),o=i("cea2"),a=i("839a")("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[a])?!!t:"RegExp"==o(e))}},8078:function(e,t,i){var n=i("3ab0");e.exports=function(e){return Object(n(e))}},"839a":function(e,t,i){var n=i("baa7")("wks"),o=i("d8b3"),a=i("0b34").Symbol,r="function"==typeof a;(e.exports=function(e){return n[e]||(n[e]=r&&a[e]||(r?a:o)("Symbol."+e))}).store=n},"83d3":function(e,t,i){e.exports=!i("26df")&&!i("0926")((function(){return 7!=Object.defineProperty(i("e8d7")("div"),"a",{get:function(){return 7}}).a}))},"84e8":function(e,t,i){var n=i("0b34"),o=i("065d"),a=i("4fd4"),r=i("d8b3")("src"),l=i("05fd"),s=(""+l).split("toString");i("76e3").inspectSource=function(e){return l.call(e)},(e.exports=function(e,t,i,l){var u="function"==typeof i;u&&(a(i,"name")||o(i,"name",t)),e[t]!==i&&(u&&(a(i,r)||o(i,r,e[t]?""+e[t]:s.join(String(t)))),e===n?e[t]=i:l?e[t]?e[t]=i:o(e,t,i):(delete e[t],o(e,t,i)))})(Function.prototype,"toString",(function(){return"function"==typeof this&&this[r]||l.call(this)}))},"87b2":function(e,t,i){var n=i("839a")("unscopables"),o=Array.prototype;null==o[n]&&i("065d")(o,n,{}),e.exports=function(e){o[n][e]=!0}},"8b5a":function(e,t){e.exports=function(e,t,i,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(i+": incorrect invocation!");return e}},"8dee":function(e,t,i){"use strict";var n=i("a86f"),o=i("8078"),a=i("201c"),r=i("212e"),l=i("43ec"),s=i("f417"),u=Math.max,c=Math.min,d=Math.floor,f=/\$([$&`']|\d\d?|<[^>]*>)/g,p=/\$([$&`']|\d\d?)/g;i("c46f")("replace",2,(function(e,t,i,m){return[function(n,o){var a=e(this),r=null==n?void 0:n[t];return void 0!==r?r.call(n,a,o):i.call(String(a),n,o)},function(e,t){var o=m(i,e,this,t);if(o.done)return o.value;var d=n(e),f=String(this),p="function"==typeof t;p||(t=String(t));var g=d.global;if(g){var _=d.unicode;d.lastIndex=0}for(var b=[];;){var y=s(d,f);if(null===y)break;if(b.push(y),!g)break;""===String(y[0])&&(d.lastIndex=l(f,a(d.lastIndex),_))}for(var v,w="",O=0,k=0;k<b.length;k++){y=b[k];for(var x=String(y[0]),M=u(c(r(y.index),f.length),0),E=[],j=1;j<y.length;j++)E.push(void 0===(v=y[j])?v:String(v));var D=y.groups;if(p){var I=[x].concat(E,M,f);void 0!==D&&I.push(D);var C=String(t.apply(void 0,I))}else C=h(x,f,M,E,D,t);M>=O&&(w+=f.slice(O,M)+C,O=M+x.length)}return w+f.slice(O)}];function h(e,t,n,a,r,l){var s=n+e.length,u=a.length,c=p;return void 0!==r&&(r=o(r),c=f),i.call(l,c,(function(i,o){var l;switch(o.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,n);case"'":return t.slice(s);case"<":l=r[o.slice(1,-1)];break;default:var c=+o;if(0===c)return i;if(c>u){var f=d(c/10);return 0===f?i:f<=u?void 0===a[f-1]?o.charAt(1):a[f-1]+o.charAt(1):i}l=a[c-1]}return void 0===l?"":l}))}}))},9224:function(e){e.exports=JSON.parse('{"name":"form-making","description":"表单设计器，CRF","version":"3.5.8","keywords":["component","vue","form","element-ui","auto"],"main":"dist/FormMaking.common.js","scripts":{"serve":"vue-cli-service serve --open --port=8087","dev":"npm run serve","build":"vue-cli-service build","lint":"vue-cli-service lint","build-bundle":"cross-env build_target_env=lib vue-cli-service build --target lib --name FormMaking ./src/index.js","build-bundle:analyse":"cross-env build_target_env=lib analyse=true npm run build-bundle","copy-bundle":"node ./bin/copy-bundle.js","build-bundle:gf":"cross-env build_target_env=lib vue-cli-service build --target lib --name FormMakingGF ./src/index-gf.js","build-bundle:gf:analyse":"cross-env build_target_env=lib analyse=true npm run build-bundle:gf","copy-bundle:gf":"node ./bin/copy-bundle-gf.js","dist":"npm run build-bundle && npm run copy-bundle && npm run build-bundle:gf && npm run copy-bundle:gf","test":"cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run","test:watch":"cross-env BABEL_ENV=test karma start test/unit/karma.conf.js","reporte:unitTests":"node build/bin/showUnitTestsReporter.js","reporte:coverage":"node build/bin/showCoverageReporter.js","prettier":"prettier --config --write src/**/*.{vue,js,scss} !src/{assets,assets/iconfont2}/*"},"dependencies":{"@vant/touch-emulator":"^1.1.0","axios":"^0.18.0","clipboard":"^2.0.1","e-vue-contextmenu":"^0.1.3","element-ui":"2.15.1","intro.js":"^2.7.0","monaco-editor":"^0.30.1","monaco-editor-webpack-plugin":"^6.0.0","normalize.css":"^8.0.0","qiniu":"^7.2.1","qiniu-js":"^2.5.1","vant":"^2.2.13","viewerjs":"^1.2.0","vue":"^2.6.5","vue-axios":"^2.1.5","vue-i18n":"5.0.3","vue-introjs":"^1.3.2","vue-ls":"^3.2.1","vue-router":"^3.0.1","vuedraggable":"^2.16.0"},"devDependencies":{"@babel/core":"^7.0.1","@types/ace":"0.0.42","@vue/cli-plugin-babel":"^3.0.0","@vue/cli-plugin-eslint":"^3.0.0","@vue/cli-service":"^3.0.0","@vue/test-utils":"^1.0.0-beta.30","babel-core":"^6.26.3","babel-eslint":"^10.0.3","babel-loader":"^8.0.6","babel-plugin-component":"^1.1.1","babel-plugin-import":"^1.13.0","babel-plugin-istanbul":"^5.2.0","babel-regenerator-runtime":"^6.5.0","chai":"^4.2.0","compression-webpack-plugin":"^5.0.2","core-js":"^2.6.5","cross-env":"^6.0.3","css-loader":"^3.3.0","husky":"^3.1.0","karma":"^4.4.1","karma-chrome-launcher":"^3.1.0","karma-coverage":"^2.0.1","karma-htmlfile-reporter":"^0.3.8","karma-mocha":"^1.3.0","karma-sinon-chai":"^2.0.2","karma-sourcemap-loader":"^0.3.7","karma-spec-reporter":"^0.0.32","karma-webpack":"^4.0.2","live-server":"^1.2.1","mocha":"^6.2.2","mockjs":"^1.1.0","node-sass":"^4.9.0","postcss-loader":"^3.0.0","prettier":"^1.19.1","progress-bar-webpack-plugin":"^1.12.1","rollup":"^0.57.1","rollup-plugin-babel":"^3.0.7","rollup-plugin-buble":"^0.19.2","rollup-plugin-uglify-es":"0.0.1","rollup-plugin-vue":"^3.0.0","sass-loader":"^7.0.1","sinon":"^7.5.0","sinon-chai":"^3.3.0","style-loader":"^1.0.1","terser-webpack-plugin":"^1.2.4","url-loader":"^3.0.0","vue-loader":"^15.7.2","vue-style-loader":"^4.1.2","vue-template-compiler":"^2.6.5","webpack-bundle-analyzer":"^4.4.0"},"babel":{"presets":["@vue/app"],"plugins":[["component",{"libraryName":"element-ui","styleLibraryName":"theme-chalk"}],["import",{"libraryName":"vant","libraryDirectory":"es","style":true},"vant"]],"env":{"test":{"plugins":["istanbul"]}}},"eslintConfig":{"root":true,"parserOptions":{"parser":"babel-eslint"},"extends":["plugin:vue/essential"]},"postcss":{"plugins":{"autoprefixer":{}}},"browserslist":["> 1%","last 2 versions","not ie <= 8"],"husky":{"hooks":{}}}')},"93ca":function(e,t,i){var n=i("3a4c"),o=i("065e");e.exports=Object.keys||function(e){return n(e,o)}},"94f0":function(e,t,i){"use strict";var n=i("0b34"),o=i("4fd4"),a=i("26df"),r=i("e99b"),l=i("84e8"),s=i("49f2").KEY,u=i("0926"),c=i("baa7"),d=i("bac3"),f=i("d8b3"),p=i("839a"),m=i("1a58"),h=i("078c"),g=i("3c56"),_=i("d1cb"),b=i("a86f"),y=i("9cff"),v=i("8078"),w=i("3471"),O=i("5d10"),k=i("5edc"),x=i("7ee3"),M=i("4441"),E=i("285b"),j=i("0c29"),D=i("bb8b"),I=i("93ca"),C=E.f,S=D.f,B=M.f,P=n.Symbol,A=n.JSON,T=A&&A.stringify,F=p("_hidden"),$=p("toPrimitive"),L={}.propertyIsEnumerable,R=c("symbol-registry"),N=c("symbols"),V=c("op-symbols"),z=Object.prototype,U="function"==typeof P&&!!j.f,W=n.QObject,K=!W||!W.prototype||!W.prototype.findChild,q=a&&u((function(){return 7!=x(S({},"a",{get:function(){return S(this,"a",{value:7}).a}})).a}))?function(e,t,i){var n=C(z,t);n&&delete z[t],S(e,t,i),n&&e!==z&&S(z,t,n)}:S,H=function(e){var t=N[e]=x(P.prototype);return t._k=e,t},G=U&&"symbol"==typeof P.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof P},J=function(e,t,i){return e===z&&J(V,t,i),b(e),t=O(t,!0),b(i),o(N,t)?(i.enumerable?(o(e,F)&&e[F][t]&&(e[F][t]=!1),i=x(i,{enumerable:k(0,!1)})):(o(e,F)||S(e,F,k(1,{})),e[F][t]=!0),q(e,t,i)):S(e,t,i)},Y=function(e,t){b(e);for(var i,n=g(t=w(t)),o=0,a=n.length;a>o;)J(e,i=n[o++],t[i]);return e},Q=function(e){var t=L.call(this,e=O(e,!0));return!(this===z&&o(N,e)&&!o(V,e))&&(!(t||!o(this,e)||!o(N,e)||o(this,F)&&this[F][e])||t)},X=function(e,t){if(e=w(e),t=O(t,!0),e!==z||!o(N,t)||o(V,t)){var i=C(e,t);return!i||!o(N,t)||o(e,F)&&e[F][t]||(i.enumerable=!0),i}},Z=function(e){for(var t,i=B(w(e)),n=[],a=0;i.length>a;)o(N,t=i[a++])||t==F||t==s||n.push(t);return n},ee=function(e){for(var t,i=e===z,n=B(i?V:w(e)),a=[],r=0;n.length>r;)!o(N,t=n[r++])||i&&!o(z,t)||a.push(N[t]);return a};U||(l((P=function(){if(this instanceof P)throw TypeError("Symbol is not a constructor!");var e=f(arguments.length>0?arguments[0]:void 0),t=function(i){this===z&&t.call(V,i),o(this,F)&&o(this[F],e)&&(this[F][e]=!1),q(this,e,k(1,i))};return a&&K&&q(z,e,{configurable:!0,set:t}),H(e)}).prototype,"toString",(function(){return this._k})),E.f=X,D.f=J,i("21d9").f=M.f=Z,i("35d4").f=Q,j.f=ee,a&&!i("3d8a")&&l(z,"propertyIsEnumerable",Q,!0),m.f=function(e){return H(p(e))}),r(r.G+r.W+r.F*!U,{Symbol:P});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ie=0;te.length>ie;)p(te[ie++]);for(var ne=I(p.store),oe=0;ne.length>oe;)h(ne[oe++]);r(r.S+r.F*!U,"Symbol",{for:function(e){return o(R,e+="")?R[e]:R[e]=P(e)},keyFor:function(e){if(!G(e))throw TypeError(e+" is not a symbol!");for(var t in R)if(R[t]===e)return t},useSetter:function(){K=!0},useSimple:function(){K=!1}}),r(r.S+r.F*!U,"Object",{create:function(e,t){return void 0===t?x(e):Y(x(e),t)},defineProperty:J,defineProperties:Y,getOwnPropertyDescriptor:X,getOwnPropertyNames:Z,getOwnPropertySymbols:ee});var ae=u((function(){j.f(1)}));r(r.S+r.F*ae,"Object",{getOwnPropertySymbols:function(e){return j.f(v(e))}}),A&&r(r.S+r.F*(!U||u((function(){var e=P();return"[null]"!=T([e])||"{}"!=T({a:e})||"{}"!=T(Object(e))}))),"JSON",{stringify:function(e){for(var t,i,n=[e],o=1;arguments.length>o;)n.push(arguments[o++]);if(i=t=n[1],(y(t)||void 0!==e)&&!G(e))return _(t)||(t=function(e,t){if("function"==typeof i&&(t=i.call(this,e,t)),!G(t))return t}),n[1]=t,T.apply(A,n)}}),P.prototype[$]||i("065d")(P.prototype,$,P.prototype.valueOf),d(P,"Symbol"),d(Math,"Math",!0),d(n.JSON,"JSON",!0)},"953d":function(e,t){e.exports={}},9585:function(e,t,i){"use strict";i("4b80")},"982e":function(e,t,i){"use strict";var n=i("e99b"),o=i("db34");n(n.P+n.F*i("581c")("includes"),"String",{includes:function(e){return!!~o(this,e,"includes").indexOf(e,arguments.length>1?arguments[1]:void 0)}})},"98de":function(e,t,i){"use strict";var n=i("bb8b").f,o=i("7ee3"),a=i("6f45"),r=i("1e4d"),l=i("8b5a"),s=i("2b37"),u=i("120f"),c=i("6fef"),d=i("f966"),f=i("26df"),p=i("49f2").fastKey,m=i("0b28"),h=f?"_s":"size",g=function(e,t){var i,n=p(t);if("F"!==n)return e._i[n];for(i=e._f;i;i=i.n)if(i.k==t)return i};e.exports={getConstructor:function(e,t,i,u){var c=e((function(e,n){l(e,c,t,"_i"),e._t=t,e._i=o(null),e._f=void 0,e._l=void 0,e[h]=0,null!=n&&s(n,i,e[u],e)}));return a(c.prototype,{clear:function(){for(var e=m(this,t),i=e._i,n=e._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete i[n.i];e._f=e._l=void 0,e[h]=0},delete:function(e){var i=m(this,t),n=g(i,e);if(n){var o=n.n,a=n.p;delete i._i[n.i],n.r=!0,a&&(a.n=o),o&&(o.p=a),i._f==n&&(i._f=o),i._l==n&&(i._l=a),i[h]--}return!!n},forEach:function(e){m(this,t);for(var i,n=r(e,arguments.length>1?arguments[1]:void 0,3);i=i?i.n:this._f;)for(n(i.v,i.k,this);i&&i.r;)i=i.p},has:function(e){return!!g(m(this,t),e)}}),f&&n(c.prototype,"size",{get:function(){return m(this,t)[h]}}),c},def:function(e,t,i){var n,o,a=g(e,t);return a?a.v=i:(e._l=a={i:o=p(t,!0),k:t,v:i,p:n=e._l,n:void 0,r:!1},e._f||(e._f=a),n&&(n.n=a),e[h]++,"F"!==o&&(e._i[o]=a)),e},getEntry:g,setStrong:function(e,t,i){u(e,t,(function(e,i){this._t=m(e,t),this._k=i,this._l=void 0}),(function(){for(var e=this._k,t=this._l;t&&t.r;)t=t.p;return this._t&&(this._l=t=t?t.n:this._t._f)?c(0,"keys"==e?t.k:"values"==e?t.v:[t.k,t.v]):(this._t=void 0,c(1))}),i?"entries":"values",!i,!0),d(t)}}},"9cff":function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},"9e44":function(e,t,i){"use strict";i("1bc7"),i("a450");var n={name:"generate-form-grid",props:["item","widgetForm","models","value","rules","remote","externaldata","de-settings"],components:{GenerateFormItem:i("aa13").a},inject:{eventBus:{from:"eventBus"}},data:function(){return{curModels:this.value,curFormModels:this.models}},mounted:function(){this.$emit("mounted")},destroyed:function(){this.$emit("destroyed")},computed:{containerSize:function(){return this.eventBus.containerSize},is2Grid:function(){return"两列布局"==this.item.name},is3Grid:function(){return"三列布局"==this.item.name},bgColorObj:function(){return this.item.options.bgColor?{"background-color":this.item.options.bgColor}:{}}},methods:{showFlag:function(e){if(!e)return!1;var t=!1,i=e&&e.columns;if(i&&i instanceof Array)for(var n=0;n<i.length;n++){for(var o=i[n]&&i[n].list?i[n].list:[],a=0;a<o.length&&!(t=this.showFlag(o[a]));a++);if(t)break}else"group"===e.type&&"panel"===e.type||(t=!0);return t},getSpan:function(e){return this.is2Grid?12:this.is3Grid?8:e},onInputChange:function(e,t){this.$emit("input-change",e,t)},fieldValueChange:function(e,t,i,n){this.$emit("field-value-change",e,t,i,n)},eventHandler:function(e,t,i){this.$emit("event",e,t,i)},fillExternalData:function(){this.$refs.grid&&this.$refs.grid.forEach((function(e){e.fillExternalData()})),this.$refs.item&&this.$refs.item.forEach((function(e){e.fillExternalData()}))}},watch:{curModels:{handler:function(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.$emit("update:value",e)},deep:!0},curFormModels:{handler:function(e){this.$emit("update:models",e)}},models:{handler:function(e){this.curFormModels=e}},value:{handler:function(e,t){null==t&&(t=""),this.curModels=e},deep:!0}}},o=i("cba8"),a=Object(o.a)(n,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.showFlag(e.item)?i("el-row",{key:e.item.key,staticClass:"gf-row gf-group",class:{"is-custom-bg":!!e.item.options.bgColor},staticStyle:{"margin-left":"0","margin-right":"0"},attrs:{gutter:e.item.options.gutter?e.item.options.gutter:10,justify:e.item.options.justify,align:e.item.options.align}},e._l(e.item.columns,(function(t,n){return i("el-col",{key:n,staticClass:"gf-col",class:{"group--box__generate-fieldset":"fieldset"==e.item.options.group_style,"gf-fieldset":"fieldset"==e.item.options.group_style,"group--box__generate-panel":"panel"==e.item.options.group_style,"gf-panel":"panel"==e.item.options.group_style},style:e.bgColorObj,attrs:{span:"xs"==e.containerSize||"sm"==e.containerSize?24:e.getSpan(t.span)}},["group"==e.item.type&&e.item.name&&!e.item.options.isLabelHide?i("div",{class:{"group--legend":"fieldset"==e.item.options.group_style,"group--panel-title":"panel"==e.item.options.group_style}},[e._v("\n            "+e._s(e.item.name)+"\n            "),e.item.options.remark?i("el-popover",{attrs:{placement:"right",title:"备注",width:"200",trigger:"hover",content:e.item.options.remark}},[i("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"14px",color:"#868180"},attrs:{slot:"reference"},slot:"reference"})]):e._e()],1):e._e(),e._l(t.list,(function(t){return[t.isDelete||"grid"!=t.type&&"group"!=t.type?t.isDelete?e._e():[i("generate-form-item",{directives:[{name:"show",rawName:"v-show",value:t.options.show,expression:"citem.options.show"}],key:t.key,ref:"item",refInFor:!0,attrs:{value:e.curModels,models:e.curFormModels,remote:e.remote,externaldata:e.externaldata,"de-settings":e.deSettings,rules:e.rules,widget:t,widgetForm:e.widgetForm},on:{"update:value":function(t){e.curModels=t},"update:models":function(t){e.curFormModels=t},"input-change":e.onInputChange,"field-value-change":e.fieldValueChange,event:e.eventHandler}})]:i("generate-form-grid",{directives:[{name:"show",rawName:"v-show",value:t.options.show,expression:"citem.options.show"}],key:t.key,ref:"grid",refInFor:!0,attrs:{value:e.curModels,models:e.curFormModels,rules:e.rules,item:t,widgetForm:e.widgetForm,remote:e.remote,externaldata:e.externaldata,"de-settings":e.deSettings},on:{"update:value":function(t){e.curModels=t},"update:models":function(t){e.curFormModels=t},"input-change":e.onInputChange,event:e.eventHandler}})]}))],2)})),1):e._e()}),[],!1,null,null,null);t.a=a.exports},"9f15":function(e,t,i){"use strict";var n=i("26df"),o=i("93ca"),a=i("0c29"),r=i("35d4"),l=i("8078"),s=i("1b96"),u=Object.assign;e.exports=!u||i("0926")((function(){var e={},t={},i=Symbol(),n="abcdefghijklmnopqrst";return e[i]=7,n.split("").forEach((function(e){t[e]=e})),7!=u({},e)[i]||Object.keys(u({},t)).join("")!=n}))?function(e,t){for(var i=l(e),u=arguments.length,c=1,d=a.f,f=r.f;u>c;)for(var p,m=s(arguments[c++]),h=d?o(m).concat(d(m)):o(m),g=h.length,_=0;g>_;)p=h[_++],n&&!f.call(m,p)||(i[p]=m[p]);return i}:u},"9f60":function(e,t,i){i("078c")("asyncIterator")},a1b8:function(e,t,i){"use strict";i.r(t);i("1bc7");var n=i("21c6"),o={components:{toolField:i("565b").a},mixins:[Object(n.a)()],type:"t_medical_image",data:function(){return{}},mounted:function(){this.loadCustomScript()},computed:{ImgItems:function(){if(!this.toolModel[this.widget.fields[0].key]||!Array.isArray(this.toolModel[this.widget.fields[0].key]))return[];var e=[];return this.toolModel[this.widget.fields[0].key].forEach((function(t){e.push(t)})),e}},methods:{getImageUrl:function(e){return this.eventBus.uploadMedicalImageConfig&&this.eventBus.uploadMedicalImageConfig.getImageUrl?this.eventBus.uploadMedicalImageConfig.getImageUrl(e):e.url},imageClickHandler:function(e){this.eventBus.uploadMedicalImageConfig&&this.eventBus.uploadMedicalImageConfig.imageClickHandler&&this.eventBus.uploadMedicalImageConfig.imageClickHandler(e)},imageRemoveHandler:function(e,t){this.eventBus.uploadMedicalImageConfig&&this.eventBus.uploadMedicalImageConfig.imageRemoveHandler?this.eventBus.uploadMedicalImageConfig.imageRemoveHandler(e,t):t()},toUpload:function(){var e=this;if(!this.eventBus.EventLibs.fm_medical_image_fun)throw new Error('没有找到函数"fm_medical_image_fun"');this.eventBus.EventLibs.fm_medical_image_fun.call(this,(function(t){e.uploadImgDialogConfirm(t)}),this.widget,this,this.eventBus.EventLibs)},removeImg:function(e){var t=this;this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.imageRemoveHandler(t.toolModel[t.widget.fields[0].key][e],(function(){t.toolModel[t.widget.fields[0].key].splice(e,1)}))})).catch((function(){}))},uploadImgDialogConfirm:function(e){this.toolModel[this.widget.fields[0].key]&&Array.isArray(this.toolModel[this.widget.fields[0].key])?this.toolModel[this.widget.fields[0].key].push(e):this.toolModel[this.widget.fields[0].key]=[e]},loadCustomScript:function(){var e=this.eventBus.EventLibs,t=this.widget.options.script,i=document.createElement("script");i.innerHTML=t,document.body.appendChild(i),setTimeout((function(){window.fm_medical_image_fun&&(e.fm_medical_image_fun=window.fm_medical_image_fun)}),500)}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("tool-field",{staticClass:"gf-tool-medical-image",attrs:{field:e.widget.fields[0]},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[e.widget.options.isMultipleImg||0==e.toolModel[e.widget.fields[0].key].length?i("div",{staticClass:"el-upload el-upload--picture-card",staticStyle:{width:"100%",height:"280px","max-width":"250px",display:"flex","align-items":"center","justify-content":"center"},style:{cursor:n?"not-allowed":"pointer"},on:{click:function(t){!n&&e.toUpload()}}},[i("el-button",{attrs:{disabled:n,type:"text",size:"mini",icon:"el-icon-plus"}})],1):e._e(),e.toolModel[e.widget.fields[0].key]&&e.toolModel[e.widget.fields[0].key].length>0?i("div",{style:{width:e.widget.options.width+"%"}},e._l(e.toolModel[e.widget.fields[0].key],(function(t,o){return i("div",{key:o,staticClass:"img-container"},[i("div",{staticStyle:{position:"absolute",top:"2px",left:"2px",color:"#b0bf03","z-index":"10"}},[e._v("共"+e._s(e.ImgItems[o].count)+"个序列")]),i("el-image",{staticStyle:{width:"100%",height:"280px","max-width":"250px"},attrs:{src:e.getImageUrl(e.ImgItems[o]),lazy:!0},nativeOn:{click:function(t){return e.imageClickHandler(e.ImgItems[o])}}}),i("div",{staticStyle:{"text-align":"center"}},[i("el-button",{staticClass:"img-delete-btn",attrs:{size:"mini",disabled:n,type:"danger",title:"删除"},on:{click:function(t){return e.removeImg(o)}}},[i("i",{staticClass:"el-icon-delete"})])],1)],1)})),0):e._e()]}}])})}),[],!1,null,null,null);t.default=r.exports},a26f:function(e,t,i){},a450:function(e,t,i){var n=i("bb8b").f,o=Function.prototype,a=/^\s*function ([^ (]*)/;"name"in o||i("26df")&&n(o,"name",{configurable:!0,get:function(){try{return(""+this).match(a)[1]}catch(e){return""}}})},a618:function(e,t){e.exports=function(e,t,i){var n=void 0===i;switch(t.length){case 0:return n?e():e.call(i);case 1:return n?e(t[0]):e.call(i,t[0]);case 2:return n?e(t[0],t[1]):e.call(i,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(i,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(i,t[0],t[1],t[2],t[3])}return e.apply(i,t)}},a7be:function(e,t,i){"use strict";function n(e,t,i,n,o,a,r){try{var l=e[a](r),s=l.value}catch(e){return void i(e)}l.done?t(s):Promise.resolve(s).then(n,o)}function o(e){return function(){var t=this,i=arguments;return new Promise((function(o,a){var r=e.apply(t,i);function l(e){n(r,o,a,l,s,"next",e)}function s(e){n(r,o,a,l,s,"throw",e)}l(void 0)}))}}i.d(t,"a",(function(){return o}))},a83a:function(e,t,i){var n=i("9cff"),o=i("e0ff").set;e.exports=function(e,t,i){var a,r=t.constructor;return r!==i&&"function"==typeof r&&(a=r.prototype)!==i.prototype&&n(a)&&o&&o(e,a),e}},a86f:function(e,t,i){var n=i("9cff");e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},aa13:function(e,t,i){"use strict";var n=i("44a4"),o=(i("25ba"),i("32ea"),i("1bc7"),i("e680"),i("0474")),a=(i("e186"),i("a7be")),r=(i("d0f2"),i("4260")),l=function(){return{directives:{emoji:r.a},inject:{all$vm:{from:"all$vm",default:function(){}},eventBus:{from:"eventBus",default:function(){}},gfItemBus:{from:"gfItemBus",default:function(){}},widget:{from:"widget",default:function(){}},injected_dataModel:{from:"dataModel",default:function(){}}},data:function(){return{dataModel:this.injected_dataModel,remoteItemLoading:!1}},watch:{dataModel:function(e){this.gfItemBus.dataModel=e},"gfItemBus.dataModel":function(e){this.dataModel=e}},computed:{isInFormBox:function(){return!!this.eventBus},fieldKey:function(){return this.gfItemBus.fieldKey},disabled:function(){return!!this.isInFormBox&&(!this.eventBus.editing[this.fieldKey]&&(!this.gfItemBus.isInGroupsItemNew&&(!!this.eventBus.formDisabled||(!!(this.eventBus.audit&&this.eventBus.audit.lock&&this.eventBus.audit.lock.length>0&&this.eventBus.audit.lock.indexOf(this.fieldKey)>=0)||(this.eventBus.disabledList.indexOf(this.widget.key)>=0||this.widget.options.disabled)))))},computedOptions:function(){return this.widget.options.options},isDictionaryOptions:function(){return this.widget.options.dictionaryType&&this.widget.options.remote},remoteLoading:function(){return!!this.isInFormBox&&(this.eventBus.remoteLoading||this.remoteItemLoading)},_isMobile:function(){return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)}},methods:{emitEvent:function(e,t,i,o){if("checkbox"==i.type&&"change"==t&&this.dataModel.length>0){var a=this.dataModel[this.dataModel.length-1],r=i.options.options.filter((function(e){return e.value==a}))[0];if(r&&r.isMutex||"other_option"==a&&i.options.otherOptionIsMutex)this.dataModel=[a];else if(this.dataModel.length>=2){var l=this.dataModel[this.dataModel.length-2],s=i.options.options.filter((function(e){return e.value==l}))[0];if(s&&s.isMutex||"other_option"==l&&i.options.otherOptionIsMutex){var u=Object(n.a)(this.dataModel);u.splice(u.length-2,1),this.dataModel=u}}}("checkbox"!=i.type&&"radio"!=i.type||"click"!=t||o&&("checkbox"==i.type&&"el-checkbox__original"==o.target.className||"radio"==i.type&&"el-radio__original"==o.target.className))&&this.gfItemBus.$emit(e,t,i,this.gfItemBus)},getFullImgUrl:function(e){return/http/.test(e)?e:this.eventBus.uploadImg.getFullImgUrl?this.eventBus.uploadImg.getFullImgUrl(e):this.eventBus.uploadImg.baseUrl+e},remoteMethod:(e=Object(a.a)(regeneratorRuntime.mark((function e(t){var i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isDictionaryOptions){e.next=2;break}return e.abrupt("return");case 2:return this.remoteItemLoading=!0,e.next=5,this.eventBus.findDictionaryOptions({widget:this.widget,query:t});case 5:i=e.sent,this.widget.options.options=i,this.remoteItemLoading=!1;case 8:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})}};var e},s=(i("8dee"),{}),u=i("f2c6");u.keys().forEach((function(e){var t=u(e);s[e.replace(/^\.\/(.*)\.\w+$/,"$1")]=t.default}));var c,d=s,f={mixins:[l()],data:function(){return{ToolWidgets:d}}},p=i("cba8"),m=Object(p.a)(f,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-tool-widget"},[e._l(e.ToolWidgets,(function(t,n){return[e.widget.type==t.type?i(t,{key:n,tag:"component"}):e._e()]}))],2)}),[],!1,null,null,null).exports,h=i("34a9"),g=i("bff0"),_=i("f1af"),b=i("32e5"),y={mixins:[l()],data:function(){return{}}},v=Object(p.a)(y,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],staticClass:"gf-input",style:{width:"100%"},attrs:{type:e.widget.options.dataType,disabled:e.disabled,placeholder:e.widget.options.placeholder},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},input:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}})}),[],!1,null,null,null).exports,w={mixins:[l()],data:function(){return{}},methods:{numberInputBlur:function(){if(this.dataModel||0===this.dataModel){var e=+this.dataModel;e!==this.dataModel&&(this.dataModel=e),this.emitEvent("event","blur",this.widget)}else this.emitEvent("event","blur",this.widget)}}},O=Object(p.a)(w,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],style:{width:"100%"},attrs:{max:e.widget.options.max,min:e.widget.options.min,type:"number",placeholder:e.widget.options.placeholder,disabled:e.disabled},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:e.numberInputBlur,input:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}})}),[],!1,null,null,null).exports,k={mixins:[l()],data:function(){return{}}},x=Object(p.a)(k,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],staticClass:"gf-textarea",style:{width:"100%"},attrs:{type:"textarea",rows:e.widget.options.rows,disabled:e.disabled,placeholder:e.widget.options.placeholder},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},input:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}})}),[],!1,null,null,null).exports,M={mixins:[l()],data:function(){return{}},methods:{handleFocus:function(){this.disabled&&this.$refs.timePicker.hidePicker(),this.emitEvent("event","focus",this.widget)}},watch:{disabled:function(e){e&&this.$refs.timePicker.hidePicker()}}},E=Object(p.a)(M,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-time-picker",{ref:"timePicker",staticClass:"gf-time",style:{width:"100%"},attrs:{"is-range":e.widget.options.isRange,placeholder:e.widget.options.placeholder,"start-placeholder":e.widget.options.startPlaceholder,"end-placeholder":e.widget.options.endPlaceholder,readonly:e.widget.options.readonly,disabled:e.disabled,editable:e.widget.options.editable,clearable:e.widget.options.clearable,arrowControl:e.widget.options.arrowControl,"value-format":e.widget.options.format},on:{focus:e.handleFocus,blur:function(t){return e.emitEvent("event","blur",e.widget)},change:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}})}),[],!1,null,null,null).exports,j={mixins:[l()],data:function(){return{}},methods:{setDateType:function(e){return"yyyy-MM-dd"==e||"yyyy/MM/dd"==e?"date":"datetime"},handleFocus:function(){this.disabled&&this.$refs.datePicker.hidePicker(),this.emitEvent("event","focus",this.widget)}},watch:{disabled:function(e){e&&this.$refs.datePicker.hidePicker()}}},D=Object(p.a)(j,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-date-picker",{ref:"datePicker",style:{width:"100%"},attrs:{type:e.setDateType(e.widget.options.format),placeholder:e.widget.options.placeholder,"start-placeholder":e.widget.options.startPlaceholder,"end-placeholder":e.widget.options.endPlaceholder,readonly:e.widget.options.readonly,disabled:e.disabled,editable:e.widget.options.editable,clearable:e.widget.options.clearable,"value-format":e.widget.options.timestamp?"timestamp":e.widget.options.format,format:e.widget.options.format},on:{focus:e.handleFocus,blur:function(t){return e.emitEvent("event","blur",e.widget)},change:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}})}),[],!1,null,null,null).exports,I={mixins:[l()],data:function(){return{}}},C=Object(p.a)(I,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("pre",{staticClass:"gf-text",style:{"margin-top":"2px","line-height":e.widget.options.lineHeight,color:e.widget.options.color,width:"100%","white-space":"break-spaces","font-weight":e.widget.options.fontWeight,"font-style":e.widget.options.fontStyle,"font-family":e.widget.options.fontFamily}},[e._v(e._s(e.widget.options.defaultValue))])}),[],!1,null,null,null).exports,S={mixins:[l()],data:function(){return{}}},B=Object(p.a)(S,(function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"gf-divider"},[t("el-divider",{attrs:{title:this.widget.options.remark,"content-position":this.widget.options.contentPosition}},[this._v("\n        "+this._s(this.widget.name)+"\n    ")])],1)}),[],!1,null,null,null).exports,P=(i("3269"),{mixins:[l()],data:function(){return{}},computed:{href:function(){var e=this.eventBus.outData,t=this.widget.options.href,i=new RegExp(/\$\{(.*?)\}/g);return t?i.test(t)?t.replace(i,(function(t,i,n){return e&&e[i]?e[i]:""})):t:""}},methods:{linkClick:function(e){"default"==this.widget.options.linkType||"goToForm"==this.widget.options.linkType&&this.widget.options.linkFormGuid&&(e.preventDefault(),this.eventBus.$emit("linkGoToForm",this.widget))}}}),A=Object(p.a)(P,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-link"},[i("el-link",{style:{display:"inline-block","text-align":"center","line-height":"32px","margin-left":e.widget.options.marginleft+"px","max-width":e.widget.options.width+"%"},attrs:{type:e.widget.options.type,underline:e.widget.options.underline,href:e.href,target:e.widget.options.target},nativeOn:{click:function(t){return e.linkClick.apply(null,arguments)}}},[e._v(e._s(e.widget.name))]),e.widget.options.remark?i("el-popover",{attrs:{placement:"right",title:"",width:"200",trigger:"hover",content:e.widget.options.remark}},[i("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"14px",color:"#868180"},attrs:{slot:"reference"},slot:"reference"})]):e._e()],1)}),[],!1,null,null,null).exports,T={mixins:[l()],data:function(){return{}},methods:{radioClick:function(e,t){var i=this;if(this.widget.options.cancelable&&!this.disabled){if("INPUT"==e.target.tagName)return;e.preventDefault(),t===this.dataModel?this.dataModel="":this.dataModel=t,this.$nextTick((function(){i.gfItemBus.$emit("event","click",i.widget,i.gfItemBus),i.gfItemBus.$emit("event","change",i.widget,i.gfItemBus)}))}}}},F=Object(p.a)(T,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-radio-group",{style:{width:"100%"},attrs:{disabled:e.disabled},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},change:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget,t)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},[e.remoteLoading?[i("div",{staticStyle:{"padding-left":"30px","text-align":"left"}},[i("el-button",{staticStyle:{"font-size":"13px"},attrs:{type:"text",icon:"el-icon-loading"}},[e._v("\n                正在获取字典项...\n            ")])],1)]:e._l(e.widget.options.options,(function(t,n){return i("el-radio",{key:n,style:{display:e.widget.options.inline?"inline-block":"block"},attrs:{label:t.value},nativeOn:{click:function(i){return e.radioClick(i,t.value)}}},[e.widget.options.remote?[e._v(e._s(t.label))]:[e.widget.options.showLabel?[t.imgUrl?i("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{title:t.label,alt:t.label,src:e.getFullImgUrl(t.imgUrl)}}):i("span",[e._v(e._s(t.label))])]:[i("span",[e._v(e._s(t.value))])]]],2)})),e.widget.options.hasOtherOption&&!e.widget.options.remote?[e.widget.options.isOtherOptionMultiLine?[i("el-radio",{attrs:{label:"other_option"},nativeOn:{click:function(t){return e.radioClick(t,"other_option")}}},[e._v(e._s(e.widget.options.otherOptionLabel))]),"other_option"===e.dataModel?i("div",{staticClass:"other-option-box"},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled||"other_option"!=e.dataModel,size:"mini",type:"textarea",rows:5},model:{value:e.widget.options.otherOptionValue,callback:function(t){e.$set(e.widget.options,"otherOptionValue",t)},expression:"widget.options.otherOptionValue"}})],1):e._e()]:i("el-radio",{attrs:{label:"other_option"},nativeOn:{click:function(t){return e.radioClick(t,"other_option")}}},[e._v(e._s(e.widget.options.otherOptionLabel)),"other_option"===e.dataModel?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],staticClass:"fm-option__extend-input",attrs:{disabled:e.disabled||"other_option"!=e.dataModel,size:"mini"},model:{value:e.widget.options.otherOptionValue,callback:function(t){e.$set(e.widget.options,"otherOptionValue",t)},expression:"widget.options.otherOptionValue"}}):e._e()],1)]:e._e()],2)}),[],!1,null,null,null).exports,$={mixins:[l()],data:function(){return{}}},L=Object(p.a)($,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-checkbox-group",{staticClass:"gf-checkbox",style:{width:"100%",display:"inline-block"},attrs:{disabled:e.disabled},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},change:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget,t)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},[e.remoteLoading?[i("div",{staticStyle:{"padding-left":"30px","text-align":"left"}},[i("el-button",{staticStyle:{"font-size":"13px"},attrs:{type:"text",icon:"el-icon-loading"}},[e._v("\n                正在获取字典项...\n            ")])],1)]:e._l(e.widget.options.options,(function(t,n){return i("el-checkbox",{key:n,style:{display:e.widget.options.inline?"inline-block":"block"},attrs:{label:t.value}},[e.widget.options.remote?[e._v(e._s(t.label))]:[e.widget.options.showLabel?[t.imgUrl?i("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{title:t.label,alt:t.label,src:e.getFullImgUrl(t.imgUrl)}}):i("span",[e._v(e._s(t.label))])]:[i("span",[e._v(e._s(t.value))])]]],2)})),e.widget.options.hasOtherOption&&!e.widget.options.remote?[e.widget.options.isOtherOptionMultiLine?[i("el-checkbox",{attrs:{label:"other_option"}},[e._v(e._s(e.widget.options.otherOptionLabel))]),e.dataModel&&e.dataModel.length>0&&e.dataModel.indexOf("other_option")>=0?i("div",{staticClass:"other-option-box"},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"textarea",rows:5,disabled:e.disabled||!e.dataModel||e.dataModel.indexOf("other_option")<0,size:"mini"},model:{value:e.widget.options.otherOptionValue,callback:function(t){e.$set(e.widget.options,"otherOptionValue",t)},expression:"widget.options.otherOptionValue"}})],1):e._e()]:i("el-checkbox",{attrs:{label:"other_option"}},[e._v(e._s(e.widget.options.otherOptionLabel)),e.dataModel&&e.dataModel.length>0&&e.dataModel.indexOf("other_option")>=0?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],staticClass:"fm-option__extend-input",attrs:{disabled:e.disabled||!e.dataModel||e.dataModel.indexOf("other_option")<0,size:"mini"},model:{value:e.widget.options.otherOptionValue,callback:function(t){e.$set(e.widget.options,"otherOptionValue",t)},expression:"widget.options.otherOptionValue"}}):e._e()],1)]:e._e()],2)}),[],!1,null,null,null).exports,R={mixins:[l()],data:function(){return{}},methods:{handleClick:function(){this.isDictionaryOptions&&this.remoteMethod(),this.emitEvent("event","click",this.widget)}}},N=(i("9585"),Object(p.a)(R,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-select",{staticClass:"gf-select",style:{width:"100%"},attrs:{disabled:e.disabled,multiple:e.widget.options.multiple,clearable:e.widget.options.clearable,placeholder:e.widget.options.placeholder,loading:e.remoteLoading,remote:e.isDictionaryOptions,"remote-method":e.isDictionaryOptions?e.remoteMethod:null,filterable:!!e.isDictionaryOptions||e.widget.options.filterable},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},change:function(t){e.$nextTick((function(){return e.emitEvent("event","change",e.widget)}))}},nativeOn:{click:function(t){return e.handleClick.apply(null,arguments)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},e._l(e.computedOptions,(function(t){return i("el-option",{key:t.value,attrs:{value:t.value,label:e.widget.options.showLabel||e.widget.options.remote?t.label:t.value}})})),1)}),[],!1,null,null,null).exports),V={mixins:[l()],data:function(){return{}},methods:{handleClick:function(){this.remoteMethod(this.$refs.dictionarySelect.query),this.emitEvent("event","click",this.widget)}}},z=Object(p.a)(V,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-select",{ref:"dictionarySelect",style:{width:"100%"},attrs:{disabled:e.disabled,multiple:e.widget.options.multiple,clearable:e.widget.options.clearable,placeholder:e.widget.options.placeholder,loading:e.remoteLoading,remote:e.isDictionaryOptions,"remote-method":e.isDictionaryOptions?e.remoteMethod:null,filterable:!!e.isDictionaryOptions||e.widget.options.filterable},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},change:function(t){e.$nextTick((function(){return e.emitEvent("event","change",e.widget)}))}},nativeOn:{click:function(t){return e.handleClick.apply(null,arguments)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},e._l(e.computedOptions,(function(t){return i("el-option",{key:t.value,attrs:{value:t.value,label:e.widget.options.showLabel||e.widget.options.remote?t.label:t.value}})})),1)}),[],!1,null,null,null).exports,U={mixins:[l()],data:function(){return{}},methods:{handleSelect:function(e){this.dataModel=e.label},fetchSuggestions:(c=Object(a.a)(regeneratorRuntime.mark((function e(t,i){var n,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isDictionaryOptions){e.next=3;break}return e.next=3,this.remoteMethod(t);case 3:n=this.widget.options.options,o=t&&!this.isDictionaryOptions?n.filter((function(e){return e.label.toLowerCase().indexOf(t.toLowerCase())>=0})):n,i(o);case 6:case"end":return e.stop()}}),e,this)}))),function(e,t){return c.apply(this,arguments)}),handleClick:function(){this.isDictionaryOptions&&this.remoteMethod(),this.emitEvent("event","click",this.widget)},handleFocus:function(){this.disabled&&this.$refs.autocomplete.close(),this.emitEvent("event","focus",this.widget)}},watch:{disabled:function(e){e&&this.$refs.autocomplete.close()}}},W=Object(p.a)(U,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-autocomplete",{ref:"autocomplete",staticClass:"gf-autocomplete",style:{width:"100%"},attrs:{disabled:e.disabled,"value-key":"label",clearable:e.widget.options.clearable,placeholder:e.widget.options.placeholder,loading:e.remoteLoading,"fetch-suggestions":e.fetchSuggestions},on:{focus:e.handleFocus,blur:function(t){return e.emitEvent("event","blur",e.widget)},input:function(t){return e.emitEvent("event","change",e.widget)},select:e.handleSelect},nativeOn:{click:function(t){return e.handleClick.apply(null,arguments)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}})}),[],!1,null,null,null).exports,K=i("5b87"),q=i("118b"),H=i("063b"),G=i("d419"),J={components:{optionRadio:q.a,OptionCheckbox:K.a,OptionSelect:H.a,OptionSelectMulti:G.a},mixins:[l()],data:function(){return{}},computed:{style:function(){return{width:this.widget.options.width+"%"}}},watch:{dataModel:{deep:!0,handler:function(e,t){this.gfItemBus.$emit("field-value-change",this.widget.key,e,t,this.gfItemBus)}}}},Y=Object(p.a)(J,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return"object"==e.widget.type?i("div",{staticClass:"group--box__generate-panel gf-object",staticStyle:{"margin-bottom":"10px"}},[e.widget.options.isLabelHide?e._e():i("div",{staticClass:"group--panel-title",staticStyle:{"margin-top":"5px"}},[e._v(e._s(e.widget.name)+"\n    ")]),i("el-row",{attrs:{gutter:0}},e._l(e.widget.properties,(function(t,n){return i("el-col",{key:n,attrs:{span:24/e.widget.options.layout}},[i("el-form-item",{attrs:{label:t.name}},[["string"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],style:e.style,attrs:{disabled:e.disabled,placeholder:""},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"textarea"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],style:e.style,attrs:{disabled:e.disabled,type:"textarea",rows:5,placeholder:""},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"inputnumber"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],style:e.style,attrs:{disabled:e.disabled,type:"number",placeholder:""},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"radio"==t.type?i("option-radio",{style:e.style,attrs:{disabled:e.disabled,field:t},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"checkbox"==t.type?i("option-checkbox",{style:e.style,attrs:{disabled:e.disabled,field:t},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"select"==t.type?i("option-select",{style:e.style,attrs:{disabled:e.disabled,field:t},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"selectMulti"==t.type?i("option-select-multi",{style:e.style,attrs:{disabled:e.disabled,field:t},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):"date"==t.type?i("el-date-picker",{style:e.style,attrs:{disabled:e.disabled,editable:!0,clearable:!0,type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd"},model:{value:e.dataModel[t.key],callback:function(i){e.$set(e.dataModel,t.key,i)},expression:"dataModel[item.key]"}}):e._e()]],2)],1)})),1)],1):e._e()}),[],!1,null,null,null).exports;i("9f60"),i("94f0"),i("0c84"),i("2843"),i("a450"),i("4057");function Q(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return X(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return X(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var Z={components:{optionRadio:q.a,OptionCheckbox:K.a,OptionSelect:H.a,OptionSelectMulti:G.a},mixins:[l()],data:function(){return{}},methods:{getSize:function(){var e=this.gfItemBus.widgetForm?this.gfItemBus.widgetForm:null,t=e?e.size:null;return t||"small"},addArrayRow:function(e){var t,i={},n=Q(e);try{for(n.s();!(t=n.n()).done;){i[t.value.key]=""}}catch(e){n.e(e)}finally{n.f()}this.dataModel||(this.dataModel=[]),this.dataModel.push(i)},removeTableRow:function(e){var t=this;this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataModel.splice(e,1)})).catch((function(){}))}},watch:{dataModel:{deep:!0,handler:function(e,t){this.gfItemBus.$emit("field-value-change",this.widget.key,e,t,this.gfItemBus)}}}},ee=Object(p.a)(Z,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.widget.columns.length>0?i("div",{staticClass:"group--box__generate-panel form-item-array gf-array",staticStyle:{"margin-bottom":"10px",padding:"0",border:"1px solid #efefef"}},[e.widget.options.isLabelHide?e._e():i("div",{staticClass:"group--panel-title",staticStyle:{margin:"0","margin-top":"5px",position:"relative"}},[e._v("\n        "+e._s(e.widget.name)+"\n        "),i("el-button",{staticStyle:{position:"absolute",right:"10px"},attrs:{size:"mini",type:"text",disabled:e.disabled},on:{click:function(t){return e.addArrayRow(e.widget.columns)}}},[e._v("添加行\n        ")])],1),i("el-table",{style:{width:e.widget.options.width+"%"},attrs:{data:e.dataModel,border:""}},[i("el-table-column",{attrs:{align:"center",label:"#",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.$index+1))]),i("el-button",{staticClass:"table-row-minus-btn",attrs:{type:"danger",icon:"el-icon-minus",size:"mini",disabled:e.disabled,circle:"",title:"删除行"},on:{click:function(i){return e.removeTableRow(t.$index)}}})]}}],null,!1,2968840099)}),e._l(e.widget.columns,(function(t,n){return i("el-table-column",{key:n,attrs:{align:"center","min-width":"200",label:t.name},scopedSlots:e._u([{key:"default",fn:function(n){return[["string"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled,size:e.getSize(),placeholder:""},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"textarea"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled,size:e.getSize(),type:"textarea",rows:5,placeholder:""},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"inputnumber"==t.type?i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:e.disabled,size:e.getSize(),type:"number",placeholder:""},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"radio"==t.type?i("option-radio",{attrs:{disabled:e.disabled,size:e.getSize(),field:t},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"checkbox"==t.type?i("option-checkbox",{attrs:{disabled:e.disabled,size:e.getSize(),field:t},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"select"==t.type?i("option-select",{attrs:{disabled:e.disabled,size:e.getSize(),field:t},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"selectMulti"==t.type?i("option-select-multi",{attrs:{disabled:e.disabled,size:e.getSize(),field:t},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):"date"==t.type?i("el-date-picker",{attrs:{disabled:e.disabled,editable:!0,clearable:!0,size:e.getSize(),type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd"},model:{value:n.row[t.key],callback:function(i){e.$set(n.row,t.key,i)},expression:"scope.row[col.key]"}}):e._e()]]}}],null,!0)})}))],2)],1):e._e()}),[],!1,null,null,null).exports,te=(i("ac67"),i("3ce5"));function ie(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function ne(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(i),!0).forEach((function(t){Object(te.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ie(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var oe={name:"UploadImgDialog",mixins:[i("495d").a],props:{visible:{type:Boolean,default:function(){return!1}},uploadImg:{type:Object,default:function(){return{}}},uploadImgData:{type:Object,default:function(){return{}}},fieldKey:{type:String,default:function(){return""}},multiple:{type:Boolean,default:function(){return!1}},"upload-img-max-size":{type:Number,default:function(){return 20}},"upload-img-max-limit":{type:Number,default:function(){return 9}}},data:function(){return{uploadImgDialogVisible:this.visible,dialogImageType:"local",dialogImageUrl:"",fileList:[],dialogImageLocal:""}},computed:{uploadImgUrl:function(){return this.uploadImg.uploadUrl},headers:function(){return{token:this.uploadImg.token}},uploadImgDataComplete:function(){return ne(ne({},this.uploadImgData),{},{fieldKey:this.fieldKey})},uploadImgMaxLimitCompute:function(){return this.multiple&&this.uploadImgMaxLimit?this.uploadImgMaxLimit:1}},methods:{submitUpload:function(e){var t=this;e.fieldKey=this.fieldKey,this.uploadImg.handleUploadImg(e,(function(e){t.dialogImageLocal=e,t.$emit("confirm",t.dialogImageLocal),t.$message.success("上传成功"),t.fileList=[],t.uploadImgDialogVisible=!1}),(function(e){t.fileList=[],t.$message.error("上传失败")}))},upLoadImgSuccess:function(e,t,i){var n=e&&"00000"===e.code&&e.data?e.data:"";this.uploadImg.isSuccess&&this.uploadImg.isSuccess(e),n?(this.dialogImageLocal=n,this.$emit("confirm",this.dialogImageLocal),this.$message.success("上传成功"),this.uploadImgDialogVisible=!1):this.$message.error("上传失败")},beforeImgUpload:function(e){var t="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type,i=e.size/1024/1024<this.uploadImgMaxSize;return t||this.$message.error("上传图片只能是 .png, .jpeg, .jpg, .gif 格式!"),i||this.$message.error("上传图片大小不能超过 "+this.uploadImgMaxSize+"MB!"),t&&i},handleExceed:function(e,t){this.$message.warning("当前限制选择".concat(this.uploadImgMaxLimit,"个文件，本次选择了 ").concat(e.length," 个文件，共选择了 ").concat(e.length+t.length," 个文件"))},upLoadImgRemove:function(e,t){},uploadImgDialogConfirm:function(){"url"==this.dialogImageType?this.dialogImageUrl?(this.uploadImgDialogVisible=!1,this.$emit("confirm",this.dialogImageUrl)):this.$message.error("请输入图片地址"):"local"==this.dialogImageType&&(this.dialogImageLocal?(this.$emit("confirm",this.dialogImageLocal),this.uploadImgDialogVisible=!1):this.$message.error("请添加图片并上传到服务器"))},uploadImgDialogClosed:function(){this.dialogImageUrl="",this.dialogImageLocal="",this.fileList=[]}},watch:{visible:function(e){this.uploadImgDialogVisible=e},uploadImgDialogVisible:function(e){this.$emit("update:visible",e)}}},ae=Object(p.a)(oe,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{staticClass:"fm-dialog",attrs:{title:"图片上传",visible:e.uploadImgDialogVisible,"append-to-body":"",width:"50%"},on:{"update:visible":function(t){e.uploadImgDialogVisible=t},closed:e.uploadImgDialogClosed}},[i("el-tabs",{staticStyle:{width:"400px"},model:{value:e.dialogImageType,callback:function(t){e.dialogImageType=t},expression:"dialogImageType"}},[i("el-tab-pane",{attrs:{label:"上传图片",name:"local"}},[i("el-upload",{ref:"upload",attrs:{action:e.uploadImg.handleUploadImg?"":e.uploadImgUrl,"http-request":e.uploadImg.handleUploadImg?e.submitUpload:void 0,headers:e.headers,data:e.uploadImgDataComplete,"on-remove":e.upLoadImgRemove,accept:".png, .jpeg, .jpg, .gif, .bmp",limit:e.uploadImgMaxLimitCompute,"on-exceed":e.handleExceed,multiple:e.uploadImgMaxLimitCompute>1,"file-list":e.fileList,"on-success":e.upLoadImgSuccess,"before-upload":e.beforeImgUpload,"auto-upload":!1}},[i("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选取文件")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:function(t){return e.$refs.upload.submit()}}},[e._v("上传到服务器")]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传jpg/png/jpeg/gif/bmp文件，且不超过"+e._s(e.uploadImgMaxSize)+"M")])],1)],1),i("el-tab-pane",{attrs:{label:"网络图片",name:"url"}},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],staticStyle:{"margin-bottom":"10px",width:"320px"},attrs:{placeholder:"请输入图片地址"},model:{value:e.dialogImageUrl,callback:function(t){e.dialogImageUrl=t},expression:"dialogImageUrl"}}),i("el-button",{attrs:{type:"primary"},on:{click:e.uploadImgDialogConfirm}},[e._v("确 定")]),i("el-image",{staticStyle:{width:"150px",height:"150px"},attrs:{"preview-src-list":[e.dialogImageUrl],src:e.dialogImageUrl}})],1)],1)],1)}),[],!1,null,null,null).exports,re={mixins:[l()],components:{UploadImgDialog:ae},data:function(){return{uploadImgDialogVisible:!1}},methods:{removeImg:function(e){var t=this;this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataModel.splice(e,1)})).catch((function(){}))},uploadImgDialogConfirm:function(e){this.dataModel&&Array.isArray(this.dataModel)?this.dataModel.push(e):this.dataModel=[e]}},computed:{fullImgUrls:function(){var e=this;if(!this.dataModel||!Array.isArray(this.dataModel))return[];var t=[];return this.dataModel.forEach((function(i){if(/http/.test(i))t.push(i);else if(e.eventBus.uploadImg.getFullImgUrl){var n=e.eventBus.uploadImg.getFullImgUrl(i);t.push(n)}else t.push(e.eventBus.uploadImg.baseUrl+i)})),t}},watch:{dataModel:function(e,t){this.gfItemBus.$emit("field-value-change",this.widget.key,e,t,this.gfItemBus)}}},le=Object(p.a)(re,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-upload-img"},[e.widget.options.isMultipleImg||0==e.dataModel.length?i("el-button",{attrs:{disabled:e.disabled,type:"primary",size:"mini"},on:{click:function(t){e.uploadImgDialogVisible=!0}}},[e._v("上传图片\n    ")]):e._e(),i("div",{style:{width:e.widget.options.width+"%"}},e._l(e.dataModel,(function(t,n){return i("div",{key:n,staticClass:"img-container"},[i("el-image",{style:{width:e.widget.options.imgWidth+"px",height:e.widget.options.imgHeight+"px"},attrs:{src:e.fullImgUrls[n],lazy:!0,"preview-src-list":e.fullImgUrls}}),i("div",{staticStyle:{"text-align":"center"}},[e.disabled?e._e():i("el-button",{staticClass:"img-delete-btn",attrs:{size:"mini",disabled:e.disabled,type:"danger",title:"删除"},on:{click:function(t){return e.removeImg(n)}}},[i("i",{staticClass:"el-icon-delete"})])],1)],1)})),0),i("upload-img-dialog",{attrs:{visible:e.uploadImgDialogVisible,"upload-img":e.eventBus.uploadImg,fieldKey:e.fieldKey,"upload-img-max-size":e.eventBus.uploadImgMaxSize,multiple:e.widget.options.isMultipleImg,"upload-img-max-limit":e.eventBus.uploadImgMaxLimit,"upload-img-data":e.eventBus.uploadImgData},on:{"update:visible":function(t){e.uploadImgDialogVisible=t},confirm:e.uploadImgDialogConfirm}})],1)}),[],!1,null,null,null).exports;i("fc02");function se(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function ue(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?se(Object(i),!0).forEach((function(t){Object(te.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):se(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var ce={name:"UploadFileDialog",props:{visible:{type:Boolean,default:function(){return!1}},uploadImg:{type:Object,default:function(){return{}}},uploadImgData:{type:Object,default:function(){return{}}},fieldKey:{type:String,default:function(){return""}},"upload-file-max-size":{type:Number,default:function(){return 20}},fileTypesAccept:String},data:function(){return{uploadFileDialogVisible:this.visible,fileList:[],dialogFileLocal:""}},computed:{uploadImgUrl:function(){return this.uploadImg.uploadUrl},accept:function(){return this.fileTypesAccept.trim()?this.fileTypesAccept.trim().split(/\s+/):[]},uploadImgDataComplete:function(){return ue(ue({},this.uploadImgData),{},{fieldKey:this.fieldKey})}},methods:{upLoadSuccess:function(e,t,i){var n=e&&"00000"===e.code&&e.data?e.data:"";n?(this.dialogFileLocal=n,this.$emit("confirm",this.dialogFileLocal),this.$message.success("上传成功"),this.uploadFileDialogVisible=!1):this.$message.error("上传失败")},beforeUpload:function(e){var t=e.size/1024/1024<this.uploadFileMaxSize;return t||this.$message.error("上传文件大小不能超过 "+this.uploadFileMaxSize+"MB!"),t},upLoadRemove:function(e,t){},uploadDialogClosed:function(){this.dialogFileLocal="",this.fileList=[]}},watch:{visible:function(e){this.uploadFileDialogVisible=e},uploadFileDialogVisible:function(e){this.$emit("update:visible",e)}}},de=Object(p.a)(ce,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{staticClass:"fm-dialog",attrs:{title:"文件上传",visible:e.uploadFileDialogVisible,"append-to-body":"",width:"50%"},on:{"update:visible":function(t){e.uploadFileDialogVisible=t},closed:e.uploadDialogClosed}},[i("el-upload",{ref:"upload",attrs:{action:e.uploadImgUrl,headers:{token:e.uploadImg.token},data:e.uploadImgDataComplete,"on-remove":e.upLoadRemove,accept:e.accept.join(","),limit:1,"file-list":e.fileList,"on-success":e.upLoadSuccess,"before-upload":e.beforeUpload,"auto-upload":!1}},[i("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选取文件")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:function(t){return e.$refs.upload.submit()}}},[e._v("上传到服务器")]),e.accept.length>0?i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("只能上传"+e._s(e.accept.join("/"))+"文件，且不超过"+e._s(e.uploadFileMaxSize)+"M")]):i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("上传文件不超过"+e._s(e.uploadFileMaxSize)+"M")])],1)],1)}),[],!1,null,null,null).exports,fe={mixins:[l()],components:{UploadFileDialog:de},data:function(){return{uploadFileDialogVisible:!1}},methods:{uploadFileDialogConfirm:function(e){this.dataModel&&Array.isArray(this.dataModel)?this.dataModel.push(e):this.dataModel=[e]},handleClose:function(e){var t=this;this.dataModel.length>0&&this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataModel.splice(t.dataModel.indexOf(e),1)})).catch((function(){}))}},computed:{},watch:{dataModel:function(e,t){this.gfItemBus.$emit("field-value-change",this.widget.key,e,t,this.gfItemBus)}}},pe=Object(p.a)(fe,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-upload-file"},[e.widget.options.isMultipleFile||0==e.dataModel.length?i("el-button",{attrs:{disabled:e.disabled,type:"primary",size:"mini"},on:{click:function(t){e.uploadFileDialogVisible=!0}}},[e._v("上传文件\n    ")]):e._e(),e._l(e.dataModel,(function(t,n){return i("el-tag",{key:n,attrs:{closable:!e.disabled,type:"success"},on:{close:function(i){return e.handleClose(t)}}},[i("a",{attrs:{href:e.getFullImgUrl(t),download:""}},[e._v(e._s(e.getFullImgUrl(t)))])])})),i("upload-file-dialog",{attrs:{visible:e.uploadFileDialogVisible,"file-types-accept":e.widget.options.fileTypesAccept,"upload-img":e.eventBus.uploadImg,"upload-img-data":e.eventBus.uploadImgData,fieldKey:e.fieldKey,"upload-file-max-size":e.eventBus.uploadFileMaxSize},on:{"update:visible":function(t){e.uploadFileDialogVisible=t},confirm:e.uploadFileDialogConfirm}})],2)}),[],!1,null,null,null).exports,me={mixins:[l()],beforeCreate:function(){this.$options.components.GenerateFormItem=De},data:function(){return{}},computed:{bgColorObj:function(){return this.widget.options.bgColor?{"background-color":this.widget.options.bgColor}:{}}},methods:{fillExternalData:function(){this.$refs.item&&this.$refs.item.forEach((function(e){e.fillExternalData()}))},clearExternalData:function(){this.$refs.item&&this.$refs.item.forEach((function(e){e.clearExternalData()}))}}},he=Object(p.a)(me,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-matrix widget-matrix",class:{"is-custom-bg":!!e.widget.options.bgColor},staticStyle:{"margin-bottom":"18px"}},[e.widget.options.isLabelHide?e._e():i("div",{staticClass:"text-center gm-table-title",style:{width:e.widget.options.width+"%"}},[i("b",[e._v(e._s(e.widget.name))])]),i("el-table",{style:Object.assign({},{width:e.widget.options.width+"%"},e.bgColorObj),attrs:{data:e.widget.matrixData,border:""}},[i("el-table-column",{attrs:{label:e.widget.matrixColumnsFirst?e.widget.matrixColumnsFirst.label:"#",width:e.widget.matrixColumnsFirst&&e.widget.matrixColumnsFirst.width?e.widget.matrixColumnsFirst.width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("b",[e._v(e._s(e.widget.matrixRow[t.$index]))])]}}])}),e._l(e.widget.matrixColumns,(function(t,n){return i("el-table-column",{key:n,attrs:{"min-width":"180","header-align":"center",width:t&&t.width?t.width:"",label:"string"==typeof t?t:t.label},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row[n],(function(t){return[t.isDelete?e._e():i("generate-form-item",{directives:[{name:"show",rawName:"v-show",value:t.options.show,expression:"col.options.show"}],key:t.key,ref:"item",refInFor:!0,class:t.options.required?"matrix-required":"",attrs:{rules:e.gfItemBus.rules,widget:t,"is-matrix-item":!0,value:e.gfItemBus.curModels,models:e.gfItemBus.curFormModels,widgetForm:e.gfItemBus.widgetForm,remote:t.remote,externaldata:e.gfItemBus.externaldata,"de-settings":e.gfItemBus.deSettings},on:{"update:value":function(t){return e.$set(e.gfItemBus,"curModels",t)},"update:models":function(t){return e.$set(e.gfItemBus,"curFormModels",t)},"input-change":e.gfItemBus.onInputChange,"field-value-change":e.gfItemBus.fieldValueChange,event:e.gfItemBus.eventHandler}})]}))]}}],null,!0)})}))],2)],1)}),[],!1,null,null,null).exports;function ge(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return _e(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return _e(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function _e(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var be={mixins:[l()],beforeCreate:function(){this.$options.components.GenerateFormItem=De},data:function(){return{}},methods:{addTableRow:function(e){var t,i={},n=ge(e);try{for(n.s();!(t=n.n()).done;){var o=t.value;i[o.key]=o.options.defaultValue}}catch(e){n.e(e)}finally{n.f()}this.dataModel||(this.dataModel=[]),this.dataModel.push(i)},removeTableRow:function(e){var t=this;this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataModel.splice(e,1)})).catch((function(){}))}},computed:{tableColumns:function(){return this.widget.tableColumns.filter((function(e){return!e.isDelete}))}},watch:{dataModel:{deep:!0,handler:function(e,t){this.gfItemBus.$emit("field-value-change",this.widget.key,e,t,this.gfItemBus)}}}},ye=Object(p.a)(be,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.tableColumns.length>0?i("div",{staticClass:"gf-table",staticStyle:{"margin-bottom":"18px"}},[i("div",{staticClass:"text-center gm-table-title",style:{width:e.widget.options.width+"%"}},[i("b",[e._v(e._s(e.widget.name))])]),i("el-table",{style:{width:e.widget.options.width+"%"},attrs:{data:e.dataModel,stripe:!0,border:""}},[i("el-table-column",{attrs:{label:"#",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.$index+1))]),t.$index+1>e.widget.options.rows?i("el-button",{staticClass:"table-row-minus-btn",attrs:{type:"danger",icon:"el-icon-minus",size:"mini",circle:"",title:"删除行"},on:{click:function(i){return e.removeTableRow(t.$index)}}}):e._e()]}}],null,!1,1096572725)}),e._l(e.tableColumns,(function(t,n){return i("el-table-column",{key:n,attrs:{label:t.options.unit?t.name+"("+t.options.unit+")":t.name},scopedSlots:e._u([{key:"default",fn:function(n){return[i("generate-form-item",{directives:[{name:"show",rawName:"v-show",value:t.options.show,expression:"col.options.show"}],key:t.key,attrs:{rules:e.gfItemBus.rules,widget:t,value:n.row,models:n.row,widgetForm:e.gfItemBus.widgetForm,remote:t.remote,externaldata:t.externaldata,"de-settings":t.DESettings,"is-table-column":!0},on:{"update:value":function(t){return e.$set(n,"row",t)},"update:models":function(t){return e.$set(n,"row",t)},"input-change":e.gfItemBus.onInputChange,event:e.gfItemBus.eventHandler}})]}}],null,!0)})}))],2),e.widget.options.canAddRows?i("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:function(t){return e.addTableRow(e.tableColumns)}}},[e._v("\n        添加")]):e._e()],1):e._e()}),[],!1,null,null,null).exports;function ve(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return we(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return we(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function we(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var Oe={mixins:[l()],beforeCreate:function(){this.$options.components.GenerateFormItem=De},provide:function(){return{groupsWidget:this.widget}},data:function(){return{}},methods:{addTableRow:function(e){var t,i={},n=ve(e);try{for(n.s();!(t=n.n()).done;){var o=t.value;i[o.key]=o.options.defaultValue}}catch(e){n.e(e)}finally{n.f()}if(this.dataModel||(this.dataModel=[]),!this.widget.groupItemsIsAdd){for(var a=[],r=0;r<this.dataModel.length;r++)a.push(!1);this.$set(this.widget,"groupItemsIsAdd",a)}this.widget.groupItemsIsAdd.push(!0),this.dataModel.push(i)},removeTableRow:function(e){var t=this;this.$confirm("确定要删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.dataModel.splice(e,1),t.widget.groupItemsIsAdd&&t.widget.groupItemsIsAdd.splice(e,1)})).catch((function(){}))},getRemoveTableRowBtnDisabled:function(e){return(!this.widget.groupItemsIsAdd||!this.widget.groupItemsIsAdd[e])&&!!this.eventBus.formDisabled},fieldValueChange:function(e,t,i,n){this.gfItemBus.$emit("field-value-change",this.widget.key+"."+e,t,i,n)}},computed:{groupItems:function(){return this.widget.groupItems.filter((function(e){return!e.isDelete}))},widgets:function(){var e=[];return Object(h.b)({list:this.widget.groupItems},(function(t){t.isDelete||e.push(t)})),e},widgetKeys:function(){return this.widgets.map((function(e){return e.key}))},groupFormRules:function(){var e={};for(var t in this.gfItemBus.rules)if(Object.hasOwnProperty.call(this.gfItemBus.rules,t)){var i=this.gfItemBus.rules[t];this.widgetKeys.indexOf(t)>=0&&(e[t]=i)}return e}}},ke=Object(p.a)(Oe,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.groupItems.length>0?i("div",{staticClass:"gf-groups",staticStyle:{"margin-bottom":"18px"}},[i("div",{staticClass:"text-center gm-table-title",style:{width:e.widget.options.width+"%"}},[i("b",[e._v(e._s(e.widget.name))])]),i("el-table",{style:{width:e.widget.options.width+"%"},attrs:{data:e.dataModel?e.dataModel:[],"show-header":!1,stripe:!0,border:""}},[i("el-table-column",{attrs:{label:"#",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.$index+1))]),t.$index+1>e.widget.options.groupItemsCount?i("el-button",{staticClass:"table-row-minus-btn",attrs:{type:"danger",icon:"el-icon-minus",size:"mini",circle:"",title:"删除行",disabled:e.getRemoveTableRowBtnDisabled(t.$index)},on:{click:function(i){return e.removeTableRow(t.$index)}}}):e._e()]}}],null,!1,3605454435)}),i("el-table-column",{attrs:{label:e.widget.name},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-form",{ref:"GroupForm",attrs:{model:t.row,rules:e.groupFormRules,size:e.eventBus.data.config.size,"label-position":e.eventBus.data.config.labelPosition,"label-width":e.eventBus.data.config.labelWidth+"px"}},e._l(e.groupItems,(function(n){return i("generate-form-item",{directives:[{name:"show",rawName:"v-show",value:n.options.show,expression:"col.options.show"}],key:n.key,attrs:{rules:e.groupFormRules,widget:n,value:t.row,models:t.row,"groups-index":t.$index,widgetForm:e.gfItemBus.widgetForm,remote:n.remote,externaldata:n.externaldata,"de-settings":n.DESettings},on:{"update:value":function(i){return e.$set(t,"row",i)},"update:models":function(i){return e.$set(t,"row",i)},"input-change":e.gfItemBus.onInputChange,event:e.gfItemBus.eventHandler,"field-value-change":e.fieldValueChange}})})),1)]}}],null,!1,3404579780)})],1),i("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:function(t){return e.addTableRow(e.groupItems)}}},[e._v("\n        添加")])],1):e._e()}),[],!1,null,null,null).exports,xe={mixins:[l()],data:function(){return{}}},Me=Object(p.a)(xe,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],style:{width:"100%"},attrs:{max:e.widget.options.max,min:e.widget.options.min,type:"number",placeholder:e.widget.options.placeholder,disabled:e.disabled},on:{focus:function(t){return e.emitEvent("event","focus",e.widget)},blur:function(t){return e.emitEvent("event","blur",e.widget)},input:function(t){return e.emitEvent("event","change",e.widget)}},nativeOn:{click:function(t){return e.emitEvent("event","click",e.widget)},dblclick:function(t){return e.emitEvent("event","dblclick",e.widget)}},model:{value:e.dataModel,callback:function(t){e.dataModel=e._n(t)},expression:"dataModel"}})}),[],!1,null,null,null).exports,Ee={name:"generate-form-item",props:{widget:{type:Object,required:!0},widgetForm:{type:Object,default:function(){return{list:[],config:h.d}}},models:{type:Object,default:function(){return{}}},value:{type:Object,default:function(){return{}}},rules:{type:Object,default:function(){return{}}},remote:{type:Object,default:function(){return{}}},externaldata:{type:Object,default:function(){return{}}},"de-settings":{type:Object,default:function(){return{deCategory:[],deItem:[],deItemProp:[],deSpecialVal:[],deObsvType:[],deObsvField:[]}}},"is-table-column":Boolean,"is-matrix-item":Boolean,"groups-index":Number},components:{AuditStatus:b.a,GfToolWidget:m,AuditDialog:g.a,GfInput:v,GfInputnumber:O,GfTextarea:x,GfTime:E,GfDate:D,GfText:C,GfDivider:B,GfLink:A,GfRadio:F,GfCheckbox:L,GfSelect:N,GfDictionary:z,GfAutocomplete:W,GfObject:Y,GfArray:ee,GfUploadImg:le,GfUploadFile:pe,GfMatrix:he,GfTable:ye,GfGroups:ke,GfScore:Me},provide:function(){return{widget:this.widget,value:this.value,dataModel:this.dataModel,gfItemBus:this,groupsIndex:this.groupsIndex}},inject:{all$vm:{from:"all$vm",default:function(){}},eventBus:{from:"eventBus",default:function(){return null}},groupsWidget:{from:"groupsWidget",default:function(){return null}}},mixins:[_.a],data:function(){var e,t=this;return this.widget.isTool?(e={},this.widget.fields.forEach((function(i){t.$set(e,i.key,t.value[i.key])}))):e=this.value2dataModel(this.value[this.widget.key],this.widget),{externalDataTable:{isShowPager:!1,loading:!1,page:1,limit:10,pageRecord:[]},searchStrInput:"",searchStr:"",externalVisible:!1,dataModel:e,curModels:this.value,curFormModels:this.models,externalDataBtnClicked:!1}},created:function(){var e=this;"imgupload"===this.widget.type&&this.remote&&this.widget.options.isQiniu&&this.remote[this.widget.options.tokenFunc]((function(t){e.widget.options.token=t})),this.isInFormBox&&this.eventBus.$on("init-change-finally",(function(){e.initChangeFinally()}))},mounted:function(){this.$emit("mounted")},destroyed:function(){this.$emit("destroyed")},computed:{externalTableDataFilter:function(){var e=this.searchStr.trim();return e?this.externaldata[this.widget.key].filter((function(t){for(var i=Object.keys(t).map((function(e){return String(t[e])})),n=0;n<i.length;n++){if(i[n].indexOf(e)>=0)return!0}return!1})):this.externaldata[this.widget.key]},externalColums:function(){for(var e=this,t=this.deSettings.deItemProp.filter((function(t){return("1"==t.displayInOutInterface||t.code==e.widget.externaldataInfo.ItemProp)&&t.categoryId==e.widget.externaldataInfo.CatID})),i=null,n=0;n<t.length;n++)if(t[n].code==this.widget.externaldataInfo.ItemProp){i=t.splice(n,1)[0];break}return i&&t.unshift(i),t},isInGroupsItemNew:function(){var e=this.groupsIndex||0==this.groupsIndex?this.groupsIndex:-1,t=this.groupsWidget&&this.groupsWidget.groupItemsIsAdd?this.groupsWidget.groupItemsIsAdd:[];return e>=0&&t[e]},isExternalDataAndDataModelNotEqual:function(){return Object(o.j)(this.externaldata,this.widget,this.dataModel)},isAuditable:function(){return"divider"!=this.widget.type&&"link"!=this.widget.type&&"table"!=this.widget.type&&"matrix"!=this.widget.type},disabled:function(){return!!this.isInFormBox&&(!this.eventBus.editing[this.widget.key]&&(!!this.eventBus.formDisabled||(!!(this.eventBus.audit&&this.eventBus.audit.lock&&this.eventBus.audit.lock.length>0&&this.eventBus.audit.lock.indexOf(this.widget.key)>=0)||(this.eventBus.disabledList.indexOf(this.widget.key)>=0||this.widget.options.disabled))))},gfItemBus:function(){return this}},methods:{externalDataSearch:function(){var e=this;this.searchStr=this.searchStrInput,this.externalDataTable.page=1,this.$nextTick((function(){e.externalDataTable.isShowPager&&e.externalDataTablePageChange(1)}))},externalDataTablePageChange:function(e){for(var t=this.externalTableDataFilter,i=this.externalDataTable.limit,n=[],o=i*(e-1);o<i*e;o++){var a=t[o];a&&n.push(a)}this.externalDataTable.pageRecord=n},externalItemClick:function(e,t){this.dataModel=this.externalData2dataModel(e[t]),this.fillOthersExternalData(e),this.externalVisible=!1},externalDataBtnClick:function(){var e=this,t=!1;this.externalDataBtnClicked||(this.externalDataBtnClicked=!0,t=!0);var i={widget:this.widget,$vm:this,isFirstTimeClickExternalDataBtn:t,setExternalDataList:function(t){e.setExternalDataList(t)}};this.eventBus.$emit("external-data-btn-click",i)},formatLabel:function(e,t){return e+"("+t+")"},onInputChange:function(e,t){this.$emit("input-change",e,t)},emitEvent:function(e,t,i,o){if("checkbox"==i.type&&"change"==t&&this.dataModel.length>0){var a=this.dataModel[this.dataModel.length-1],r=i.options.options.filter((function(e){return e.value==a}))[0];if(r&&r.isMutex||"other_option"==a&&i.options.otherOptionIsMutex)this.dataModel=[a];else if(this.dataModel.length>=2){var l=this.dataModel[this.dataModel.length-2],s=i.options.options.filter((function(e){return e.value==l}))[0];if(s&&s.isMutex||"other_option"==l&&i.options.otherOptionIsMutex){var u=Object(n.a)(this.dataModel);u.splice(u.length-2,1),this.dataModel=u}}}("checkbox"!=i.type&&"radio"!=i.type||"click"!=t||o&&("checkbox"==i.type&&"el-checkbox__original"==o.target.className||"radio"==i.type&&"el-radio__original"==o.target.className))&&this.$emit(e,t,i,this)},initChangeFinally:function(){this.all$vm&&(this.all$vm[this.widget.key]=this,this.all$vm[this.fieldKey]=this,this.widget.event&&this.widget.event.types&&this.widget.event.types.changeFinally&&this.widget.event.types.changeFinally.length>0&&this.$emit("event","change",this.widget,this))},eventHandler:function(e,t,i){this.$emit("event",e,t,i)},value2dataModel:o.r,dataModel2value:function(e,t){return Object(o.b)(e,this.widget,t)},externalData2dataModel:function(e){return Object(o.c)(e,this.widget)},setExternalDataList:function(e){e&&e.length>0&&(this.externaldata[this.widget.key]=e)},fillExternalData:function(){var e=this;if(this.widget.isTool)this.$nextTick((function(){e.$emit("widget-tool-fill-externaldata")}));else{if("matrix"==this.widget.type)return void(this.$refs.gfMatrix&&this.$refs.gfMatrix.fillExternalData());var t=this.externaldata[this.widget.key];if(t&&t.length>0){var i=null;this.widget.externaldataInfo.ItemProp&&(i=this.widget.externaldataInfo.ItemProp),i&&!this.dataModel&&0!==this.dataModel&&(this.dataModel=this.externalData2dataModel(t[0][i]),this.$emit("event","change",this.widget,this)),this.fillOthersExternalData(t[0])}}},fillOthersExternalData:function(e){var t=this;this.widget.externaldataInfo.others&&this.widget.externaldataInfo.others.length>0&&this.widget.externaldataInfo.others.forEach((function(i){if(i.key&&i.ItemProp&&t.all$vm[i.key]){var n=e[i.ItemProp];if(void 0!==n){var a=t.all$vm[i.key].widget;a.isTool?t.$nextTick((function(){t.all$vm[a.key].$emit("widget-tool-fill-other-externaldata",i.key,n)})):t.all$vm[i.key].dataModel=Object(o.c)(n,a)}}}))},fillOutData:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if((!this.dataModel||e)&&this.widget.options.outDataKey&&this.eventBus.outData&&void 0!==this.eventBus.outData[this.widget.options.outDataKey]){var t=this.eventBus.outData[this.widget.options.outDataKey];this.dataModel=Object(o.l)(t,this.widget),"other_option"===this.dataModel&&(this.widget.options.otherOptionValue=t)}},clearExternalData:function(){if("matrix"!=this.widget.type){var e=this.externaldata[this.widget.key];e&&e.length>0&&this.externaldata.isFill&&this.widget.externaldataInfo.ItemProp&&(this.dataModel="")}else this.$refs.gfMatrix.clearExternalData()},getFullImgUrl:function(e){return this.isInFormBox?/http/.test(e)?e:this.eventBus.uploadImg.getFullImgUrl?this.eventBus.uploadImg.getFullImgUrl(e):this.eventBus.uploadImg.baseUrl+e:e},fieldValueChange:function(e,t,i,n){this.$emit("field-value-change",e,t,i,n)}},watch:{dataModel:{handler:function(e,t){var i=this;if(null==t&&(t=""),JSON.stringify(e)!=JSON.stringify(t))if(this.widget.isTool)this.models&&(this.widget.fields.forEach((function(t){i.models[t.key]=e[t.key],i.value[t.key]=e[t.key]})),this.$emit("update:models",this.models),this.$emit("update:value",this.value));else{this.models&&(this.models[this.widget.key]=e,this.$emit("update:models",this.models)),this.$emit("input-change",e,this.widget.key);var n=this.value[this.widget.key],o=this.dataModel2value(e);this.value[this.widget.key]=o,this.$emit("field-value-change",this.widget.key,o,n,this),this.$emit("update:value",this.value)}},immediate:!0},models:{deep:!0,handler:function(e,t){if(e[this.widget.key]!=t[this.widget.key])if(this.curFormModels=e,this.widget.isTool){var i={};this.widget.fields.forEach((function(t){i[t.key]=e[t.key]})),this.dataModel=i}else this.dataModel=this.value2dataModel(e[this.widget.key],this.widget)}},curModels:{handler:function(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.$emit("update:value",e)},deep:!0},curFormModels:{handler:function(e){this.$emit("update:models",e)}},value:{handler:function(e,t){if(!this.widget.isTool){var i=e?e[this.widget.key]:"",n=t?t[this.widget.key]:"";null==n&&(n=""),this.curModels=e,this.dataModel=this.value2dataModel(i,this.widget)}},deep:!0},"widget.options.otherOptionValue":{handler:function(e,t){var i=this.dataModel2value(this.dataModel);this.value[this.widget.key]=i,this.$emit("update:value",this.value),this.$emit("field-value-change",this.widget.key,i,t,this)}}}},je=(i("21c3"),i("7e36"),Object(p.a)(Ee,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{class:{"ex-form-item":e.deSettings&&e.externaldata[e.widget.key]&&e.widget.externaldataInfo.ItemProp,"widget-audit":e.isAuditable&&e.isAuditMode&&!e.widget.isTool&&"groups"!=e.widget.type}},["divider"==e.widget.type||"link"==e.widget.type||"table"==e.widget.type||"groups"==e.widget.type||"matrix"==e.widget.type||"object"==e.widget.type||"array"==e.widget.type||e.widget.isTool?[e.widget.isTool?e._e():i("div",{staticStyle:{position:"relative"}},[e.isAuditable&&!e.isInGroupsItemNew?i("audit-status",{staticStyle:{position:"absolute",top:"0",left:"-5px","z-index":"2"}}):e._e(),"divider"==e.widget.type?i("gf-divider"):e._e(),"link"==e.widget.type?i("gf-link"):e._e(),"object"==e.widget.type?i("gf-object"):e._e(),"array"==e.widget.type?i("gf-array"):e._e(),"matrix"==e.widget.type?i("gf-matrix",{ref:"gfMatrix"}):e._e(),"table"==e.widget.type?i("gf-table"):e._e(),"groups"==e.widget.type?i("gf-groups"):e._e(),"object"!=e.widget.type&&"array"!=e.widget.type&&!e.widget.isTool||e.isInGroupsItemNew?e._e():i("div",{staticClass:"suffix-btns-outter"},[i("div",{staticClass:"gf-btn-box gf-box-right"},[e.optionBtnPermission(2)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"通过"},on:{click:e.toLockField}},[e._v("通过")]):e._e(),e.optionBtnPermission(4)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"解锁"},on:{click:e.toUnlockField}},[e._v("解锁")]):e._e(),e.optionBtnPermission(3)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"回复"},on:{click:e.openAuditDialog}},[e._v("回复")]):e._e(),e.optionBtnPermission(1)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"审核"},on:{click:e.openAuditDialog}},[e._v("审核")]):e._e(),e.optionBtnPermission(5)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"编辑"},on:{click:e.doEditing}},[e._v("编辑")]):e._e(),e.optionBtnPermission(6)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"保存"},on:{click:e.saveField}},[e._v("保存")]):e._e(),e.optionBtnPermission(7)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"撤回"},on:{click:e.toApplyBack}},[e._v("撤回")]):e._e(),i("apply-back-reason-dialog",{attrs:{visible:e.ApplyBackReasonDialogVisible},on:{"update:visible":function(t){e.ApplyBackReasonDialogVisible=t},confirm:e.applyBackConfirm}})],1)])],1),e.widget.isTool?i("gf-tool-widget",{ref:"ToolWiget"}):e._e()]:[i("el-form-item",{class:{"is-matrix-item":e.isMatrixItem,"form-item-add-mode-status-0":0===e.widget.options.addModeStatus,"form-item-add-mode-status-1":1===e.widget.options.addModeStatus,"form-item__table-cell":e.isTableColumn||e.isMatrixItem,"not-externale-data-equal":e.isExternalDataAndDataModelNotEqual,"widget-label-top":e.widget.options.isLabelTop},attrs:{prop:e.widget.key},scopedSlots:e._u([{key:"label",fn:function(){return[i("span",[e.isAuditable&&!e.isInGroupsItemNew?i("audit-status"):e._e(),e.isMatrixItem?e._e():i("span",{style:{color:e.widget.options.nameColor||"#606266","font-style":e.widget.options.isNameItalic?"italic":"normal","font-weight":e.widget.options.isNameBold?"bold":"normal","font-family":e.widget.options.nameFamily?e.widget.options.nameFamily:"Arial","font-size":e.widget.options.nameSize?e.widget.options.nameSize:"14px"}},[e.widget.options.required?i("i",{staticClass:"required-x"}):e._e(),e._v("\n                            "+e._s(e.isTableColumn||e.isMatrixItem||e.widget.options.isLabelHide?"":Object.keys(e.widget.options).indexOf("unit")>=0&&""!=e.widget.options.unit&&("input"==e.widget.type||"inputnumber"==e.widget.type)?e.formatLabel(e.widget.name,e.widget.options.unit):e.widget.name)+e._s(e.isTableColumn||e.isMatrixItem||e.widget.options.isLabelHide||e.widget.options.isScore?"":":")),e.widget.options.remark?i("el-popover",{attrs:{placement:"right","popper-class":"gf-remark-popover",title:"",width:"200",trigger:"hover",content:e.widget.options.remark}},[i("i",{staticClass:"el-icon-question",staticStyle:{"font-size":"14px",color:"#868180"},attrs:{slot:"reference"},slot:"reference"})]):e._e(),e.widget.nameImgUrl?i("div",{staticStyle:{margin:"10px"}},[i("el-image",{staticStyle:{width:"500px",height:"200px"},attrs:{fit:"contain","preview-src-list":[e.getFullImgUrl(e.widget.nameImgUrl)],src:e.getFullImgUrl(e.widget.nameImgUrl)}})],1):e._e()],1)],1)]},proxy:!0}],null,!1,3101539618)},["text"!==e.widget.type&&"link"!==e.widget.type?i("span",{staticClass:"matrix-required-x"}):e._e(),"input"==e.widget.type?i("gf-input"):e._e(),"inputnumber"==e.widget.type?i("gf-inputnumber"):e._e(),"textarea"==e.widget.type?i("gf-textarea"):e._e(),"time"==e.widget.type?i("gf-time"):e._e(),"date"==e.widget.type?i("gf-date"):e._e(),"text"==e.widget.type?i("gf-text"):e._e(),"radio"==e.widget.type?i("gf-radio"):e._e(),"checkbox"==e.widget.type?i("gf-checkbox"):e._e(),"select"==e.widget.type?i("gf-select"):e._e(),"dictionary"==e.widget.type?i("gf-dictionary"):e._e(),"autocomplete"==e.widget.type?i("gf-autocomplete"):e._e(),"uploadImg"==e.widget.type?i("gf-upload-img"):e._e(),"uploadFile"==e.widget.type?i("gf-upload-file"):e._e(),"score"==e.widget.type?i("gf-score"):e._e(),i("div",{staticClass:"suffix-btns-inner"},[i("div",{staticClass:"gf-btn-box-inner"},[!e.disabled&&e.deSettings&&e.externaldata[e.widget.key]&&e.widget.externaldataInfo.ItemProp?i("el-popover",{staticClass:"ex-button",attrs:{"popper-class":"de-settings-popover",trigger:"click"},model:{value:e.externalVisible,callback:function(t){e.externalVisible=t},expression:"externalVisible"}},[i("div",{staticStyle:{"margin-bottom":"10px"}},[i("el-input",{staticClass:"external-search-input",staticStyle:{width:"200px"},attrs:{size:"small","prefix-icon":"el-icon-search",clearable:"",placeholder:"输入内容搜索"},on:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.externalDataSearch.apply(null,arguments)}},model:{value:e.searchStrInput,callback:function(t){e.searchStrInput=t},expression:"searchStrInput"}},[i("el-button",{attrs:{slot:"append",type:"primary"},on:{click:e.externalDataSearch},slot:"append"},[e._v("查询")])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.externalDataTable.loading,expression:"externalDataTable.loading"}],staticStyle:{width:"100%"},attrs:{"max-height":"400",data:e.externalDataTable.isShowPager?e.externalDataTable.pageRecord:e.externalTableDataFilter,border:"",size:e.widgetForm.config.size,stripe:!0}},[i("el-table-column",{attrs:{label:"操作",width:"100",fixed:""},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:e.widgetForm.config.size,type:"primary"},on:{click:function(i){return e.externalItemClick(t.row,e.widget.externaldataInfo.ItemProp)}}},[e._v("选择")])]}}],null,!1,551054273)}),e._l(e.externalColums,(function(e){return i("el-table-column",{key:e.id,attrs:{"show-overflow-tooltip":!0,width:e.displayInOutInterfaceWidth>100?e.displayInOutInterfaceWidth:100,prop:e.code,label:e.name}})}))],2),i("el-pagination",{directives:[{name:"show",rawName:"v-show",value:e.externalDataTable.isShowPager,expression:"externalDataTable.isShowPager"}],attrs:{layout:"prev, pager, next, total","current-page":e.externalDataTable.page,total:e.externalTableDataFilter.length},on:{"current-change":e.externalDataTablePageChange,"update:currentPage":function(t){return e.$set(e.externalDataTable,"page",t)},"update:current-page":function(t){return e.$set(e.externalDataTable,"page",t)}}}),i("el-button",{staticClass:"ex-button__inner",attrs:{slot:"reference",type:"default",size:"mini",title:"外部数据"},on:{click:e.externalDataBtnClick},slot:"reference"},[e._v("外\n                            ")])],1):e._e()],1)]),i("div",{staticClass:"suffix-btns-outter"},[e.isExternalDataAndDataModelNotEqual?i("el-tooltip",{staticClass:"ex-button-tooltip",attrs:{effect:"dark",content:"当前字段的值与外部数据的取值不一致 (当前值:"+e.isExternalDataAndDataModelNotEqual.now+",外部接口值:"+e.isExternalDataAndDataModelNotEqual.external+")",placement:"bottom"}},[i("el-button",{attrs:{size:e.widgetForm.config.size,icon:"el-icon-warning"}})],1):e._e(),e.isInGroupsItemNew||"text"==e.widget.type?e._e():i("div",{staticClass:"gf-btn-box"},[e.optionBtnPermission(2)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"通过"},on:{click:e.toLockField}},[e._v("通过")]):e._e(),e.optionBtnPermission(4)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"解锁"},on:{click:e.toUnlockField}},[e._v("解锁")]):e._e(),e.optionBtnPermission(3)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"回复"},on:{click:function(t){return e.openAuditDialog({})}}},[e._v("回复")]):e._e(),e.optionBtnPermission(1)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"审核"},on:{click:function(t){return e.openAuditDialog({})}}},[e._v("审核")]):e._e(),e.optionBtnPermission(5)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"编辑"},on:{click:e.doEditing}},[e._v("编辑")]):e._e(),e.optionBtnPermission(6)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"保存"},on:{click:e.saveField}},[e._v("保存")]):e._e(),e.optionBtnPermission(7)?i("el-button",{staticClass:"audit-btn",attrs:{type:"text",size:"mini",title:"撤回"},on:{click:e.toApplyBack}},[e._v("撤回")]):e._e(),i("apply-back-reason-dialog",{attrs:{visible:e.ApplyBackReasonDialogVisible},on:{"update:visible":function(t){e.ApplyBackReasonDialogVisible=t},confirm:e.applyBackConfirm}})],1)],1)],1)]],2),!e.widget.isTool&&e.isAuditMode&&e.auditDialogVisible?i("audit-dialog",{attrs:{visible:e.auditDialogVisible,"open-params":e.openParams},on:{"update:visible":function(t){e.auditDialogVisible=t}}}):e._e()],1)}),[],!1,null,null,null)),De=t.a=je.exports},aa18:function(e,t,i){"use strict";var n=i("e99b"),o=i("52a4")(!0);n(n.P,"Array",{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),i("87b2")("includes")},ac67:function(e,t,i){var n=i("e99b"),o=i("e7c8"),a=i("3471"),r=i("285b"),l=i("1374");n(n.S,"Object",{getOwnPropertyDescriptors:function(e){for(var t,i,n=a(e),s=r.f,u=o(n),c={},d=0;u.length>d;)void 0!==(i=s(n,t=u[d++]))&&l(c,t,i);return c}})},addc:function(e,t,i){var n=i("4fd4"),o=i("8078"),a=i("3a0d")("IE_PROTO"),r=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),n(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?r:null}},adf4:function(e,t,i){"use strict";i.d(t,"a",(function(){return r})),i.d(t,"b",(function(){return l})),i.d(t,"c",(function(){return s}));i("ac67"),i("1bc7"),i("25ba"),i("32ea");var n=i("3ce5");function o(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function a(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?o(Object(i),!0).forEach((function(t){Object(n.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var r={Auditor:0,Filler:1},l={role:r.Filler,doubt:[],reply:[],lock:[],submit:[],backApply:[],agreeBackApply:[]};function s(e){if(!e)return l;if("string"!=typeof e)return a(a({},l),e);try{var t=JSON.parse(e);return a(a({},l),t)}catch(e){return l}}},aeb8:function(e,t,i){var n=i("0b34").navigator;e.exports=n&&n.userAgent||""},afcd:function(e,t,i){"use strict";i.r(t);i("a450"),i("1bc7"),i("fc02");var n=i("f8b5"),o=i("21c6"),a=i("118b"),r=i("7286"),l=i("0474"),s={mixins:[Object(o.a)()],components:{OptionRadio:a.a},type:"t_as",data:function(){return{fields_n:[]}},computed:{years:function(){var e=this.toolModel[this.widget.fields[0].key];if(!e)return[];var t=new Date,i=new Date(e);if(i.getTime()>t.getTime())return[];for(var o=Math.floor((t.getTime()-i.getTime())/31536e6),a=[],r=e.split("-"),s=Object(n.a)(r,3),u=s[0],c=s[1],d=s[2],f=0;f<o;f++){var p=new Date("".concat(+u+f,"-").concat(c,"-").concat(d));p=new Date(p.getTime()+864e5),a.push({year_n:f+1,startDate:Object(l.m)(p),endDate:"".concat(+u+f+1,"-").concat(c,"-").concat(d)})}return a},before1Year:function(){var e=this.toolModel[this.widget.fields[0].key];if(e){var t=e.split("-"),i=Object(n.a)(t,3),o=i[0],a=i[1],r=i[2],s=new Date("".concat(o,"-").concat(a,"-").concat(r));return s=new Date(s.getTime()-864e5),{year_n:-1,startDate:"".concat(+o-1,"-").concat(a,"-").concat(r),endDate:Object(l.m)(s)}}return null}},created:function(){var e=this;this.$watch("toolModel.".concat(this.widget.fields[0].key),(function(t){e.fields_n=[];var i=e.years.length;if(i>1)for(var n=function(t){var i=JSON.parse(JSON.stringify(e.widget.afterSurgeryOfYear_n.fields));i.forEach((function(i){i.name=i.name.replaceAll("dateRange","dre").replaceAll("n"," ".concat(t+2," ")).replaceAll("dre","dateRange"),i.key="".concat(e.widget.key,"_").concat(i.code,"_").concat(t+2),e.$set(e.eventBus.dataOptions,i.key,i)})),e.fields_n.push(i)},o=0;o<i-1;o++)n(o)})),this.gfItemBus.getItemScore=function(){return e.getItemScore()},this.gfItemBus.getItemScoreObj=function(){return e.getItemScoreObj()},this.gfItemBus.getNames=function(){return e.getNames()},this.$watch("toolModel",(function(t){e.emitEvent("event","change",e.widget)}),{deep:!0})},methods:{handleFocus:function(){this.widget.options.disabled&&this.$refs.datePicker.hidePicker()},disabledDate:function(e){return e>Date.now()},getComputedLabel:function(e,t,i){var n=null;if(n=t<0?this.before1Year:this.years[t]){if("1004_a1"==i){var o=n.startDate,a=Object(l.m)(new Date(new Date(o).getTime()+2592e6));return e.replaceAll("${dateRange}"," ".concat(o," ~ ").concat(a," ")).replaceAll("n"," ".concat(n.year_n," "))}if("1005_a1"==i){var r=Object(l.m)(new Date(new Date(n.startDate).getTime()+26784e5)),s=n.endDate;return e.replaceAll("${dateRange}"," ".concat(r," ~ ").concat(s," ")).replaceAll("n"," ".concat(n.year_n," "))}return e.replaceAll("${dateRange}"," ".concat(n.startDate," ~ ").concat(n.endDate," ")).replaceAll("n"," ".concat(n.year_n," "))}return e.replaceAll("${dateRange}","  ")},getField:function(e,t){return this.fields_n[e]&&this.fields_n[e][t]?this.fields_n[e][t]:{}},getItemScore:function(){var e=this,t=0,i=this.toolModel[this.widget.fields[1].key]||0;return this.years.forEach((function(n){var o=0;if(1===n.year_n){var a=e.widget.fields[4].key;o+=e.getScoreByRule(a,i)}else{var r="".concat(e.widget.key,"_").concat(e.widget.afterSurgeryOfYear_n.fields[0].code,"_").concat(n.year_n);o+=e.getScoreByRule(r,i)}t+=o})),t},getItemScoreObj:function(){var e=this,t={},i=this.toolModel[this.widget.fields[1].key]||0;return this.years.forEach((function(n){var o=0;if(1===n.year_n){var a=e.widget.fields[4].key;o+=e.getScoreByRule(a,i)}else{var r="".concat(e.widget.key,"_").concat(e.widget.afterSurgeryOfYear_n.fields[0].code,"_").concat(n.year_n);o+=e.getScoreByRule(r,i)}t["".concat(e.widget.key,"_score_").concat(n.year_n)]=o})),t},getNames:function(){var e=this,t={};return this.years.forEach((function(i){t["".concat(e.widget.key,"_score_").concat(i.year_n)]="术后第".concat(i.year_n,"年")})),t},getScoreByRule:function(e,t){var i=this.toolModel[e],n=this.eventBus.dataOptions[e],o=Object(r.a)(i,n);if(!o)return 0;if(t<=0&&(t=1),t>=9)if("1"==o.type){var a=o.value&&+o.value>=0?+o.value:0;if(0===a)return 1;if(a>=1&&a<=3)return 3;if(a>=4&&a<=.5*t)return 4;if(a>4&&a<=2*t)return 5;if(a>2*t)return 6}else{if("0"==o.value)return 1;if("1"==o.value)return 2}else if(t<9){if("1"==o.type){var l=o.value&&+o.value>=0?+o.value:0;return 0===l?1:1===l?3:2===l?4:3===l?5:6}if("0"==o.value)return 1;if("1"==o.value)return 2}return 0}},watch:{"widget.options.disabled":function(e){e&&this.$refs.datePicker.hidePicker()}}},u=i("cba8"),c=Object(u.a)(s,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"wf-tool-afterSurgery"},[i("tool-field",{attrs:{field:e.widget.fields[0]},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-date-picker",{ref:"datePicker",staticStyle:{width:"100%"},attrs:{disabled:n,editable:!0,clearable:!0,"picker-options":{disabledDate:e.disabledDate},type:"date",placeholder:"请选择日期","value-format":"yyyy-MM-dd"},on:{focus:e.handleFocus},model:{value:e.toolModel[e.widget.fields[0].key],callback:function(t){e.$set(e.toolModel,e.widget.fields[0].key,t)},expression:"toolModel[widget.fields[0].key]"}})]}}])}),i("tool-field",{attrs:{field:e.widget.fields[1],rules:[{required:"true",trigger:"blur",message:"手术前一年内发作次数必填"}],label:e.getComputedLabel(e.widget.fields[1].name,-1)},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{disabled:n,type:"number",placeholder:""},model:{value:e.toolModel[e.widget.fields[1].key],callback:function(t){e.$set(e.toolModel,e.widget.fields[1].key,t)},expression:"toolModel[widget.fields[1].key]"}})]}}])}),e.years.length>0?i("div",[i("div",e._l(e.widget.fields.length-2,(function(t){return i("tool-field",{key:t,attrs:{field:e.widget.fields[t+1],label:e.getComputedLabel(e.widget.fields[t+1].name,0,e.widget.fields[t+1].code)},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.disabled;return[i("option-radio",{attrs:{disabled:o,field:e.widget.fields[t+1]},model:{value:e.toolModel[e.widget.fields[t+1].key],callback:function(i){e.$set(e.toolModel,e.widget.fields[t+1].key,i)},expression:"toolModel[widget.fields[i+1].key]"}})]}}],null,!0)})})),1)]):e._e(),e.years.length>1?e._l(e.years.length-1,(function(t){return i("div",{key:t},[i("div",e._l(e.widget.afterSurgeryOfYear_n.fields,(function(n,o){return i("tool-field",{key:o,attrs:{field:e.getField(t-1,o),label:e.getComputedLabel(n.name,t)},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.disabled;return[i("option-radio",{attrs:{disabled:r,field:e.getField(t-1,o)},model:{value:e.toolModel[e.widget.key+"_"+n.code+"_"+(t+1)],callback:function(i){e.$set(e.toolModel,e.widget.key+"_"+n.code+"_"+(t+1),i)},expression:"toolModel[`${widget.key}_${field.code}_${i+1}`]"}})]}}],null,!0)})})),1)])})):e._e()],2)}),[],!1,null,null,null);t.default=c.exports},b07f:function(e,t,i){},b1d4:function(e,t,i){var n=i("a86f");e.exports=function(e,t,i,o){try{return o?t(n(i)[0],i[1]):t(i)}catch(t){var a=e.return;throw void 0!==a&&n(a.call(e)),t}}},b20f:function(e,t,i){},b2be:function(e,t,i){var n=i("e99b"),o=i("76e3"),a=i("0926");e.exports=function(e,t){var i=(o.Object||{})[e]||Object[e],r={};r[e]=t(i),n(n.S+n.F*a((function(){i(1)})),"Object",r)}},baa7:function(e,t,i){var n=i("76e3"),o=i("0b34"),a=o["__core-js_shared__"]||(o["__core-js_shared__"]={});(e.exports=function(e,t){return a[e]||(a[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:i("3d8a")?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},bac3:function(e,t,i){var n=i("bb8b").f,o=i("4fd4"),a=i("839a")("toStringTag");e.exports=function(e,t,i){e&&!o(e=i?e:e.prototype,a)&&n(e,a,{configurable:!0,value:t})}},bb8b:function(e,t,i){var n=i("a86f"),o=i("83d3"),a=i("5d10"),r=Object.defineProperty;t.f=i("26df")?Object.defineProperty:function(e,t,i){if(n(e),t=a(t,!0),n(i),o)try{return r(e,t,i)}catch(e){}if("get"in i||"set"in i)throw TypeError("Accessors not supported!");return"value"in i&&(e[t]=i.value),e}},bbcc:function(e,t,i){var n=i("0b34").document;e.exports=n&&n.documentElement},bdfd:function(e,t,i){"use strict";i.d(t,"a",(function(){return c}));i("ac67"),i("25ba"),i("32ea"),i("0c84"),i("2843"),i("4057");var n=i("3ce5"),o=(i("1bc7"),i("aa18"),i("34a9")),a={click:"单击",dblclick:"双击",blur:"失去焦点",focus:"获得焦点",change:"值改变",show:"显示",hide:"隐藏"};function r(e){var t={key:e.key,types:{}};for(var i in a)t.types[i]="show"==i||"hide"==i?{logic:"and",statements:[]}:[];return t}function l(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function s(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?l(Object(i),!0).forEach((function(t){Object(n.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var u=Object(o.e)(o.c);function c(e){var t,i=JSON.parse(JSON.stringify(e));return i.config=(t=i.config)?(p(t,JSON.parse(JSON.stringify(o.d)),JSON.parse(JSON.stringify(t))),t):JSON.parse(JSON.stringify(o.d)),Object(o.b)(i,(function(e){d(e)}),!0),i}function d(e){if(e.label&&(u[e.label]||u[e.type])){var t=u[e.label]?u[e.label]:u[e.type],i=JSON.parse(JSON.stringify(t));i.options.remoteFunc="func_"+e.key,p(e,s(s({},i),{},{event:r(e),externaldataInfo:Object(o.f)()}),JSON.parse(JSON.stringify(e))),"checkbox"!==e.type||Array.isArray(e.options.defaultValue)||(e.options.defaultValue=[]),e.isTool&&e.fields&&e.fields.length>0&&e.fields.forEach((function(e){p(e,{externaldataInfo:Object(o.f)()},JSON.parse(JSON.stringify(e)))}))}}var f=function(e){return"[object Object]"===Object.prototype.toString.call(e)};function p(){var e=Array.from(arguments);if(e.length<2)return e[0];var t=e[0];return e.shift(),e.forEach((function(e){if(f(e))for(var i in f(t)||(t={}),e)t[i]&&f(e[i])?t[i]=p(t[i],e[i]):t[i]=e[i];else e instanceof Array&&(t instanceof Array||(t=[]),e.forEach((function(e,i){f(e)?t[i]=p(t[i]):t[i]=e})))})),t}},bf73:function(e,t,i){"use strict";var n=i("0353");i("e99b")({target:"RegExp",proto:!0,forced:n!==/./.exec},{exec:n})},bff0:function(e,t,i){"use strict";var n,o=i("3ce5"),a=i("495d"),r=i("adf4"),l=i("f1af"),s={mixins:[a.a],props:{visible:{type:Boolean,default:function(){return!1}}},data:function(){return{dialogVisible:this.visible,form:{reason:""},loading:!1}},methods:{confirm:function(){var e=this;this.$refs.form.validate().then((function(){e.loading=!0,e.$emit("confirm",e.form.reason)}))}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){e||(this.loading=!1,this.form.reason="",this.$refs.form&&this.$refs.form.clearValidate()),this.$emit("update:visible",e)}}},u=i("cba8"),c=Object(u.a)(s,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:"不同意撤回",visible:e.dialogVisible,"append-to-body":!0,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{ref:"form",attrs:{model:e.form}},[i("el-form-item",{attrs:{label:"不同意原因",prop:"reason",rules:[{required:!0,trigger:"blur",message:"不同意原因必填"}],"label-width":"100px"}},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"textarea",rows:5},model:{value:e.form.reason,callback:function(t){e.$set(e.form,"reason",t)},expression:"form.reason"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),i("el-button",{attrs:{loading:e.loading,type:"primary"},on:{click:e.confirm}},[e._v("确 定")])],1)],1)}),[],!1,null,null,null).exports,d=(n={mixins:[a.a],props:{visible:{type:Boolean,default:function(){return!1}},openParams:{type:Object,default:function(){return{}}}},inject:{eventBus:{from:"eventBus",default:function(){}},gfItemBus:{from:"gfItemBus",default:function(){}},widget:{from:"widget",default:function(){}},field:{from:"field",default:function(){return!1}},toolModel:{from:"toolModel",default:function(){}},groupsWidget:{from:"groupsWidget",default:function(){return null}},groupsIndex:{from:"groupsIndex",default:function(){return null}}},components:{NotAgreeReasonDialog:c}},Object(o.a)(n,"mixins",[l.a]),Object(o.a)(n,"data",(function(){return{NotAgreeReasonDialogVisible:!1,types:{Doubt:{value:0,label:"质疑",type:"danger"},Reply:{value:1,label:"回复",type:"info"},BackApply:{value:2,label:"撤回申请",type:"warning"},Agree:{value:3,label:"同意撤回",type:"success"},NotAgree:{value:4,label:"不同意撤回",type:"danger"},FieldModify:{value:5,label:"字段修改",type:"info"}},typesAudit:[0,3,4],typesReply:[1,2,5],AuditRoleEnum:r.a,dialogVisible:this.visible,message:"",replyTypes:[{label:"录入错误(需重新录入字段)",replyType:1},{label:"原始资料错误(不需要修改字段)",replyType:0}],doubtTypes:[{label:"录入错误"},{label:"其他错误"}],replyTypeIndex:0,doubtTypeIndex:0}})),Object(o.a)(n,"created",(function(){this.getRecord()})),Object(o.a)(n,"computed",{auditReadOnly:function(){return this.eventBus.isAuditMode&&this.eventBus.isAuditMode.readonly},containerId:function(){return this.openParams.containerId},onClose:function(){return this.openParams.onClose},toBackApply:function(){return this.openParams.toBackApply},lastMessage:function(){return this.record&&this.record.length>0?this.record[this.record.length-1]:null},isLastAgreeAndIsFiller:function(){return this.lastMessage&&!this.isAuditor&&this.lastMessage.type==this.types.Agree.value},isLastReplied:function(){return this.lastMessage&&!this.isAuditor&&1==this.lastMessage.type&&(1==this.lastMessage.replyType||3==this.lastMessage.replyType)},isLastApply:function(){return this.lastMessage&&this.isAuditor&&2==this.lastMessage.type},canSendMessage:function(){return!this.isLocked&&((!this.lastMessage||this.lastMessage.type!=this.types.BackApply.value)&&((!this.lastMessage||this.lastMessage.type!=this.types.Agree.value)&&(!(this.lastMessage&&!this.isAuditor&&this.lastMessage.type==this.types.Reply.value)&&((!this.lastMessage||!this.isAuditor||this.lastMessage.type!=this.types.Doubt.value)&&(!(this.lastMessage&&!this.isAuditor&&this.lastMessage.type==this.types.FieldModify.value)&&(!(this.lastMessage&&!this.isAuditor&&this.lastMessage.type==this.types.NotAgree.value)&&!this.loadingRecord))))))}}),Object(o.a)(n,"methods",{getFirstChar:function(e){return e&&"string"==typeof e?e[0].toUpperCase():""},getFullContent:function(e){return e.type==this.types.BackApply.value?"申请撤回提交，申请原因："+e.content:e.type==this.types.NotAgree.value?"撤回申请不通过，原因："+e.content:e.content},getTag:function(e){for(var t in this.types)if(Object.hasOwnProperty.call(this.types,t)){var i=this.types[t];if(i.value==e.type)return i}return null},modify:function(){var e=this;this.dialogVisible=!1,this.onClose&&this.onClose(!0),this.$set(this.eventBus.editing,this.fieldKey,!0),this.$nextTick((function(){e.eventBus.scroll2Widget(e.fieldKey)}))},notModify:function(){this.$set(this.eventBus.editing,this.fieldKey,!1);var e={fieldKey:this.fieldKey,fieldName:this.fieldName,roleType:this.roleType,fieldValue:this.fieldValue,content:"提交数据："+this.fieldValue};this.onClose&&this.onClose(!0),this.eventBus.$emit("save-field",this.fieldKey,e)},agree:function(){var e={type:3,content:this.message||"同意撤回申请"};this.sendMsg(e)},notAgreeConfirm:function(e){var t=this,i={type:4,content:e};this.sendMsg(i,(function(){t.NotAgreeReasonDialogVisible=!1}))},notAgree:function(){this.NotAgreeReasonDialogVisible=!0},send:function(){if(this.isAuditor){var e={content:"(".concat(this.doubtTypes[this.doubtTypeIndex].label,") ").concat(this.message)};this.sendMsg(e)}else{var t={replyType:this.replyTypes[this.replyTypeIndex].replyType,content:"(".concat(this.replyTypes[this.replyTypeIndex].label,") ").concat(this.message)};this.toBackApply&&(t.type=2),this.sendMsg(t)}}}),Object(o.a)(n,"watch",{dialogVisible:function(e){e?this.containerId&&document.getElementById(this.containerId).appendChild(this.$el):(this.$emit("close"),this.onClose&&this.onClose())},visible:function(e){this.dialogVisible=e}}),Object(o.a)(n,"destroyed",(function(){this.containerId&&this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)})),n),f=(i("636a"),Object(u.a)(d,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.dialogVisible?i("div",{staticClass:"chat-container"},[i("div",{staticClass:"field-name-container"},[i("span",{staticClass:"field-name"},[e._v("字段名称："+e._s(e.fieldName)+"("+e._s(e.fieldKey)+")")])]),i("div",{ref:"ChatRecordList",staticClass:"chat-record-list"},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingRecord,expression:"loadingRecord"}]},[e.record&&e.record.length>0?e._l(e.record,(function(t,n){return i("div",{key:n,staticClass:"chat-record-item left",class:e.typesAudit.indexOf(t.type)>=0?"chat-audit":"chat-replied"},[i("div",{staticClass:"username-circle"},[e._v("\n                        "+e._s(t.opNickName?t.opNickName.slice(0,1).toUpperCase():"")+"\n                    ")]),i("div",{staticClass:"record-item"},[i("div",{staticClass:"record-item-desc"},[i("span",[e._v(e._s(t.opNickName))]),i("span",{staticClass:"op-user-name"},[e._v(e._s(t.opUserName))]),i("span",{staticClass:"op-date"},[e._v(e._s(t.opDate))]),e.getTag(t)?i("el-tag",{attrs:{type:e.getTag(t).type,size:"mini"}},[e._v(e._s(e.getTag(t).label))]):e._e()],1),i("div",{staticClass:"record-item-content"},[e.getFullContent(t)?[e.getFullContent(t).length<200?i("span",[e._v(e._s(e.getFullContent(t)))]):[i("el-popover",{attrs:{placement:"top",width:"400",trigger:"click",content:e.getFullContent(t)}},[i("span",{attrs:{slot:"reference"},slot:"reference"},[e._v("...（点击查看数据详情）")])])]]:i("span",[e._v("-")])],2),e.auditReadOnly||!e.isLastReplied&&!e.isLastAgreeAndIsFiller||n!=e.record.length-1?e._e():i("div",{staticClass:"op-btn-filler"},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:e.modify}},[e._v("修改答案\n                            ")]),i("el-button",{attrs:{type:"default",size:"mini",icon:"el-icon-circle-close"},on:{click:e.notModify}},[e._v("\n                                不修改")])],1),!e.auditReadOnly&&e.isLastApply&&n==e.record.length-1?i("div",{staticClass:"op-btn-filler"},[i("el-button",{attrs:{type:"primary",loading:e.loadingBtn,size:"mini",icon:"el-icon-check"},on:{click:e.agree}},[e._v("同意\n                            ")]),i("el-button",{attrs:{type:"danger",loading:e.loadingBtn,size:"mini",icon:"el-icon-close"},on:{click:e.notAgree}},[e._v("\n                                不同意")])],1):e._e()])])})):i("div",{staticClass:"record-empty"},[e._v("\n                暂无更多记录\n            ")])],2)]),e.canSendMessage&&!e.auditReadOnly?[e.roleType==e.AuditRoleEnum.Auditor?i("div",{staticClass:"chat-input-container"},[i("el-select",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{size:"mini",placeholder:""},model:{value:e.doubtTypeIndex,callback:function(t){e.doubtTypeIndex=t},expression:"doubtTypeIndex"}},e._l(e.doubtTypes,(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:t}})})),1),i("div",{staticClass:"chat-input"},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"textarea",rows:3,placeholder:"请输入消息..."},model:{value:e.message,callback:function(t){e.message=t},expression:"message"}}),e.isLastApply?e._e():i("el-button",{attrs:{size:"small",type:"primary",loading:e.loadingBtn},on:{click:e.send}},[e._v("确定")])],1)],1):i("div",{staticClass:"chat-input-container"},[e.toBackApply?e._e():i("el-select",{staticStyle:{width:"250px","margin-left":"10px"},attrs:{size:"mini",placeholder:""},model:{value:e.replyTypeIndex,callback:function(t){e.replyTypeIndex=t},expression:"replyTypeIndex"}},e._l(e.replyTypes,(function(e,t){return i("el-option",{key:t,attrs:{label:e.label,value:t}})})),1),i("div",{staticClass:"chat-input"},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"textarea",rows:2,placeholder:"请输入消息..."},model:{value:e.message,callback:function(t){e.message=t},expression:"message"}}),e.isLastApply?e._e():i("el-button",{attrs:{size:"small",type:"primary",loading:e.loadingBtn},on:{click:e.send}},[e._v("提交")])],1)],1)]:e._e(),i("not-agree-reason-dialog",{attrs:{visible:e.NotAgreeReasonDialogVisible},on:{"update:visible":function(t){e.NotAgreeReasonDialogVisible=t},confirm:e.notAgreeConfirm}})],2):e._e()}),[],!1,null,"2c535e5b",null).exports),p={props:{visible:{type:Boolean,default:function(){return!1}},openParams:{type:Object,default:function(){return{}}}},inject:{eventBus:{from:"eventBus",default:function(){return{}}}},components:{AuditContent:f},data:function(){return{dialogVisible:!1}},created:function(){var e=this;this.$nextTick((function(){e.dialogVisible=!0}))},computed:{containerId:function(){return this.openParams.containerId},onClose:function(){return this.openParams.onClose},containerSize:function(){return this.eventBus.containerSize}},methods:{},watch:{dialogVisible:function(e){this.$emit("update:visible",e)}}},m=Object(u.a)(p,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.containerId?i("audit-content",{attrs:{"open-params":e.openParams,visible:e.dialogVisible},on:{close:function(t){e.dialogVisible=!1}}}):i("el-drawer",{staticStyle:{"min-width":"350px"},attrs:{size:"xs"===e.containerSize?"100%":"30%",title:"质疑记录",visible:e.dialogVisible,direction:"rtl",appendToBody:!0},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("audit-content",{attrs:{"open-params":e.openParams,visible:e.dialogVisible},on:{close:function(t){e.dialogVisible=!1}}})],1)}),[],!1,null,null,null);t.a=m.exports},c43e:function(e,t,i){"use strict";i.r(t);var n=i("21c6"),o={mixins:[Object(n.a)()],type:"t_bmi",data:function(){return{}},computed:{containerSize:function(){return this.eventBus.containerSize},heightField:function(){return this.widget.fields[0]},weightField:function(){return this.widget.fields[1]},BMIField:function(){return this.widget.fields[2]}},methods:{calculateBMI:function(){var e=this;this.$nextTick((function(){var t=e.toolModel[e.heightField.key],i=e.toolModel[e.weightField.key];if(t&&i){"cm"==e.heightField.unit&&(t/=100),"g"==e.weightField.unit&&(i/=100);var n=i/Math.pow(t,2);e.toolModel[e.BMIField.key]=e.roundFun(n,e.widget.fields[2].decimalPoint)}}))},roundFun:function(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"gf-tool-bmi"},[e.widget.options.show_BMI_name&&e.widget.name?i("span",[e._v(e._s(e.widget.name))]):e._e(),i("el-row",{staticStyle:{"margin-left":"0","margin-right":"0"},attrs:{gutter:10}},[i("el-col",{attrs:{span:"xs"==e.containerSize||"sm"==e.containerSize?24:8}},[i("tool-field",{attrs:{field:e.heightField,label:e.heightField.name+"("+e.heightField.unit+")"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"number",placeholder:"",disabled:n},on:{change:e.calculateBMI},model:{value:e.toolModel[e.heightField.key],callback:function(t){e.$set(e.toolModel,e.heightField.key,t)},expression:"toolModel[heightField.key]"}})]}}])})],1),i("el-col",{attrs:{span:"xs"==e.containerSize||"sm"==e.containerSize?24:8}},[i("tool-field",{attrs:{field:e.weightField,label:e.weightField.name+"("+e.weightField.unit+")"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"number",placeholder:"",disabled:n},on:{change:e.calculateBMI},model:{value:e.toolModel[e.weightField.key],callback:function(t){e.$set(e.toolModel,e.weightField.key,t)},expression:"toolModel[weightField.key]"}})]}}])})],1),i("el-col",{attrs:{span:"xs"==e.containerSize||"sm"==e.containerSize?24:8}},[i("tool-field",{attrs:{field:e.BMIField,label:e.BMIField.name+"(kg/m²)"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{placeholder:"",type:"number",disabled:n},model:{value:e.toolModel[e.BMIField.key],callback:function(t){e.$set(e.toolModel,e.BMIField.key,t)},expression:"toolModel[BMIField.key]"}})]}}])})],1)],1)],1)}),[],!1,null,null,null);t.default=r.exports},c46f:function(e,t,i){"use strict";i("bf73");var n=i("84e8"),o=i("065d"),a=i("0926"),r=i("3ab0"),l=i("839a"),s=i("0353"),u=l("species"),c=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),d=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var i="ab".split(e);return 2===i.length&&"a"===i[0]&&"b"===i[1]}();e.exports=function(e,t,i){var f=l(e),p=!a((function(){var t={};return t[f]=function(){return 7},7!=""[e](t)})),m=p?!a((function(){var t=!1,i=/a/;return i.exec=function(){return t=!0,null},"split"===e&&(i.constructor={},i.constructor[u]=function(){return i}),i[f](""),!t})):void 0;if(!p||!m||"replace"===e&&!c||"split"===e&&!d){var h=/./[f],g=i(r,f,""[e],(function(e,t,i,n,o){return t.exec===s?p&&!o?{done:!0,value:h.call(t,i,n)}:{done:!0,value:e.call(i,t,n)}:{done:!1}})),_=g[0],b=g[1];n(String.prototype,e,_),o(RegExp.prototype,f,2==t?function(e,t){return b.call(e,this,t)}:function(e){return b.call(e,this)})}}},c5cb:function(e,t,i){"use strict";var n=i("98de"),o=i("0b28");e.exports=i("0bca")("Set",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return n.def(o(this,"Set"),e=0===e?0:e,e)}},n)},cba8:function(e,t,i){"use strict";function n(e,t,i,n,o,a,r,l){var s,u="function"==typeof e?e.options:e;if(t&&(u.render=t,u.staticRenderFns=i,u._compiled=!0),n&&(u.functional=!0),a&&(u._scopeId="data-v-"+a),r?(s=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(r)},u._ssrRegister=s):o&&(s=l?function(){o.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:o),s)if(u.functional){u._injectStyles=s;var c=u.render;u.render=function(e,t){return s.call(t),c(e,t)}}else{var d=u.beforeCreate;u.beforeCreate=d?[].concat(d,s):[s]}return{exports:e,options:u}}i.d(t,"a",(function(){return n}))},cea2:function(e,t){var i={}.toString;e.exports=function(e){return i.call(e).slice(8,-1)}},d0f2:function(e,t,i){"use strict";var n=i("a86f"),o=i("201c"),a=i("43ec"),r=i("f417");i("c46f")("match",1,(function(e,t,i,l){return[function(i){var n=e(this),o=null==i?void 0:i[t];return void 0!==o?o.call(i,n):new RegExp(i)[t](String(n))},function(e){var t=l(i,e,this);if(t.done)return t.value;var s=n(e),u=String(this);if(!s.global)return r(s,u);var c=s.unicode;s.lastIndex=0;for(var d,f=[],p=0;null!==(d=r(s,u));){var m=String(d[0]);f[p]=m,""===m&&(s.lastIndex=a(u,o(s.lastIndex),c)),p++}return 0===p?null:f}]}))},d1cb:function(e,t,i){var n=i("cea2");e.exports=Array.isArray||function(e){return"Array"==n(e)}},d3b2:function(e,t,i){"use strict";i.r(t),i.d(t,"getIsContainer",(function(){return l})),i.d(t,"_modifyWidgetListOptions",(function(){return s})),i.d(t,"show",(function(){return u})),i.d(t,"hide",(function(){return c})),i.d(t,"abled",(function(){return d})),i.d(t,"disabled",(function(){return f})),i.d(t,"_assignSelfOrTarget",(function(){return p})),i.d(t,"roundFun",(function(){return m})),i.d(t,"BMI",(function(){return h})),i.d(t,"DMY_avg",(function(){return g})),i.d(t,"YYXLQD",(function(){return _})),i.d(t,"ABI",(function(){return b})),i.d(t,"XKZS",(function(){return y})),i.d(t,"XSCL",(function(){return v})),i.d(t,"RFI",(function(){return w})),i.d(t,"Ccr",(function(){return O})),i.d(t,"eGFR",(function(){return k})),i.d(t,"CRTBMJ",(function(){return x})),i.d(t,"DAS28_ESR",(function(){return M})),i.d(t,"CDAI",(function(){return E})),i.d(t,"_commonCalc",(function(){return j})),i.d(t,"add",(function(){return D})),i.d(t,"subtract",(function(){return I})),i.d(t,"multiply",(function(){return C})),i.d(t,"divide",(function(){return S})),i.d(t,"hybridCompute",(function(){return B})),i.d(t,"max",(function(){return P})),i.d(t,"min",(function(){return A})),i.d(t,"_assign",(function(){return T})),i.d(t,"assign",(function(){return F})),i.d(t,"_parseComparedRules",(function(){return $})),i.d(t,"_compared",(function(){return L})),i.d(t,"rangeAssign",(function(){return R})),i.d(t,"date_interval",(function(){return N})),i.d(t,"date_after_day",(function(){return V})),i.d(t,"date_before_day",(function(){return z})),i.d(t,"value2date",(function(){return U})),i.d(t,"dateFormate",(function(){return W})),i.d(t,"add0",(function(){return K}));i("9f60"),i("94f0"),i("0c84"),i("2843"),i("a450"),i("4057"),i("d0f2");var n=i("f8b5"),o=i("6c36");i("1bc7"),i("fc02");function a(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return r(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return r(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,l=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return l=e.done,e},e:function(e){s=!0,a=e},f:function(){try{l||null==i.return||i.return()}finally{if(s)throw a}}}}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function l(e){var t=e.split("_"),i=!1;return"c"==t[t.length-1]&&(i=!0,t.pop(),e=t.join("_")),{key:e,isContainer:i}}function s(e,t,i,n){var o,r=a(e);try{for(r.s();!(o=r.n()).done;){var s=o.value,u=l(s[s.length-1]).key;t[u]&&t[u].options&&(t[u].options[i]=n)}}catch(e){r.e(e)}finally{r.f()}}function u(e,t,i){s(e.widget_list,this.dataOptions,"show",!0)}function c(e,t,i){s(e.widget_list,this.dataOptions,"show",!1)}function d(e,t,i){s(e.widget_list,this.dataOptions,"disabled",!1)}function f(e,t,i){s(e.widget_list,this.dataOptions,"disabled",!0)}function p(e,t,i){e.target?T(e,t.all$vm[e.target],t.all$vm[e.target].widget,i):T(e,t,t.widget,i)}function m(e,t){var i=+e;return"number"!=typeof i?e:Math.round(i*Math.pow(10,t))/Math.pow(10,t)}function h(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,o/Math.pow(n,2))}function g(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,(n+2*o)/3)}function _(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,.4*(.122*o+72.638*o-117.109))}function b(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,n/o)}function y(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,n/o)}function v(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,n*o)}function w(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value],a=this.formModel[e.value_arr[2].value];n&&o&&a&&p(e,i,n*o/a)}function O(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value],a=this.formModel[e.value_arr[2].value],r=this.formModel[e.value_arr[3].value];if(n&&o&&a&&r)if(1==n)p(e,i,(140-o)*a/72*r);else if(2==n){p(e,i,(140-o)*a*.85/72*r)}}function k(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value],a=this.formModel[e.value_arr[2].value];if(n&&o&&a)if(1==n)p(e,i,186*Math.pow(o,-1.154)*Math.pow(a,-.203));else if(2==n){p(e,i,186*Math.pow(o,-1.154)*Math.pow(a,-.203)*.742)}}function x(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value];n&&o&&p(e,i,.61*n+.0128*o-.1529)}function M(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value],a=this.formModel[e.value_arr[2].value],r=this.formModel[e.value_arr[3].value];n&&o&&a&&r&&p(e,i,.56*Math.pow(n,.5)+.28*Math.pow(o,.5)+Math.log(a)+.014*r)}function E(e,t,i){var n=this.formModel[e.value_arr[0].value],o=this.formModel[e.value_arr[1].value],a=this.formModel[e.value_arr[2].value],r=this.formModel[e.value_arr[3].value];n&&o&&a&&r&&p(e,i,n+o+a+r)}function j(e,t){var i=e.options,n=e.widget,a=e.$vm,r=e.formModel,l=0;!i.value_dynamic_arr||i.value_dynamic_arr.length<=0||(i.value_dynamic_arr.forEach((function(e,i){if(""!==e.value){var n=0;if("0"===e.valueType)n=+e.value,l=0==i?n:t(n,l,e,i);else if("1"===e.valueType)n=+r[e.value],"object"==Object(o.a)(n)&&void 0!==n.value&&(n=+n.value),l=0==i?n:t(n,l,e,i);else if("2"===e.valueType){var s=a.eventBus.outData[e.value];(s||0===s)&&(n=s,l=0==i?n:t(n,l,e,i))}}})),i.target?T(i,a.all$vm[i.target],a.all$vm[i.target].widget,l):T(i,a,n,l))}function D(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return t+e}))}function I(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return t-e}))}function C(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return t*e}))}function S(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return e?t/e:""}))}function B(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return"add"==i.operator?t+e:"subtract"==i.operator?t-e:"multiply"==i.operator?t*e:"divide"==i.operator&&0!=e?t/e:void 0}))}function P(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return Math.max(t,e)}))}function A(e,t,i){j({options:e,widget:t,$vm:i,formModel:this.formModel},(function(e,t,i,n){return Math.min(t,e)}))}function T(e,t,i,n){if(n||0===n||"inputnumber"!=i.type){var o=e.decimalPoint?m(n,e.decimalPoint):n;if("input"==i.type||"radio"==i.type)t.dataModel!==o+""&&(t.dataModel=o+"",t.emitEvent&&t.emitEvent("event","change",i));else if("inputnumber"==i.type){if(isNaN(+o))return;t.dataModel!==+o&&(t.dataModel=+o,t.emitEvent&&t.emitEvent("event","change",i))}else t.dataModel!==o&&(t.dataModel=o,t.emitEvent&&t.emitEvent("event","change",i))}}function F(e,t,i){var n=e.assignConfig,o=e.target,a=n.value,r=n.valueType,l=i.all$vm[o];"0"===r?T(e,l,l.widget,a):"1"===r&&a&&T(e,l,l.widget,i.all$vm[a].dataModel)}function $(e){var t=[];return e.trim()?(e.trim().split(/[\n]/).forEach((function(e){var i=e.trim().split("="),o=Object(n.a)(i,2),a=o[0],r=o[1],l=a.trim(),s=+r.trim(),u=l.match(/([\(\[])\s*(\+?\-?\d+\.?\d?|~)\s*,(\+?\-?\d+\.?\d?|~)\s*([\)\]])/);if(u){var c=Object(n.a)(u,5),d=(c[0],c[1]),f=c[2],p=c[3],m=c[4];t.push({left_type:d,left_value:"~"==f?"~":+f,right_value:"~"==p?"~":+p,right_type:m,value:+s})}})),t):t}function L(e,t){for(var i=0;i<e.length;i++){var n=e[i],o=n.left_type,a=n.left_value,r=n.right_value,l=n.right_type,s=n.value,u=!1;if(u="~"==r||(")"==l&&t<r||"]"==l&&t<=r),("~"==a||("("==o&&t>a||"["==o&&t>=a))&&u)return s}}function R(e,t,i){var n=e.comparedWidget,o=e.comparedRules,a=e.assignTarget;if(o&&a){var r=n?i.all$vm[n].dataModel:i.dataModel;if(""!==r){var l=i.all$vm[a],s=L($(o),+r);s&&T(e,l,l.widget,s)}}}function N(e,t,i){var n=e.value_arr;if(n[0].value&&n[1].value){var o=i.all$vm[n[0].value],a=i.all$vm[n[1].value];if(o.dataModel&&a.dataModel){var r=U(a.dataModel)-U(o.dataModel);""==n[2].value||"天"==n[2].value?e.target?T(e,i.all$vm[e.target],i.all$vm[e.target].widget,parseInt(r/864e5)):T(e,i,t,parseInt(r/864e5)):"小时"==n[2].value&&(e.target?T(e,i.all$vm[e.target],i.all$vm[e.target].widget,parseInt(r/36e5)):T(e,i,t,parseInt(r/36e5)))}}}function V(e,t,i){var n=e.value_arr;if(n[0].value&&n[1].value){var o=i.all$vm[n[0].value],a=n[1].value;if(o.dataModel&&a){var r=W(new Date(U(o.dataModel).getMilliseconds()+24*a*3600*1e3),t);e.target?i.all$vm[e.target].dataModel=r:i.dataModel=r}}}function z(e,t,i){var n=e.value_arr;if(n[0].value&&n[1].value){var o=i.all$vm[n[0].value],a=n[1].value;if(o.dataModel&&a){var r=W(new Date(U(o.dataModel).getMilliseconds()-24*a*3600*1e3),t);e.target?i.all$vm[e.target].dataModel=r:i.dataModel=r}}}function U(e){if(!e)return null;if("number"==typeof e)return new Date(e);if("string"==typeof e){var t=e.split(" "),i=Object(n.a)(t,2),o=i[0],a=i[1],r=o.split(/[\/-]/),l=Object(n.a)(r,3),s=l[0],u=l[1],c=l[2];u=+u-1;var d=(a=a||"00:00:00").split(":"),f=Object(n.a)(d,3),p=f[0],m=f[1],h=f[2];return new Date(+s,+u,+c,+p,+m,+h)}return e}function W(e,t){if(!e)return null;var i=K(e.getFullYear()),n=K(e.getMonth()+1),o=K(e.getDay()),a=K(e.getHours()),r=K(e.getMinutes()),l=K(e.getSeconds());if("date"!=t.type)return"";if(t.options.timestamp)return e.getMilliseconds();var s=t.options.format;return"yyyy-MM-dd"==s?"".concat(i,"-").concat(n,"-").concat(o):"yyyy/MM/dd"==s?"".concat(i,"/").concat(n,"/").concat(o):"yyyy-MM-dd hh:mm:ss"==s?"".concat(i,"-").concat(n,"-").concat(o," ").concat(a,":").concat(r,":").concat(l):"yyyy/MM/dd hh:mm:ss"==s?"".concat(i,"/").concat(n,"/").concat(o," ").concat(a,":").concat(r,":").concat(l):void 0}function K(e){return e<10&&"number"==typeof e?"0"+e:""+e}},d419:function(e,t,i){"use strict";var n=i("7286"),o={props:{value:{},field:{}},data:function(){return{dataModel:Object(n.b)(this.value,this.field)||[]}},watch:{value:{deep:!0,handler:function(e){this.dataModel=Object(n.b)(e,this.field)}},dataModel:function(e,t){JSON.stringify(e)!=JSON.stringify(t)&&this.$emit("input",Object(n.a)(e,this.field))}}},a=i("cba8"),r=Object(a.a)(o,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-select",e._b({staticClass:"option-select-multi",attrs:{multiple:!0},model:{value:e.dataModel,callback:function(t){e.dataModel=t},expression:"dataModel"}},"el-select",e.$attrs,!1),e._l(e.field.options,(function(e,t){return i("el-option",{key:t,attrs:{value:e.value,label:e.label}})})),1)}),[],!1,null,null,null);t.a=r.exports},d445:function(e,t,i){var n=i("cea2"),o=i("839a")("toStringTag"),a="Arguments"==n(function(){return arguments}());e.exports=function(e){var t,i,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(i=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),o))?i:a?n(t):"Object"==(r=n(t))&&"function"==typeof t.callee?"Arguments":r}},d4c9:function(e,t,i){"use strict";var n=i("3250");function o(e){var t,i;this.promise=new e((function(e,n){if(void 0!==t||void 0!==i)throw TypeError("Bad Promise constructor");t=e,i=n})),this.resolve=n(t),this.reject=n(i)}e.exports.f=function(e){return new o(e)}},d8b3:function(e,t){var i=0,n=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++i+n).toString(36))}},db34:function(e,t,i){var n=i("804d"),o=i("3ab0");e.exports=function(e,t,i){if(n(t))throw TypeError("String#"+i+" doesn't accept regex!");return String(o(e))}},dcea:function(e,t,i){var n=i("953d"),o=i("839a")("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||a[o]===e)}},de49:function(e,t,i){i("26df")&&"g"!=/./g.flags&&i("bb8b").f(RegExp.prototype,"flags",{configurable:!0,get:i("6bf8")})},e0e3:function(e,t,i){"use strict";i.r(t);var n=i("063b"),o=i("21c6"),a=[{label:"汉族",value:"HA",pinyin:"hz"},{label:"蒙古族",value:"MG",pinyin:"mgz"},{label:"回族",value:"HU",pinyin:"hz"},{label:"藏族",value:"ZA",pinyin:"zz"},{label:"维吾尔族",value:"UG",pinyin:"wwez"},{label:"苗族",value:"MH",pinyin:"mz"},{label:"彝族",value:"YI",pinyin:"yz"},{label:"壮族",value:"ZH",pinyin:"zz"},{label:"布依族",value:"BY",pinyin:"by"},{label:"朝鲜族",value:"CS",pinyin:"cx"},{label:"满族",value:"MA",pinyin:"mz"},{label:"侗族",value:"DO",pinyin:"dz"},{label:"瑶族",value:"YA",pinyin:"yz"},{label:"白族",value:"BA",pinyin:"bz"},{label:"土家族",value:"TJ",pinyin:"tjz"},{label:"哈尼族",value:"HN",pinyin:"hnz"},{label:"哈萨克族",value:"KZ",pinyin:"hskz"},{label:"傣族",value:"DA",pinyin:"dz"},{label:"黎族",value:"LI",pinyin:"lz"},{label:"傈僳族",value:"LS",pinyin:"llz"},{label:"佤族",value:"VA",pinyin:"wz"},{label:"畲族",value:"SH",pinyin:"sz"},{label:"高山族",value:"GS",pinyin:"gsz"},{label:"拉祜族",value:"LH",pinyin:"lhz"},{label:"水族",value:"SU",pinyin:"sz"},{label:"东乡族",value:"DX",pinyin:"dxz"},{label:"纳西族",value:"NX",pinyin:"nxz"},{label:"景颇族",value:"JP",pinyin:"jpz"},{label:"柯尔克孜族",value:"KG",pinyin:"kekzz"},{label:"土族",value:"TU",pinyin:"tz"},{label:"达斡尔族",value:"DU",pinyin:"dwez"},{label:"仫佬族",value:"ML",pinyin:"mlz"},{label:"羌族",value:"QI",pinyin:"qz"},{label:"布朗族",value:"BL",pinyin:"blz"},{label:"撒拉族",value:"SL",pinyin:"slz"},{label:"毛南族",value:"MN",pinyin:"mnz"},{label:"仡佬族",value:"GL",pinyin:"qlz"},{label:"锡伯族",value:"XB",pinyin:"xbz"},{label:"阿昌族",value:"AC",pinyin:"acz"},{label:"普米族",value:"PM",pinyin:"pmz"},{label:"塔吉克族",value:"TA",pinyin:"tjkz"},{label:"怒族",value:"NU",pinyin:"nz"},{label:"乌孜别克族",value:"UZ",pinyin:"wzbkz"},{label:"俄罗斯族",value:"RS",pinyin:"elsz"},{label:"鄂温克族",value:"EW",pinyin:"ewkz"},{label:"德昂族",value:"DE",pinyin:"daz"},{label:"保安族",value:"BN",pinyin:"baz"},{label:"裕固族",value:"YG",pinyin:"ygz"},{label:"京族",value:"GI",pinyin:"jz"},{label:"塔塔尔族",value:"TT",pinyin:"ttez"},{label:"独龙族",value:"DR",pinyin:"dlz"},{label:"鄂伦春族",value:"OR",pinyin:"elcz"},{label:"赫哲族",value:"HZ",pinyin:"hzz"},{label:"门巴族",value:"MB",pinyin:"mbz"},{label:"珞巴族",value:"LB",pinyin:"lbz"},{label:"基诺族",value:"JN",pinyin:"jnz"}],r={components:{optionSelect:n.a},mixins:[Object(o.a)()],type:"t_nation",data:function(){return{curField:{type:"select",options:a}}},methods:{filterMethod:function(e){this.curField.options=e?a.filter((function(t){return(t.pinyin+t.label+t.value).toLowerCase().indexOf(e.toLowerCase())>=0})):a}}},l=i("cba8"),s=Object(l.a)(r,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("tool-field",{staticClass:"gf-tool-nation",attrs:{field:e.widget.fields[0]},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.disabled;return[i("option-select",{staticStyle:{width:"100%"},attrs:{clearable:!0,"filter-method":e.filterMethod,filterable:!0,disabled:n,field:e.curField,placeholder:e.widget.options.placeholder},model:{value:e.toolModel[e.widget.fields[0].key],callback:function(t){e.$set(e.toolModel,e.widget.fields[0].key,t)},expression:"toolModel[widget.fields[0].key]"}})]}}])})}),[],!1,null,null,null);t.default=s.exports},e0ff:function(e,t,i){var n=i("9cff"),o=i("a86f"),a=function(e,t){if(o(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=i("1e4d")(Function.call,i("285b").f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,i){return a(e,i),t?e.__proto__=i:n(e,i),e}}({},!1):void 0),check:a}},e186:function(e,t,i){var n=function(e){"use strict";var t=Object.prototype,i=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",r=n.toStringTag||"@@toStringTag";function l(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,i){return e[t]=i}}function s(e,t,i,n){var o=t&&t.prototype instanceof d?t:d,a=Object.create(o.prototype),r=new k(n||[]);return a._invoke=function(e,t,i){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return M()}for(i.method=o,i.arg=a;;){var r=i.delegate;if(r){var l=v(r,i);if(l){if(l===c)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var s=u(e,t,i);if("normal"===s.type){if(n=i.done?"completed":"suspendedYield",s.arg===c)continue;return{value:s.arg,done:i.done}}"throw"===s.type&&(n="completed",i.method="throw",i.arg=s.arg)}}}(e,i,r),a}function u(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var c={};function d(){}function f(){}function p(){}var m={};l(m,o,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(x([])));g&&g!==t&&i.call(g,o)&&(m=g);var _=p.prototype=d.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){var n;this._invoke=function(o,a){function r(){return new t((function(n,r){!function n(o,a,r,l){var s=u(e[o],e,a);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==typeof d&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,r,l)}),(function(e){n("throw",e,r,l)})):t.resolve(d).then((function(e){c.value=e,r(c)}),(function(e){return n("throw",e,r,l)}))}l(s.arg)}(o,a,n,r)}))}return n=n?n.then(r,r):r()}}function v(e,t){var i=e.iterator[t.method];if(void 0===i){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,v(e,t),"throw"===t.method))return c;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return c}var n=u(i,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,c;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,c):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,c)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function x(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:M}}function M(){return{value:void 0,done:!0}}return f.prototype=p,l(_,"constructor",p),l(p,"constructor",f),f.displayName=l(p,r,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,l(e,r,"GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},b(y.prototype),l(y.prototype,a,(function(){return this})),e.AsyncIterator=y,e.async=function(t,i,n,o,a){void 0===a&&(a=Promise);var r=new y(s(t,i,n,o),a);return e.isGeneratorFunction(i)?r:r.next().then((function(e){return e.done?e.value:r.next()}))},b(_),l(_,r,"Generator"),l(_,o,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var i in e)t.push(i);return t.reverse(),function i(){for(;t.length;){var n=t.pop();if(n in e)return i.value=n,i.done=!1,i}return i.done=!0,i}},e.values=x,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(i,n){return r.type="throw",r.arg=e,t.next=i,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],r=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var l=i.call(a,"catchLoc"),s=i.call(a,"finallyLoc");if(l&&s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var r=a?a.completion:{};return r.type=e,r.arg=t,a?(this.method="next",this.next=a.finallyLoc,c):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),c},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),O(i),c}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var o=n.arg;O(i)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:x(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),c}},e}(e.exports);try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},e3bb:function(e,t,i){var n=i("d445"),o=i("839a")("iterator"),a=i("953d");e.exports=i("76e3").getIteratorMethod=function(e){if(null!=e)return e[o]||e["@@iterator"]||a[n(e)]}},e67d:function(e,t){!function(e){var t=e.getElementsByTagName("script");"currentScript"in e||Object.defineProperty(e,"currentScript",{get:function(){try{throw new Error}catch(n){var e,i=(/.*at [^\(]*\((.*):.+:.+\)$/gi.exec(n.stack)||[!1])[1];for(e in t)if(t[e].src==i||"interactive"==t[e].readyState)return t[e];return null}}})}(document)},e680:function(e,t,i){"use strict";var n=i("0b34"),o=i("4fd4"),a=i("cea2"),r=i("a83a"),l=i("5d10"),s=i("0926"),u=i("21d9").f,c=i("285b").f,d=i("bb8b").f,f=i("eb34").trim,p=n.Number,m=p,h=p.prototype,g="Number"==a(i("7ee3")(h)),_="trim"in String.prototype,b=function(e){var t=l(e,!1);if("string"==typeof t&&t.length>2){var i,n,o,a=(t=_?t.trim():f(t,3)).charCodeAt(0);if(43===a||45===a){if(88===(i=t.charCodeAt(2))||120===i)return NaN}else if(48===a){switch(t.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+t}for(var r,s=t.slice(2),u=0,c=s.length;u<c;u++)if((r=s.charCodeAt(u))<48||r>o)return NaN;return parseInt(s,n)}}return+t};if(!p(" 0o1")||!p("0b1")||p("+0x1")){p=function(e){var t=arguments.length<1?0:e,i=this;return i instanceof p&&(g?s((function(){h.valueOf.call(i)})):"Number"!=a(i))?r(new m(b(t)),i,p):b(t)};for(var y,v=i("26df")?u(m):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;v.length>w;w++)o(m,y=v[w])&&!o(p,y)&&d(p,y,c(m,y));p.prototype=h,h.constructor=p,i("84e8")(n,"Number",p)}},e7c8:function(e,t,i){var n=i("21d9"),o=i("0c29"),a=i("a86f"),r=i("0b34").Reflect;e.exports=r&&r.ownKeys||function(e){var t=n.f(a(e)),i=o.f;return i?t.concat(i(e)):t}},e8d7:function(e,t,i){var n=i("9cff"),o=i("0b34").document,a=n(o)&&n(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},e99b:function(e,t,i){var n=i("0b34"),o=i("76e3"),a=i("065d"),r=i("84e8"),l=i("1e4d"),s=function(e,t,i){var u,c,d,f,p=e&s.F,m=e&s.G,h=e&s.S,g=e&s.P,_=e&s.B,b=m?n:h?n[t]||(n[t]={}):(n[t]||{}).prototype,y=m?o:o[t]||(o[t]={}),v=y.prototype||(y.prototype={});for(u in m&&(i=t),i)d=((c=!p&&b&&void 0!==b[u])?b:i)[u],f=_&&c?l(d,n):g&&"function"==typeof d?l(Function.call,d):d,b&&r(b,u,d,e&s.U),y[u]!=d&&a(y,u,f),g&&v[u]!=d&&(v[u]=d)};n.core=o,s.F=1,s.G=2,s.S=4,s.P=8,s.B=16,s.W=32,s.U=64,s.R=128,e.exports=s},e9bb:function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es7_object_get_own_property_descriptors__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("ac67"),core_js_modules_es7_object_get_own_property_descriptors__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es7_object_get_own_property_descriptors__WEBPACK_IMPORTED_MODULE_0__),core_js_modules_es7_symbol_async_iterator__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("9f60"),core_js_modules_es7_symbol_async_iterator__WEBPACK_IMPORTED_MODULE_1___default=__webpack_require__.n(core_js_modules_es7_symbol_async_iterator__WEBPACK_IMPORTED_MODULE_1__),core_js_modules_es6_symbol__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("94f0"),core_js_modules_es6_symbol__WEBPACK_IMPORTED_MODULE_2___default=__webpack_require__.n(core_js_modules_es6_symbol__WEBPACK_IMPORTED_MODULE_2__),core_js_modules_es6_string_iterator__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("0c84"),core_js_modules_es6_string_iterator__WEBPACK_IMPORTED_MODULE_3___default=__webpack_require__.n(core_js_modules_es6_string_iterator__WEBPACK_IMPORTED_MODULE_3__),core_js_modules_es6_array_from__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("2843"),core_js_modules_es6_array_from__WEBPACK_IMPORTED_MODULE_4___default=__webpack_require__.n(core_js_modules_es6_array_from__WEBPACK_IMPORTED_MODULE_4__),core_js_modules_es6_regexp_to_string__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("4057"),core_js_modules_es6_regexp_to_string__WEBPACK_IMPORTED_MODULE_5___default=__webpack_require__.n(core_js_modules_es6_regexp_to_string__WEBPACK_IMPORTED_MODULE_5__),regenerator_runtime_runtime__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("e186"),regenerator_runtime_runtime__WEBPACK_IMPORTED_MODULE_6___default=__webpack_require__.n(regenerator_runtime_runtime__WEBPACK_IMPORTED_MODULE_6__),D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("a7be"),D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("f8b5"),core_js_modules_es6_regexp_constructor__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("3269"),core_js_modules_es6_regexp_constructor__WEBPACK_IMPORTED_MODULE_9___default=__webpack_require__.n(core_js_modules_es6_regexp_constructor__WEBPACK_IMPORTED_MODULE_9__),core_js_modules_es6_object_assign__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("6ba0"),core_js_modules_es6_object_assign__WEBPACK_IMPORTED_MODULE_10___default=__webpack_require__.n(core_js_modules_es6_object_assign__WEBPACK_IMPORTED_MODULE_10__),core_js_modules_es6_array_iterator__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__("25ba"),core_js_modules_es6_array_iterator__WEBPACK_IMPORTED_MODULE_11___default=__webpack_require__.n(core_js_modules_es6_array_iterator__WEBPACK_IMPORTED_MODULE_11__),core_js_modules_es6_object_keys__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__("32ea"),core_js_modules_es6_object_keys__WEBPACK_IMPORTED_MODULE_12___default=__webpack_require__.n(core_js_modules_es6_object_keys__WEBPACK_IMPORTED_MODULE_12__),core_js_modules_es6_promise__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__("5f1c"),core_js_modules_es6_promise__WEBPACK_IMPORTED_MODULE_13___default=__webpack_require__.n(core_js_modules_es6_promise__WEBPACK_IMPORTED_MODULE_13__),core_js_modules_es6_regexp_split__WEBPACK_IMPORTED_MODULE_14__=__webpack_require__("fc02"),core_js_modules_es6_regexp_split__WEBPACK_IMPORTED_MODULE_14___default=__webpack_require__.n(core_js_modules_es6_regexp_split__WEBPACK_IMPORTED_MODULE_14__),D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_15__=__webpack_require__("3ce5"),D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_16__=__webpack_require__("44a4"),core_js_modules_es6_function_name__WEBPACK_IMPORTED_MODULE_17__=__webpack_require__("a450"),core_js_modules_es6_function_name__WEBPACK_IMPORTED_MODULE_17___default=__webpack_require__.n(core_js_modules_es6_function_name__WEBPACK_IMPORTED_MODULE_17__),core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_18__=__webpack_require__("1bc7"),core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_18___default=__webpack_require__.n(core_js_modules_web_dom_iterable__WEBPACK_IMPORTED_MODULE_18__),core_js_modules_es6_number_constructor__WEBPACK_IMPORTED_MODULE_19__=__webpack_require__("e680"),core_js_modules_es6_number_constructor__WEBPACK_IMPORTED_MODULE_19___default=__webpack_require__.n(core_js_modules_es6_number_constructor__WEBPACK_IMPORTED_MODULE_19__),_GenerateFormItem__WEBPACK_IMPORTED_MODULE_20__=__webpack_require__("aa13"),_GenerateFormGrid__WEBPACK_IMPORTED_MODULE_21__=__webpack_require__("9e44"),_DataPreviewDialog__WEBPACK_IMPORTED_MODULE_22__=__webpack_require__("3076"),_common_CusDialog__WEBPACK_IMPORTED_MODULE_23__=__webpack_require__("fed1"),_EventLibs_js__WEBPACK_IMPORTED_MODULE_24__=__webpack_require__("d3b2"),_utils_js__WEBPACK_IMPORTED_MODULE_25__=__webpack_require__("0474"),_audit_audit_utils_js__WEBPACK_IMPORTED_MODULE_26__=__webpack_require__("adf4"),_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__=__webpack_require__("34a9"),_makingForm_widgetConfig_tree_shaking__WEBPACK_IMPORTED_MODULE_28__=__webpack_require__("bdfd"),_components_makingForm_layout_themes__WEBPACK_IMPORTED_MODULE_29__=__webpack_require__("3e4e"),_components_makingForm_scriptManage_demo__WEBPACK_IMPORTED_MODULE_30__=__webpack_require__("20b3"),_gf_meta_field_vue__WEBPACK_IMPORTED_MODULE_31__=__webpack_require__("6c16");function ownKeys(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(i),!0).forEach((function(t){Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_15__.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function _createForOfIteratorHelper(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==i.return||i.return()}finally{if(l)throw a}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var default_data={list:[],config:_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.d},_findDictionaryOptions;__webpack_exports__.a={name:"fm-generate-form",components:{GenerateFormItem:_GenerateFormItem__WEBPACK_IMPORTED_MODULE_20__.a,DataPreviewDialog:_DataPreviewDialog__WEBPACK_IMPORTED_MODULE_22__.a,GenerateFormGrid:_GenerateFormGrid__WEBPACK_IMPORTED_MODULE_21__.a,CusDialog:_common_CusDialog__WEBPACK_IMPORTED_MODULE_23__.a,GfMetaField:_gf_meta_field_vue__WEBPACK_IMPORTED_MODULE_31__.a},props:{"json-data":{type:Object},"get-json-data":{type:Function},"json-data-http-option":{type:Object},"de-settings":{type:Object},"get-de-settings":{type:Function},"de-settings-http-option":{type:Object},"external-data":{type:Object},"get-external-data":{type:Function},"external-data-http-option":{type:Object},value:{type:Object},"get-value":{type:Function},"value-http-option":{type:Object},"form-disabled":{type:Boolean,default:function(){return!1}},"is-add-mode":{type:Boolean,default:function(){return!1}},"get-add-mode-status":{type:Function},"theme-code":{type:String,default:function(){return""}},"upload-img-max-size":{type:Number,default:function(){return 20}},"upload-file-max-size":{type:Number,default:function(){return 20}},"upload-img":{type:Object,default:function(){return{uploadUrl:"",baseUrl:"",token:""}}},"upload-img-data":{type:Object,default:function(){return{}}},"out-data":{type:Object,default:function(){return{}}},"get-out-data":{type:Function},"show-fill-in-rate":{type:Boolean,default:function(){return!1}},"is-audit-mode":{type:Object,default:function(){return null}},"audit-json":{type:[Object,String]},"get-audit-json":{type:Function},"get-audit-field-record":{type:Function},"add-audit-field-record":{type:Function},"lock-field":{type:Function},"unlock-field":{type:Function},"get-dictionary-options":{type:Function},"upload-medical-image-config":{type:Object}},provide:function(){return{all$vm:this.all$vm,eventBus:this.eventBus}},data:function(){return{isAllComponentDestroyed:!1,isAllComponentDestroying:!1,destroyAllComponentsCallBack:null,componentsLoadedMap:null,EventLibs:_EventLibs_js__WEBPACK_IMPORTED_MODULE_24__,containerSize:"lg",loading:!1,remoteLoading:!1,curPageIndex:1,data:default_data,remote:null,metaValue:{},models:{},formModel:{},defaultModels:{},dataOptions:{},rules:{},scope:{model:{}},DESettings:{deCategory:[],deItem:[],deItemProp:[],deSpecialVal:[],deObsvType:[],deObsvField:[]},externaldata:{},all$vm:{},tableDataMap:{},eventBus:this,fillInRate:0,isFormLoaded:!1,modelsCached:{},audit:JSON.parse(JSON.stringify(_audit_audit_utils_js__WEBPACK_IMPORTED_MODULE_26__.b)),editing:{},allRemoteWidget:[],dictionaryOptionsMap:{},disabledList:[]}},computed:{jsonDataKey:function(){return this.$route&&this.$route.query&&this.$route.query.formGuid?"jsonData_"+this.$route.query.formGuid:"jsonData"},allPagination:function(){return this.data.list.filter((function(e){return"pagination"==e.type}))},curPageShowRange:function(){if(0==this.allPagination.length)return null;var e=this.allPagination[this.curPageIndex-1],t=this.data.list.map((function(e){return e.key})).indexOf(e.key),i=-1;if(this.curPageIndex<=1)i=-1;else{var n=this.allPagination[this.curPageIndex-2];i=this.data.list.map((function(e){return e.key})).indexOf(n.key)}return{start:i,end:t}},progressColor:function(){return Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.g)(this.fillInRate)}},mounted:function(){var e=this;this.setContainerSize(),window.addEventListener("resize",this.resizeHandle);var t=window.preview;t&&(this.remote=t.remote),this.jsonData?this.setJSONData(this.jsonData):this.jsonDataHttpOption?this.$http(this.jsonDataHttpOption).then((function(t){t.Result?e.setJSONData(t.DataObject):e.$message({message:"获取外部json元数据失败"+t.Message,type:"error"})})).catch((function(e){})):this.getJsonData?this.getJsonData((function(t){e.setJSONData(t)})):(this.data=this.$ls.get(this.jsonDataKey),this.setThemeStyle(),this.generateModel(this.data.list),this.generateFormModel(),this.$ls.on(this.jsonDataKey,(function(t){e.data=t,e.generateModel(e.data.list)}))),this.deSettings?this.setDESettings(this.deSettings):this.deSettingsHttpOption?this.$http(this.deSettingsHttpOption).then((function(t){t.Result?e.setDESettings(t.DataObject):e.$message({message:"获取接口设置数据失败"+t.Message,type:"error"})})).catch((function(e){})):this.getDeSettings&&this.getDeSettings((function(t){e.setDESettings(t)}))},methods:{setDisabledList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.disabledList=e},getAllRemoteScripts:function(){var e=this.data.config.scripts.list;if(0!==e.length){var t="";e.forEach((function(e){t+=e.content+"\n"}));var i=document.getElementById("gf-script");i&&document.body.removeChild(i);var n=document.createElement("script");n.id="gf-script",n.innerHTML=t,document.body.appendChild(n),setTimeout((function(){e.forEach((function(e){window[e.functionName]&&(_EventLibs_js__WEBPACK_IMPORTED_MODULE_24__[e.functionName]=window[e.functionName])}))}),0)}},resizeHandle:function(){this.setContainerSize()},getThemeClass:function(){var e=this.data.config?this.data.config.theme:null;return e&&e.name?"theme-"+e.name:"theme-default"},setThemeStyle:function(){var e=this.data.config?this.data.config.theme:null;e&&(e.formBackgroundColor&&this.$el.style.setProperty("--gf-form-background",e.formBackgroundColor),e.whBackgroundColor&&this.$el.style.setProperty("--gf-title-background",e.whBackgroundColor),e.inputBorderColor&&this.$el.style.setProperty("--gf-input-border-color",e.inputBorderColor))},generateModel:function(e,t,i,n){var o=this;t||(n||(this.models={}),this.rules={},this.dataOptions={});for(var a=0;a<e.length;a++)if("grid"===e[a].type||"group"===e[a].type)this.dataOptions[e[a].key]=e[a],e[a].columns.forEach((function(e){o.generateModel(e.list,!0,i)}));else if("table"==e[a].type){if(!n){for(var r=[],l=0;l<e[a].options.rows;l++){var s,u={},c=_createForOfIteratorHelper(e[a].tableColumns);try{for(c.s();!(s=c.n()).done;){var d=s.value;u[d.key]=d.options.defaultValue}}catch(e){c.e(e)}finally{c.f()}r.push(u)}this.$set(this.models,e[a].key,r)}this.dataOptions[e[a].key]=e[a]}else if("groups"==e[a].type){if(!n){for(var f=[],p=0;p<e[a].options.groupItemsCount;p++){var m,h={},g=_createForOfIteratorHelper(e[a].groupItems);try{for(g.s();!(m=g.n()).done;){var _=m.value;if(_.isTool){if(_.options&&_.fields&&_.fields.length>0)for(var b=0;b<_.fields.length;b++){var y=_.fields[b];this.dataOptions[y.key]=y,h[y.key]=_.options.defaultValue||""}}else h[_.key]=_.options.defaultValue;this.dataOptions[_.key]=_,this.generateFormRules(this.rules,_)}}catch(e){g.e(e)}finally{g.f()}f.push(h)}this.$set(this.models,e[a].key,f)}this.dataOptions[e[a].key]=e[a]}else if("matrix"==e[a].type){this.dataOptions[e[a].key]=e[a];var v,w=_createForOfIteratorHelper(e[a].matrixData);try{for(w.s();!(v=w.n()).done;){var O,k=_createForOfIteratorHelper(v.value);try{for(k.s();!(O=k.n()).done;){var x=O.value;this.generateModel(x,!0,i)}}catch(e){k.e(e)}finally{k.f()}}}catch(e){w.e(e)}finally{w.f()}}else{if(!n)if(i)i[e[a].key]=e[a].options.defaultValue;else if(e[a].isTool){if(e[a].options&&e[a].fields&&e[a].fields.length>0)for(var M=0;M<e[a].fields.length;M++){var E=e[a].fields[M];this.$set(this.models,E.key,E.defaultValue||"")}}else this.$set(this.models,e[a].key,e[a].options.defaultValue);if(e[a].isTool){if(this.dataOptions[e[a].key]=e[a],e[a].options&&e[a].fields&&e[a].fields.length>0)for(var j=0;j<e[a].fields.length;j++){var D=e[a].fields[j];this.dataOptions[D.key]=D,this.dataOptions[D.key].isToolSubField=!0,this.dataOptions[D.key].toolKey=e[a].key}}else this.dataOptions[e[a].key]=e[a];this.generateFormRules(this.rules,e[a])}},generateMetaValue:function(e){var t=this;e.config.scoreConfig.metaFieldList.forEach((function(e){e.code&&t.$set(t.metaValue,e.code,"")}))},generateFormRules:function generateFormRules(rules,widget){rules[widget.key]&&widget.rules?rules[widget.key]=[].concat(Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_16__.a)(rules[widget.key]),Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_16__.a)(widget.rules.map((function(item){return item.pattern?_objectSpread(_objectSpread({},item),{},{pattern:eval(item.pattern)}):_objectSpread({},item)})))):widget.rules&&(rules[widget.key]=Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_16__.a)(widget.rules.map((function(item){return item.pattern?_objectSpread(_objectSpread({},item),{},{pattern:eval(item.pattern)}):_objectSpread({},item)})))),"inputnumber"==widget.type&&(""!=widget.options.max&&null!=widget.options.max&&rules[widget.key].push({trigger:"change",validator:function(e,t,i){(t||0===t||widget.options.required)&&+t>widget.options.max?i(new Error("输入值大于最大值："+widget.options.max)):i()}}),""!=widget.options.min&&null!=widget.options.min&&rules[widget.key].push({trigger:"change",validator:function(e,t,i){(t||0===t||widget.options.required)&&+t<widget.options.min?i(new Error("输入值小于最小值："+widget.options.min)):i()}}))},initValue:function(e){var t=this;this.value?(this.setValue(this.value),e&&e()):this.valueHttpOption?this.$http(this.valueHttpOption).then((function(i){i.Result?(t.setValue(ret.DataObject),e&&e()):t.$message({message:"获取表单数据失败"+i.Message,type:"error"})})).catch((function(e){})):this.getValue&&this.getValue((function(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.setValue(i,n),e&&t.$nextTick((function(){e()}))}),JSON.parse(JSON.stringify(this.data)))},initExternalData:function(e,t){var i=this;this.externalData?(this.setExternalData(this.externalData),t&&t()):this.externalDataHttpOption?this.$http(this.externalDataHttpOption).then((function(e){e.Result?(i.setExternalData(e.DataObject),t&&t()):i.$message({message:"获取接口数据失败"+e.Message,type:"error"})})).catch((function(e){})):this.getExternalData?this.getExternalData((function(e,n){i.setExternalData(e,n),t&&t()}),e):t&&t()},generateFormModel:function(){for(var e in this.models){e.split("_")[0];var t=this.value2dataModel(this.models[e],this.dataOptions[e]);this.formModel[e]!==t&&this.$set(this.formModel,e,t)}},value2dataModel:_utils_js__WEBPACK_IMPORTED_MODULE_25__.r,getData:function(){var e=this;return new Promise((function(t,i){e.$refs.generateForm.validate((function(n,o){n?t(Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.p)(e.models,e.dataOptions)):i(o)}))}))},getMetaFieldFillData:function(){var e=this;return this.$refs.GfMetaField?new Promise((function(t,i){e.$refs.GfMetaField.$refs.form.validate((function(n,o){n?t(JSON.parse(JSON.stringify(e.metaValue))):i(o)}))})):new Promise((function(e){e({})}))},getStatisticalData:function(){var e={},t=Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.p)(this.models,this.dataOptions);return Object(_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.b)(this.data,(function(i){i.isDelete||i.options&&i.options.isStatistical&&i.options.code&&void 0!==t[i.key]&&(e[i.options.code]=t[i.key])})),e},getNames:function(){var e=this,t=this.getFillDataNames();return"score"===this.data.config.mode&&this.data.config.scoreConfig.metaFieldList.length>0&&this.data.config.scoreConfig.metaFieldList.forEach((function(e){t[e.code]=e.name})),"score"===this.data.config.mode&&this.data.config.scoreConfig.scoreFieldList.length>0&&this.data.config.scoreConfig.scoreFieldList.forEach((function(e){t[e.code]=e.name})),Object.keys(this.all$vm).forEach((function(i){var n=e.all$vm[i];if(n.getNames){var o=n.getNames();Object.assign(t,o)}})),t},getFillDataNames:function(){var e={};return this.allFieldsForEach((function(t){var i=t.key,n=t.name;e[i]=n})),e},allFieldsForEach:function(e){var t=this,i=Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.p)(this.models,this.dataOptions);Object.keys(i).forEach((function(n){if(t.dataOptions[n]){var o=t.dataOptions[n].name;e&&e({key:n,name:o,widget:t.dataOptions[n],value:i[n]})}else{var a=new RegExp(/^(.+)\.(.+)\^(\d)$/g);if(a.test(n)){var r=n.split(a),l=Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__.a)(r,5),s=(l[0],l[1]),u=l[2],c=l[3];l[4];if(t.dataOptions[s]&&t.dataOptions[u]){var d=t.dataOptions[s].name+"."+t.dataOptions[u].name+"(第".concat(+c+1,"次)");e&&e({key:n,name:d,widget:t.dataOptions[u],value:i[n]})}}}}))},getScoreFieldData:function(e){var t=this,i={};"score"===this.data.config.mode&&this.data.config.scoreConfig.scoreFieldList.length>0&&this.data.config.scoreConfig.scoreFieldList.forEach((function(n){e&&e!==n.code||(i[n.code]=t.getScoreByExpression(n.scoreExpression))}));return e||Object.keys(this.all$vm).forEach((function(e){var n=t.all$vm[e];if(n.getItemScoreObj){var o=n.getItemScoreObj();Object.assign(i,o)}})),i},getAudit:function(){return this.audit},refreshAuditStatus:function(){this.$emit("update-audit-json")},getScore:function(){var e=0,t=this.getDataOptionsShowed();for(var i in t)if(Object.hasOwnProperty.call(t,i)){var n=t[i];if(n.options&&n.options.isScore&&"score"!=n.type){var o=n.key;n.type;e+=this.getWidgetScore(n,this.models[o])}}return e},getWidgetScore:function(e,t){return this.all$vm[e.key]&&this.all$vm[e.key].getItemScore?this.all$vm[e.key].getItemScore():Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.i)(e,t)},getFillInRate:function(){return Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.f)(this.models,this.dataOptions)},getPrintHtml:function(){this.tableDataMap=Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.h)(this.dataOptions,this.models);var e=Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.a)(this.data,this.tableDataMap);return'<div class="fm-print-html">'.concat(e,"</div>")},getChangedFields:function(){return Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.d)(this.modelsCached,this.models,this.dataOptions)},resetData:function(){var e=this;this.$refs.generateForm.clearValidate(),this.$refs.GfMetaField&&this.$refs.GfMetaField.$refs.form.clearValidate(),Object.keys(this.models).forEach((function(t){e.models[t]=""})),Object.keys(this.metaValue).forEach((function(t){e.metaValue[t]=""}))},setDESettings:function setDESettings(DESettings){!DESettings||"{}"==DESettings||DESettings instanceof Object&&"{}"==JSON.stringify(DESettings)?DESettings={deCategory:[],deItem:[],deItemProp:[],deSpecialVal:[],deObsvType:[],deObsvField:[]}:"string"==typeof DESettings&&(DESettings=eval("("+DESettings+")"),DESettings.deCategory||(DESettings.deCategory=[]),DESettings.deItem||(DESettings.deItem=[]),DESettings.deItemProp||(DESettings.deItemProp=[]),DESettings.deSpecialVal||(DESettings.deSpecialVal=[]),DESettings.deObsvType||(DESettings.deObsvType=[]),DESettings.deObsvField||(DESettings.deObsvField=[])),this.DESettings=DESettings},setValue:function setValue(value){var _this12=this,otherConfig=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};otherConfig.metaValue&&Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.k)(otherConfig.metaValue)&&Object.assign(this.metaValue,JSON.parse(JSON.stringify(otherConfig.metaValue))),!value||"{}"==value||value instanceof Object&&"{}"==JSON.stringify(value)||("string"==typeof value?(value=eval("("+value+")"),this.mergeValue(value)):this.mergeValue(value)),this.modelsCached=JSON.parse(JSON.stringify(this.models)),this.models=JSON.parse(JSON.stringify(this.models)),this.generateFormModel(),this.$nextTick((function(){_this12.isFormLoaded=!0}))},mergeValue:function(e){var t=this,i=this.models;for(var n in i)if(i.hasOwnProperty(n)&&void 0!==e[n]){if(this.dataOptions[n]&&this.dataOptions[n].options&&this.dataOptions[n].options.remote&&this.dataOptions[n].options.dictionaryType){var o=this.dataOptions[n];("select"===o.type||"dictionary"===o.type&&e[n]&&0===o.options.options.length)&&function(){var i=e[n],a=[];if(Array.isArray(i))i.forEach((function(e){var t=JSON.parse(JSON.stringify(e));a.push(t)}));else{var r=JSON.parse(JSON.stringify(i));a.push(r)}t.$set(o.options,"options",a)}()}i[n]=e[n]}Object.keys(e).forEach((function(n){void 0===i[n]&&t.$set(i,n,e[n])})),Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.q)(e,i,this.dataOptions)},mountAllComponents:function(){var e=this;this.componentsLoadedMap={},0!==this.data.list.length?(this.data.list.forEach((function(t,i){e.$set(e.componentsLoadedMap,i,!1)})),this.$nextTick((function(){e.$set(e.componentsLoadedMap,0,!0),e.data.list[0].isDelete&&e.componentMounted(0)}))):this.allComponentMounted()},componentMounted:function(e){var t=this;this.isAllComponentDestroying?this.componentsLoadedMap&&(this.$set(this.componentsLoadedMap,e,!1),this.data.list[e].isDelete&&this.componentDestroyed(e)):this.componentsLoadedMap&&void 0!==this.componentsLoadedMap[e+1]?setTimeout((function(){t.$set(t.componentsLoadedMap,e+1,!0),!0===t.data.list[e+1].isDelete&&t.componentMounted(e+1)}),0):this.allComponentMounted()},allComponentMounted:function(){var e=this;if(this.isAllComponentDestroyed=!1,this.destroyAllComponentsCallBack=null,this.eventHandler("formDataLoaded",this.data.config,this),this.$emit("allComponentMounted"),this.initExternalData(this.data,(function(){e.eventHandler("formOpenAfter",e.data.config,e)})),this.$emit("init-change-finally"),this.isAuditMode){if(this.auditJson)this.audit=Object(_audit_audit_utils_js__WEBPACK_IMPORTED_MODULE_26__.c)(this.auditJson);else if(this.getAuditJson){var t=this;this.getAuditJson((function(e){t.audit=Object(_audit_audit_utils_js__WEBPACK_IMPORTED_MODULE_26__.c)(e)}))}this.$on("update-audit-json",(function(){if(e.auditJson)e.audit=Object(_audit_audit_utils_js__WEBPACK_IMPORTED_MODULE_26__.c)(e.auditJson);else if(e.getAuditJson){var t=e;e.getAuditJson((function(e){t.audit=Object(_audit_audit_utils_js__WEBPACK_IMPORTED_MODULE_26__.c)(e)}))}}))}this.getOutData?this.getOutData((function(t){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Object.assign(e.outData,t),e.$nextTick((function(){e.fillOutData(i)}))})):Object.keys(this.outData).length>0&&this.$nextTick((function(){e.fillOutData()}))},destroyAllComponents:function(e){this.isAllComponentDestroying=!0,this.destroyAllComponentsCallBack=e,this.data.list.length>0&&this.componentsLoadedMap?(this.$set(this.componentsLoadedMap,this.data.list.length-1,!1),this.data.list[this.data.list.length-1].isDelete&&this.componentDestroyed(this.data.list.length-1)):this.allComponentDestroyed()},componentDestroyed:function(e){var t=this;this.componentsLoadedMap&&void 0!==this.componentsLoadedMap[e-1]?setTimeout((function(){t.$set(t.componentsLoadedMap,e-1,!1),!0===t.data.list[e-1].isDelete&&t.componentDestroyed(e-1)}),0):this.allComponentDestroyed()},allComponentDestroyed:function(){this.isAllComponentDestroyed||(this.isAllComponentDestroying=!1,this.componentsLoadedMap=null,this.destroyAllComponentsCallBack&&this.destroyAllComponentsCallBack(),this.$emit("allComponentDestroyed"),this.isAllComponentDestroyed=!0)},setJSONData:function(e){var t=this;if(this.isFormLoaded=!1,e=Object(_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.g)(e),e=Object(_makingForm_widgetConfig_tree_shaking__WEBPACK_IMPORTED_MODULE_28__.a)(e),this.themeCode&&!e.config.theme.code){var i=Object(_components_makingForm_layout_themes__WEBPACK_IMPORTED_MODULE_29__.a)(this.themeCode);i&&(e.config.theme.code=i.code,e.config.theme.name=i.name,e.config.theme.formBackgroundColor=i.formBackgroundColor,e.config.theme.whBackgroundColor=i.whBackgroundColor,e.config.theme.inputBorderColor=i.inputBorderColor)}if(this.data=e,this.mountAllComponents(),this.getAllRemoteScripts(),this.setThemeStyle(),this.generateModel(this.data.list),this.generateMetaValue(this.data),this.eventHandler("formOpenBefore",this.data.config,this),this.initValue(),this.isAddMode){this.getAddModeStatus?this.getAddModeStatus(this.setAddModeStatus):this.setAddModeStatus({})}var n={};this.allRemoteWidget=[],Object(_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.b)(e,(function(e){if(!e.isDelete&&(n[e.key]=e,e.options.remote&&e.options.dictionaryType&&("radio"==e.type||"checkbox"==e.type)&&t.allRemoteWidget.push(e),e.nameCode&&!e.options.code&&(e.options.code=e.nameCode),"matrix"==e.type)){for(var i=0;i<e.matrixColumns.length;i++){var o=e.matrixColumns[i];"string"==typeof o&&(e.matrixColumns[i]={label:o,width:""})}e.matrixColumnsFirst||(e.matrixColumnsFirst={label:"#",width:""})}})),this.$nextTick((function(){t.loadAllRemoteWidget()})),Object(_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.b)(e,(function(e){!e.isDelete&&e.event&&e.event.types&&[e.event.types.hide?e.event.types.hide:{},e.event.types.show?e.event.types.show:{}].forEach((function(i,o){if(i&&i.statements&&i.statements.length>0){var a=i.statements,r=[];a.forEach((function(e){r.indexOf(e.key)<0&&r.push(e.key),"1"==e.value_type&&r.indexOf(e.value)<0&&r.push(e.value)}));var l=1==o;r.forEach((function(o){n[o]&&t.addHideOrShowEvent(n[o],i,e.key,l)}))}}));!e.isDelete&&e.scoreExpression&&e.scoreExpression.items&&e.scoreExpression.items.length>0&&e.scoreExpression.items.forEach((function(i){t.addGetScoreEvent(n,i.key,e.scoreExpression,e.key)}))}),!0)},addHideOrShowEvent:function(e,t,i,n){e.event.types.changeFinally||(e.event.types.changeFinally=[]),e.event.types.changeFinally.push({name:n?"show":"hide",condition:t,operate:[{function_name:n?"show":"hide",label:n?"显示":"隐藏",options:{widget_list:[[i]]}}]})},addGetScoreEvent:function(e,t,i,n){var o=this,a=this.data.config.scoreConfig.scoreFieldList;if(a.map((function(e){return e.code})).indexOf(t)>=0){var r=a.filter((function(e){return e.code===t}))[0];r.scoreExpression&&r.scoreExpression.items&&r.scoreExpression.items.forEach((function(t){o.addGetScoreEvent(e,t.key,i,n)}))}else if(e[t]){var l=e[t];l.event.types.getScore||(l.event.types.getScore=[]),l.event.types.getScore.push({expression:i,target:n})}},setAddModeStatus:function(e){var t={};e&&(t="string"==typeof e?JSON.parse(e):e),Object(_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.b)(this.data,(function(e){e.options&&void 0!==e.options.disabled&&(void 0===t[e.key]?e.options.disabled=!0:0==t[e.key]?e.options.addModeStatus=0:1==t[e.key]&&(e.options.addModeStatus=1))}))},setExternalData:function setExternalData(data,isFill){var _this20=this;!data||"{}"==data||data instanceof Object&&"{}"==JSON.stringify(data)?data=[]:"string"==typeof data&&(data=eval("("+data+")"));var ret={},_iterator5=_createForOfIteratorHelper(data),_step5;try{for(_iterator5.s();!(_step5=_iterator5.n()).done;){var item=_step5.value;item.ControlID?ret[item.ControlID]=item.DataObject:item.Message&&(ret[item.Message]=item.DataObject)}}catch(e){_iterator5.e(e)}finally{_iterator5.f()}isFill?("{}"!=JSON.stringify(this.externaldata)&&1==this.externaldata.isFill&&this.clearExternalData(),this.externaldata=ret,this.externaldata.isFill=!0,this.$nextTick((function(){_this20.fillExternalData()}))):this.externaldata=ret},fillExternalData:function(){this.$refs.grid&&this.$refs.grid.forEach((function(e){e.fillExternalData()})),this.$refs.group&&this.$refs.group.forEach((function(e){e.fillExternalData()})),this.$refs.item&&this.$refs.item.forEach((function(e){e.fillExternalData()}))},clearExternalData:function(){this.$refs.grid&&this.$refs.grid.forEach((function(e){e.clearExternalData()})),this.$refs.group&&this.$refs.group.forEach((function(e){e.clearExternalData()})),this.$refs.item&&this.$refs.item.forEach((function(e){e.clearExternalData()}))},onInputChange:function(e,t){this.formModel[t]=e,this.$emit("input-change",e,t,this.models)},fieldValueChange:function(e,t,i,n){var o=this;this.isFormLoaded&&this.$nextTick((function(){o.$nextTick((function(){o.$emit("field-value-change",e,t,i,n)}))}))},eventHandler:function(e,t,i){var n=this,o=t.event;if(o){o.key;var a=o.types;if(a){"change"==e&&(a.changeFinally&&this.execEvent(a.changeFinally,t,i,!1),a.getScore&&this.$nextTick((function(){n.execFillScore(a.getScore,t,i)})));var r=a[e];r&&0!=r.length&&this.execEvent(r,t,i)}}},execEvent:function(e,t,i){var n=this,o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];this.$nextTick((function(){var a,r=_createForOfIteratorHelper(e);try{for(r.s();!(a=r.n()).done;){var l=a.value,s=!0,u=l.condition,c=l.operate;if("and"==u.logic?u.statements.forEach((function(e){n.isOneStatementPass(e)||(s=!1)})):(s=!1,u.statements.forEach((function(e){n.isOneStatementPass(e)&&(s=!0)}))),s&&(c.forEach((function(e){var o=e.function_name,a=e.options;_EventLibs_js__WEBPACK_IMPORTED_MODULE_24__[o]&&_EventLibs_js__WEBPACK_IMPORTED_MODULE_24__[o].call(n,a,t,i,_EventLibs_js__WEBPACK_IMPORTED_MODULE_24__)})),o))break}}catch(e){r.e(e)}finally{r.f()}}))},isOneStatementPass:function(e){var t=e.key,i=e.connector,n=e.value,o=e.value_type;if(!t||!i||""==n)return!1;var a=t,r=this.formModel[a];switch("1"==o&&(n=this.formModel[n]),i){case"eq":return r==n;case"neq":return r!=n;case"gt":return+r>+n;case"lt":return+r<+n;case"gt_or_eq":return+r>+n||+r==+n;case"lt_or_eq":return+r<+n||+r==+n;case"in":return r.indexOf(n+"")>=0;case"notin":return r.indexOf(n+"")<0;default:return!1}},execFillScore:function(e,t,i){var n=this;e.forEach((function(e){var t=e.expression,i=e.target,o=n.getScoreByExpression(t);n.all$vm[i]&&(n.all$vm[i].dataModel=o,n.eventHandler("change",n.all$vm[i].widget,n.all$vm[i]))}))},getScoreByExpression:function(e){var t=this,i=0,n=this.data.config.scoreConfig.scoreFieldList;return e.items&&0!==e.items.length?(e.items.forEach((function(e){var o=null===e.multiple||""===e.multiple||void 0===e.multiple||"number"!=typeof+e.multiple?1:+e.multiple,a=null===e.divisor||""===e.divisor||void 0===e.divisor||"number"!=typeof+e.divisor||0==+e.divisor?1:+e.divisor;if(n.map((function(e){return e.code})).indexOf(e.key)>=0){var r=+t.getScoreFieldData(e.key)[e.key];i+=r&&"number"==typeof r?r*o/a:0}else{var l=t.dataOptions[e.key];if(l&&!1!==l.options.show)if("score"===l.type)i+=t.models[l.key]&&"number"==typeof+t.models[l.key]?+t.models[l.key]*o/a:0;else{var s=+t.getWidgetScore(l,t.models[l.key])*o/a;i+=s&&"number"==typeof s?s:0}}})),(e.decimalPoint||""+e.decimalPoint=="0")&&(i=this.roundFun(i,+e.decimalPoint)),i):0},roundFun:function(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)},refresh:function(){},fillOutData:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];for(var t in this.all$vm)if(Object.hasOwnProperty.call(this.all$vm,t)){var i=this.all$vm[t];i.fillOutData(e)}},getDataOptionsShowed:function(){return Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.e)(this.dataOptions)},WidgetIterator:_makingForm_widgetConfig_widgetsConfig_js__WEBPACK_IMPORTED_MODULE_27__.b,scroll2Widget:function(e){if(e){var t=this.all$vm[e];if(t){t.$el.scrollIntoView({behavior:"smooth",block:"start"});var i=t.$el.querySelector(".el-radio,input,textarea");i&&i.focus&&i.focus()}}},loadAllRemoteWidget:function(){var e=this,t=this.allRemoteWidget.map((function(e){return e.options.dictionaryOptions.cateCode}));0!=t.length&&(this.remoteLoading=!0,this.getDictionaryOptions({params:t,all:!0},(function(t){e.setDictionaryOptions(t),e.remoteLoading=!1})))},setDictionaryOptions:function(e,t){var i=this;e&&Object.keys(e).length>0&&Object.keys(e).forEach((function(n){i.allRemoteWidget.forEach((function(o){var a=o.key,r=i.dataOptions[a]?i.dataOptions[a]:null;if(r&&r.options.dictionaryOptions.cateCode==n){var l=i.transformDic2Options(e[n]);if(t&&t.fieldKey)a===t.fieldKey&&(i.$set(i.dictionaryOptionsMap,a,l),i.$set(r.options,"options",l));else if(i.$set(i.dictionaryOptionsMap,a,l),i.$set(r.options,"options",l),i.all$vm[a]&&i.all$vm[a].dataModel2value){var s=i.all$vm[a];s.value[s.widget.key]=s.dataModel2value(s.dataModel)}}}))}))},transformDic2Options:function(e){return Object(_utils_js__WEBPACK_IMPORTED_MODULE_25__.n)(e)},findDictionaryOptions:(_findDictionaryOptions=Object(D_fangziqian_work_form_making_node_modules_babel_runtime_7_17_7_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_7__.a)(regeneratorRuntime.mark((function e(t){var i,n,o,a,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.widget,n=t.query,o=void 0===n?"":n,a=i.key,!this.dictionaryOptionsMap[a]||o){e.next=6;break}return e.abrupt("return",this.dictionaryOptionsMap[a]);case 6:return e.abrupt("return",new Promise((function(e){if(r.getDictionaryOptions){var t={fieldKey:i.key,page:1,limit:10,dictCateId:i.options.dictionaryOptions.id,dictName:o};r.getDictionaryOptions({params:t,all:!1},(function(n){var o=n&&n.list?n.list:[],l={};l[i.options.dictionaryOptions.cateCode]=o,0==r.allRemoteWidget.filter((function(e){return e.key==i.key})).length&&r.allRemoteWidget.push(i),r.setDictionaryOptions(l,t),e(r.dictionaryOptionsMap[a]||[])}))}else e([])})));case 7:case"end":return e.stop()}}),e,this)}))),function(e){return _findDictionaryOptions.apply(this,arguments)}),setContainerSize:function(){var e=this.getContainerSize();this.containerSize=e},getContainerSize:function(){var e=document.querySelector(".gf-form");if(e){var t=e.getClientRects();if(t.length>0){var i=t[0].width;return i<768?"xs":i<992?"sm":"lg"}return"lg"}return"lg"},getAllImgAndFileFields:function(){var e=[];return this.allFieldsForEach((function(t){var i=t.key,n=t.name,o=t.widget,a=t.value;"uploadFile"!==o.type&&"uploadImg"!==o.type||e.push({key:i,name:n,value:a})})),e}},beforeDestroy:function(){this.allComponentDestroyed(),window.removeEventListener("resize",this.resizeHandle)},watch:{models:{handler:function(e){var t=this;this.$emit("update:value",e),this.generateFormModel(),this.$nextTick((function(){t.fillInRate=t.getFillInRate()}))},deep:!0},value:{handler:function(e){Object.assign(this.models,e)}}}}},eb0d:function(e,t,i){},eb34:function(e,t,i){var n=i("e99b"),o=i("3ab0"),a=i("0926"),r=i("5dc3"),l="["+r+"]",s=RegExp("^"+l+l+"*"),u=RegExp(l+l+"*$"),c=function(e,t,i){var o={},l=a((function(){return!!r[e]()||"​"!="​"[e]()})),s=o[e]=l?t(d):r[e];i&&(o[i]=s),n(n.P+n.F*l,"String",o)},d=c.trim=function(e,t){return e=String(o(e)),1&t&&(e=e.replace(s,"")),2&t&&(e=e.replace(u,"")),e};e.exports=c},edb4:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var n=i("484e");function o(e,t){if(e){if("string"==typeof e)return Object(n.a)(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Object(n.a)(e,t):void 0}}},edec:function(e,t,i){var n,o,a,r=i("1e4d"),l=i("a618"),s=i("bbcc"),u=i("e8d7"),c=i("0b34"),d=c.process,f=c.setImmediate,p=c.clearImmediate,m=c.MessageChannel,h=c.Dispatch,g=0,_={},b=function(){var e=+this;if(_.hasOwnProperty(e)){var t=_[e];delete _[e],t()}},y=function(e){b.call(e.data)};f&&p||(f=function(e){for(var t=[],i=1;arguments.length>i;)t.push(arguments[i++]);return _[++g]=function(){l("function"==typeof e?e:Function(e),t)},n(g),g},p=function(e){delete _[e]},"process"==i("cea2")(d)?n=function(e){d.nextTick(r(b,e,1))}:h&&h.now?n=function(e){h.now(r(b,e,1))}:m?(a=(o=new m).port2,o.port1.onmessage=y,n=r(a.postMessage,a,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(n=function(e){c.postMessage(e+"","*")},c.addEventListener("message",y,!1)):n="onreadystatechange"in u("script")?function(e){s.appendChild(u("script")).onreadystatechange=function(){s.removeChild(this),b.call(e)}}:function(e){setTimeout(r(b,e,1),0)}),e.exports={set:f,clear:p}},f1af:function(e,t,i){"use strict";i("ac67"),i("e186");var n=i("a7be"),o=(i("6ba0"),i("6c36")),a=(i("25ba"),i("32ea"),i("1bc7"),i("3ce5")),r=(i("a450"),i("fc02"),i("adf4")),l=i("0474"),s={mixins:[i("495d").a],props:{visible:{type:Boolean,default:function(){return!1}}},data:function(){return{dialogVisible:this.visible,loading:!1,form:{reason:""}}},methods:{confirm:function(){var e=this;this.$refs.form.validate().then((function(){e.loading=!0,e.$emit("confirm",e.form.reason)}))}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){e||(this.loading=!1,this.form.reason="",this.$refs.form&&this.$refs.form.clearValidate()),this.$emit("update:visible",e)}}},u=i("cba8"),c=Object(u.a)(s,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:"申请撤回提交",visible:e.dialogVisible,"append-to-body":!0,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{ref:"form",attrs:{model:e.form}},[i("el-form-item",{attrs:{label:"撤回原因",prop:"reason",rules:[{required:!0,trigger:"blur",message:"撤回原因必填"}],"label-width":"100px"}},[i("el-input",{directives:[{name:"emoji",rawName:"v-emoji"}],attrs:{type:"textarea",rows:5},model:{value:e.form.reason,callback:function(t){e.$set(e.form,"reason",t)},expression:"form.reason"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),i("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.confirm}},[e._v("确 定")])],1)],1)}),[],!1,null,null,null).exports;function d(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function f(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?d(Object(i),!0).forEach((function(t){Object(a.a)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):d(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var p,m;t.a={components:{ApplyBackReasonDialog:c},data:function(){return{ApplyBackReasonDialogVisible:!1,auditDialogVisible:!1,record:[],loadingBtn:!1,loadingRecord:!1}},computed:{isInFormBox:function(){return!!this.eventBus},fieldKey:function(){var e="";return e=this.field?this.field.key:this.widget.key,this.groupsWidget&&(e=this.groupsWidget.key+"."+e),void 0!==this.groupsIndex&&null!==this.groupsIndex&&(e+="^"+this.groupsIndex),e},fieldKeyWithOutIndex:function(){return this.fieldKey.split("^")[0]},fieldName:function(){return this.field?this.field.name:this.widget.name},fieldValue:function(){return this.field?Object(l.o)(this.toolModel,f(f({},this.field),{},{type:this.field.type?this.field.type:this.widget.type})):Object(l.o)(this.gfItemBus.value,this.widget)},roleType:function(){return this.isInFormBox?this.eventBus.audit.role:r.b.role},auditReadOnly:function(){return!!this.isInFormBox&&(this.eventBus.isAuditMode&&this.eventBus.isAuditMode.readonly)},isAuditMode:function(){return!!this.isInFormBox&&this.eventBus.isAuditMode},username:function(){return this.isInFormBox?this.eventBus.audit.username:""},isAuditor:function(){return this.roleType==r.a.Auditor},isQuestioned:function(){return!!this.isInFormBox&&(this.eventBus.audit.doubt.indexOf(this.fieldKey)>=0||this.eventBus.audit.doubt.indexOf(this.fieldKeyWithOutIndex)>=0)},isReplied:function(){return!!this.isInFormBox&&(this.eventBus.audit.reply.indexOf(this.fieldKey)>=0||this.eventBus.audit.reply.indexOf(this.fieldKeyWithOutIndex)>=0)},isLocked:function(){return!!this.isInFormBox&&(this.eventBus.audit.lock.indexOf(this.fieldKey)>=0||this.eventBus.audit.lock.indexOf(this.fieldKeyWithOutIndex)>=0)},isSubmit:function(){return!!this.isInFormBox&&(this.eventBus.audit.submit.indexOf(this.fieldKey)>=0||this.eventBus.audit.submit.indexOf(this.fieldKeyWithOutIndex)>=0)},isBackApply:function(){return!!this.isInFormBox&&(this.eventBus.audit.backApply.indexOf(this.fieldKey)>=0||this.eventBus.audit.backApply.indexOf(this.fieldKeyWithOutIndex)>=0)},isAgreeBackApply:function(){return!!this.isInFormBox&&(this.eventBus.audit.agreeBackApply.indexOf(this.fieldKey)>=0||this.eventBus.audit.agreeBackApply.indexOf(this.fieldKeyWithOutIndex)>=0)},changedFields:function(){var e={};return this.isInFormBox?(this.eventBus.getChangedFields().forEach((function(t){e[t.key]=t})),e):e},messageForFiller:function(){var e=this;if(!this.isInFormBox)return"";if(this.roleType==r.a.Filler){var t=Object.keys(this.changedFields).filter((function(t){return t.indexOf(e.fieldKey)>=0}));if(0===t.length)return"提交数据："+this.fieldValue;if(1===t.length){var i=this.changedFields[t[0]];return'将字段"'.concat(i.name,'"(').concat(i.key,')的值由"').concat(("object"===Object(o.a)(i.oldValue)?i.oldValueRaw:i.oldValue)+""||"-",'"修改为"').concat(("object"===Object(o.a)(i.newValue)?i.newValueRaw:i.newValue)+""||"-",'"')}if(t.length>1){var n=[];return t.forEach((function(t){n.push(e.changedFields[t])})),n.map((function(e){return'将字段"'.concat(e.name,'"(').concat(e.key,')的值由"').concat(("object"===Object(o.a)(e.oldValue)?e.oldValueRaw:e.oldValue)+""||"-",'"修改为"').concat(("object"===Object(o.a)(e.newValue)?e.newValueRaw:e.newValue)+""||"-",'"')})).join("\n")}return"提交数据："+this.fieldValue}return"提交数据："+this.fieldValue},isEditing:function(){return!!this.isInFormBox&&!!this.eventBus.editing[this.fieldKey]},formDisabled:function(){return!!this.isInFormBox&&!!this.eventBus.formDisabled}},methods:{sendMsg:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0,n=this.message;if(n||t.content){var o={fieldKey:this.fieldKey,fieldName:this.fieldName,roleType:this.roleType,type:this.roleType,fieldValue:this.fieldValue,content:n};Object.assign(o,t),this.eventBus.addAuditFieldRecord&&(this.loadingBtn=!0,this.eventBus.addAuditFieldRecord(o,(function(){e.loadingBtn=!1,e.message="",e.getRecord((function(){e.eventBus.$emit("update-audit-json")})),i&&i()})))}else this.$message.error("提交内容不能为空")},openAuditDialog:function(e){this.auditDialogVisible||(this.auditDialogVisible=!0,this.openParams=e)},closeAuditDialog:function(){this.auditDialogVisible&&(this.auditDialogVisible=!1,this.onClose&&this.onClose())},toLockField:(m=Object(n.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.$confirm("确定要通过吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 3:this.lockField(),e.next=8;break;case 6:e.prev=6,e.t0=e.catch(0);case 8:case"end":return e.stop()}}),e,this,[[0,6]])}))),function(){return m.apply(this,arguments)}),toUnlockField:(p=Object(n.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.$confirm("确定要解锁吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 3:this.unlockField(),e.next=8;break;case 6:e.prev=6,e.t0=e.catch(0);case 8:case"end":return e.stop()}}),e,this,[[0,6]])}))),function(){return p.apply(this,arguments)}),lockField:function(){var e=this,t={fieldKey:this.fieldKey,roleType:this.roleType,lock:1};this.eventBus.lockField&&(this.eventBus.loading=!0,this.eventBus.lockField(t,(function(){e.eventBus.loading=!1,e.eventBus.$emit("update-audit-json")})))},unlockField:function(){var e=this,t={fieldKey:this.fieldKey,roleType:this.roleType};this.eventBus.unlockField&&(this.eventBus.loading=!0,this.eventBus.unlockField(t,(function(){e.eventBus.loading=!1,e.eventBus.$emit("update-audit-json")})))},getRecord:function(e){var t=this;this.eventBus.getAuditFieldRecord&&(this.loadingRecord=!0,this.eventBus.getAuditFieldRecord({fieldKey:this.fieldKey,roleType:this.roleType},(function(i){t.loadingRecord=!1,t.record=i,e&&e()})))},doEditing:function(){this.$set(this.eventBus.editing,this.fieldKey,!0)},saveField:function(){var e={fieldKey:this.fieldKey,fieldName:this.fieldName,roleType:this.roleType,fieldValue:this.fieldValue,content:this.messageForFiller};this.eventBus.$emit("save-field",this.fieldKey,e)},optionBtnPermission:function(e){return!this.auditReadOnly&&(!!this.formDisabled&&(1==e?this.isAuditMode&&this.isAuditor&&!this.isLocked&&!this.isQuestioned&&!this.isAgreeBackApply:2==e?this.isAuditMode&&this.isAuditor&&!this.isQuestioned&&!this.isLocked&&!this.isBackApply&&!this.isAgreeBackApply:3==e?this.isAuditMode&&!this.isAuditor&&this.isQuestioned:4!=e&&(5==e?this.isAuditMode&&!this.isAuditor&&this.isAgreeBackApply&&!this.isEditing:6==e?this.isAuditMode&&!this.isAuditor&&this.isEditing:7==e?this.isAuditMode&&!this.isAuditor&&this.isSubmit:void 0)))},toApplyBack:function(){this.ApplyBackReasonDialogVisible=!0},applyBackConfirm:function(e){var t=this,i={type:2,content:e};this.sendMsg(i,(function(){t.ApplyBackReasonDialogVisible=!1}))}}}},f2c6:function(e,t,i){var n={"./gf-tool-BMI.vue":"c43e","./gf-tool-afterSurgery.vue":"afcd","./gf-tool-birthday.vue":"24f6","./gf-tool-float.vue":"7f89","./gf-tool-medical-image.vue":"a1b8","./gf-tool-nation.vue":"e0e3","./gf-tool-table.vue":"7a53"};function o(e){var t=a(e);return i(t)}function a(e){if(!i.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}o.keys=function(){return Object.keys(n)},o.resolve=a,e.exports=o,o.id="f2c6"},f417:function(e,t,i){"use strict";var n=i("d445"),o=RegExp.prototype.exec;e.exports=function(e,t){var i=e.exec;if("function"==typeof i){var a=i.call(e,t);if("object"!=typeof a)throw new TypeError("RegExp exec method returned something other than an Object or null");return a}if("RegExp"!==n(e))throw new TypeError("RegExp#exec called on incompatible receiver");return o.call(e,t)}},f8b5:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var n=i("edb4");function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var n,o,a=[],r=!0,l=!1;try{for(i=i.call(e);!(r=(n=i.next()).done)&&(a.push(n.value),!t||a.length!==t);r=!0);}catch(e){l=!0,o=e}finally{try{r||null==i.return||i.return()}finally{if(l)throw o}}return a}}(e,t)||Object(n.a)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},f962:function(e,t,i){
/*!
 * clipboard.js v2.0.10
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
var n;n=function(){return function(){var e={686:function(e,t,i){"use strict";i.d(t,{default:function(){return O}});var n=i(279),o=i.n(n),a=i(370),r=i.n(a),l=i(817),s=i.n(l);function u(e){try{return document.execCommand(e)}catch(e){return!1}}var c=function(e){var t=s()(e);return u("cut"),t};function d(e){var t="rtl"===document.documentElement.getAttribute("dir"),i=document.createElement("textarea");i.style.fontSize="12pt",i.style.border="0",i.style.padding="0",i.style.margin="0",i.style.position="absolute",i.style[t?"right":"left"]="-9999px";var n=window.pageYOffset||document.documentElement.scrollTop;return i.style.top="".concat(n,"px"),i.setAttribute("readonly",""),i.value=e,i}var f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},i="";if("string"==typeof e){var n=d(e);t.container.appendChild(n),i=s()(n),u("copy"),n.remove()}else i=s()(e),u("copy");return i};function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.action,i=void 0===t?"copy":t,n=e.container,o=e.target,a=e.text;if("copy"!==i&&"cut"!==i)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==p(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===i&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===i&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return a?f(a,{container:n}):o?"cut"===i?c(o):f(o,{container:n}):void 0};function h(e){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function g(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _(e,t){return(_=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function b(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=v(e);if(t){var o=v(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return y(this,i)}}function y(e,t){return!t||"object"!==h(t)&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function v(e){return(v=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function w(e,t){var i="data-clipboard-".concat(e);if(t.hasAttribute(i))return t.getAttribute(i)}var O=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&_(e,t)}(a,e);var t,i,n,o=b(a);function a(e,t){var i;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(i=o.call(this)).resolveOptions(t),i.listenClick(e),i}return t=a,n=[{key:"copy",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return f(e,t)}},{key:"cut",value:function(e){return c(e)}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],t="string"==typeof e?[e]:e,i=!!document.queryCommandSupported;return t.forEach((function(e){i=i&&!!document.queryCommandSupported(e)})),i}}],(i=[{key:"resolveOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"==typeof e.action?e.action:this.defaultAction,this.target="function"==typeof e.target?e.target:this.defaultTarget,this.text="function"==typeof e.text?e.text:this.defaultText,this.container="object"===h(e.container)?e.container:document.body}},{key:"listenClick",value:function(e){var t=this;this.listener=r()(e,"click",(function(e){return t.onClick(e)}))}},{key:"onClick",value:function(e){var t=e.delegateTarget||e.currentTarget,i=this.action(t)||"copy",n=m({action:i,container:this.container,target:this.target(t),text:this.text(t)});this.emit(n?"success":"error",{action:i,text:n,trigger:t,clearSelection:function(){t&&t.focus(),document.activeElement.blur(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(e){return w("action",e)}},{key:"defaultTarget",value:function(e){var t=w("target",e);if(t)return document.querySelector(t)}},{key:"defaultText",value:function(e){return w("text",e)}},{key:"destroy",value:function(){this.listener.destroy()}}])&&g(t.prototype,i),n&&g(t,n),a}(o())},828:function(e){if("undefined"!=typeof Element&&!Element.prototype.matches){var t=Element.prototype;t.matches=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector}e.exports=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}}},438:function(e,t,i){var n=i(828);function o(e,t,i,n,o){var r=a.apply(this,arguments);return e.addEventListener(i,r,o),{destroy:function(){e.removeEventListener(i,r,o)}}}function a(e,t,i,o){return function(i){i.delegateTarget=n(i.target,t),i.delegateTarget&&o.call(e,i)}}e.exports=function(e,t,i,n,a){return"function"==typeof e.addEventListener?o.apply(null,arguments):"function"==typeof i?o.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return o(e,t,i,n,a)})))}},879:function(e,t){t.node=function(e){return void 0!==e&&e instanceof HTMLElement&&1===e.nodeType},t.nodeList=function(e){var i=Object.prototype.toString.call(e);return void 0!==e&&("[object NodeList]"===i||"[object HTMLCollection]"===i)&&"length"in e&&(0===e.length||t.node(e[0]))},t.string=function(e){return"string"==typeof e||e instanceof String},t.fn=function(e){return"[object Function]"===Object.prototype.toString.call(e)}},370:function(e,t,i){var n=i(879),o=i(438);e.exports=function(e,t,i){if(!e&&!t&&!i)throw new Error("Missing required arguments");if(!n.string(t))throw new TypeError("Second argument must be a String");if(!n.fn(i))throw new TypeError("Third argument must be a Function");if(n.node(e))return function(e,t,i){return e.addEventListener(t,i),{destroy:function(){e.removeEventListener(t,i)}}}(e,t,i);if(n.nodeList(e))return function(e,t,i){return Array.prototype.forEach.call(e,(function(e){e.addEventListener(t,i)})),{destroy:function(){Array.prototype.forEach.call(e,(function(e){e.removeEventListener(t,i)}))}}}(e,t,i);if(n.string(e))return function(e,t,i){return o(document.body,e,t,i)}(e,t,i);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}},817:function(e){e.exports=function(e){var t;if("SELECT"===e.nodeName)e.focus(),t=e.value;else if("INPUT"===e.nodeName||"TEXTAREA"===e.nodeName){var i=e.hasAttribute("readonly");i||e.setAttribute("readonly",""),e.select(),e.setSelectionRange(0,e.value.length),i||e.removeAttribute("readonly"),t=e.value}else{e.hasAttribute("contenteditable")&&e.focus();var n=window.getSelection(),o=document.createRange();o.selectNodeContents(e),n.removeAllRanges(),n.addRange(o),t=n.toString()}return t}},279:function(e){function t(){}t.prototype={on:function(e,t,i){var n=this.e||(this.e={});return(n[e]||(n[e]=[])).push({fn:t,ctx:i}),this},once:function(e,t,i){var n=this;function o(){n.off(e,o),t.apply(i,arguments)}return o._=t,this.on(e,o,i)},emit:function(e){for(var t=[].slice.call(arguments,1),i=((this.e||(this.e={}))[e]||[]).slice(),n=0,o=i.length;n<o;n++)i[n].fn.apply(i[n].ctx,t);return this},off:function(e,t){var i=this.e||(this.e={}),n=i[e],o=[];if(n&&t)for(var a=0,r=n.length;a<r;a++)n[a].fn!==t&&n[a].fn._!==t&&o.push(n[a]);return o.length?i[e]=o:delete i[e],this}},e.exports=t,e.exports.TinyEmitter=t}},t={};function i(n){if(t[n])return t[n].exports;var o=t[n]={exports:{}};return e[n](o,o.exports,i),o.exports}return i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i(686)}().default},e.exports=n()},f966:function(e,t,i){"use strict";var n=i("0b34"),o=i("bb8b"),a=i("26df"),r=i("839a")("species");e.exports=function(e){var t=n[e];a&&t&&!t[r]&&o.f(t,r,{configurable:!0,get:function(){return this}})}},fb49:function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},fc02:function(e,t,i){"use strict";var n=i("804d"),o=i("a86f"),a=i("1b0b"),r=i("43ec"),l=i("201c"),s=i("f417"),u=i("0353"),c=i("0926"),d=Math.min,f=[].push,p="length",m=!c((function(){RegExp(4294967295,"y")}));i("c46f")("split",2,(function(e,t,i,c){var h;return h="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1)[p]||2!="ab".split(/(?:ab)*/)[p]||4!=".".split(/(.?)(.?)/)[p]||".".split(/()()/)[p]>1||"".split(/.?/)[p]?function(e,t){var o=String(this);if(void 0===e&&0===t)return[];if(!n(e))return i.call(o,e,t);for(var a,r,l,s=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),d=0,m=void 0===t?4294967295:t>>>0,h=new RegExp(e.source,c+"g");(a=u.call(h,o))&&!((r=h.lastIndex)>d&&(s.push(o.slice(d,a.index)),a[p]>1&&a.index<o[p]&&f.apply(s,a.slice(1)),l=a[0][p],d=r,s[p]>=m));)h.lastIndex===a.index&&h.lastIndex++;return d===o[p]?!l&&h.test("")||s.push(""):s.push(o.slice(d)),s[p]>m?s.slice(0,m):s}:"0".split(void 0,0)[p]?function(e,t){return void 0===e&&0===t?[]:i.call(this,e,t)}:i,[function(i,n){var o=e(this),a=null==i?void 0:i[t];return void 0!==a?a.call(i,o,n):h.call(String(o),i,n)},function(e,t){var n=c(h,e,this,t,h!==i);if(n.done)return n.value;var u=o(e),f=String(this),p=a(u,RegExp),g=u.unicode,_=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(m?"y":"g"),b=new p(m?u:"^(?:"+u.source+")",_),y=void 0===t?4294967295:t>>>0;if(0===y)return[];if(0===f.length)return null===s(b,f)?[f]:[];for(var v=0,w=0,O=[];w<f.length;){b.lastIndex=m?w:0;var k,x=s(b,m?f:f.slice(w));if(null===x||(k=d(l(b.lastIndex+(m?0:w)),f.length))===v)w=r(f,w,g);else{if(O.push(f.slice(v,w)),O.length===y)return O;for(var M=1;M<=x.length-1;M++)if(O.push(x[M]),O.length===y)return O;w=v=k}}return O.push(f.slice(v)),O}]}))},fed1:function(e,t,i){"use strict";var n={props:{visible:Boolean,fullscreen:Boolean,loadingText:{type:String,default:""},title:{type:String,default:"表单预览"},width:{type:String,default:"600px"},top:{type:String,default:"15vh"},form:{type:Boolean,default:!0},action:{type:Boolean,default:!0}},computed:{show:function(){return!this.form||this.showForm}},data:function(){return{loading:!1,dialogVisible:this.visible,id:"dialog_"+(new Date).getTime(),showForm:!1}},methods:{close:function(){this.dialogVisible=!1},submit:function(){this.loading=!0,this.$emit("on-submit")},end:function(){this.loading=!1}},mounted:function(){},watch:{dialogVisible:function(e){var t=this;e?this.showForm=!0:(this.loading=!1,this.$emit("on-close"),setTimeout((function(){t.showForm=!1}),300))},visible:function(e){this.dialogVisible=e}}},o=(i("3080"),i("cba8")),a=Object(o.a)(n,(function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{ref:"elDialog",staticClass:"cus-dialog-container",attrs:{title:e.title,visible:e.dialogVisible,"close-on-click-modal":!1,"append-to-body":"",center:"",fullscreen:e.fullscreen,top:e.top,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.show?i("div",[e._t("default")],2):e._e(),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"dialog-footer",attrs:{slot:"footer","element-loading-text":e.loadingText},slot:"footer"},[e.action?[e._t("action",(function(){return[i("el-button",{on:{click:e.close}},[e._v(e._s(e.$t("fm.actions.cancel")))]),i("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v(e._s(e.$t("fm.actions.confirm")))])]}))]:e._e()],2)])}),[],!1,null,null,null);t.a=a.exports}})}));