package com.boot.modules.project.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.EnrollDetailEntity;
import com.boot.modules.project.vo.EnrollDetailVo;

import java.util.List;
import java.util.Map;

public interface EnrollDetailService extends IService<EnrollDetailEntity> {
    List<String> getMatchKey(String empi);

    PageUtils getAllByPage(Map<String, Object> params);
}
