package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.project.entity.EnrollDetailEntity;
import com.boot.modules.project.vo.EnrollDetailVo;
import com.boot.modules.sys.vo.DiseaseVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EnrollDetailDao extends BaseMapper<EnrollDetailEntity> {
    /**
     * 分页获取执行详情
     * @param pageFilter
     * @param qw
     * @return
     */
    IPage<EnrollDetailVo> getByQuery(IPage<EnrollDetailVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<EnrollDetailVo> qw);
}