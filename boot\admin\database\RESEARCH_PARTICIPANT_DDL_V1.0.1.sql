/*
 添加基础数据
 research_participant 版本：V1.0.0

 Date: 15/05/2023 10:38:24
*/

--
-- Table structure for table `rp_project_dept`
--

DROP TABLE IF EXISTS `rp_project_dept`;
CREATE TABLE `rp_project_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `dept_id` bigint(20) NOT NULL,
  `is_primary` int(2) NOT NULL COMMENT '是否主中心',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `p_project_id_index` (`project_id`) USING BTREE,
  KEY `p_dept_id_index` (`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_project_dept_permission`
--

DROP TABLE IF EXISTS `rp_project_dept_permission`;
CREATE TABLE `rp_project_dept_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `dept_id` bigint(20) NOT NULL COMMENT '机构id',
  `dept_code` varchar(50) COLLATE utf8_croatian_ci DEFAULT NULL COMMENT '机构编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `dept_id` (`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 COLLATE=utf8_croatian_ci ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_project_center_config`
--

DROP TABLE IF EXISTS `rp_project_center_config`;
CREATE TABLE `rp_project_center_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(50) NOT NULL COMMENT '字段中文名',
  `code` varchar(50) NOT NULL COMMENT '字段code，对应英文名',
  `is_show` int(2) NOT NULL DEFAULT '1' COMMENT '是否展示，默认展示为1  0为不展示',
  `center_id` bigint(20) NOT NULL COMMENT '中心id',
  `sort` int(10) NOT NULL DEFAULT '1' COMMENT '排序 ，默认为1',
  `subproject_id` bigint(20) NOT NULL COMMENT '子项目id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `center_id` (`center_id`) USING BTREE,
  KEY `centerAndFieldName` (`center_id`,`name`,`code`) USING BTREE,
  KEY `subId` (`subproject_id`,`center_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1374 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='项目中心配置';

--
-- Table structure for table `rp_project`
--

DROP TABLE IF EXISTS `rp_project`;
CREATE TABLE `rp_project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `project_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '项目名称',
  `description` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '项目描述',
  `end_date` datetime DEFAULT NULL COMMENT '项目结束时间',
  `start_date` datetime DEFAULT NULL COMMENT '项目开始时间',
  `create_time` datetime DEFAULT NULL COMMENT '项目创建时间',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建者',
  `status` int(2) NOT NULL COMMENT '项目状态 0：停用 1：启用',
  `type` int(1) NOT NULL COMMENT '项目类型，1：单病钟；2：多病种',
  `project_category` int(2) NOT NULL COMMENT '单中心：1，多中心：2',
  `sub_project_count` int(10) DEFAULT NULL COMMENT '子项目最多数量',
  `data_export_audit` int(2) DEFAULT NULL COMMENT '数据导出审核权限0：否：1：是',
  `enable_group_member` int(2) DEFAULT NULL COMMENT '启用入组员0：否 ，1：是',
  `enable_audit` int(2) DEFAULT NULL COMMENT '启用审核0:否，1：是',
  `data_double_export` int(2) DEFAULT NULL COMMENT '数据双审核0：否，1：是',
  `project_admin_id` bigint(20) DEFAULT NULL COMMENT '项目管理员用户ID',
  `enable_download_image` int(2) DEFAULT NULL COMMENT '启用图像下载0:否，1：是',
  `main_dept_id` bigint(20) NOT NULL COMMENT '所属科室ID',
  `enable_hyt` int(2) NOT NULL DEFAULT '0' COMMENT '是否启用华医通：1启用；0不启用',
  `enrollments_estimated_count` int(10) DEFAULT 0 COMMENT '预计入组数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `project_name_unique_index` (`project_name`) USING BTREE,
  KEY `project_type_index` (`type`) USING BTREE,
  KEY `project_status_index` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_projrole_setting`
--

DROP TABLE IF EXISTS `rp_projrole_setting`;
CREATE TABLE `rp_projrole_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sub_project_id` bigint(20) NOT NULL,
  `role_id` bigint(20) NOT NULL,
  `data_auth` int(2) NOT NULL COMMENT '1:全部数据 2. 所在中心 3. 自己录入的',
  `is_tuomin` int(2) DEFAULT NULL COMMENT '是否脱敏0 :否  1：是',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sub_project_id` (`sub_project_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_project_setting`
--

DROP TABLE IF EXISTS `rp_project_setting`;
CREATE TABLE `rp_project_setting` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sub_project_id` bigint(20) NOT NULL COMMENT '子项目ID',
  `setting` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '每个步骤的状态 0未完成，1已完成 \r\n{\r\n  intoGrp:1,\r\n  deForm:0,\r\n  crfForm:0,\r\n  followup:0,\r\n  userGrp:0\r\n}',
  `out` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '退出时在哪一步，枚举类型\r\n\r\nintoGrp  //1 入组调度\r\ndeForm  //2 数据订阅\r\ncrfForm  //3 crf\r\nfollowUp//4 随访流程\r\nuserGrp  //5 成员管理',
  `status` int(255) unsigned zerofill NOT NULL COMMENT '状态： 0未完成， 1已完成',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sub_project_id` (`sub_project_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='项目流程设置表';

--
-- Table structure for table `rp_sub_project`
--
DROP TABLE IF EXISTS `rp_sub_project`;
CREATE TABLE `rp_sub_project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '研究组主键id',
  `name` varchar(30) DEFAULT NULL,
  `description` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '研究组说明',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建用户',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `project_id` bigint(20) NOT NULL COMMENT '关联的项目id',
  `enabled` int(2) NOT NULL COMMENT '1.启用（审核通过），0非启用（待审核） 默认是启用的， 当多病种项目中超出创建上限时需要关闭， 只有当管理源审核时才启用  2:审核不通过',
  `enable_audit` int(2) NOT NULL DEFAULT '1' COMMENT '是否启用审核 0：不启用 1：启用 默认1',
  `update_time` varchar(20) DEFAULT NULL COMMENT '?޸?ʱ?',
  `status` int(2) NOT NULL DEFAULT '1' COMMENT '????Ŀ????һ?? 0:δ??ʼ 1?????ã?2.ͣ?',
  `is_private` bigint(1) NOT NULL DEFAULT '0' COMMENT '默认值为0-公开；1-私密',
  `concern_inpatient_status` int(2) NOT NULL DEFAULT '0' COMMENT '1关注，0不关注',
  `enable_api` int(2) NOT NULL DEFAULT '0' COMMENT '是否启用服务 0：不启用 1：启用 默认0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `s_project_id_index` (`project_id`) USING BTREE,
  KEY `s_create_user_id_index` (`create_user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=86 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_user_subproject`
--
DROP TABLE IF EXISTS `rp_user_subproject`;
CREATE TABLE `rp_user_subproject` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `sub_project_id` bigint(20) NOT NULL COMMENT '关联的子项目ID',
  `proj_role_id` bigint(20) DEFAULT NULL COMMENT '子项目角色ID',
  `project_dept_id` bigint(20) NOT NULL COMMENT '项目中心ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sub_project_id` (`sub_project_id`) USING BTREE,
  KEY `project_dept_id` (`project_dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_backup_task`
--

DROP TABLE IF EXISTS `rp_backup_task`;
CREATE TABLE `rp_backup_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_name` varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '备份文件名称',
  `file_length` bigint(20) DEFAULT NULL COMMENT '备份文件大小',
  `backup_datetime` datetime DEFAULT NULL COMMENT '备份时间',
  `file_path` varchar(200) COLLATE utf8_bin DEFAULT NULL COMMENT '备份文件路径',
  `backup_status` int(4) DEFAULT NULL COMMENT '备份任务执行状态(0:  成功  1：备份失败  2：压缩失败 3.其他异常)',
  `backup_error_message` varchar(200) COLLATE utf8_bin DEFAULT NULL COMMENT '备份任务失败原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='备份任务记录表';


--
-- Table structure for table `rp_sys_application`
--

DROP TABLE IF EXISTS `rp_sys_application`;
CREATE TABLE `rp_sys_application` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '应用ID',
  `app_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '应用名称',
  `app_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '应用编码(缩写)',
  `is_default` int(11) DEFAULT NULL COMMENT '是否为默认应用',
  `app_desc` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '应用描述',
  `external` int(2) NOT NULL DEFAULT '0' COMMENT '是否是外部应用 1：是--外部应用    0：否-本系统应用，默认为0',
  `sort` int(255) NOT NULL DEFAULT '1' COMMENT '排序',
  `is_project_app` int(2) NOT NULL DEFAULT '0' COMMENT '是否是项目应用标识0：否，1：是',
  `external_link` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '外部应用链接',
  `is_new_windows_open` int(2) DEFAULT NULL COMMENT '是否新窗口打开',
  `home_router` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '应用首页路由',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code` (`app_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_sys_captcha`
--

DROP TABLE IF EXISTS `rp_sys_captcha`;
CREATE TABLE `rp_sys_captcha` (
  `uuid` char(36) CHARACTER SET utf8mb4 NOT NULL COMMENT 'uuid',
  `code` varchar(6) CHARACTER SET utf8mb4 NOT NULL COMMENT '验证码',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`uuid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统验证码';

--
-- Table structure for table `rp_sys_config`
--

DROP TABLE IF EXISTS `rp_sys_config`;
CREATE TABLE `rp_sys_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `param_key` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'key',
  `param_value` varchar(2000) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'value',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态   0：隐藏   1：显示',
  `remark` varchar(500) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '备注',
  `type` int(2) NOT NULL DEFAULT '0' COMMENT '配置类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `param_key` (`param_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统配置信息表';

--
-- Table structure for table `rp_sys_dept`
--

DROP TABLE IF EXISTS `rp_sys_dept`;
CREATE TABLE `rp_sys_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pid` bigint(20) DEFAULT '0' COMMENT '上级部门ID，一级部门为0',
  `name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '部门名称',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `del_flag` tinyint(4) DEFAULT '0' COMMENT '是否删除  -1：已删除  0：正常',
  `is_research_center` bigint(2) NOT NULL DEFAULT '0' COMMENT '是否作为研究中心 默认为否0  1：可以作为研究中心',
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '科室编码',
  `is_data_center` int(2) DEFAULT '0' COMMENT '是否为数据中心：1：是，0：不是，默认为0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='部门管理';

--
-- Table structure for table `rp_sys_dict_cate`
--

DROP TABLE IF EXISTS `rp_sys_dict_cate`;
CREATE TABLE `rp_sys_dict_cate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典类型表主键',
  `cate_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字典类型名称',
  `cate_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字典分类编码',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `dict_cate_index` (`cate_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='字典分类表';

--
-- Table structure for table `rp_sys_dict_item`
--

DROP TABLE IF EXISTS `rp_sys_dict_item`;
CREATE TABLE `rp_sys_dict_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字典名称',
  `code` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字典码',
  `dict_cate_id` bigint(20) NOT NULL COMMENT '字典分类表关联id',
  `value` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字典值',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标记  -1：已删除  0：正常',
  `pinyin` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '字典拼音',
  `first_letter` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '字典项拼音首字母',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `type` (`dict_cate_id`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据字典表';

--
-- Table structure for table `rp_sys_ip_filter`
--

DROP TABLE IF EXISTS `rp_sys_ip_filter`;
CREATE TABLE `rp_sys_ip_filter` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ip` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `mark` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- Table structure for table `rp_sys_log`
--

DROP TABLE IF EXISTS `rp_sys_log`;
CREATE TABLE `rp_sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '用户名',
  `operation` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '用户操作',
  `method` varchar(200) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '请求方法',
  `url` varchar(200) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '请求URL',
  `params` longtext COMMENT '请求参数',
  `time` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `ip` varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT 'IP地址',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `opera_type` int(11) DEFAULT NULL COMMENT '操作类型',
  `status` int(11) DEFAULT NULL COMMENT '操作状态',
  `error_info` longtext CHARACTER SET utf8mb4 COMMENT '操作异常日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6331 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统日志';

--
-- Table structure for table `rp_sys_menu`
--

DROP TABLE IF EXISTS `rp_sys_menu`;
CREATE TABLE `rp_sys_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pid` bigint(20) NOT NULL COMMENT '父权限id',
  `name` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '权限菜单名',
  `url` varchar(200) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '菜单url',
  `permissions` varchar(500) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT ' 权限代码列表（user:list,user:create)',
  `type` int(11) NOT NULL COMMENT '类型（0˵菜单，1-按钮）',
  `icon` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '按钮前端vue编码',
  `sort` int(11) NOT NULL COMMENT '菜单排序',
  `enabled` int(11) NOT NULL COMMENT '是否启用，0-未启用，1-启用',
  `app_id` bigint(20) DEFAULT NULL COMMENT '所属应用id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=451 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='权限菜单表';

--
-- Table structure for table `rp_recycle`
--

DROP TABLE IF EXISTS `rp_recycle`;
CREATE TABLE `rp_recycle` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '回收站表主键id',
  `entityname` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '删除表对应的entity对象名',
  `implname` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '删除表对应的serviceImpl',
  `username` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '删除用户',
  `data` varchar(1000) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '删除事件',
  `date` date NOT NULL COMMENT '被删除的信息',
  `sort` int(11) NOT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `data` (`data`) USING BTREE,
  KEY `entityname` (`entityname`) USING BTREE,
  KEY `username` (`username`) USING BTREE,
  KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='回收站表';

--
-- Table structure for table `rp_resource_category`
--

DROP TABLE IF EXISTS `rp_resource_category`;
CREATE TABLE `rp_resource_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '资源目录主键id',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '资源目录名称',
  `catagory_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '资源目录code',
  `sort` int(11) NOT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `catagory_code` (`catagory_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据资源目录表';

--
-- Table structure for table `rp_resource_dic`
--

DROP TABLE IF EXISTS `rp_resource_dic`;
CREATE TABLE `rp_resource_dic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '资源目录分类信息主键id',
  `catagory_id` bigint(20) NOT NULL COMMENT '关联rp_resource_catagory资源目录主键id',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '资源目录分类信息名称',
  `catagory_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '资源目录分类信息code',
  `datatype` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '资源目录分类数据类型',
  `sort` int(11) NOT NULL COMMENT '排序',
  `unit` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `catagory_code` (`catagory_code`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `catagorycode` (`catagory_code`) USING BTREE,
  KEY `datatype` (`datatype`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据资源目录分类信息表';

--
-- Table structure for table `rp_sys_role_dept`
--

DROP TABLE IF EXISTS `rp_sys_role_dept`;
CREATE TABLE `rp_sys_role_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `d_role_id_index` (`role_id`) USING BTREE,
  KEY `d_dept_id_index` (`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色与部门对应关系';

--
-- Table structure for table `rp_sys_role`
--

DROP TABLE IF EXISTS `rp_sys_role`;
CREATE TABLE `rp_sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(100) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '角色名称',
  `remark` varchar(100) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `role_level` int(11) NOT NULL COMMENT '职能级别',
  `index_url` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '角色首页路由',
  `is_sys_admin` int(2) DEFAULT NULL COMMENT '是否是系统管理员 0：否，1：是',
  `is_proj_admin` int(2) DEFAULT NULL COMMENT '是否是项目管理员 0：否 1：是',
  `scope` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '角色使用范围，字符串数组,可为单中心、多中心（数字）',
  `is_input` int(2) NOT NULL DEFAULT '0' COMMENT '是否为录入人员：1是；0否',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `role_name_iunique` (`role_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色';

--
-- Table structure for table `rp_sys_role_menu`
--

DROP TABLE IF EXISTS `rp_sys_role_menu`;
CREATE TABLE `rp_sys_role_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(20) NOT NULL COMMENT ' 角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT ' 菜单权限ID',
  `type` bigint(1) NOT NULL DEFAULT '1' COMMENT '内外网标志位1-内网 2-外网',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `m_role_id_index` (`role_id`) USING BTREE,
  KEY `m_menu_id_index` (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2518 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色权限表';

--
-- Table structure for table `rp_task`
--

DROP TABLE IF EXISTS `rp_task`;
CREATE TABLE `rp_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '代办表主键id',
  `status` int(10) NOT NULL COMMENT '代办状态，0代表任务代办',
  `business_code` varchar(10) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '业务编码',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务对应id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `bus_code` (`business_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='业务代办表';

--
-- Table structure for table `rp_task_related`
--

DROP TABLE IF EXISTS `rp_task_related`;
CREATE TABLE `rp_task_related` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '代办关联表主键id',
  `range_type` int(10) NOT NULL COMMENT '代办任务范围类型',
  `task_id` bigint(20) NOT NULL COMMENT '任务id',
  `related_id` bigint(20) NOT NULL COMMENT '关联代办id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `range_type` (`range_type`) USING BTREE,
  KEY `task_id` (`task_id`) USING BTREE,
  KEY `related_id` (`related_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='业务代办关联表';

--
-- Table structure for table `rp_sys_user_dept`
--

DROP TABLE IF EXISTS `rp_sys_user_dept`;
CREATE TABLE `rp_sys_user_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `dept_id` bigint(20) NOT NULL COMMENT '机构id',
  `dept_code` varchar(50) DEFAULT NULL COMMENT '机构code，冗余个字段',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `user_id_2` (`user_id`,`dept_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户的机构权限';

--
-- Table structure for table `rp_sys_user`
--

DROP TABLE IF EXISTS `rp_sys_user`;
CREATE TABLE `rp_sys_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '用户昵称',
  `salt` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '盐',
  `email` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '手机号',
  `status` tinyint(4) NOT NULL COMMENT '状态  0：禁用   1：正常',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建用户ID',
  `expired_date` date DEFAULT NULL COMMENT '过期时间',
  `login_error_count` int(2) DEFAULT '0' COMMENT '十分钟内连续登录错误次数，十分钟内连续5次登录失败则锁定账号',
  `first_error_login_time` datetime DEFAULT NULL COMMENT '第一次登录错误时间',
  `foreign_id` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '其他系统id',
  `ca_user_id` varchar(100) DEFAULT NULL COMMENT 'CA认证用户ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE,
  KEY `user_dept_id` (`create_time`) USING BTREE,
  KEY `status_index` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统用户';

--
-- Table structure for table `rp_sys_user_his`
--

DROP TABLE IF EXISTS `rp_sys_user_his`;
CREATE TABLE `rp_sys_user_his` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `dept_id` bigint(20) NOT NULL COMMENT '机构id，不同机构的his工号可能不一样',
  `dept_code` varchar(255) NOT NULL COMMENT '机构编码',
  `his_foreign_id` varchar(255) NOT NULL COMMENT 'his工号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `deptID` (`dept_id`) USING BTREE,
  KEY `forign` (`his_foreign_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户his公共关系';

--
-- Table structure for table `rp_sys_user_role`
--

DROP TABLE IF EXISTS `rp_sys_user_role`;
CREATE TABLE `rp_sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id_index` (`user_id`) USING BTREE,
  KEY `role_id_index` (`role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户与角色对应关系';

--
-- Table structure for table `rp_sys_user_token`
--

DROP TABLE IF EXISTS `rp_sys_user_token`;
CREATE TABLE `rp_sys_user_token` (
  `user_id` mediumtext NOT NULL,
  `token` varchar(100) CHARACTER SET utf8mb4 NOT NULL COMMENT 'token',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `login_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `id_card` varchar(50) DEFAULT NULL COMMENT '身份证，用于患者登录',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sub_proj_patient_id` bigint(20) DEFAULT NULL COMMENT '病例ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `token` (`token`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统用户Token';


--
-- Table structure for table `rp_group_config`
--

DROP TABLE IF EXISTS `rp_group_config`;
CREATE TABLE `rp_group_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL COMMENT '项目id',
  `sub_project_id` bigint(20) NOT NULL COMMENT '子项目id',
  `expression` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '表达式',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '描述',
  `sort` int(10) NOT NULL DEFAULT 0 COMMENT '排序 ，默认为1',
  `necessary` int(2) NOT NULL DEFAULT 0 COMMENT '必要与否(0-是；1-否)',
  `type` int(2) NOT NULL DEFAULT 0 COMMENT '条件类型（0-纳入；1-排除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `sort` (`sort`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='纳排入组配置表';


DROP TABLE IF EXISTS `rp_sys_disease`;
CREATE TABLE `rp_sys_disease` (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `disease_name` varchar(100) DEFAULT NULL COMMENT '病种名',
   `dept_id` bigint(20) DEFAULT NULL COMMENT '机构id',
   `create_time` datetime NOT NULL COMMENT '创建时间',
   PRIMARY KEY (`id`) USING BTREE,
   KEY `dept_id` (`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='病种信息表';

-- 
-- Table rp_project
-- 2023/05/22
--
alter table rp_project add COLUMN disease_id int(20) NOT NULL COMMENT  '关联病种id';
alter table rp_project add COLUMN sign_dateline int(5) NOT NULL COMMENT  '签署时间限制';

-- reserach.rp_inbound_process definition

CREATE TABLE `rp_inbound_process` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `sub_project_id` bigint(20) NOT NULL,
  `empiid` varchar(100) NOT NULL,
  `out_reason` varchar(100) DEFAULT NULL,
  `inbound_time` date DEFAULT NULL,
  `signing_time` date DEFAULT NULL,
  `out_time` date DEFAULT NULL COMMENT '退出时间',
  `signing_audit_id` bigint(20) DEFAULT NULL,
  `inbound_id` bigint(20) DEFAULT NULL,
  `status` int(2) NOT NULL DEFAULT 1 COMMENT '受试者状态 2-拟入组 3-已入组 4-异常退出 5-正常退出',
  `sign_expire_days` int(5) NOT NULL COMMENT  '签署时间剩余',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `empiid` (`empiid`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='受试者表';

-- reserach.rp_kelin_config definition

CREATE TABLE `rp_kelin_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `disease_id` bigint(20),
  `expression_id` varchar(256),
  `request_id` varchar(256) NOT NULL,
  `export_id` varchar(256) DEFAULT NULL,
  `group_config_id` bigint(20) NOT NULL,
  `solr_create_time` varchar(100) DEFAULT NULL,
  `create_time` varchar(100) DEFAULT NULL,
  `solr_qure_start_time` varchar(100) DEFAULT NULL,
  `solr_qure_end_time` varchar(100) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `response_status` varchar(100) DEFAULT NULL,
  `message` varchar(100) DEFAULT NULL,
  `total_record` bigint(20) DEFAULT NULL,
  `sub_project_id` bigint(20) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `request_id` (`request_id`) USING BTREE,
  KEY `group_config_id` (`group_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='柯林检索任务表';
-- reserach.rp_enroll_record definition

CREATE TABLE `rp_enroll_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `sub_project_id` bigint(20) NOT NULL,
  `group_config_id` bigint(20) NOT NULL,
  `start_time` varchar(100) NOT NULL,
  `end_time` varchar(100) NOT NULL,
  `status` int(11) NOT NULL,
  `total_count` bigint(20) DEFAULT NULL,
  `success_count` bigint(20) DEFAULT NULL,
  `fail_count` bigint(20) DEFAULT NULL,
  `type` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `group_config_id` (`group_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='执行记录表';


-- ----------------------------
-- Table structure for edc_rdr_table  2023-05-23
-- ----------------------------
DROP TABLE IF EXISTS `rp_rdr_table`;
CREATE TABLE `rp_rdr_table`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cname` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '表单中文名',
  `ename` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '表单英文名',
  `alias` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '表别名',
  `kl_table_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '对照柯林表名',
  `kl_alias` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '柯林表别名',
  `is_show` int(2) NULL DEFAULT 1 COMMENT '是否显示  1：显示；0：不显示',
  `sync_order` int(2) NULL DEFAULT 0 COMMENT '同步顺序 0：基本信息；1：就诊；2：检验、检查等',
  `is_export` int(2) NULL DEFAULT 1 COMMENT '是否查询导出 0：否，1：是',
  `is_modified` int(2) NULL DEFAULT 0 COMMENT '是否修改 0：未修改，1:已修改',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `rdr_tb_ename_index`(`ename`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'rdr表名称对照关系表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for rp_rdr_field  2023-05-23
-- ----------------------------
DROP TABLE IF EXISTS `rp_rdr_field`;
CREATE TABLE `rp_rdr_field`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `table_id` bigint(20) NOT NULL COMMENT '关联edc_rdr_table表主键id',
  `cname` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字段中文名',
  `ename` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '字段英文名',
  `field_explain` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '字段说明',
  `is_union_key` int(2) NULL DEFAULT NULL COMMENT '是否为联合主键 1：是；0：否',
  `type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '字段类型',
  `length` int(2) NULL DEFAULT 0 COMMENT '字段类型',
  `kl_field_name` varchar(300) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '对照柯林表字段名',
  `sequence_no` int(2) NULL DEFAULT 0 COMMENT '排序字段',
  `is_show` int(2) NULL DEFAULT 1 COMMENT '是否显示  1：显示；0：不显示',
  `is_export` int(2) NULL DEFAULT 1 COMMENT '是否查询导出 0：否，1：是',
  `export_level` int(2) NULL DEFAULT 0 COMMENT '导出表头分类：0：无；1：主表头；2：主表头-需合并；3：副表头',
  `export_unique` int(2) NULL DEFAULT 0 COMMENT '导出唯一标识：0：无；1：行唯一标识；2：列唯一标识；',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `table_id`(`table_id`) USING BTREE,
  INDEX `ename`(`ename`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1826 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'rdr字段名称对照关系表' ROW_FORMAT = Dynamic;

--
-- Table rp_kelin_config
-- 2023/05/23
--
alter table rp_kelin_config add COLUMN kl_start_time varchar(255) default '' COMMENT  '任务开始时间';
alter table rp_kelin_config add COLUMN kl_end_time varchar(255) default '' COMMENT  '任务结束时间';
alter table rp_kelin_config add COLUMN notice_status int(2) NOT NULL COMMENT  '通知状态（0-未通知，1-已通知）';
--
-- Table rp_enroll_detail
-- 2023/05/23
-- reserach.rp_enroll_detail definition
CREATE TABLE `rp_enroll_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_id` bigint(20) NOT NULL,
  `sub_project_id` bigint(20) NOT NULL,
  `group_config_id` bigint(20) NOT NULL,
  `record_id` bigint(20) NOT NULL,
  `empi` varchar(100) DEFAULT NULL,
  `visit_id` varchar(100) NOT NULL,
  `visit_date` varchar(100) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `message` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `group_config_id` (`group_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '执行详情表' ROW_FORMAT = Dynamic;



--
-- Table rp_doctor_recommendation
-- 2023/05/23
-- reserach.rp_enroll_detail definition
CREATE TABLE `rp_doctor_recommendation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `recommend_id` bigint(20) NOT NULL COMMENT '推荐医生id',
  `project_id` bigint(20) NOT NULL COMMENT '推荐项目ID',
  `sub_project_id` bigint(20) NOT NULL COMMENT '推荐子项目ID',
  `empiid` varchar(50) NOT NULL COMMENT '推荐的患者Empi',
  `recommend_reason` text COMMENT '推荐理由',
  `recommend_time` date NOT NULL COMMENT '推荐时间',
  `type` int(2) NOT NULL COMMENT '推荐类型 0-系统推荐 1-手动推荐',
  `status` int(2) NOT NULL DEFAULT 1 COMMENT '推荐状态 0-已入组， 1-正常',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE,
  KEY `empiid` (`empiid`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '医生推荐表' ROW_FORMAT = Dynamic;

--
-- Table rp_doctor_recommendation
-- 2023/05/23
-- reserach.rp_enroll_detail definition
CREATE TABLE `rp_patient` (
  `empiid` varchar(20) NOT NULL ,
  `regno` varchar(20) DEFAULT '' COMMENT '患者登记号',
  `name` varchar(20) NOT NULL COMMENT '患者姓名',
  `gender` int(2) NOT NULL COMMENT '性别',
  `birthday` varchar(20) NOT NULL COMMENT '出生日期',
  `id_card` varchar(18) NOT NULL COMMENT '患者身份证号',
  `initials` varchar(20) DEFAULT '' COMMENT '患者姓名缩写',
  `tel` varchar(11) NOT NULL COMMENT '电话号码',
  `status` int(2) NOT NULL DEFAULT 0 COMMENT '推荐状态 0-拟推荐， 1-已推荐， 2-拟入组， 3-已入组， 4-异常退出， 5-正常退出',
  PRIMARY KEY (`empiid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '患者信息表' ROW_FORMAT = Dynamic;

-- 2023/05/29
ALTER TABLE rp_inbound_process CHANGE COLUMN inbound_time inbound_time datetime DEFAULT NULL;
ALTER TABLE rp_inbound_process CHANGE COLUMN signing_time signing_time datetime DEFAULT NULL;
ALTER TABLE rp_inbound_process CHANGE COLUMN out_time out_time datetime DEFAULT NULL;
ALTER TABLE rp_doctor_recommendation CHANGE COLUMN recommend_time recommend_time datetime DEFAULT NULL;

ALTER TABLE rp_project CHANGE COLUMN project_name `project_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '项目名称';
ALTER TABLE rp_sub_project CHANGE COLUMN name name varchar(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '项目名称';

-- 2023/05/30
alter table rp_enroll_detail add COLUMN doctor_name varchar(30) COMMENT  '主治医生';
alter table rp_doctor_recommendation add COLUMN sort int(10) NOT NULL DEFAULT 0 COMMENT  '排序';

-- 2023/05/31
alter table rp_inbound_process add COLUMN doctor_recommend_id bigint(20) NOT NULL DEFAULT 0 COMMENT  '医生推荐表ID';

-- 2023/06/01
ALTER TABLE rp_doctor_recommendation CHANGE COLUMN recommend_time recommend_time varchar(20) DEFAULT NULL;

-- 2023/06/06
alter table rp_project add COLUMN project_type int(2) COMMENT  '项目类型：IIT/GCP';