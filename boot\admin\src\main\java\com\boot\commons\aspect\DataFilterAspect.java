package com.boot.commons.aspect;

import com.boot.commons.annotation.DataFilter;
import com.boot.commons.constants.Const;
import com.boot.commons.exception.BusinessException;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysRoleDeptService;
import com.boot.modules.sys.service.SysUserRoleService;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 数据过滤，切面处理类
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataFilterAspect {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";


    @Resource
    private SysDeptService sysDeptService;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysRoleDeptService sysRoleDeptService;

    @Pointcut("@annotation(com.boot.commons.annotation.DataFilter)")
    public void dataFilterCut() {

    }

    @Before("dataFilterCut()")
    public void dataFilter(JoinPoint point) {
        Object params = point.getArgs()[0];
        if (params != null && params instanceof Map) {

            //用户
            SysUserEntity user = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal());

            if (user != null) {
                //如果不是超级管理员，则进行数据过滤
                if (user.getId() != Const.SUPER_ADMIN) {
                    Map map = (Map) params;
                    map.put(Const.SQL_FILTER, getSQLFilter(user, point));
                }
            }

            return;
        }

        throw new BusinessException("数据权限接口，只能是Map类型参数，且不能为NULL");
    }

    /**
     * 获取数据过滤的SQL
     */
    private String getSQLFilter(SysUserEntity user, JoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        DataFilter dataFilter = signature.getMethod().getAnnotation(DataFilter.class);

        // todo  获取当前登录用户的数据范围权限， 查询全部还是指定组织结构的

        //获取表的别名
        String tableAlias = dataFilter.tableAlias();
        if (StringUtils.isNotBlank(tableAlias)) {
            tableAlias += ".";
        }

        //部门ID列表
        Set<Long> deptIdList = new HashSet<>();

        // 机构科室数据权限 (用户当前登录的系统角色的科室权限及 用户所在科室权限）
//        List<Long> roleIdList = sysUserRoleService.queryRoleIdList(user.getId());
//        //用户角色对应的部门ID列表
//        if(roleIdList.size() > 0){
//            List<Long> userDeptIdList = sysRoleDeptService.queryDeptIdList(roleIdList.toArray(new Long[0]));
//            deptIdList.addAll(userDeptIdList);
//        }

        //用户子部门ID列表
        if (dataFilter.subDept()) {
            List<String> deptIdStringList = Arrays.asList(user.getDeptId().split(","));
            List<Long> curDeptIdList = new ArrayList<>();
            for (String deptIdString : deptIdStringList) {
                deptIdList.add(Long.parseLong(deptIdString));
            }
            List<Long> subDeptIdList = sysDeptService.getSubDeptIdList(curDeptIdList);
            deptIdList.addAll(subDeptIdList);
        }

        StringBuilder sqlFilter = new StringBuilder();
        sqlFilter.append(" (");

        if (deptIdList.size() > 0) {
            sqlFilter.append(tableAlias).append(dataFilter.deptId()).append(" in(").append(StringUtils.join(deptIdList, ",")).append(")");
        }

        //没有本部门数据权限，也能查询本人数据
        if (dataFilter.user()) {
            if (deptIdList.size() > 0) {
                sqlFilter.append(" or ");
            }
            sqlFilter.append(tableAlias).append(dataFilter.userId()).append("=").append(user.getId());
        }

        sqlFilter.append(")");

        if (sqlFilter.toString().trim().equals("()")) {
            return null;
        }

        return sqlFilter.toString();
    }
}
