package com.boot.modules.cas.service;

import com.boot.modules.cas.vo.CasResult;
import com.boot.modules.cas.vo.UserAndRolesVo;

/**
 * 对门户push 过来的方法进行统一处理
 *
 * <AUTHOR>
 */
public interface CasUserPushService {

    /**
     * 对推送来的用户信息数据进行业务处理
     * @param userAndRolesVo 接口指定到数据结构
     * @param loginUserId 登录用户id
     * @return  boolean 操作结构是否成功
     * */
    CasResult doPushUsers(UserAndRolesVo userAndRolesVo, Long loginUserId);

}
