package com.boot.modules.openApi.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.openApi.entity.EdcReqLogEntity;
import com.boot.modules.openApi.service.EdcReqLogService;
import com.boot.modules.openApi.utils.AesUtil;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.service.ProjectService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Api(tags = "执行记录管理")
@RestController
@RequestMapping("/open/api/edc/log")
public class EdcReqLogController {

    @Resource
    private EdcReqLogService reqLogService;

    @Resource
    private ProjectService projectService;

    @Resource
    private InboundProcessService inboundProcessService;

    @PostMapping("/callback")
    public Result callback(@RequestBody Map<String, Object> params) {
        if (!AesUtil.checkSign(params)) {
            return R.fail("校验失败");
        }
        String requestId = MapUtils.getValue(params, "requestId", String.class);

        if (StringUtils.isEmpty(requestId)) {
            return R.fail("未绑定requestId");
        }

        Integer status = MapUtils.getValue(params, "status", Integer.class);

        if (status == null) {
            return R.fail("未制定响应状态");
        }

        if (status == 1) {
            // 更新执行状态
            reqLogService.update(new UpdateWrapper<EdcReqLogEntity>().lambda()
                    .set(EdcReqLogEntity::getStatus, 1)
                    .eq(EdcReqLogEntity::getId, requestId));

            Integer type = MapUtils.getValue(params, "type", Integer.class);
            EdcReqLogEntity logEntity = reqLogService.getById(requestId);
            if (type == 1) {
                //项目类型同步需要更新项目id
                Integer projectId = MapUtils.getValue(params, "projectId", Integer.class);

                if (projectId == null) {
                    return R.fail("未设置成功 edc 项目id");
                }
                if (logEntity != null) {
                    projectService.update(new UpdateWrapper<ProjectEntity>().lambda()
                            //temp
                            .set(ProjectEntity::getEdcProjectId, projectId)
                            .eq(ProjectEntity::getId, logEntity.getProjectId()));
                }
            } else {
                // 更新用户id
                Integer patId = MapUtils.getValue(params, "patId", Integer.class);
                if (patId == null) {
                    return R.fail("未设置成功 edc 入组患者id");
                }
                if (logEntity != null) {
                    inboundProcessService.update(new UpdateWrapper<InboundProcessEntity>().lambda()
                            //temp
                            .set(InboundProcessEntity::getEdcPatId, patId)
                            .eq(InboundProcessEntity::getProjectId, logEntity.getProjectId())
                            .eq(InboundProcessEntity::getEmpiid, logEntity.getEmpiid()));
                }
            }
        } else {
            // 更新执行状态
            String errMessage = MapUtils.getValue(params, "errMessage", String.class);
            reqLogService.update(new UpdateWrapper<EdcReqLogEntity>().lambda()
                    .set(EdcReqLogEntity::getErrMessage, errMessage)
                    .set(EdcReqLogEntity::getStatus, 0)
                    .eq(EdcReqLogEntity::getId, requestId));
        }

        return R.success();
    }
}
