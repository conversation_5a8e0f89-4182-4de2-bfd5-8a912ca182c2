package com.boot.modules.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.project.service.GroupConfigService;
import com.boot.modules.sync.entity.KelinConfigEntity;
import com.boot.modules.sync.service.KelinConfigService;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Api(tags = "纳排配置管理")
@RestController
@RequestMapping("/proj/expression")
public class GroupConfigController extends AbstractController {

    private static ExecutorService executorService = Executors.newFixedThreadPool(10);
    @Resource
    private GroupConfigService groupConfigService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private KelinConfigService kelinConfigService;

    @GetMapping("/list/{subProjectId}/{type}")
    @ApiOperation(
            value = "查询指定项目纳排管理配置列表",
            notes = "查询指定项目纳排管理配置列表")
    public Result list(@PathVariable long subProjectId,
                       @PathVariable int type,
                       @RequestParam (value = "isYlt", required = false) Integer isYlt) {
        if (isYlt == null) {
            //默认情况返回华西的配置
            isYlt = 0;
        }
        return R.success(groupConfigService.listBySubProjId(subProjectId, type, isYlt));
    }

    @GetMapping("/info/{id}")
    @ApiOperation(
            value = "查询指定ID的纳排管理配置",
            notes = "查询指定ID的纳排管理配置")
    public Result info(@PathVariable long id) {

        return R.success(groupConfigService.getById(id));
    }

    @PostMapping("")
    @ApiOperation(
            value = "新增纳排条件",
            notes = "新增纳排条件")
    public Result add(@RequestBody GroupConfigEntity groupConfigEntity) {
//        return groupConfigService.saveConfig(groupConfigEntity) ? R.success() : R.fail();
        return groupConfigService.saveConfig(groupConfigEntity, getUser()) ? R.success() : R.fail();
    }

    @PostMapping("/add/batch")
    @ApiOperation(
            value = "批量新增纳排条件",
            notes = "批量新增纳排条件")
    public Result batchAdd(@RequestBody List<GroupConfigEntity> groupConfigEntitys) {
        return groupConfigService.saveConfigs(groupConfigEntitys, getUser()) ? R.success() : R.fail();
    }

    @PutMapping("")
    @ApiOperation(
            value = "修改纳排条件",
            notes = "修改纳排条件")
    public Result update(@RequestBody GroupConfigEntity groupConfigEntity) {
//        groupConfigService.updateById(groupConfigEntity);
        groupConfigService.updateConfig(groupConfigEntity, getUser());
        return R.success();
    }

    @PutMapping("update/sort/batch")
    @ApiOperation(
            value = "批量修改纳排条件排序",
            notes = "批量修改纳排条件排序")
    public Result updateSort(@RequestBody List<GroupConfigEntity> groupConfigEntityList) {
        groupConfigService.updateSort(groupConfigEntityList);

        return R.success();
    }

    @DeleteMapping("")
    @ApiOperation(
            value = "批量删除纳排条件",
            notes = "批量删除纳排条件")
    public Result deleteBatch(@RequestBody List<Long> idList) {
        //1.删除纳排条件
        groupConfigService.removeByIds(idList);
        //2.删除克林任务
        QueryWrapper<KelinConfigEntity> qw = new QueryWrapper<>();
        qw.lambda().in(KelinConfigEntity::getGroupConfigId, idList);
        //得到纳排对应的柯林任务
        List<KelinConfigEntity> kelinConfigEntityList = kelinConfigService.list(qw);
        List<Long> configIdList = new ArrayList<>();
        //遍历
        for (KelinConfigEntity kelinConfigEntity : kelinConfigEntityList) {
            configIdList.add(kelinConfigEntity.getId());
        }
        //判空删除柯林任务表
        if (!CollectionUtils.isEmpty(configIdList)) {
            configIdList = configIdList.stream().distinct().collect(Collectors.toList());
            kelinConfigService.removeByIds(configIdList);
        }
        return R.success();
    }

    @PostMapping("/exec/{subProjectId}/{userId}")
    @ApiOperation(
            value = "执行配置",
            notes = "执行配置")
    public Result exec(@PathVariable long subProjectId,
                       @PathVariable long userId) {
        SysUserEntity userEntity = sysUserService.getById(userId);
        executorService.execute(() -> {
            groupConfigService.execConfig(subProjectId, userEntity);
        });
        return R.success();
    }
}
