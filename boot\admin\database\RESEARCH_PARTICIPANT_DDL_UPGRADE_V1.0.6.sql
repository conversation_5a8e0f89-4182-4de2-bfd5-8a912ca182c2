CREATE TABLE rp_edc_req_data_log (
	id bigint(20) auto_increment NOT NULL COMMENT 'id',
	url varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '请求url',
	name varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '请求名称',
	project_id bigint(20) NOT NULL COMMENT '项目id',
	`empiid` varchar(100) DEFAULT '' COMMENT '患者empi id',
	`status` int(2) NOT NULL DEFAULT '1' COMMENT '失败：0，成功：1?',
	err_message varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '错误信息',
	CONSTRAINT `PRIMARY` PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8
COLLATE=utf8_general_ci
COMMENT='edc请求错误日志记录表';

alter table rp_project add column `enable_sync` int(2) DEFAULT 0 COMMENT '启用edc同步0:否，1：是';
alter table rp_project add column `edc_project_id` bigint(20) DEFAULT NULL COMMENT 'edc项目id';
-- new
alter table rp_edc_req_data_log add column type int(2) Not NULL COMMENT '同步类型';

-- 2023-10-27
ALTER TABLE rp_doctor_recommendation ADD source int(2) DEFAULT 0 NOT NULL;
ALTER TABLE rp_doctor_recommendation ADD recommend_his_dept_code varchar(100) NULL COMMENT '推荐用户的HIS医疗机构ID';

-- 2024-01-02
ALTER TABLE rp_inbound_process ADD edc_pat_id bigint(20) DEFAULT NULL COMMENT '同步edc患者id';

