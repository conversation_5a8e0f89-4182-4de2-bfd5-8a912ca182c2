package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.sys.entity.SysRoleMenuEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色与菜单对应关系
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysRoleMenuDao extends BaseMapper<SysRoleMenuEntity> {

    /**
     * 根据角色ID数组，批量删除
     */
    int deleteBatch(Long[] roleIds);

    /**
     * 根据条件查询 appId 过滤
     *
     * @param qw
     * @return
     */
    List<SysRoleMenuEntity> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysRoleMenuEntity> qw);

    /**
     * 获取角色在指定应用的权限
     *
     * @param appId
     * @param roleId
     * @return
     */
    List<String> getRolePermission(@Param("appId") Long appId, @Param("roleId") Long roleId);
}
