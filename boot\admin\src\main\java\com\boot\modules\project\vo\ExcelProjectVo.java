package com.boot.modules.project.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目表实体类
 */
@Data
public class ExcelProjectVo implements Serializable {

    /**
     * 项目名称
     */
    @ExcelProperty(index = 0, value = {"项目名称(必填)"})
    private String projectName;

    /**
     * 项目类型 1：单中心 ；2：多中心
     */
    @ExcelProperty(index = 1, value = {"研究中心类型(必填; 1表示单中心,2表示多中心)"})
    private Integer projectCategory;

    /**
     * 项目类型 1：单中心 ；2：多中心
     */
    @ExcelProperty(index = 2, value = {"项目类型(1表示干预性IIT,2表示GCP,3表示前瞻观察性,4表示专病队列)"})
    private Integer projectType;

    /**
     * 所属病种id
     */
    @ExcelProperty(index = 3, value = {"关联病种ID(必填)"})
    private Integer diseaseId;

    /**
     * 主中心id，对应部门机构id
     */
    @ExcelProperty(index = 4, value = {"科室ID(必填)"})
    private Long primaryCenter;

    /**
     * 分中心id，对应部门机构id，分中心可以有多个，只有多中心项目有此
     */
    @ExcelProperty(index = 5, value = {"分中心ID(多中心项目)"})
    private String branchCenter;

    /**
     * 项目管理员ID
     */
    @ExcelProperty(index = 6, value = {"PI(必填)"})
    private Long projectAdminId;


    /**
     * 项目开始时间
     */
    @ExcelProperty(index = 7, value = {"开始时间"})
    private String startDate;

    /**
     * 项目结束时间
     */
    @ExcelProperty(index = 8, value = {"结束时间"})
    private String endDate;

    /**
     * 项目状态 0:未开始 1：启用，2.停用
     */
    @ExcelProperty(index = 9, value = {"项目状态(必填; 0表示立项,1表示在研,2表示结束)"})
    private Integer status;

    /**
     * 知情同意签署时限
     */
    @ExcelProperty(index = 10, value = {"签署知情同意有效期(必填; 单位天)"})
    private Integer signDateline;

    /**
     * 立项年度
     */
    @ExcelProperty(index = 11, value = {"立项年度"})
    private String approvalYear;

    /**
     * 伦理批件号
     */
    @ExcelProperty(index = 12, value = {"伦理批件号"})
    private String approvalNumber;

    /**
     * 项目联系人
     */
    @ExcelProperty(index = 13, value = {"项目联系人"})
    private String contacts;

    /**
     * 项目联系人电话
     */
    @ExcelProperty(index = 14, value = {"项目联系人电话"})
    private String contactsPhone;

    /**
     * 多中心角色
     */
    @ExcelProperty(index = 15, value = {"多中心角色（1表示牵头,0表示参与）"})
    private Integer multicenterType;

    /**
     * 多中心描述
     */
    @ExcelProperty(index = 16, value = {"多中心描述"})
    private String multicenterDescription;

    /**
     * 预计入组量
     */
    @ExcelProperty(index = 17, value = {"预计入组量"})
    private Integer enrollmentsEstimatedCount;

    /**
     * 导入失败
     */
    @ExcelProperty(index = 18, value = {"导入失败信息"})
    private String errorInfo;

    /**
     * 同步到EDC（1表示是,0表示否）
     */
    @ExcelProperty(index = 19, value = {"同步到EDC（1表示是,0表示否）"})
    private Integer enableSync;

    /**
     * 启用审核（1表示启用,0表示不启用）
     */
    @ExcelProperty(index = 20, value = {"启用审核（1表示启用,0表示不启用）"})
    private Integer enableAudit;

    /**
     * 启用图像下载（1表示启用,0表示不启用）
     */
    @ExcelProperty(index = 21, value = {"启用图像下载（1表示启用,0表示不启用）"})
    private Integer enableDownloadImage;

    /**
     * 启用华医通（1表示启用,0表示不启用）
     */
    @ExcelProperty(index = 22, value = {"启用华医通（1表示启用,0表示不启用）"})
    private Integer enableHyt;
}
