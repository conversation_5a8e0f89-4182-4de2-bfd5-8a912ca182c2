package com.boot.modules.project.redis;

import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.RedisUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.project.entity.EnrollDetailEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Component
public class EnrollDetailRedis {
    /**
     * Redis相关键前缀
     */
    public static final String REDIS_PATIENT = "patient_";
    public static final String REDIS_EMPI = "empi_";
    public static final String REDIS_CONFIG = "config_";
    public static final String REDIS_ONEDAY = "oneday_";
    public static final String REDIS_DOCTOR = "doctor_";
    public static final String UNDER_SCORE = "_";

    @Autowired
    private RedisUtils redisUtils;

    public void saveOrUpdateEmpi(EnrollDetailEntity enrollDetailEntity) {
        try {
            if (enrollDetailEntity == null) {
                return;
            }
            String key = REDIS_EMPI + enrollDetailEntity.getEmpi() + UNDER_SCORE + enrollDetailEntity.getVisitId();
            String value = enrollDetailEntity.getGroupConfigId().toString();
            redisUtils.setSet(key, value);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("保存Redis失败");
        }
    }

    public void saveOrUpdateConfig(EnrollDetailEntity enrollDetailEntity) {
        try {
            if (enrollDetailEntity == null) {
                return;
            }
            String key = REDIS_CONFIG + enrollDetailEntity.getSubProjectId() + UNDER_SCORE + enrollDetailEntity.getGroupConfigId();
            String value = enrollDetailEntity.getEmpi();
            redisUtils.setSet(key, value);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("保存Redis失败");
        }
    }

    public void saveOrUpdatePatient(EnrollDetailEntity enrollDetailEntity) {
        try {
            if (enrollDetailEntity == null) {
                return;
            }
            String key = REDIS_PATIENT + enrollDetailEntity.getEmpi();
            String value = enrollDetailEntity.getVisitId() + UNDER_SCORE + enrollDetailEntity.getVisitDate();
            redisUtils.setSet(key, value);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("保存Redis失败");
        }
    }

    public void saveOnedayEmpi(String empi) {
        try {
            if (StringUtils.isEmpty(empi)) {
                return;
            }
            String key = REDIS_ONEDAY + DateUtils.getNowTimeStr();
            String value = empi;
            redisUtils.setSet(key, value);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("保存Redis失败");
        }
    }

    public void saveDoctorEmpi(EnrollDetailEntity enrollDetailEntity) {
        try {
            if (enrollDetailEntity == null) {
                return;
            }
            String key = REDIS_DOCTOR + enrollDetailEntity.getDoctorName();
            String value = enrollDetailEntity.getEmpi();
            redisUtils.setSet(key, value);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("保存Redis失败");
        }
    }

    public void remove(String key, String value) {
        try {
            redisUtils.removeSet(key, value);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("移除Redis失败");
        }
    }

    public void delete(String key) {
        try {
            redisUtils.delete(key);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("删除Redis失败");
        }
    }

    public void deleteBatch(List<String> keyList) {
        try {
            redisUtils.deleteBatch(keyList);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("批量删除Redis失败");
        }
    }

    public List<String> get(String key) {
        try {
            return redisUtils.getSet(key, String.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("查询Redis失败");
        }
    }

    public List<String> uinon(List<String> keyList) {
        try {
            if (CollectionUtils.isEmpty(keyList)) {
                return new ArrayList<>();
            }
            return redisUtils.unionSet(keyList, String.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("查询Redis并集失败");
        }
    }

    public List<String> intersect(List<String> keyList) {
        try {
            if (CollectionUtils.isEmpty(keyList)) {
                return new ArrayList<>();
            }
            return redisUtils.intersectSet(keyList, String.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("查询Redis交集失败");
        }
    }

    public List<String> difference(List<String> keyList) {
        try {
            if (CollectionUtils.isEmpty(keyList)) {
                return new ArrayList<>();
            }
            return redisUtils.differenceSet(keyList, String.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("查询Redis差集失败");
        }
    }

    public List<String> matchkey(String key) {
        try {
            return redisUtils.matchKey(key);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("查询Redis匹配键值失败");
        }
    }
}