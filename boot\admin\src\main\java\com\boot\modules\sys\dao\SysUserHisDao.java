package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserHisEntity;
import com.boot.modules.sys.vo.UserHisForeignVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc :
 * @create 2022-05-05
 */
@Mapper
@CacheNamespace
public interface SysUserHisDao extends BaseMapper<SysUserHisEntity> {
    IPage<UserHisForeignVo> queryPage(IPage<UserHisForeignVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<UserHisForeignVo> qw);

    List<UserHisForeignVo> selectByQuery(@Param(Constants.WRAPPER) QueryWrapper<UserHisForeignVo> qw);

    List<SysUserEntity> getByForeignAndDept(@Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);
}
