package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.constant.CacheConst;
import com.boot.commons.enums.PatientDefaultFieldEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.EhCacheUtil;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.modules.project.dao.ProjectCenterConfigDao;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.entity.ProjectCenterConfigEntity;
import com.boot.modules.project.service.ProjDeptService;
import com.boot.modules.project.service.ProjectCenterConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :项目中心配置
 * @create 2021-09-08
 */
@Service
public class ProjectCenterConfigServiceImpl extends ServiceImpl<ProjectCenterConfigDao, ProjectCenterConfigEntity> implements ProjectCenterConfigService {

    @Resource
    private ProjDeptService projDeptService;


    /**
     * 分页
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        Long centerId = MapUtils.getValue(params, "centerId", Long.class);

        QueryWrapper<ProjectCenterConfigEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(subProjectId != null, ProjectCenterConfigEntity::getSubprojectId, subProjectId)
                .eq(centerId != null, ProjectCenterConfigEntity::getCenterId, centerId);

        IPage<ProjectCenterConfigEntity> pageFilter = new Query<ProjectCenterConfigEntity>().getPage(params);

        IPage<ProjectCenterConfigEntity> page = this.page(pageFilter);

        return new PageUtils(page);
    }

    @Override
    public List<ProjectCenterConfigEntity> getAllDefaultFields(Long subProjectId) {

        List<ProjDeptEntity> allCenter = projDeptService.getBySubProjectId(subProjectId);
        if (CollectionUtils.isEmpty(allCenter)) {
            throw new BusinessException("当前项目没有设置中心");
        }
        List<ProjectCenterConfigEntity> defaultFields = new ArrayList<>();

        for (ProjDeptEntity projDeptEntity : allCenter) {
            for (PatientDefaultFieldEnum defaultFieldEnum : PatientDefaultFieldEnum.values()) {

                ProjectCenterConfigEntity centerConfigEntity = new ProjectCenterConfigEntity();

                centerConfigEntity.setName(defaultFieldEnum.getName());
                centerConfigEntity.setCode(defaultFieldEnum.getKey());
//                if (defaultFieldEnum == PatientDefaultFieldEnum.FILTER_NO) {
//                    centerConfigEntity.setIsShow(0);
//                } else {
//                    centerConfigEntity.setIsShow(1);
//                }
                centerConfigEntity.setIsShow(defaultFieldEnum.getIsShow());
                ;
                centerConfigEntity.setSubprojectId(subProjectId);
                centerConfigEntity.setCenterId(projDeptEntity.getDeptId());
                centerConfigEntity.setSort(defaultFieldEnum.getSort());

                defaultFields.add(centerConfigEntity);
            }
        }

        return defaultFields;
    }


    @Override
    public List<ProjectCenterConfigEntity> getByQuery(Long subprojectId, Long centerId) {

        Object cache = EhCacheUtil.getInstance().get(CacheConst.PROJECT_CACHE_NAME, CacheConst.PROJECT_CENTER_CONFIG_KEY + subprojectId + "_" + centerId);

        List<ProjectCenterConfigEntity> result = new ArrayList<>();
        if (cache != null) {
            result = (List<ProjectCenterConfigEntity>) cache;
        } else {
            result = this.list(
                    new QueryWrapper<ProjectCenterConfigEntity>()
                            .lambda()
                            .eq(ProjectCenterConfigEntity::getSubprojectId, subprojectId)
                            .eq(ProjectCenterConfigEntity::getCenterId, centerId)
            );

            EhCacheUtil.getInstance().put(CacheConst.PROJECT_CACHE_NAME, CacheConst.PROJECT_CENTER_CONFIG_KEY + subprojectId + "_" + centerId, result);
        }

        return result;
    }

    /**
     * 新增 或者 修改中心配置
     *
     * @param configList
     * @return
     */
    @Override
    public boolean addOrUpdate(List<ProjectCenterConfigEntity> configList) {

        boolean res = true;

        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }

        Long subprojectId = configList.get(0).getSubprojectId();
        Long centerId = configList.get(0).getCenterId();

        EhCacheUtil.getInstance().delete(CacheConst.PROJECT_CACHE_NAME, CacheConst.PROJECT_CENTER_CONFIG_KEY + subprojectId + "_" + centerId);

        //判断中心配置有没有数据
        int count = this.count(
                new QueryWrapper<ProjectCenterConfigEntity>()
                        .lambda().eq(ProjectCenterConfigEntity::getSubprojectId, subprojectId)
        );
        if (count <= 0) {
            //多个中心的所有默认字段
            List<ProjectCenterConfigEntity> allDefaultFields = this.getAllDefaultFields(subprojectId);

            List<ProjectCenterConfigEntity> needRemoveDefaultFields = new ArrayList<>();

            a:
            for (ProjectCenterConfigEntity defaultField : allDefaultFields) {
                for (ProjectCenterConfigEntity centerConfigEntity : configList) {

                    if (centerConfigEntity.getCode().equals(defaultField.getCode()) &&
                            centerConfigEntity.getCenterId().equals(defaultField.getCenterId())) {
                        needRemoveDefaultFields.add(defaultField);
                        break a;
                    }
                }
            }
            allDefaultFields.removeAll(needRemoveDefaultFields);

            configList.addAll(allDefaultFields);
        }
        res = this.saveOrUpdateBatch(configList);

        return res;
    }

    /**
     * 在项目创建的时候 自动添加默认字段的配置 并给添加上默认排序
     *
     * @param projDeptList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean autoSaveDefaultFields(Long subProjectId, List<ProjDeptEntity> projDeptList) {

        if (subProjectId == null) {
            throw new BusinessException("请先生成项目");
        }

        if (CollectionUtils.isEmpty(projDeptList)) {
            throw new BusinessException("请给项目分科研中心");
        }
        List<ProjectCenterConfigEntity> defaultFieldsConfig = new ArrayList<>();

        for (ProjDeptEntity projDeptEntity : projDeptList) {
            for (PatientDefaultFieldEnum defaultFieldEnum : PatientDefaultFieldEnum.values()) {

                ProjectCenterConfigEntity centerConfigEntity = new ProjectCenterConfigEntity();

                centerConfigEntity.setName(defaultFieldEnum.getName());
                centerConfigEntity.setCode(defaultFieldEnum.getKey());
                centerConfigEntity.setIsShow(defaultFieldEnum.getIsShow());
                centerConfigEntity.setSubprojectId(subProjectId);
                centerConfigEntity.setCenterId(projDeptEntity.getDeptId());
                centerConfigEntity.setSort(defaultFieldEnum.getSort());

                defaultFieldsConfig.add(centerConfigEntity);
            }
            EhCacheUtil.getInstance().delete(CacheConst.PROJECT_CACHE_NAME, CacheConst.PROJECT_CENTER_CONFIG_KEY + subProjectId + "_" + projDeptEntity.getDeptId());
        }
        boolean res = this.saveBatch(defaultFieldsConfig);

        if (!res) {
            throw new BusinessException("生成默认字段错误");
        }
        return res;
    }
}

