package com.boot.modules.patient.service.Impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.constant.HxConst;
import com.boot.commons.enums.DataPermissionEnum;
import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.excel.model.ReadExcelListener;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.*;
import com.boot.commons.utils.file.FileHelper;
import com.boot.modules.external.service.KeLinService;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.openApi.entity.EdcReqLogEntity;
import com.boot.modules.openApi.service.EdcReqLogService;
import com.boot.modules.openApi.service.EdcService;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dao.InboundProcessDao;
import com.boot.modules.patient.dao.PatientDao;
import com.boot.modules.patient.dto.InboundProcessDto;
import com.boot.modules.patient.dto.PatientDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.entity.PatImportRecordEntity;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatImportRecordService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.patient.vo.DoctorRecommendPatientVo;
import com.boot.modules.patient.vo.InboundPatientVo;
import com.boot.modules.patient.vo.PatientSyncVO;
import com.boot.modules.project.dao.SubProjectDao;
import com.boot.modules.project.entity.*;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.project.redis.EnrollDetailRedis;
import com.boot.modules.project.service.*;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sync.model.KelinSearchResponse;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysRoleService;
import com.boot.modules.sys.service.SysUserService;
import com.sun.jna.platform.unix.solaris.LibKstat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.boot.commons.enums.PatStatusEnum.UNLOCK;
import static com.boot.commons.enums.PatTypeEnum.*;
import static com.boot.commons.utils.StringUtils.getBirthDayFromIdCard;
import static com.boot.commons.utils.StringUtils.getSexFromIdCard;
import static com.boot.modules.project.redis.EnrollDetailRedis.REDIS_CONFIG;

@Service
@Slf4j
public class PatientServiceImpl extends ServiceImpl<PatientDao, PatientEntity> implements PatientService {
    @Resource
    private KeLinService keLinService;

    @Resource
    private SysUserService userService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private GroupConfigService groupConfigService;

    @Resource
    private EnrollDetailRedis enrollDetailRedis;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private DoctorRecommendationDao doctorRecommendationDao;

    @Resource
    private InboundProcessDao inboundProcessDao;

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectRoleSettingService projectRoleSettingService;

    @Resource
    private SysRoleService roleService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private PatImportRecordService patImportRecordService;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private EdcService edcService;

    @Resource
    private EdcReqLogService reqLogService;

    @Resource
    private SubProjectDao subProjectDao;

    @Resource
    private ProjDeptService projDeptService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void intoGroup(List<String> empiList, Map<String, String> empiRegMap, Map<String, String> nationMap) {
        if (CollectionUtils.isEmpty(empiList)) {
            return;
        }
        //记录在推荐池里已到的empi集合
        List<String> reEmpiList = new ArrayList<>();
        //去重empi
        empiList = empiList.stream().distinct().collect(Collectors.toList());
        //查询是否有重复
        QueryWrapper<PatientEntity> qw = new QueryWrapper<>();
        qw.lambda().in(PatientEntity::getEmpiid, empiList);
        List<PatientEntity> patientList = this.list(qw);
        //循环
        for (PatientEntity patient : patientList) {
            reEmpiList.add(patient.getEmpiid());
        }
        //过滤出未到推荐池的empi
        empiList = empiList.stream().filter(o -> !reEmpiList.contains(o)).collect(Collectors.toList());
        //已到推荐池
        if (CollectionUtils.isEmpty(empiList)) {
            return;
        }
        List<Object> medCodeList = new ArrayList<>();
        medCodeList.add(HxConst.HX_DEPT_CODE);
        KelinSearchResponse exportPatDataRes = keLinService.getPatientByEmpiids((empiList.toArray(new String[empiList.size()])), medCodeList);
        if (exportPatDataRes == null) {
            log.error("查询柯林接口异常返回为空");
            return;
        }
        if (exportPatDataRes.getCode() == null || exportPatDataRes.getCode() != HxConst.RESPONSE_SUCCESS) {
            log.error("查询柯林接口异常（通过empi查基本信息数据）--HTTP状态码:{}, 异常原因:{}", exportPatDataRes.getCode(), exportPatDataRes.getData());
            return;
        }
        List<Map<String, Object>> exportPatDataList = exportPatDataRes.getData().getList();
//        List<PatientEntity> patientEntities = new ArrayList<>();
        List<String> dumpEmpiList = new ArrayList<>();
        //遍历
        for (Map<String, Object> pat : exportPatDataList) {
            String empiid = pat.get(HxConst.KELIN_PAT_EMPI_ID_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_EMPI_ID_FIELD).toString();
            if (StringUtils.isEmpty(empiid)) {
                continue;
            }
            // 取患者指定登记号的那行数据
            String curEmpiRegNo = empiRegMap.get(empiid);
            String regno = pat.get(HxConst.KELIN_PAT_REGNO_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_REGNO_FIELD).toString();
            if (!StringUtils.isEmpty(curEmpiRegNo) && !StringUtils.isEmpty(regno) && !curEmpiRegNo.equals(regno)) {
                continue;
            }
            String name = pat.get(HxConst.KELIN_PAT_NAME_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_NAME_FIELD).toString();
            String idCard = pat.get(HxConst.KELIN_PAT_ID_CARD_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_ID_CARD_FIELD).toString();
            String gender = pat.get(HxConst.KELIN_PAT_GENDER_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_GENDER_FIELD).toString();
            String birthday = pat.get(HxConst.KELIN_PAT_BIRTHDAY_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_BIRTHDAY_FIELD).toString().length() >= 11 ? pat.get(HxConst.KELIN_PAT_BIRTHDAY_FIELD).toString().substring(0, 10) : pat.get(HxConst.KELIN_PAT_BIRTHDAY_FIELD).toString();
            String tel = pat.get(HxConst.KELIN_PAT_MOBILE_NO_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_MOBILE_NO_FIELD).toString();
            //设置民族
            String nation = "";
            if (nationMap != null && !nationMap.isEmpty() && nationMap.containsKey(empiid)) {
                //优先设置了民族的从设置的拿，没有则统一从r0获取
                nation = nationMap.get(empiid);
            } else {
                nation = pat.get(HxConst.KELIN_PAT_NATION_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_NATION_FIELD).toString();
            }

            PatientEntity patientEntity = new PatientEntity();
            patientEntity.setRegno(regno);
            patientEntity.setEmpiid(empiid);
            patientEntity.setSrcEmpiid(empiid);
            patientEntity.setName(name);
            patientEntity.setType(0);
            patientEntity.setNation(nation);
            switch (gender) {
                case "男":
                    patientEntity.setGender(0);
                    break;
                case "女":
                    patientEntity.setGender(1);
                    break;
                default:
                    patientEntity.setGender(2);
                    break;
            }
            patientEntity.setBirthday(birthday);
            patientEntity.setIdCard(idCard);
            String initial = "";
            try {
                initial = ChineseCharToEn.convertHanzi2Pinyin(name, false);
            } catch (Exception ex) {
                log.error("姓名转换失败" + name);
            }
            patientEntity.setInitials(initial);
            patientEntity.setTel(tel);
            patientEntity.setStatus(0);
//            patientEntities.add(patientEntity);
            if (dumpEmpiList.contains(patientEntity.getEmpiid())) {
                continue;
            }
            try {
                this.save(patientEntity);
                dumpEmpiList.add(patientEntity.getEmpiid());
            } catch (Exception ex) {
                log.error("病例入组失败" + patientEntity + "///" + ex.getMessage());
                continue;
            }
        }
//        //根据empiid去重
//        patientEntities = patientEntities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>
//                (Comparator.comparing(PatientEntity::getEmpiid))), ArrayList::new));
//        this.saveBatch(patientEntities);
    }

    @Override
    public PageUtils listBySubProjectId(Map<String, Object> params) {
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        String inputQuery = MapUtils.getValue(params, "inputQuery", String.class);
        String isRead = MapUtils.getValue(params, "isRead", String.class);
        Long medId = MapUtils.getValue(params, "medId", Long.class);
        if (status == null) {
            throw new BusinessException("患者状态为空，请重新填写");
        }

        Long subProjectId = Long.parseLong(params.get("subProjectId").toString());

        if (subProjectId == null) {
            throw new BusinessException("子项目id为空，请重新填写");
        }

        // 1.获取对应受试者基础记录
        IPage<InboundProcessDto> result;
        IPage<InboundProcessDto> pageFilter = new Query<InboundProcessDto>().getPage(params);

        // 2.获取对应数据权限
        Long userId = MapUtils.getValue(params, "userId", Long.class);
        SubProjectEntity subProjectEntity = subProjectService.getById(subProjectId);
        DataPermissionEnum permission;

        if (userId.equals(0L) || projectService.getById(subProjectEntity.getProjectId()).getProjectAdminId().equals(userId)) {
            permission = DataPermissionEnum.All_DATA;
        } else {
            List<UserSubProjectEntity> list = userSubProjectService.list(new QueryWrapper<UserSubProjectEntity>().lambda()
                    .eq(UserSubProjectEntity::getUserId, userId)
                    .eq(UserSubProjectEntity::getSubProjectId, subProjectId));

            if (CollectionUtils.isEmpty(list)) {
                throw new BusinessException("当前用户未在本项目中设置角色");
            }

            Long roleId = list.get(0).getProjRoleId();

            List<ProjectRoleSettingEntity> projectRoleSettingEntityList = projectRoleSettingService.list(new QueryWrapper<ProjectRoleSettingEntity>().lambda()
                    .eq(ProjectRoleSettingEntity::getRoleId, roleId)
                    .eq(ProjectRoleSettingEntity::getSubProjectId, subProjectId));

            if (CollectionUtils.isEmpty(projectRoleSettingEntityList)) {
                SysRoleEntity role = roleService.getById(roleId);
                String roleName = role.getRoleName();

                switch (roleName) {
                    case "PI":
                        permission = DataPermissionEnum.All_DATA;
                        break;
                    case "SubI":
                        permission = DataPermissionEnum.OWNER_DEPT;
                        break;
                    default:
                        permission = DataPermissionEnum.OWENER;
                        break;
                }
            } else {
                permission = DataPermissionEnum.getByCode(projectRoleSettingEntityList.get(0).getDataAuth());
            }
        }

        switch (PatTypeEnum.getByCode(status)) {
            // 2.查询推荐列表
            case RECOMMENDED:
                QueryWrapper<InboundProcessDto> qw = new QueryWrapper<InboundProcessDto>()
                        .eq("a.sub_project_id", subProjectId)
                        .eq("a.status", RECOMMENDED.getCode())
                        .eq("b.status", UNLOCK.getCode())
                        .eq(medId != null, "a.top_recommend_dept_id", medId)
                        .and(!StringUtils.isEmpty(inputQuery), orqw -> {
                            orqw.like("b.name", inputQuery).or()
                                    .like("b.regno", inputQuery);
                        })
                        .eq(!StringUtils.isEmpty(isRead), "a.is_read", isRead);
                result = this.baseMapper.listDoctorRecommendationBySubProjectId(pageFilter, qw);
                break;
            // 3.查询预推荐列表
            case PROPOSE_INBOUND:
            case INBOUNDED:
                // 4.查询推荐列表
                qw = new QueryWrapper<InboundProcessDto>()
                        .eq("a.status", status)
                        .eq("a.sub_project_id", subProjectId)
                        .eq(medId != null, "c.top_recommend_dept_id", medId)
                        .and(!StringUtils.isEmpty(inputQuery), orqw -> {
                            orqw.like("b.name", inputQuery).or()
                                    .like("b.regno", inputQuery);
                        })
                        .eq(!StringUtils.isEmpty(isRead), "c.is_read", isRead);

                switch (permission) {
                    case OWENER:
                        qw.eq("a.inbound_id", userId);
                        break;
                    case OWNER_DEPT:
                        List<Long> deptIdList = MapUtils.getValue(params, "deptId", List.class);
                        List<Long> userIdList = userService.getUserIdByDeptId(deptIdList);
                        if (CollectionUtils.isEmpty(userIdList)) {
                            return null;
                        }
                        qw.in("a.inbound_id", userIdList);
                        break;
                    default:
                        break;
                }
                result = this.baseMapper.listInboundProcessBySubProjectId(pageFilter, qw);

                List<InboundProcessDto> records = result.getRecords();

                // 5.获取已入组患者同步信息
                if (!CollectionUtils.isEmpty(records)) {
                    List<String> empiList = records.stream().map(InboundProcessDto::getEmpiid).collect(Collectors.toList());
                    Map<String, EdcReqLogEntity> map = reqLogService.getByEmpiList(records.get(0).getProjectId(), empiList);

                    records.stream().forEach(record -> {
                        if (map.containsKey(record.getEmpiid())) {
                            EdcReqLogEntity edcReqLogEntity = map.get(record.getEmpiid());
                            record.setSyncStatus(edcReqLogEntity.getStatus() == 1 ? 2 : 1);
                            record.setErrorMessage(edcReqLogEntity.getErrMessage());
                        } else {
                            record.setSyncStatus(1);
                        }
                    });
                }
                break;
            // 5.查询退出列表
            case EXIT_ABNORMALLY:
                qw = new QueryWrapper<InboundProcessDto>()
                        .eq("a.sub_project_id", subProjectId)
                        .eq(medId != null, "c.top_recommend_dept_id", medId)
                        .and(orQw -> {
                            orQw.eq("a.status", EXIT_NORMALLY.getCode())
                                    .or().eq("a.status", EXIT_ABNORMALLY.getCode());
                        })
                        .and(!StringUtils.isEmpty(inputQuery), orqw -> {
                            orqw.like("b.name", inputQuery).or()
                                    .like("b.regno", inputQuery);
                        })
                        .eq(!StringUtils.isEmpty(isRead), "c.is_read", isRead);

                result = this.baseMapper.listInboundProcessBySubProjectId(pageFilter, qw);
                break;

            default:
                throw new BusinessException("受试者状态值有误，不存在code为" + status + "的状态");

        }

        List<InboundProcessDto> records = result.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return null;
        }

        // 6.获取所有相关医生名列表
        List<Long> userIdList = new ArrayList<>();
        for (InboundProcessDto inboundProcessDto : records) {
            Long recommendId = inboundProcessDto.getRecommendId();
            if (recommendId != null && !userIdList.contains(recommendId)) {
                userIdList.add(recommendId);
            }

            Long inboundId = inboundProcessDto.getInboundId();
            if (inboundId != null && !userIdList.contains(inboundId)) {
                userIdList.add(inboundId);
            }

            Long signingAuditId = inboundProcessDto.getSigningAuditId();
            if (signingAuditId != null && !userIdList.contains(signingAuditId)) {
                userIdList.add(signingAuditId);
            }
        }

        // 7.获取用户id 用户名对照map
        List<SysUserEntity> sysUserEntities = userService.listByIds(userIdList);
        if (CollectionUtils.isEmpty(sysUserEntities)) {
            return null;
        }

        Map<Long, String> map = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getId, SysUserEntity::getNickname));

        // 8.设置相关医生用户名
        for (InboundProcessDto inboundProcessDto : records) {
            Long recommendId = inboundProcessDto.getRecommendId();

            if (recommendId != null && map.containsKey(recommendId)) {
                inboundProcessDto.setRecommendName(map.get(recommendId));
            }

            Long inboundId = inboundProcessDto.getInboundId();

            if (inboundId != null && map.containsKey(inboundId)) {
                inboundProcessDto.setInboundName(map.get(inboundId));
            }

            Long signingAuditId = inboundProcessDto.getSigningAuditId();
            if (signingAuditId != null && map.containsKey(signingAuditId)) {
                inboundProcessDto.setSigningAuditName(map.get(signingAuditId));
            }
        }

        return new PageUtils(result);
    }


    @Override
    public PageUtils listByUserId(Map<String, Object> params) {
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        //模糊查询条件(登记号、患者姓名、项目名、推荐人名)
        String inputQuery = MapUtils.getValue(params, "query", String.class);
        String isRead = MapUtils.getValue(params, "isRead", String.class);
        //医疗机构ID
        Long medId = MapUtils.getValue(params, "medId", Long.class);
        if (status == null) {
            throw new BusinessException("患者状态为空，请重新填写");
        }

        Long userId = MapUtils.getValue(params, "userId", Long.class);
        List<Long> subProjectIds = new ArrayList<>();
        //获取参与的项目
        if (userId.equals(0L)) {
            List<SubProjectEntity> subProjectEntityList = subProjectService.list();
            if (CollectionUtils.isEmpty(subProjectEntityList)) {
                return null;
            }
            subProjectIds = subProjectEntityList.stream().map(SubProjectEntity::getId).distinct().collect(Collectors.toList());
        } else {
            List<UserSubProjectEntity> list = userSubProjectService.list(new QueryWrapper<UserSubProjectEntity>().lambda()
                    .eq(UserSubProjectEntity::getUserId, userId));
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            subProjectIds = list.stream().map(UserSubProjectEntity::getSubProjectId).distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(subProjectIds)) {
            return null;
        }

        // 1.获取对应受试者基础记录
        IPage<InboundProcessDto> result;
        IPage<InboundProcessDto> pageFilter = new Query<InboundProcessDto>().getPage(params);
        DataPermissionEnum permission = DataPermissionEnum.All_DATA;

        switch (PatTypeEnum.getByCode(status)) {
            // 2.查询推荐列表
            case RECOMMENDED:
                QueryWrapper<InboundProcessDto> qw = new QueryWrapper<InboundProcessDto>()
                        .eq(medId != null, "a.top_recommend_dept_id", medId)
                        .in("a.sub_project_id", subProjectIds)
                        .eq("a.status", RECOMMENDED.getCode())
                        .eq("b.status", UNLOCK.getCode())
                        .and(!StringUtils.isEmpty(inputQuery), orqw -> {
                            orqw.like("b.name", inputQuery).or()
                                    .like("b.regno", inputQuery)
                                    .or()
                                    //推荐医生名
                                    .like("e.nickname", inputQuery)
                                    .or()
                                    //项目名
                                    .like("d.name", inputQuery);
                        })
                        .eq(!StringUtils.isEmpty(isRead), "a.is_read", isRead)
                        .eq("a.type", 2);
                result = this.baseMapper.listDoctorRecommendationMobile(pageFilter, qw);
                break;
            // 3.查询预推荐列表
            case PROPOSE_INBOUND:
            case INBOUNDED:
                // 4.查询推荐列表
                qw = new QueryWrapper<InboundProcessDto>()
                        .eq(medId != null, "c.top_recommend_dept_id", medId)
                        .eq("a.status", status)
                        .in("a.sub_project_id", subProjectIds)
                        .and(!StringUtils.isEmpty(inputQuery), orqw -> {
                            orqw.like("b.name", inputQuery).or()
                                    .like("b.regno", inputQuery)
                                    .or()
                                    //推荐医生名
                                    .like("e.nickname", inputQuery)
                                    .or()
                                    //项目名
                                    .like("d.name", inputQuery);
                        })
                        .eq(!StringUtils.isEmpty(isRead), "c.is_read", isRead);

                switch (permission) {
                    case OWENER:
                        qw.eq("a.inbound_id", userId);
                        break;
                    case OWNER_DEPT:
                        List<Long> deptIdList = MapUtils.getValue(params, "deptId", List.class);
                        List<Long> userIdList = userService.getUserIdByDeptId(deptIdList);
                        if (CollectionUtils.isEmpty(userIdList)) {
                            return null;
                        }
                        qw.in("a.inbound_id", userIdList);
                        break;
                    default:
                        break;
                }
                qw.eq("c.type", 2);
                if (status.equals(PROPOSE_INBOUND.getCode())) {
                    qw.orderByAsc("a.sign_expire_days");
                }
                result = this.baseMapper.listInboundProcessMobile(pageFilter, qw);

                List<InboundProcessDto> records = result.getRecords();

                // 5.获取已入组患者同步信息
                if (!CollectionUtils.isEmpty(records)) {
                    List<String> empiList = records.stream().map(InboundProcessDto::getEmpiid).collect(Collectors.toList());
                    Map<String, EdcReqLogEntity> map = reqLogService.getByEmpiList(records.get(0).getProjectId(), empiList);

                    records.stream().forEach(record -> {
                        if (map.containsKey(record.getEmpiid())) {
                            EdcReqLogEntity edcReqLogEntity = map.get(record.getEmpiid());
                            record.setSyncStatus(edcReqLogEntity.getStatus() == 1 ? 2 : 1);
                            record.setErrorMessage(edcReqLogEntity.getErrMessage());
                        } else {
                            record.setSyncStatus(1);
                        }
                    });
                }
                break;
            // 5.查询退出列表
            case EXIT_ABNORMALLY:
                qw = new QueryWrapper<InboundProcessDto>()
                        .eq(medId != null, "c.top_recommend_dept_id", medId)
                        .in("a.sub_project_id", subProjectIds)
                        .and(orQw -> {
                            orQw.eq("a.status", EXIT_NORMALLY.getCode())
                                    .or().eq("a.status", EXIT_ABNORMALLY.getCode());
                        })
                        .and(!StringUtils.isEmpty(inputQuery), orqw -> {
                            orqw.like("b.name", inputQuery)
                                    .or()
                                    .like("b.regno", inputQuery)
                                    .or()
                                    //推荐医生名
                                    .like("e.nickname", inputQuery)
                                    .or()
                                    //项目名
                                    .like("d.name", inputQuery);
                        })
                        .eq(!StringUtils.isEmpty(isRead), "c.is_read", isRead);
                qw.eq("c.type", 2);
                result = this.baseMapper.listInboundProcessMobile(pageFilter, qw);
                break;

            default:
                throw new BusinessException("受试者状态值有误，不存在code为" + status + "的状态");

        }

        // 6.获取所有相关医生名列表
        List<Long> userIdList = new ArrayList<>();

        List<InboundProcessDto> records = result.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return null;
        }

        for (InboundProcessDto inboundProcessDto : records) {
            Long recommendId = inboundProcessDto.getRecommendId();
            if (recommendId != null && !userIdList.contains(recommendId)) {
                userIdList.add(recommendId);
            }

            Long inboundId = inboundProcessDto.getInboundId();
            if (inboundId != null && !userIdList.contains(inboundId)) {
                userIdList.add(inboundId);
            }

            Long signingAuditId = inboundProcessDto.getSigningAuditId();
            if (signingAuditId != null && !userIdList.contains(signingAuditId)) {
                userIdList.add(signingAuditId);
            }
        }

        // 7.获取用户id 用户名对照map
        List<SysUserEntity> sysUserEntities = userService.listByIds(userIdList);
        if (CollectionUtils.isEmpty(sysUserEntities)) {
            return null;
        }

        Map<Long, String> map = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getId, SysUserEntity::getNickname));

        // 8.设置相关医生用户名
        for (InboundProcessDto inboundProcessDto : records) {
            Long recommendId = inboundProcessDto.getRecommendId();

            if (recommendId != null && map.containsKey(recommendId)) {
                inboundProcessDto.setRecommendName(map.get(recommendId));
            }

            Long inboundId = inboundProcessDto.getInboundId();

            if (inboundId != null && map.containsKey(inboundId)) {
                inboundProcessDto.setInboundName(map.get(inboundId));
            }

            Long signingAuditId = inboundProcessDto.getSigningAuditId();
            if (signingAuditId != null && map.containsKey(signingAuditId)) {
                inboundProcessDto.setSigningAuditName(map.get(signingAuditId));
            }
        }

        return new PageUtils(result);
    }

    @Override
    public PageUtils hisList(Map<String, Object> params) {
        // 1.获取对应受试者基础记录
        IPage<InboundProcessDto> pageFilter = new Query<InboundProcessDto>().getPage(params);

        Integer status = MapUtils.getValue(params, "status", Integer.class);
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        String dateStr = MapUtils.getValue(params, "date", String.class);
        Long hisUserId = MapUtils.getValue(params, "hisUserId", Long.class);
        String name = MapUtils.getValue(params, "name", String.class);

        QueryWrapper<InboundProcessDto> qw = new QueryWrapper<>();

        if (hisUserId != null && hisUserId != 0L) {
            qw.eq("a.recommend_id", hisUserId);
        }

        if (subProjectId != null) {
            qw.eq("a.sub_project_id", subProjectId);
        }

        if (diseaseId != null) {
            qw.eq("c.disease_id", diseaseId);
        }

        if (type != null) {
            qw.eq("a.type", type);
        }

        if (!StringUtils.isEmpty(dateStr)) {
            String startDate = dateStr;
            String endDate = DateUtils.format(DateUtils.addDateDays(DateUtils.getByDateStr(dateStr), 1));

            qw.lt("a.recommend_time", endDate);
            qw.gt("a.recommend_time", startDate);
        }

        if (!StringUtils.isEmpty(name)) {
            qw.and(andQw -> {
                andQw.like("b.name", name).or()
                        .like("b.regno", name);
            });
        }

        // 2.设置受试者状态
        if (status == RECOMMENDED.getCode()) {
            qw.eq("a.status", RECOMMENDED.getCode());
        } else if (status != null) {
            qw.eq("e.status", status);
        }

        IPage<InboundProcessDto> result = this.baseMapper.listDoctorRecommendation(pageFilter, qw);

        // 3.设置受试者状态
        List<InboundProcessDto> records = result.getRecords();

        if (!CollectionUtils.isEmpty(records)) {
            if (status != null) {
                records.forEach(record -> {
                    record.setStatus(status);
                });
            } else {
                records.forEach(record -> {
                    if (record.getStatus() == null) {
                        record.setStatus(RECOMMENDED.getCode());
                    }
                });
            }
        }

        return new PageUtils(result);
    }

    @Override
    public void recommendPatSort(Long subProjectId) {
        // 查所有子项目
        List<SubProjectEntity> subProjectEntityList = subProjectService.list();
        if (CollectionUtils.isEmpty(subProjectEntityList)) {
            return;
        }

        for (SubProjectEntity subProjectEntity : subProjectEntityList) {
            if (!subProjectEntity.getStatus().equals(1)) {
                continue;
            }
            if (subProjectId != null && !subProjectId.equals(subProjectEntity.getId())) {
                continue;
            }
            // 查医生推荐列表
            List<DoctorRecommendationEntity> doctorRecommendationEntityList = doctorRecommendationService.list(new QueryWrapper<DoctorRecommendationEntity>().lambda()
                    .eq(DoctorRecommendationEntity::getSubProjectId, subProjectEntity.getId()));

            List<GroupConfigEntity> includeConfigEntityList = groupConfigService.list(new QueryWrapper<GroupConfigEntity>()
                    .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectEntity.getId())
                    .eq(GroupConfigEntity::getType, 0));
            List<String> necessary = new ArrayList<>();
            List<String> unnecessary = new ArrayList<>();
            for (GroupConfigEntity groupConfigEntity : includeConfigEntityList) {
                String key = REDIS_CONFIG + groupConfigEntity.getSubProjectId() + EnrollDetailRedis.UNDER_SCORE + groupConfigEntity.getId();
                if (groupConfigEntity.getNecessary() == 1) {
                    necessary.add(key);
                } else {
                    unnecessary.add(key);
                }
            }
            // 满足条件组合
            Map<List<String>, Integer> map = new LinkedHashMap<>();
            map.put(necessary, necessary.size()); // 满足所有必要条件

            // 非必要条件全排列
            Set<Set<String>> combinationAll = new HashSet<>();
            for (int c = 1; c <= unnecessary.size(); c++) {
                combinationAll.addAll(CombinationUtil.combination(unnecessary, new ArrayList<>(), c));
            }
            for (Set<String> set : combinationAll) {
                List<String> keys = new ArrayList<String>(set);
                keys.addAll(necessary);
                map.put(keys, keys.size());
            }

            // 计算排序
            for (List<String> keys : map.keySet()) {
                List<String> empiList = enrollDetailRedis.intersect(keys);
                List<DoctorRecommendationEntity> update = new ArrayList<>();
                doctorRecommendationEntityList.forEach(doctorRecommendationEntity -> {
                    if (empiList.contains(doctorRecommendationEntity.getEmpiid())) {
                        doctorRecommendationEntity.setSort(map.get(keys));
                        update.add(doctorRecommendationEntity);
                    }
                });
                if (CollectionUtils.isEmpty(update)) {
                    continue;
                }
                doctorRecommendationService.updateBatchById(doctorRecommendationEntityList);
            }

//            List<GroupConfigEntity> groupConfigEntityList = groupConfigService.list(new QueryWrapper<GroupConfigEntity>()
//                    .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectEntity.getId()));
//            List<String> includes = new ArrayList<>();
//            List<String> excludes = new ArrayList<>();
//            int sort = 1;
//            for (GroupConfigEntity groupConfigEntity : groupConfigEntityList) {
//                String key = REDIS_CONFIG + groupConfigEntity.getSubProjectId() + "_" + groupConfigEntity.getId();
//                if (groupConfigEntity.getType() == 0) {
//                    includes.add(key);
//                } else {
//                    excludes.add(key);
//                }
//                // 查纳入的交集
//                List<String> includeEmpiList = enrollDetailRedis.intersect(includes);
//                // 查排除的并集
//                List<String> excludeEmpiList = enrollDetailRedis.uinon(excludes);
//                // 取差集
//                List<String> empiList = ListUtils.getDifferenceListThanList(includeEmpiList, excludeEmpiList);
//                int finalSort = sort;
//                List<DoctorRecommendationEntity> update = new ArrayList<>();
//                doctorRecommendationEntityList.forEach(doctorRecommendationEntity -> {
//                    if (empiList.contains(doctorRecommendationEntity.getEmpiid())) {
//                        doctorRecommendationEntity.setSort(finalSort);
//                        update.add(doctorRecommendationEntity);
//                    }
//                });
//                if (CollectionUtils.isEmpty(update)) {
//                    continue;
//                }
//                doctorRecommendationService.updateBatchById(doctorRecommendationEntityList);
//                sort += 1;
//            }

        }
    }

    @Override
    public void excelImport(MultipartFile file, PatImportRecordEntity patImportRecordEntity, Long projectId, Long subProjectId) throws IOException {
        List<PatientDto> errInfoList = new ArrayList<>();
        List<PatientDto> list;
        try {
            //解析excel
            ReadExcelListener readExcelListener = new ReadExcelListener(PatientDto.class);
            list = ExcelUtils.readExcel(file, PatientDto.class, readExcelListener);
        } catch (Exception ex) {
            patImportRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            patImportRecordEntity.setStatus(2);
            patImportRecordEntity.setErrorMsg("读取excel文件出错，请检查文件格式");
            patImportRecordService.updateById(patImportRecordEntity);
            return;
        }
        if (CollectionUtils.isEmpty(list)) {
            patImportRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            patImportRecordEntity.setStatus(2);
            patImportRecordEntity.setErrorMsg("读取excel文件出错，请检查文件格式");
            patImportRecordService.updateById(patImportRecordEntity);
            return;
        }
        //1.医疗机构
        List<String> deptNameList = list.stream().map(PatientDto::getDeptName).distinct().collect(Collectors.toList());
        QueryWrapper<SysDeptEntity> qwa = new QueryWrapper<>();
        qwa.lambda().eq(SysDeptEntity::getPid, 0);
        List<SysDeptEntity> deptList = sysDeptService.list(qwa);
        List<Map<String, Object>> patDataList = new ArrayList<>();
        List<PatientDto> errorList = new ArrayList<>();
        for (String deptName : deptNameList) {
            try {
                List<PatientDto> error = new ArrayList<>();
                if (StringUtils.isEmpty(deptName)) {
                    //未填机构
                    error = list.stream().filter(o -> StringUtils.isEmpty(o.getDeptName())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(error)) {
                        errorList.addAll(error);
                    }
                    continue;
                }
                List<SysDeptEntity> sysDeptEntities = deptList.stream().filter(o -> deptName.equals(o.getName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sysDeptEntities)) {
                    //系统内无对照
                    error = list.stream().filter(o -> deptName.equals(o.getDeptName())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(error)) {
                        errorList.addAll(error);
                    }
                    continue;
                }
                String code = sysDeptEntities.get(0).getCode();
                //根据登记号和医疗机构查询
                List<String> regnos = list.stream().filter(o -> deptName.equals(o.getDeptName())).map(PatientDto::getRegno).collect(Collectors.toList());
                KelinSearchResponse kelinSearchResponse = keLinService.getPatientByRegNoAndMed(regnos.toArray(new String[regnos.size()]), code);
                if (kelinSearchResponse == null) {
                    log.error("查询柯林接口异常返回为空");
                    patImportRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                    patImportRecordEntity.setStatus(2);
                    patImportRecordEntity.setErrorMsg("查询柯林接口异常返回为空");
                    patImportRecordService.updateById(patImportRecordEntity);
                    return;
                }
                if (kelinSearchResponse.getCode() == null || kelinSearchResponse.getCode() != HxConst.RESPONSE_SUCCESS) {
                    log.error("查询柯林接口异常（通过登记号和医疗机构查基本信息数据）--HTTP状态码:{}, 异常原因:{}", kelinSearchResponse.getCode(), kelinSearchResponse.getData());
                    patImportRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                    patImportRecordEntity.setStatus(2);
                    patImportRecordEntity.setErrorMsg("查询柯林接口异常（通过登记号和医疗机构查基本信息数据）");
                    patImportRecordService.updateById(patImportRecordEntity);
                    return;
                }
                if (!CollectionUtils.isEmpty(kelinSearchResponse.getData().getList())) {
                    patDataList.addAll(kelinSearchResponse.getData().getList());
                }
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
        List<String> idCards = new ArrayList<>();
        for (Map<String, Object> map : patDataList) {
            if (map.get(HxConst.KELIN_PAT_ID_CARD_FIELD) != null) {
                idCards.add(map.get(HxConst.KELIN_PAT_ID_CARD_FIELD).toString());
            }
        }
        idCards.addAll(list.stream().map(PatientDto::getIdCard).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(idCards)) {
            patImportRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            patImportRecordEntity.setStatus(2);
            patImportRecordEntity.setErrorMsg("有效身份证全为空！");
            patImportRecordService.updateById(patImportRecordEntity);
            return;
        }
        // 查项目信息
        ProjectEntity projectEntity = projectService.getById(projectId);
        // 判断当前项目是否为GCP或IIT项目
        Boolean isLock = inboundProcessService.isLockProject(projectEntity);
        // 已存在的病例信息
        QueryWrapper<PatientEntity> qw = new QueryWrapper<>();
        List<String> empiList = patDataList.stream().map(p -> p.get(HxConst.KELIN_PAT_EMPI_ID_FIELD).toString()).distinct().collect(Collectors.toList());
        List<PatientEntity> existPatientList = this.list(
                new QueryWrapper<PatientEntity>()
                        .lambda().in(!CollectionUtils.isEmpty(empiList), PatientEntity::getEmpiid, empiList)
                        .or().in(!CollectionUtils.isEmpty(idCards), PatientEntity::getIdCard, idCards));

        // 已存在的推荐信息
        List<DoctorRecommendPatientVo> existRecommendList = doctorRecommendationDao.getRecommendPatient(
                new QueryWrapper<DoctorRecommendPatientVo>()
                        .eq("a.status", RECOMMENDED.getCode())
                        .and(qw1 -> {
                            qw1.in(!CollectionUtils.isEmpty(empiList), "a.empiid", empiList)
                                    .or().in(!CollectionUtils.isEmpty(idCards), "b.id_card", idCards);
                        }));

        // 已存在的拟入组、已入组信息
        List<InboundPatientVo> existInboundList = inboundProcessDao.getInboundPatient(
                new QueryWrapper<InboundPatientVo>()
                        .and(qw1 -> {
                            qw1.eq("a.status", PROPOSE_INBOUND.getCode())
                                    .or().eq("a.status", INBOUNDED.getCode());
                        })
                        .and(qw2 -> {
                            qw2.in(!CollectionUtils.isEmpty(empiList), "a.empiid", empiList)
                                    .or().in(!CollectionUtils.isEmpty(idCards), "b.id_card", idCards);
                        })
        );
        // 查所有的GCP、IIT项目
        List<ProjectEntity> lockProjectList = projectService.list(
                new QueryWrapper<ProjectEntity>()
                        .lambda().eq(ProjectEntity::getProjectType, ProjectStudyTypeEnum.GCP.getCode())
                        .or().eq(ProjectEntity::getProjectType, ProjectStudyTypeEnum.INTERVENTION_IIT.getCode()));
        List<Long> lockProjectIds = lockProjectList.stream().map(p -> p.getId()).collect(Collectors.toList());

        List<String> regnoList = new ArrayList<>();
        for (PatientDto patientDto : list) {
            PatientDto errorInfo = new PatientDto();
            errorInfo = patientDto;
            if (regnoList.contains(patientDto.getRegno())) {
                log.error("该患者登记号重复,已导入该患者");
                errorInfo.setErrorInfo("该患者登记号重复,已导入该患者");
                errInfoList.add(errorInfo);
                continue;
            }
            regnoList.add(patientDto.getRegno());
            try {
                if (errorList.contains(patientDto)) {
                    log.error("医疗机构填写错误");
                    errorInfo.setErrorInfo("医疗机构填写错误");
                    errInfoList.add(errorInfo);
                    continue;
                }
                String idcard = patientDto.getIdCard();
                String regno = patientDto.getRegno();
                String finalRegno = regno;
                boolean needVerify = true;
                List<Map<String, Object>> curIdCardInfo = patDataList.stream().filter(p -> p.get(HxConst.KELIN_PAT_REGNO_FIELD).equals(finalRegno)).collect(Collectors.toList());
                if (StringUtils.isEmpty(idcard)) {
                    if (!CollectionUtils.isEmpty(curIdCardInfo)) {
                        idcard = getValidCard(curIdCardInfo);
                        needVerify = false;
                    }
                }
                // 身份证号为空或非法时不导入
                if (StringUtils.isEmpty(idcard)
                ) {
                    log.error("受试者身份证号格式不正确");
                    errorInfo.setErrorInfo("受试者身份证号格式不正确");
                    errInfoList.add(errorInfo);
                    continue;
                } else {
                    String empi = "NF" + IdUtils.getEmpi();// 数据中心未查到的患者
                    String name = patientDto.getName();
                    String tel = "";
                    Integer gender = null;
                    String birthday = "";
                    if (!CollectionUtils.isEmpty(curIdCardInfo)) {
                        if (ObjectUtils.isNotEmpty(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_EMPI_ID_FIELD))) {
                            empi = curIdCardInfo.get(0).get(HxConst.KELIN_PAT_EMPI_ID_FIELD).toString();
                        }
                        if (ObjectUtils.isNotEmpty(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_MOBILE_NO_FIELD))) {
                            tel = curIdCardInfo.get(0).get(HxConst.KELIN_PAT_MOBILE_NO_FIELD).toString();
                        }

                        regno = ObjectUtils.isNotEmpty(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_REGNO_FIELD))
                                ? curIdCardInfo.get(0).get(HxConst.KELIN_PAT_REGNO_FIELD).toString() : regno;
                        name = ObjectUtils.isNotEmpty(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_NAME_FIELD))
                                ? curIdCardInfo.get(0).get(HxConst.KELIN_PAT_NAME_FIELD).toString() : name;

                        String curIdcard = String.valueOf(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_ID_CARD_FIELD));

                        String sex = String.valueOf(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_GENDER_FIELD));

                        birthday = String.valueOf(curIdCardInfo.get(0).get(HxConst.KELIN_PAT_BIRTHDAY_FIELD));
                        switch (sex) {
                            case "男":
                                gender = 0;
                                break;
                            case "女":
                                gender = 1;
                                break;
                            default:
                                gender = 2;
                                break;

                        }
                        //身份证校验不通过取r0的数据
                        if (needVerify && !IdCardUtil.isValidCard(idcard)) {
                            idcard = getValidCard(curIdCardInfo);
                        }
                        // 身份证号或姓名为空时不导入
                        if (StringUtils.isEmpty(idcard)) {
                            log.error("受试者数据中心身份证未能通过校验");
                            errorInfo.setErrorInfo("受试者数据中心身份证未能通过校验");
                            errInfoList.add(errorInfo);
                            continue;
                        }
                    }

                    // 身份证号或姓名为空时不导入
                    if (StringUtils.isEmpty(patientDto.getName())) {
                        log.error("受试者姓名不能为空");
                        errorInfo.setErrorInfo("受试者姓名不能为空");
                        errInfoList.add(errorInfo);
                        continue;
                    }
                    //验证日期合法性
                    String intoDate;
                    try {
                        Date date = DateUtils.deserialize(patientDto.getIntoDate());
                        intoDate = DateUtils.getStr(date);
                    } catch (Exception ex) {
//                        intoDate = DateUtils.getNowTimeStr();
                        log.error("入组日期格式错误" + name);
                        errorInfo.setErrorInfo("入组日期格式错误");
                        errInfoList.add(errorInfo);
                        continue;
                    }

                    //验证日期合法性
                    String signDate;
                    try {
                        Date date = DateUtils.deserialize(patientDto.getSignDate());
                        signDate = DateUtils.getStr(date);
                    } catch (Exception ex) {
                        log.error("签约日期格式错误" + name);
//                        signDate = DateUtils.getNowTimeStr();
                        errorInfo.setErrorInfo("签约日期格式错误");
                        errInfoList.add(errorInfo);
                        continue;
                    }

                    // 是否重复状态，默认为0
                    // 0-无重复：推荐、入组，并修改推荐、入组状态
                    // 1-本项目已推荐：入组，并修改推荐、入组状态
                    // 2-本项目拟入组：修改推荐、入组状态
                    // 3-本项目已签：不处理，提示重复
                    // 4-其他项目拟入组或已签：不处理，提示重复
                    // 判断患者重复性
                    Integer repeatStatus = 0;
                    String finalEmpi = empi;
                    // 本项目的推荐
                    String finalIdcard = idcard;
                    List<DoctorRecommendationEntity> curProjectPatRecommend = existRecommendList.stream().filter(p -> p.getProjectId().equals(projectId)
                            && ((p.getIdCard() != null && p.getIdCard().equals(finalIdcard))
                            || (p.getEmpiid() != null && p.getEmpiid().equals(finalEmpi)))).collect(Collectors.toList());
                    // 本项目拟入组或入组
                    List<InboundProcessEntity> curProjectPatInbound = existInboundList.stream().filter(p -> p.getProjectId().equals(projectId)
                            && ((p.getIdCard() != null && p.getIdCard().equals(finalIdcard))
                            || (p.getEmpiid() != null && p.getEmpiid().equals(finalEmpi)))).collect(Collectors.toList());
                    // GCP或IIT项目拟入组或入组的
                    List<InboundProcessEntity> curPatLockInbound = existInboundList.stream().filter(p -> lockProjectIds.contains(p.getProjectId())
                            && ((p.getIdCard() != null && p.getIdCard().equals(finalIdcard))
                            || (p.getEmpiid() != null && p.getEmpiid().equals(finalEmpi)))).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(curProjectPatRecommend) && CollectionUtils.isEmpty(curProjectPatInbound) && CollectionUtils.isEmpty(curPatLockInbound)) {
                        repeatStatus = 0;
                    } else if (!CollectionUtils.isEmpty(curProjectPatRecommend) && CollectionUtils.isEmpty(curProjectPatInbound) && CollectionUtils.isEmpty(curPatLockInbound)) {
                        repeatStatus = 1;
                    } else if (CollectionUtils.isEmpty(curProjectPatRecommend) && !CollectionUtils.isEmpty(curProjectPatInbound) && CollectionUtils.isEmpty(curPatLockInbound)) {
                        if (curProjectPatInbound.stream().filter(p -> p.getStatus().equals(INBOUNDED.getCode())).count() > 0) {
                            repeatStatus = 3;
                        } else {
                            repeatStatus = 2;
                        }
                    } else if (!CollectionUtils.isEmpty(curPatLockInbound)) {
                        repeatStatus = 4;
                    }

                    List<PatientEntity> curPat = existPatientList.stream().filter(p -> p.getIdCard().equals(finalIdcard)
                            || p.getEmpiid().equals(finalEmpi)).collect(Collectors.toList());
                    PatientEntity patientEntity = CollectionUtils.isEmpty(curPat) ? new PatientEntity() : curPat.get(0);
                    empi = CollectionUtils.isEmpty(curPat) ? empi : curPat.get(0).getEmpiid();
                    switch (repeatStatus) {
                        case 0:
                            // 患者信息
                            // 根据empi、idcard判断病例信息是否已存在，已存在的不需要新增病例记录
                            if (CollectionUtils.isEmpty(curPat)) {
                                patientEntity.setRegno(regno);
                                patientEntity.setEmpiid(empi);
                                patientEntity.setSrcEmpiid(empi);
                                patientEntity.setName(name);
                                patientEntity.setType(1);
                                patientEntity.setIdCard(idcard);
                                patientEntity.setTel(tel);
                                // 从身份证号截取性别
                                if (gender == null) {
                                    gender = getSexFromIdCard(idcard);
                                }
                                if (ObjectUtils.isEmpty(gender)) {
                                    log.error("身份证格式错误" + idcard);
                                    errorInfo.setErrorInfo("身份证格式错误，解析性别异常");
                                    errInfoList.add(errorInfo);
                                    continue;
                                }
                                patientEntity.setGender(gender);

                                // 从身份证号截取出生日期
                                if (StringUtils.isEmpty(birthday)) {
                                    birthday = getBirthDayFromIdCard(idcard);
                                }
                                if (StringUtils.isEmpty(birthday)) {
                                    log.error("身份证格式错误" + idcard);
                                    errorInfo.setErrorInfo("身份证格式错误，解析出生日期异常");
                                    errInfoList.add(errorInfo);
                                    continue;
                                }
                                patientEntity.setBirthday(birthday);
                                String initial = "";
                                try {
                                    initial = ChineseCharToEn.convertHanzi2Pinyin(name, false);
                                } catch (Exception ex) {
                                    log.error("姓名转换失败" + name);
                                }
                                patientEntity.setInitials(initial);
                                patientEntity.setStatus(isLock ? 1 : 0);
                                this.save(patientEntity);
                            } else {
                                if (isLock && patientEntity.getStatus() != 1) {
                                    UpdateWrapper<PatientEntity> updatePat = new UpdateWrapper<PatientEntity>();
                                    updatePat.lambda().eq(PatientEntity::getEmpiid, empi)
                                            .or().eq(PatientEntity::getIdCard, idcard);
                                    updatePat.lambda().set(PatientEntity::getStatus, 1);
                                    this.update(updatePat);
                                }
                            }
                            // 新增医生推荐信息
                            DoctorRecommendationEntity doctorRecommendationEntity = new DoctorRecommendationEntity();
                            doctorRecommendationEntity.setEmpiid(empi);
                            doctorRecommendationEntity.setStatus(INBOUNDED.getCode());
                            doctorRecommendationEntity.setProjectId(projectId);
                            doctorRecommendationEntity.setSubProjectId(subProjectId);
                            doctorRecommendationEntity.setRecommendId(projectEntity.getProjectAdminId());
                            doctorRecommendationEntity.setRecommendReason("批量导入");
                            doctorRecommendationEntity.setRecommendTime(intoDate);
//                            doctorRecommendationEntity.setRecommendTime(StringUtils.isEmpty(patientDto.getIntoDate()) ? "" : patientDto.getIntoDate());
                            doctorRecommendationEntity.setType(1);
                            doctorRecommendationEntity.setRecommendDeptId(projectEntity.getMainDeptId());
                            doctorRecommendationEntity.setSource(1);
                            doctorRecommendationService.save(doctorRecommendationEntity);

                            // 新增受试者入组信息
                            InboundProcessEntity inboundProcessEntity = new InboundProcessEntity();
                            inboundProcessEntity.setEmpiid(empi);
                            inboundProcessEntity.setDoctorRecommendId(doctorRecommendationEntity.getId());
                            inboundProcessEntity.setProjectId(projectId);
                            inboundProcessEntity.setSubProjectId(subProjectId);
                            inboundProcessEntity.setInboundId(projectEntity.getProjectAdminId());
                            inboundProcessEntity.setInboundTime(intoDate);
                            inboundProcessEntity.setStatus(INBOUNDED.getCode());
                            inboundProcessEntity.setSignExpireDays(projectEntity.getSignDateline());
                            inboundProcessEntity.setSigningTime(signDate);
                            inboundProcessEntity.setSigningOpTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                            inboundProcessEntity.setSigningAuditId(projectEntity.getProjectAdminId());
                            inboundProcessService.save(inboundProcessEntity);
                            break;
                        case 1:
                            // 1-本项目已推荐：入组，并修改推荐、入组状态
                            // 更新患者锁定状态
                            if (isLock && patientEntity.getStatus() != 1) {
                                UpdateWrapper<PatientEntity> updatePat = new UpdateWrapper<PatientEntity>();
                                updatePat.lambda().eq(PatientEntity::getEmpiid, empi)
                                        .or().eq(PatientEntity::getIdCard, idcard);
                                updatePat.lambda().set(PatientEntity::getStatus, 1);
                                this.update(updatePat);
                            }

                            // 更新推荐状态
                            Long existRecommendId = curProjectPatRecommend.get(0).getId();
                            UpdateWrapper<DoctorRecommendationEntity> updateWrapper = new UpdateWrapper<>();
                            updateWrapper.lambda().eq(DoctorRecommendationEntity::getId, existRecommendId);
                            updateWrapper.lambda().set(DoctorRecommendationEntity::getStatus, INBOUNDED.getCode());
                            doctorRecommendationService.update(updateWrapper);

                            // 新增受试者入组信息
                            InboundProcessEntity inboundProcessEntity1 = new InboundProcessEntity();
                            inboundProcessEntity1.setEmpiid(empi);
                            inboundProcessEntity1.setDoctorRecommendId(existRecommendId);
                            inboundProcessEntity1.setProjectId(projectId);
                            inboundProcessEntity1.setSubProjectId(subProjectId);
                            inboundProcessEntity1.setInboundId(projectEntity.getProjectAdminId());
                            inboundProcessEntity1.setInboundTime(intoDate);
                            inboundProcessEntity1.setStatus(INBOUNDED.getCode());
                            inboundProcessEntity1.setSignExpireDays(projectEntity.getSignDateline());
                            inboundProcessEntity1.setSigningTime(signDate);
                            inboundProcessEntity1.setSigningOpTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                            inboundProcessEntity1.setSigningAuditId(projectEntity.getProjectAdminId());
                            inboundProcessService.save(inboundProcessEntity1);
                            break;
                        case 2:
                            //2-本项目拟入组：修改推荐、入组状态
                            // 更新患者锁定状态
                            if (isLock && patientEntity.getStatus() != 1) {
                                UpdateWrapper<PatientEntity> updatePat = new UpdateWrapper<PatientEntity>();
                                updatePat.lambda().eq(PatientEntity::getEmpiid, empi)
                                        .or().eq(PatientEntity::getIdCard, idcard);
                                updatePat.lambda().set(PatientEntity::getStatus, 1);
                                this.update(updatePat);
                            }
                            //更新推荐状态
                            Long existRecommendId2 = curProjectPatInbound.get(0).getDoctorRecommendId();
                            UpdateWrapper<DoctorRecommendationEntity> updateWrapper2 = new UpdateWrapper<>();
                            updateWrapper2.lambda().eq(DoctorRecommendationEntity::getId, existRecommendId2);
                            updateWrapper2.lambda().set(DoctorRecommendationEntity::getStatus, INBOUNDED.getCode());
                            doctorRecommendationService.update(updateWrapper2);
                            // 更新入组状态
                            Long existInboundId2 = curProjectPatInbound.get(0).getId();
                            UpdateWrapper<InboundProcessEntity> updateWrapper3 = new UpdateWrapper<>();
                            updateWrapper3.lambda().eq(InboundProcessEntity::getId, existInboundId2);
                            updateWrapper3.lambda().set(InboundProcessEntity::getStatus, INBOUNDED.getCode());
                            updateWrapper3.lambda().set(InboundProcessEntity::getSigningAuditId, projectEntity.getProjectAdminId());
                            updateWrapper3.lambda().set(InboundProcessEntity::getSigningTime, signDate);
                            updateWrapper3.lambda().set(InboundProcessEntity::getSigningOpTime, DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                            inboundProcessService.update(updateWrapper3);
                            break;
                        case 3:
                            log.error("受试者" + name + "已入组本项目且签订了知情同意书");
                            errorInfo.setErrorInfo("受试者" + name + "已入组本项目且签订了知情同意书");
                            errInfoList.add(errorInfo);
                            break;
                        case 4:
                            log.error("受试者" + name + "已参与GCP或干预性IIT项目");
                            errorInfo.setErrorInfo("受试者" + name + "已参与GCP或干预性IIT项目");
                            errInfoList.add(errorInfo);
                            break;
                        default:
                            log.error("受试者" + name + "未导入");
                            errorInfo.setErrorInfo("受试者" + name + "未导入");
                            errInfoList.add(errorInfo);
                            break;
                    }
                }
            } catch (Exception ex) {
                log.error("受试者{}导入失败", patientDto, ex);
                errorInfo.setErrorInfo(ex.getMessage());
                errInfoList.add(errorInfo);
                continue;
            }
        }

        if (!CollectionUtils.isEmpty(errInfoList)) {
            String filePath = generateErrorExcel(errInfoList);
            patImportRecordEntity.setErrorInfoUrl(filePath);
            patImportRecordEntity.setStatus(1);
            patImportRecordEntity.setErrorMsg("部分受试者导入失败");
        } else {
            patImportRecordEntity.setStatus(1);
        }
        patImportRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        patImportRecordService.updateById(patImportRecordEntity);
        return;
    }

    /**
     * 从r0获取正确的身份证号
     *
     * @param infoLists
     * @return
     */
    private String getValidCard(List<Map<String, Object>> infoLists) {
        String idCard = null;
        for (Map<String, Object> map : infoLists) {
            Object o = map.get(HxConst.KELIN_PAT_ID_CARD_FIELD);
            if (ObjectUtils.isEmpty(o)) {
                continue;
            }
            if (IdCardUtil.isValidCard(o.toString())) {
                idCard = o.toString();
                break;
            }
        }
        return idCard;
    }


    /**
     * 将错误信息放入到excel中去
     *
     * @param errInfoList
     * @return
     */
    private String generateErrorExcel(List<PatientDto> errInfoList) throws IOException {
        if (CollectionUtils.isEmpty(errInfoList)) {
            return null;
        }
        try {
            List<List<String>> heads = ExcelUtils.getHeads(PatientDto.class);
            Field[] fields = PatientDto.class.getDeclaredFields();
            Map<String, String> fieldMap = new HashMap<>();
            for (Field field : fields) {
                fieldMap.put(field.getName(), field.getAnnotation(ExcelProperty.class).value()[0]);
            }
            List<Map<String, String>> dataList = JSONObject.parseObject(JSON.toJSONString(errInfoList), ArrayList.class);
            List<Map<String, String>> dataListNew = new ArrayList<>();
            for (Map<String, String> data : dataList) {
                Map<String, String> dataNew = new HashMap<>();
                for (String key : data.keySet()) {
                    dataNew.put(fieldMap.get(key), data.get(key));
                }
                dataListNew.add(dataNew);
            }
            String fileName = IdUtils.getEmpi() + ".xls";
            ExcelUtils.createExcel(FileHelper.getImportErrorInfoPath(), fileName, heads, JSONArray.parseArray(JSON.toJSONString(dataListNew)));
            return fileName;
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
            throw new BusinessException("创建excel失败");
        }
    }

    @Override
    public PageUtils patList(Map<String, Object> params) {
        // 1.获取对应受试者基础记录
        IPage<InboundProcessDto> pageFilter = new Query<InboundProcessDto>().getPage(params);

        Integer status = MapUtils.getValue(params, "status", Integer.class);
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        String startDate = MapUtils.getValue(params, "startDate", String.class);
        String endDate = MapUtils.getValue(params, "endDate", String.class);
        Long userId = MapUtils.getValue(params, "userId", Long.class);
        Long deptId = MapUtils.getValue(params, "deptId", Long.class);
        String name = MapUtils.getValue(params, "name", String.class);
        Long medId = MapUtils.getValue(params, "medId", Long.class);
        String projectName = MapUtils.getValue(params, "projectName", String.class);

        QueryWrapper<InboundProcessDto> qw = new QueryWrapper<>();

        if (userId != null && userId != 0L) {
            qw.eq("a.recommend_id", userId);
        }

        if (deptId != null) {
            qw.eq("a.recommend_dept_id", deptId);
        }

        if (subProjectId != null) {
            qw.eq("a.sub_project_id", subProjectId);
        }

        if (diseaseId != null) {
            qw.eq("c.disease_id", diseaseId);
        }

        if (type != null) {
            qw.eq("a.type", type);
        }

        if (!StringUtils.isEmpty(startDate)) {
            qw.lt("a.recommend_time", endDate);
            qw.gt("a.recommend_time", startDate);
        }

        if (!StringUtils.isEmpty(name)) {
            qw.and(andQw -> {
                andQw.like("b.name", name).or()
                        .like("b.regno", name);
            });
        }

        // 传机构ID
        if (medId != null) {
            qw.eq("a.top_recommend_dept_id", medId);
        }
        if (!StringUtils.isEmpty(projectName)) {
            qw.like("c.project_name", projectName);
        }
        // 2.设置受试者状态
        if (status == RECOMMENDED.getCode()) {
            qw.eq("a.status", RECOMMENDED.getCode());
        } else if (status != null) {
            qw.eq("e.status", status);
        }

        IPage<InboundProcessDto> result = this.baseMapper.listDoctorRecommendation(pageFilter, qw);

        // 3.设置受试者状态
        List<InboundProcessDto> records = result.getRecords();

        if (!CollectionUtils.isEmpty(records)) {
            // 补充CRC用户信息
            List<Long> subProjectIds = records.stream().map(p -> p.getSubProjectId()).collect(Collectors.toList());
            QueryWrapper<SubProjectVo> qwCrc = new QueryWrapper<>();
            qwCrc.in("b.id", subProjectIds);
            qwCrc.eq("d.role_name", "CRC");
            List<SubProjectVo> crc = subProjectDao.getSubProjCRC(qwCrc);
            Map<Long, List<SubProjectVo>> groupCrc = crc.stream().collect(Collectors.groupingBy(o -> o.getId()));
            records.forEach(record -> {
                List<SubProjectVo> curSubProjectCrc = groupCrc.get(record.getSubProjectId());
                if (!CollectionUtils.isEmpty(curSubProjectCrc)) {
                    List<String> crcStrList = new ArrayList<>();
                    for (SubProjectVo one : curSubProjectCrc) {
                        String crcUserName = StringUtils.isEmpty(one.getCrcUserName()) ? "" : one.getCrcUserName();
                        String crcPhoneNumber = StringUtils.isEmpty(one.getCrcPhoneNumber()) ? "" : one.getCrcPhoneNumber();
                        String userInfo = crcUserName + crcPhoneNumber;
                        if (!StringUtils.isEmpty(userInfo)) {
                            crcStrList.add(userInfo);
                        }
                    }
                    record.setCrc(StringUtils.join(crcStrList, ","));
                }
            });

            if (status != null) {
                records.forEach(record -> {
                    record.setStatus(status);
                });
            } else {
                records.forEach(record -> {
                    if (record.getStatus() == null) {
                        record.setStatus(RECOMMENDED.getCode());
                    }
                });
            }

            // 补充推荐医生名称
            List<Long> recommendIds = records.stream().map(p -> p.getRecommendId()).distinct().collect(Collectors.toList());
            List<SysUserEntity> recommendUsers = userService.list(new QueryWrapper<SysUserEntity>()
                    .lambda().in(SysUserEntity::getId, recommendIds));
            Map<Long, SysUserEntity> recommendUserMap = recommendUsers.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            records.forEach(record -> {
                record.setRecommendName(recommendUserMap.get(record.getRecommendId()).getNickname());
            });

            // 补充项目PI名称
            List<String> projectAdminIds = records.stream().map(p -> p.getProjectAdminId()).distinct().collect(Collectors.toList());
            List<SysUserEntity> projectAdmins = userService.list(new QueryWrapper<SysUserEntity>()
                    .lambda().in(SysUserEntity::getId, projectAdminIds));
            Map<Long, SysUserEntity> projectAdminMap = projectAdmins.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            records.forEach(record -> {
                record.setProjectAdminName(projectAdminMap.get(Long.parseLong(record.getProjectAdminId())).getNickname());
            });

            // 补充推荐医生科室
            List<Long> recommendDeptIds = records.stream().map(p -> p.getRecommendDeptId()).distinct().collect(Collectors.toList());
            List<SysDeptEntity> recommendDepts = sysDeptService.list(new QueryWrapper<SysDeptEntity>()
                    .lambda().in(SysDeptEntity::getId, recommendDeptIds));
            Map<Long, SysDeptEntity> recommendDeptMap = recommendDepts.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            records.forEach(record -> {
                record.setRecommendDeptName(recommendDeptMap.get(record.getRecommendDeptId()).getName());
            });

            // 补充临床科室名称
            List<Long> projectIds = records.stream().map(p -> p.getProjectId()).distinct().collect(Collectors.toList());
            List<ProjDeptEntity> projDeptEntities = projDeptService.list(new QueryWrapper<ProjDeptEntity>()
                    .lambda().in(ProjDeptEntity::getProjectId, projectIds).eq(ProjDeptEntity::getIsPrimary, 1));
            Map<Long, ProjDeptEntity> projectMap = projDeptEntities.stream().collect(Collectors.toMap(o -> o.getProjectId(), o -> o));
            List<Long> projectDeptIds = projDeptEntities.stream().map(p -> p.getDeptId()).distinct().collect(Collectors.toList());
            List<SysDeptEntity> projectDepts = sysDeptService.list(new QueryWrapper<SysDeptEntity>()
                    .lambda().in(SysDeptEntity::getId, projectDeptIds));
            Map<Long, SysDeptEntity> projectDeptMap = projectDepts.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
            records.forEach(record -> {
                Long curDeptId = projectMap.get(record.getProjectId()).getDeptId();
                record.setProjectDeptName(projectDeptMap.get(curDeptId).getName());
            });
        }

        return new PageUtils(result);
    }

    @Override
    public void sync(Long projectId, List<String> empiList) {
        // 1.获取项目信息
        projectId = subProjectService.getById(projectId).getProjectId();
        List<ProjectEntity> projectEntityList = projectService.list(new QueryWrapper<ProjectEntity>()
                .lambda().eq(ProjectEntity::getId, projectId)
                .eq(ProjectEntity::getEnableSync, 1));
        if (CollectionUtils.isEmpty(projectEntityList)) {
            throw new BusinessException("该项目未开启 edc 同步， 请开启后重试");
        }
        ProjectEntity projectEntity = projectEntityList.get(0);

        // 2.全量同步患者
        Map<String, String> needAsynPatient = inboundProcessService.listNotSync(projectId, empiList);
        if (needAsynPatient == null) {
            return;
        }

        // 按项目分组
        for (String empi : needAsynPatient.keySet()) {
            edcService.intoPatient(empi, needAsynPatient.get(empi), projectId, projectEntity.getEdcProjectId(), projectEntity.getProjectAdminId());
        }
    }

    @Override
    public void intoGroupByMobile(MobilePatientDTO mobilePatientDTO, String empiid) {
        //查询身份证是否存在，存在则不保存
        //查询是否有重复
        QueryWrapper<PatientEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(PatientEntity::getIdCard, mobilePatientDTO.getIdCard());
        List<PatientEntity> patientList = this.list(qw);
        //已到推荐池
        if (!CollectionUtils.isEmpty(patientList)) {
            log.info("身份证{}已在患者记录表", mobilePatientDTO.getIdCard());
            return;
        }
        PatientEntity entity = new PatientEntity();
        BeanUtils.copyProperties(mobilePatientDTO, entity);
        entity.setEmpiid(empiid);
        entity.setType(0);
        String initial = "";
        try {
            initial = ChineseCharToEn.convertHanzi2Pinyin(mobilePatientDTO.getName(), false);
        } catch (Exception ex) {
            log.error("姓名转换失败" + mobilePatientDTO.getName());
        }
        entity.setInitials(initial);

        // 从身份证号截取出生日期
        String birthday = "";
        try {
            birthday = getBirthDayFromIdCard(mobilePatientDTO.getIdCard());
        } catch (Exception e) {
            throw new BusinessException("身份证获取失败");
        }
        entity.setBirthday(birthday);
        entity.setTel(mobilePatientDTO.getPhone());

        //新加赋值
        entity.setIsNeedSync(1);
        entity.setSrcEmpiid(empiid);

        //重新转下性别，医联体前端和受试者后端用的不一致,医联体 1-男 2-女 其他-其他值
        Integer gender = mobilePatientDTO.getGender();
        if (gender == null) {
            entity.setGender(2);
        } else {
            switch (gender) {
                case 1:
                    //男
                    entity.setGender(0);
                    break;
                case 2:
                    //女
                    entity.setGender(1);
                    break;
                default:
                    entity.setGender(2);
                    break;
            }
        }
        this.save(entity);
    }

    @Override
    public void resetInfo() {
        QueryWrapper<PatientEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(PatientEntity::getIsNeedSync, 1);
        List<PatientEntity> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            log.info("今日无待同步的病例");
            return;
        }
        Map<String, List<PatientEntity>> map = list.stream()
                .collect(Collectors.groupingBy(PatientEntity::getMedCode));
        for (Map.Entry<String, List<PatientEntity>> m : map.entrySet()) {
            String medCode = m.getKey();
            try {
                //一个机构一个机构更新
                //查询科林并更新
                List<PatientEntity> cur = m.getValue();
                if (cur.size() > HxConst.KELIN_PATIENT_FETCH_SIZE) {
                    //分页执行
                    executeByPage(cur, medCode);
                } else {
                    searchAndUpdate(m.getValue(), medCode);
                }
            } catch (Exception e) {
                log.error("请求医疗机构{}信息失败,错误原因", medCode, e);
            }
        }
    }

    private void executeByPage(List<PatientEntity> cur, String medCode) {
        //分批次查询
        int total = cur.size();
        //单批次100条，多了科林查不出来
        int size = HxConst.KELIN_PATIENT_FETCH_SIZE;
        int le = total % size;
        int count = total / size;
        if (le != 0) {
            count = count + 1;
        }
        for (int i = 0; i <= count - 1; i++) {
            try {
                int start = i * size;
                int end = (i + 1) * size;
                if (i == count - 1) {
                    end = total;
                }
                List<PatientEntity> pageList = cur.subList(start, end);
                log.info("单次执行第" + i + "次病案号长度" + pageList.size());
                //批量执行
                searchAndUpdate(pageList, medCode);
            } catch (Exception e) {
                log.error("单次执行错误", e);
            }
        }
    }

    /**
     * 查询科林信息并更新
     *
     * @param patientEntities
     * @param medCode
     */
    private void searchAndUpdate(List<PatientEntity> patientEntities, String medCode) {
        if (CollectionUtils.isEmpty(patientEntities) || StringUtils.isBlank(medCode)) {
            return;
        }
        String[] idCards = patientEntities.stream().map(PatientEntity::getIdCard).toArray(String[]::new);
        String[] medCodes = {medCode};
        //请求科林1接口
        KelinSearchResponse kelinSearchResponse = keLinService.getPatientInfoByIdCard(idCards, medCodes);
        if (kelinSearchResponse == null) {
            log.error("查询柯林接口异常返回为空");
            return;
        }
        if (kelinSearchResponse.getCode() == null || kelinSearchResponse.getCode() != HxConst.RESPONSE_SUCCESS) {
            log.error("查询柯林接口异常（通过身份证和医疗机构查基本信息数据）--HTTP状态码:{}, 异常原因:{}", kelinSearchResponse.getCode(), kelinSearchResponse.getData());
            return;
        }
        List<Map<String, Object>> exportPatDataList = kelinSearchResponse.getData().getList();
        Map<String, PatientSyncVO> mapping = new HashMap<>();
        //遍历
        for (Map<String, Object> pat : exportPatDataList) {
            String empiid = pat.get(HxConst.KELIN_PAT_EMPI_ID_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_EMPI_ID_FIELD).toString();
            if (StringUtils.isEmpty(empiid)) {
                continue;
            }
            String idCard = pat.get(HxConst.KELIN_PAT_ID_CARD_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_ID_CARD_FIELD).toString();
            if (StringUtils.isEmpty(idCard)) {
                continue;
            }
            String regno = pat.get(HxConst.KELIN_PAT_REGNO_FIELD) == null ? "" : pat.get(HxConst.KELIN_PAT_REGNO_FIELD).toString();
            if (StringUtils.isEmpty(regno)) {
                continue;
            }
            PatientSyncVO vo = new PatientSyncVO();
            vo.setRegno(regno);
            vo.setSrcEmpiid(empiid);
            mapping.put(idCard, vo);
        }
        List<PatientEntity> updateList = new ArrayList<>();
        for (PatientEntity patient : patientEntities) {
            String idCard = patient.getIdCard();
            if (StringUtils.isBlank(idCard)) {
                continue;
            }
            PatientSyncVO vo = mapping.get(idCard);
            if (vo == null) {
                return;
            }
            //重置的属性在这设置
            patient.setSrcEmpiid(vo.getSrcEmpiid());
            patient.setRegno(vo.getRegno());
            patient.setIsNeedSync(0);
            updateList.add(patient);
        }
        //更新
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
    }
}

