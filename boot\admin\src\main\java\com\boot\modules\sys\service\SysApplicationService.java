package com.boot.modules.sys.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysApplicationEntity;
import com.boot.modules.sys.entity.SysRoleEntity;

import java.util.Map;


/**
 * 应用
 *
 * <AUTHOR>
 */
public interface SysApplicationService extends IService<SysApplicationEntity> {


	PageUtils queryPage(Map<String, Object> params);
}
