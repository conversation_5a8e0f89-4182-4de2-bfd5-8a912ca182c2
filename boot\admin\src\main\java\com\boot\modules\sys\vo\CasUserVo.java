package com.boot.modules.sys.vo;

import lombok.Data;

import java.util.List;

/**
 * cas同步用户信息vo对象
 * <AUTHOR>
 * @createTime 2021年11月22日 17:54:00
 */
@Data
public class CasUserVo {
    // 登录账号
    private String login_name;
    // 中文名
    private String user_name;
    // his工号
    private String gx_number;
    private String description;
    private String email;
    private String telephone;
    private String address;
    // 角色
    private List<CASRole> partner_roles;

    @Data
    public static class CASRole {
        // 角色id,id为项目id^子项目id^角色id拼接而成
        private String id;
        // 角色名称
        private String label;
    }

}
