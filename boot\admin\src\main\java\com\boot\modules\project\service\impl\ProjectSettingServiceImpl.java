package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.modules.project.dao.ProjectSettingDao;
import com.boot.modules.project.entity.ProjectSettingEntity;
import com.boot.modules.project.service.ProjectSettingService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25
 * @time 17:08
 * @desc 项目流程设置表业务实现类
 **/

@Service
public class ProjectSettingServiceImpl extends ServiceImpl<ProjectSettingDao, ProjectSettingEntity> implements ProjectSettingService {

    @Override
    public ProjectSettingEntity getBySubProjectId(Long subProjectId) {
        List<ProjectSettingEntity> list = this.list(
                new QueryWrapper<ProjectSettingEntity>()
                        .lambda().eq(ProjectSettingEntity::getSubProjectId, subProjectId)
        );
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 新增项目流程设置
     */
    @Override
    public boolean add(ProjectSettingEntity projectSetting) {
        return this.save(projectSetting);
    }
}

