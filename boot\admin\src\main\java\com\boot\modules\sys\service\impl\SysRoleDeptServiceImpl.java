package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.ListUtils;
import com.boot.modules.sys.dao.SysRoleDeptDao;
import com.boot.modules.sys.entity.SysRoleDeptEntity;
import com.boot.modules.sys.entity.SysRoleMenuEntity;
import com.boot.modules.sys.service.SysRoleDeptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 角色与部门对应关系
 *
 * <AUTHOR>
 */
@Service
public class SysRoleDeptServiceImpl extends ServiceImpl<SysRoleDeptDao, SysRoleDeptEntity> implements SysRoleDeptService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(Long roleId, List<Long> deptIdList) {
        List<SysRoleDeptEntity> depts = this.list(new QueryWrapper<SysRoleDeptEntity>().lambda().eq(SysRoleDeptEntity::getRoleId, roleId));
        List<Long> deptIds = depts.stream().map(SysRoleDeptEntity::getDeptId).collect(Collectors.toList());

        //增加的元素组成的列表
        List<Long> aList = ListUtils.getAddListThanList(deptIdList, deptIds);
        List<SysRoleDeptEntity> add = new ArrayList<>();
        for (Long id : aList) {
            SysRoleDeptEntity sysRoleDeptEntity = new SysRoleDeptEntity();
            sysRoleDeptEntity.setDeptId(id);
            sysRoleDeptEntity.setRoleId(roleId);

            add.add(sysRoleDeptEntity);
        }
        if(!CollectionUtils.isEmpty(add)){
            this.saveBatch(add);
        }

        //减少的元素组成的列表
        List<Long> bList = ListUtils.getReduceListThanList(deptIdList, deptIds);
        if(!CollectionUtils.isEmpty(bList)){
            baseMapper.delete(
                    new QueryWrapper<SysRoleDeptEntity>().
                            lambda()
                            .eq(SysRoleDeptEntity::getRoleId, roleId)
                            .in(SysRoleDeptEntity::getDeptId, bList)
            );
        }

    }

    @Override
    public List<Long> queryDeptIdList(Long[] roleIds) {

        return baseMapper.queryDeptIdList(roleIds);
    }

    @Override
    public int deleteBatch(Long[] roleIds) {
        return baseMapper.deleteBatch(roleIds);
    }
}
