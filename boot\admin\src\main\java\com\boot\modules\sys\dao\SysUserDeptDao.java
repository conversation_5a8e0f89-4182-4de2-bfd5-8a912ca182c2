//package com.boot.modules.sys.dao;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.baomidou.mybatisplus.core.toolkit.Constants;
//import com.boot.modules.patient.dto.DepartmentReportDto;
//import com.boot.modules.patient.dto.PersonalReportDto;
//import com.boot.modules.sys.entity.SysUserDeptEntity;
//import com.boot.modules.sys.vo.UserPermissionVo;
//import org.apache.ibatis.annotations.CacheNamespace;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @desc :
// * @create 2022-05-19
// */
//@Mapper
//@CacheNamespace
//public interface SysUserDeptDao extends BaseMapper<SysUserDeptEntity> {
//    List<UserPermissionVo> getUserPermission(@Param(Constants.WRAPPER) QueryWrapper<UserPermissionVo> qw);
//
//    List<DepartmentReportDto> getDeptUserCount(@Param(Constants.WRAPPER) QueryWrapper<DepartmentReportDto> qw);
//
//    List<PersonalReportDto> getAllUserDept();
//}
