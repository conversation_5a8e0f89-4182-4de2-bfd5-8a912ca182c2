package com.boot.modules.openApi.utils;

import cn.hutool.core.codec.Base64;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.StringUtils;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class AesUtil {
    private static String secretKey = StringUtils.isEmpty(BootAdminProperties.rpSecretKey) ? "0CoJUM6Qyw8W8jec" : BootAdminProperties.rpSecretKey;
    private static String ivStr = StringUtils.isEmpty(BootAdminProperties.rpIvStr) ? "DYgjCetcVrj2W9xf" : BootAdminProperties.rpIvStr;
    private final static String CIPHER_MODE_PADDING = "AES/CBC/PKCS5Padding";

    public static String decrypt(String encryptedStr) {
        try {
            //创建AES秘钥
            SecretKeySpec skeySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "AES");
            // 创建密码器
            Cipher cipher = Cipher.getInstance(CIPHER_MODE_PADDING);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivStr.getBytes("UTF-8"));
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivParameterSpec);
            // 解密
            byte[] bytes = Base64.decode(encryptedStr);
            byte[] original = cipher.doFinal(bytes);
            return new String(original);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return null;
    }

    /**
     * AES解密方法,去除偏移量,加密模式和受试者通用的不一致
     * */
    public static String aesDecryptNoIv(String encryptedData, String key) {
        try {
            String CIPHER_MODE_PADDING = "AES/ECB/PKCS5Padding";
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance(CIPHER_MODE_PADDING);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] decryptedBytes = cipher.doFinal(org.apache.commons.codec.binary.Base64.decodeBase64(encryptedData.replaceAll(" ","+")));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public static String encrypt(String content){
        if(StringUtils.isBlank(content)){
            return null;
        }
        if(StringUtils.isNotBlank(secretKey)&&secretKey.length()==16){
            try {
                byte[] bytes = secretKey.getBytes("UTF-8");
                SecretKeySpec keySpec = new SecretKeySpec(bytes,"AES");
                Cipher cipher =Cipher.getInstance(CIPHER_MODE_PADDING);
                IvParameterSpec ivParameterSpec = new IvParameterSpec(ivStr.getBytes("UTF-8"));
                cipher.init(Cipher.ENCRYPT_MODE,keySpec,ivParameterSpec);
                byte[] encrypted = cipher.doFinal(content.getBytes("UTF-8"));
                return Base64Utils.encodeToString(encrypted);
            } catch (Exception ex){
                ex.printStackTrace();
            }
        }
        return null;
    }

    public static boolean checkSign(Map<String, Object> params) {
        String content = MapUtils.getValue(params, "sign", String.class);
        String result = decrypt(content);
        if (StringUtils.isBlank(result)) {
            return false;
        }
        String appId = BootAdminProperties.appId;
        result = result.substring(result.length() - appId.length() , result.length());
        if (StringUtils.isBlank(result)) {
            return false;
        }
        return result.equals(appId);
    }


    /**
     * 固定死加密参数 针对ICO外部调用受试者的校验，ico+时间戳
     * @param sign
     * @return
     */
    public static boolean checkSignForIco(String sign) {
        if (StringUtils.isBlank(sign)) {
            throw new BusinessException("校验参数为空!");
        }
        String result = aesDecryptNoIv(sign, "0CoJUM6Qyw8W8jec");
        if (StringUtils.isBlank(result)) {
            return false;
        }
        String appId = "ico";
        String time = result.substring(appId.length());
        String prefix = result.substring(0, appId.length());
        if (StringUtils.isBlank(time) || StringUtils.isBlank(prefix)) {
            return false;
        }
        return prefix.equals(appId);
    }
}

