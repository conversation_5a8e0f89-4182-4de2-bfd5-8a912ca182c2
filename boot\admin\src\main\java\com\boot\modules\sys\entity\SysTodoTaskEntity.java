package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc :代办任务表对应entity对象
 * @create 2021-05-13
 */
@Data
@Accessors(chain = true)
@TableName("rp_task")
public class SysTodoTaskEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 状态, 0 待处理
     */
    @NotBlank(message = "状态不许为空", groups = {AddGroup.class})
    private Integer status;
    /**
     * 业务编码
     */
    @NotBlank(message = "状态不许为空", groups = {AddGroup.class})
    private String businessCode;

    /**
     * 对应业务的ID
     */
    private Long businessId;
}
