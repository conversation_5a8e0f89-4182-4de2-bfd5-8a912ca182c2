package com.boot.commons.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Component
@Getter
@Setter
@ConfigurationProperties("sso")
public class SSOConfigurationProperties {
    /**
     * 是否开启单点登录
     */
    private Boolean enable = false;

    /**
     * 单点登录医院简称，默认为default，不同医院使用的单点登录模式可能不同
     * default: oauth2
     * hzy: 湖南中医附院 创星平台
     */
    private String hospital = "default";

    /**
     * 单点登录类型
     */
    private String type;
    /**
     * 单点登录请求地址
     */
    private String serverLoginUrl = "http://hxdmc.cn/cas/login";
    /**
     * 单点登出请求地址
     */
    private String serverLogoutUrl = "http://hxdmc.cn/cas/logout";
    /**
     * 当前服务器地址
     */
    private String clientHostUrl = "";
    /**
     * 前端服务主页地址
     */
    private String frontIndexUrl;
    /**
     * 移动端登录页面
     */
    private String appLoginUrl;
    /**
     * 移动端主页
     */
    private String appIndexUrl;

    /**
     * 华西医院 门户appCode
     */
    private String appCode = "ESC";

    /**
     * cas 服务器跟Url
     */
    private String serverLoginUrlPrefix = "http://hxdmc.cn/cas";

    /**
     * 后端代理标识 (华西统一门户NGINX中维护的EDC后端代理的标识)
     */
    private String backendProxySign;

    /**
     * 后端主页，后端重定向接口地址
     */
    private String backendRedirectUrl = "http://hxdmc.cn/research/sso/home";

    /**
     * 前端登录地址
     */
    private String frontLoginUrl = "http://hxdmc.cn/research-web/#/login";
}
