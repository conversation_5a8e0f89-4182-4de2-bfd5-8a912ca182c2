package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysIpFilterEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

public interface SysIpFilterService extends IService<SysIpFilterEntity> {

    Boolean isWhiteList(String ip);

    PageUtils queryPage(Map<String, Object> params);
}
