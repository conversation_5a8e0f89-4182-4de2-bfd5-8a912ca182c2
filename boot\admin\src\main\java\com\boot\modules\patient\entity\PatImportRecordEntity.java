package com.boot.modules.patient.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


/**
 * 病例导入记录
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_pat_import_record")
public class PatImportRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId
	private Long id;

	/**
	 * 关联子项目id
	 */
	private Long subProjectId;

	/**
	 * 导入状态。0：导入进行中，1：导出成功，2：导入失败
	 */
	private Integer status;

	/**
	 * 错误信息
	 */
	private String errorMsg;

	/**
	 * 详细信息
	 */
	private String errorInfoUrl;

	/**
	 * 操作用户ID
	 */
	private Long userId;

	/**
	 * 开始时间
	 */
	private String startTime;

	/**
	 * 结束时间
	 */
	private String endTime;

	@TableField(exist = false)
	private String userName;

	/**
	 * 操作用户
	 */
	@TableField(exist = false)
	private String nickName;
}
