package com.boot.modules.patient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.patient.dto.ExitDto;
import com.boot.modules.patient.dto.PersonalReportDto;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.vo.InboundProjectDieaseVo;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.project.vo.TotalProjectVo;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface InboundProcessService extends IService<InboundProcessEntity> {

    /**
     * 受试者入组
     *
     * @param empi
     * @param subProjectId
     * @param userId
     */
    void inbound(Long id, String empi, Long projectId, Long subProjectId, Long userId);

    /**
     * 签署知情同意书
     *
     * @param empi
     * @param subProjectId
     * @param userId
     */
    void sign(Long id, String empi, Long subProjectId, String date, Long userId);

    /**
     * 受试者退出
     *
     * @param exitDto
     */
    void exit(ExitDto exitDto);

    /**
     * 受试者退回
     *
     * @param exitDto
     */
    void revert(ExitDto exitDto);


    /**
     * 受试者驳回
     * @param exitDto
     */
    void refuse(ExitDto exitDto);

    /**
     * 受试者已入组
     *
     * @return
     */
    List<String> getEmpi();

    /**
     * 本项目受试者已入组
     *
     * @return
     */
    List<String> getEmpiByProjectId(Long subProjectId);

    /**
     * 清理过期未签署同意书患者
     */
    void expiredPatClean() throws ParseException;

    /**
     * 统计入组量
     *
     * @param date
     * @return
     */
    List<PersonalReportDto> realityPersonalData(String date);

//    /**
//     * 统计科室入组量
//     *
//     * @param date
//     * @return
//     */
//    List<DepartmentReportDto> realityDepartmentData(String date);

    /**
     * 病例已入组项目
     *
     * @param qw
     * @return
     */
    List<InboundProjectDieaseVo> getInboundProject(String empi, Boolean isLock);

    /**
     * 设置科室相关推荐数
     *
     * @param result
     * @param startTime
     * @param endTime
     */
    void setInboundCount(List<ReportDto> result, Date startTime, Date endTime);

    /**
     * 设置总入组量统计
     * @param reportDto
     * @param startTime
     * @param endTime
     */
    void setTotalInboundCount(ReportDto reportDto, Date startTime, Date endTime);

    /**
     * 设置项目相关推荐数
     *
     * @param records
     */
    void setProjectInboundCount(List<ProjectVo> records, Date startTime, Date endTime);

    /**
     * 获取按项目类型配置的入组数
     * @param projectIdList 项目id列表
     * @return
     */
    Map<String, TotalProjectVo> groupInboundCountByType(List<Long> projectIdList, Date startTime, Date endTime);

    /**
     * 设置医生相关推荐数
     *
     * @param result
     * @param startTime
     * @param endTime
     */
    void setPerInboundCount(List<ReportDto> result, Date startTime, Date endTime);

    boolean isLockProject(ProjectEntity projectEntity);

    /**
     * 获取未同步患者 empi 列表
     * @param projectId
     */
    Map<String,String> listNotSync(Long projectId, List<String> empiList);
}
