package com.boot.modules.sys.controller;


import com.boot.commons.annotation.SysLog;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.constant.CacheConst;
import com.boot.commons.constants.Const;
import com.boot.commons.enums.LogTypeEnum;
import com.boot.commons.enums.UserStatusEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.socket.WSMessageService;
import com.boot.commons.utils.ClientInfoUtil;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.EhCacheUtil;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.sys.dto.PassWordChangeDto;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import com.boot.modules.sys.form.SysLoginForm;
import com.boot.modules.sys.service.MailService;
import com.boot.modules.sys.service.SysCaptchaService;
import com.boot.modules.sys.service.SysUserService;
import com.boot.modules.sys.service.SysUserTokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;

/**
 * 登录相关
 *
 * <AUTHOR>
 */
@Api(tags = "登录相关")
@Controller
public class SysLoginController extends AbstractController {
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserTokenService sysUserTokenService;
    @Resource
    private SysCaptchaService sysCaptchaService;
    @Resource
    private WSMessageService wsMessageService;
    @Resource
    private MailService mailService;

    @Resource
    private SSOConfigurationProperties ssoConfigurationProperties;

    private boolean loginType = BootAdminProperties.userLgoin;

    /**
     * 获取验证码
     *
     * @param response HttpServletResponse
     * @throws IOException 异常
     */
    @ApiOperation(
            value = "获取验证码",
            notes = "获取登录验证码")
    @GetMapping("captcha.jpg")
    public void captcha(HttpServletResponse response, String uuid) throws IOException {
        response.setHeader("Cache-Control", "no-store, no-cache");
        response.setContentType("image/jpeg");

        if (BootAdminProperties.captcha) {
            //获取图片验证码
            BufferedImage image = sysCaptchaService.getCaptcha(uuid);

            ServletOutputStream out = response.getOutputStream();
            ImageIO.write(image, "jpg", out);
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * 判断是否已登录
     */
    @ApiOperation(
            value = "验证用户信息以及初始判断是否已登录过",
            notes = "验证用户信息以及初始判断是否已登录过")
    @SysLog(value = "用户登录", type = LogTypeEnum.LOGIN)
    @PostMapping("/sys/login")
    @ResponseBody
    public Result login(@RequestBody SysLoginForm form, HttpServletRequest request) throws ParseException {
        Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        if (assertion != null) {
            String name = assertion.getPrincipal().getName();
        }
        //当前登录ip地址和时间
        String loginIp = request.getRemoteAddr();
        //定义token相关信息

        if (SecurityUtils.getSubject() != null) {
            SecurityUtils.getSubject().logout();
        }

        if (BootAdminProperties.captcha) {
            boolean captcha = sysCaptchaService.validate(form.getUuid(), form.getCaptcha());
            if (!captcha) {
                return R.fail("验证码不正确");
            }
        }
        //用户信息
        SysUserEntity user = null;

        if (BootAdminProperties.adminAccount.equals(form.getUsername())) {
            // 超级管理员

            //检验超级管理员的IP登录限制
            adminLoginIpCheck(request);

            user = new SysUserEntity();
            user.setId(Const.SUPER_ADMIN);
            user.setUsername(form.getUsername());
            user.setNickname("超级管理员");
            user.setIsSuperAdmin(1);
            user.setIsSysAdmin(0);
            String pwd = new Sha256Hash(form.getPassword(), BootAdminProperties.salt).toHex();
            if (!pwd.equals(BootAdminProperties.adminPassword)) {
                return R.fail("账号或密码不正确");
            }
        } else {
            user = sysUserService.queryByUserName(form.getUsername());
            if (user == null || user.getId() == null) {
                return R.fail("账号不存在");
            } else {
                //过期时间,判断是否过了过期时间
                Date expireDate = user.getExpiredDate();
                if (expireDate != null && expireDate.before(new Date())) {
                    return R.fail("账号已过期");
                }

                if (CollectionUtils.isEmpty(user.getRoleNameList())) {
                    return R.fail("账号未分配系统角色，无法登录");
                }

                // 非his界面登录，需要验证密码正确性
                if (form.getIsHis() == null || form.getIsHis().equals("0")) {
                    String curPassword = new Sha256Hash(form.getPassword(), user.getSalt()).toHex();
                    //账号不存在、密码错误
                    if (!user.getPassword().equals(curPassword)) {

                        Map<String, Object> map = passwordErrorCheck(user);
                        if (map != null) {
                            SysUserEntity newUser = (SysUserEntity) map.get("user");
                            sysUserService.updateById(newUser);

                            String errorMessage = (String) map.get("errorMessage");
                            return R.fail(errorMessage);
                        }

                        return R.fail("账号或密码不正确");
                    }
                }
                //账号锁定
                if (user.getStatus() == 0) {
                    return R.fail("账号已被锁定,请联系管理员");
                }
            }
        }
        return loginCheck(user, loginIp);
    }

    /**
     * 登录
     */
    @ApiOperation(
            value = "登录",
            notes = "登录")
    @SysLog(value = "用户登录", type = LogTypeEnum.LOGIN)
    @PostMapping("/sys/loginafter")
    @ResponseBody
    @CrossOrigin
    public Result loginAfter(@RequestBody SysLoginForm form, HttpServletRequest request) {
        //获取登录IP地址
        String loginIp = request.getRemoteAddr();
        //用户信息
        SysUserEntity user = null;
        String message = "抱歉，您的账号已在" + loginIp + "登录，您已下线";
        if (BootAdminProperties.adminAccount.equals(form.getUsername())) {
            // 超级管理员
            user = new SysUserEntity();
            user.setId(Const.SUPER_ADMIN);
            user.setUsername(form.getUsername());
            user.setNickname("超级管理员");
            wsMessageService.sendToAllTerminal(Const.SUPER_ADMIN, message);
        } else {
            user = sysUserService.queryByUserName(form.getUsername());
            wsMessageService.sendToAllTerminal(user.getId(), message);
        }
        //生成token，并保存到数据库
        Result r = sysUserTokenService.createToken(user.getId(), loginIp);
        user.setToken(r.getToken());
        // 将用户信息返回前端
        r.setData(user);

        return r;
    }

    /**
     * 退出
     */
    @SysLog(value = "退出登录", type = LogTypeEnum.LOGIN)
    @PostMapping("/sys/logout")
    @ResponseBody
    public void logout(HttpServletResponse response) throws IOException {
        //清理HIS缓存
        EhCacheUtil.getInstance().delete(CacheConst.CACHE_HIS_USER_KEY, getUserId().toString());
        //清理HYT缓存
        EhCacheUtil.getInstance().delete(CacheConst.CACHE_HYT_USER_KEY, getUserId().toString());
        sysUserTokenService.logout(getUserId());
        if (SecurityUtils.getSubject() != null) {
            SecurityUtils.getSubject().logout();
        }
        response.sendRedirect(ssoConfigurationProperties.getServerLogoutUrl());
        return ;
    }


    @SysLog(value = "获取加密后的密码")
    @GetMapping("/sys/encrypted/{password}")
    @ResponseBody
    public Result encryption(@PathVariable String password) {
        String pwd = new Sha256Hash(password, BootAdminProperties.salt).toHex();
        return R.success(pwd);
    }

    @SysLog(value = "找回密码获取验证码")
    @PostMapping("/sys/verifyCode")
    @ResponseBody
    public Result verifyCode(@RequestBody PassWordChangeDto wordChangeDto) {
        mailService.sendMail(wordChangeDto);
        return R.success("邮件发送成功，请注意查收");
    }

    @SysLog(value = "找回密码，修改密码")
    @PostMapping("/sys/change")
    @ResponseBody
    public Result change(@RequestBody PassWordChangeDto wordChangeDto) {

        Boolean flag = sysUserService.changePwd(wordChangeDto);

        return flag ? R.success("修改密码成功", true) : R.fail("修改密码失败");
    }

    @SysLog(value = "检查输入验证码是否一致")
    @PostMapping("/sys/verification")
    @ResponseBody
    public Result verification(@RequestBody PassWordChangeDto wordChangeDto) {
        String username = wordChangeDto.getUsername();
        String code = wordChangeDto.getCode();
        String verifyCode = (String) EhCacheUtil.getInstance().get("codeCache", username);
        if (verifyCode == null) {
            throw new BusinessException("验证码已超时，请重新获取！");
        }
        if (!code.equals(verifyCode)) {
            return R.fail("验证码不正确！");
        }
        return R.success("验证码正确", true);

    }


    /**
     * 判断用户是否已登录方法提取
     */
    private Result loginCheck(SysUserEntity user, String loginIp) {
        //获取当前时间，定义判断已登录用户code为001
//        SysUserTokenEntity userToken = sysUserTokenService.getById(user.getId());
        // modify by cx 判断是否为PAD端登录
        String userId = ClientInfoUtil.mobileType() + user.getId();
        List<SysUserTokenEntity> userTokenList = sysUserTokenService.getByUserId(userId, null);
        SysUserTokenEntity userToken = CollectionUtils.isEmpty(userTokenList) ? null : userTokenList.get(0);
        if (userToken == null) {
            // 第一次登录
            Result result = sysUserTokenService.createToken(user.getId(), loginIp);
            if (result == null) {
                return R.fail("生成token失败，请重新登录");
            }
            user.setToken(result.getToken());
            // 将用户信息返回前端
            result.setData(user);
            return result;
        }

        String userIp = userToken.getLoginIp();
        Date expireTime = userToken.getExpireTime();
        Date nowTime = new Date();

        if (loginType) {
            if (!nowTime.before(expireTime)) {
                Result result = sysUserTokenService.createToken(user.getId(), loginIp);
                user.setToken(result.getToken());
                // 将用户信息返回前端
                result.setData(user);
                return result;
            }
            return allowLogin(userToken, user, loginIp);
        } else {
            if (!loginIp.equals(userIp)) {
                //生成token，并保存到数据库
                Result result = sysUserTokenService.createToken(user.getId(), loginIp);
                if (result == null) {
                    return R.fail("生成token失败，请重新登录");
                }
                user.setToken(result.getToken());
                // 将用户信息返回前端
                result.setData(user);
                return result;
            }
        }

        return allowLogin(userToken, user, loginIp);

    }

    /**
     * 允许多人同时在线登录
     *
     * @param userToken
     * @param user
     * @param loginIp
     * @return
     */
    private Result allowLogin(SysUserTokenEntity userToken, SysUserEntity user, String loginIp) {
        Date nowTime = new Date();
        userToken.setExpireTime(new Date(nowTime.getTime() + (3600 * 12 * 1000)));
        userToken.setLoginIp(loginIp);
        boolean flag = sysUserTokenService.updateToken(userToken);
        if (user.getLoginErrorCount() != null && user.getLoginErrorCount() != 0) {
            user.setLoginErrorCount(0);
            flag = flag && sysUserService.updateById(user);
        }
        if (flag) {
            user.setToken(userToken.getToken());
            Result result = R.success();
            result.setData(user);
            result.setToken(userToken.getToken());
            result.setExpire(3600 * 12);
            return result;
        }
        return R.fail("数据异常，请稍后重试");
    }

    /**
     * 密码错误限制
     * 此方法的前提是
     *
     * @param curUser
     */
    private Map<String, Object> passwordErrorCheck(SysUserEntity curUser) throws ParseException {

        boolean enabled = BootAdminProperties.loginPasswordCheckEnable;
        Map<String, Object> result = new HashMap<>();
        if (enabled) {
            String errorMessage = "";

            //在某个时间范围内连续登录错误，则锁定   分钟未单位
            Integer rangeTime = BootAdminProperties.loginPasswordCheckTime;
            //连续错误次数
            Integer errorCount = BootAdminProperties.loginPasswordCheckCount;

            //获取是否被锁定的状态
            int isLocked = 0;
            if (curUser.getStatus() == null) {
                curUser.setStatus(UserStatusEnum.UN_LOCK.getType());
            } else {
                isLocked = curUser.getStatus();
            }
            //当前用户已经错误的次数
            int loginErrorCount = 0;
            if (curUser.getLoginErrorCount() == null) {
                curUser.setLoginErrorCount(0);
            } else {
                loginErrorCount = curUser.getLoginErrorCount();
            }

            //2.判断账号是否被锁定  被锁定是登陆错误次数一定是5，所以只判断一次
            if (isLocked == UserStatusEnum.LOCK.getType()) {
                //todo 若需要加设定多长时间解锁，则在这添加代码即可
                errorMessage = "账号已被锁定,请联系管理员";
            } else {
                //3.没有被锁定，密码不正确的情况
                int intervalTime = DateUtils.getSecondsBetweenTwo(DateUtils.format(curUser.getFirstErrorLoginTime(),
                        DateUtils.DATE_TIME_PATTERN), DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
                //3.1：当本次登录错误次数为0 ，则说明是第一次出错，修改第一次登录错误时间,如果间隔时间超过了10分钟，则次数清0,但因为这次错了，则为1，修改第一次错误时间，并保存到数据库中去
                if (loginErrorCount == 0 || intervalTime >= rangeTime) {
                    curUser.setLoginErrorCount(1);
                    curUser.setFirstErrorLoginTime(new Date());
                    errorMessage = "密码不正确,您若连续五次输入不正确，您的账号将被锁定!";
                } else if (loginErrorCount == errorCount - 1) {
                    //3.2：当本次登录错误是否等于4次，若是，则说明是第五次错误了，要锁定了。若不是，则计数+1
                    curUser.setLoginErrorCount(errorCount);
                    curUser.setStatus(UserStatusEnum.LOCK.getType());
                    errorMessage = "密码错误,您的账号已被锁定,请联系管理员解锁!";
                } else {
                    //3.3: 登录错误
                    loginErrorCount += 1;
                    curUser.setLoginErrorCount(loginErrorCount);
                    errorMessage = "密码错误,您还有" + (errorCount - loginErrorCount) + "次机会,否则您的账号将被锁定!";
                }
            }
            result.put("user", curUser);
            result.put("errorMessage", errorMessage);
        } else {
            result = null;
        }
        return result;
    }


    /**
     * 超级用户登录IP限制  检查
     */
    private void adminLoginIpCheck(HttpServletRequest request) {
        String ip = getIpAddr(request);
        boolean isCheck = BootAdminProperties.isAdminLoginIpCheck;
        if (isCheck) {

            String allowIpStr = BootAdminProperties.adminLoginIpWhiteList;
            if (StringUtils.isNotBlank(allowIpStr)) {
                matchIp(allowIpStr, ip);
            }
        }

    }

    /**
     * 获取ip地址
     *
     * @param request
     * @return
     */
    private static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        if (ip.split(",").length > 1) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

    /**
     * 一个段一个段的匹配ip,匹配*
     *
     * @param ipWhiteLists
     * @param ip
     */
    private static void matchIp(String ipWhiteLists, String ip) {
        //设置一个数字，每次匹配成功一个段，则加一
        int result = 0;
        if (! StringUtils.isEmpty(ipWhiteLists) && ! StringUtils.isEmpty(ip)) {
            //设置的白名单中的每一个ip
            String[] ipList = ipWhiteLists.split(",");
            for (String ipSection : ipList) {
                //每次新的ip匹配，初始化
                result = 0;
                //设置的ip段 以.隔开
                List<String> ipStr = Arrays.asList(ipSection.split("\\."));
                if (ipStr.size() != 4) {
                    throw new BusinessException("设置的ip不符合四段式规定");
                }
                //当前ip的ip段
                List<String> curIpSection = Arrays.asList(ip.split("\\."));
                if (curIpSection.size() != 4) {
                    throw new BusinessException("当前ip不符合四段式规定");
                }
                for (int i = 0; i < ipStr.size(); i++) {
                    if (!"*".equals(ipStr.get(i))) {
                        if (!curIpSection.get(i).equals(ipStr.get(i))) {
                            break;
                        }
                    }
                    result += 1;
                }
                //说明有一个设置的ip和当前ip匹配上了
                if (result == 4) {
                    break;
                }
            }
            if (result != 4) {
                throw new BusinessException("当前IP地址不允许登录此账号");
            }
        }

    }

}
