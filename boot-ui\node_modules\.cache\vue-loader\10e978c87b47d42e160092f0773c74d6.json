{"remainingRequest": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue", "mtime": 1755140556641}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport mixinViewModule from '@/mixins/view-module'\r\nimport { hisApiService } from '@/views/d2admin/his/his-recommend-api.js'\r\nexport default {\r\n\tmixins: [mixinViewModule],\r\n\tprops: {\r\n\t\t// 推荐医院\r\n\t\thospitalList: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn []\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tforeignId: '',\r\n\t\t\tregNo: '',\r\n\t\t\tdoctorUserId: '', // 当前用户id\r\n\t\t\tpatEmpi: '',\r\n\t\t\tmixinViewModuleOptions: {\r\n\t\t\t\tactivatedIsNeed: true,\r\n\t\t\t\tgetDataListURL: '/pat/list',\r\n\t\t\t\tgetDataListIsPage: true\r\n\t\t\t},\r\n\t\t\tdataForm: {\r\n\t\t\t\tstatus: '1',\r\n\t\t\t\tprojectId: '',\r\n\t\t\t\tsubProjectId: '',\r\n\t\t\t\tisRead: '',\r\n\t\t\t\tinputQuery: '',\r\n\t\t\t\torder: 'desc', // 排序，asc／desc\r\n\t\t\t\torderField: 'recommendTime', // 排序，字段\r\n\t\t\t\tmedId: ''\r\n\t\t\t},\r\n\t\t\toptions: [\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 1,\r\n\t\t\t\t\tlabel: '已阅'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 0,\r\n\t\t\t\t\tlabel: '未阅'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tvisible: false,\r\n\t\t\tcurrentRow: null, // 当前操作的行数据\r\n\t\t\trecommendInfo: {}, // 推荐信息\r\n\t\t\treturnForm: {\r\n\t\t\t\treturnReason: '',\r\n\t\t\t\totherReason: ''\r\n\t\t\t},\r\n\t\t\treturnRules: {\r\n\t\t\t\treturnReason: [\r\n\t\t\t\t\t{ required: true, message: '请选择退回原因', trigger: 'change' }\r\n\t\t\t\t],\r\n\t\t\t\totherReason: [\r\n\t\t\t\t\t{ required: true, message: '请输入具体原因', trigger: 'blur' }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\twatch: {},\r\n\tcreated() {\r\n\t\tthis.init()\r\n\t},\r\n\tmethods: {\r\n\t\tsetTestData() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.dataList = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tprojectId: 64,\r\n\t\t\t\t\t\tsubProjectId: 3326,\r\n\t\t\t\t\t\tempiid: 121647588,\r\n\t\t\t\t\t\tvisitId: 1,\r\n\t\t\t\t\t\tuserId: 1,\r\n\t\t\t\t\t\tname: '张三',\r\n\t\t\t\t\t\tgender: 1,\r\n\t\t\t\t\t\tbirthday: '1992-04-11',\r\n\t\t\t\t\t\tidCard: '15555555555',\r\n\t\t\t\t\t\tinitials: 'zs',\r\n\t\t\t\t\t\ttel: '15555555555',\r\n\t\t\t\t\t\trecommendId: 1,\r\n\t\t\t\t\t\trecommendName: '张三',\r\n\t\t\t\t\t\trecommendReason: '',\r\n\t\t\t\t\t\trecommendTime: '2023-04-11',\r\n\t\t\t\t\t\toutReason: '',\r\n\t\t\t\t\t\tinboundTime: '2023-04-11',\r\n\t\t\t\t\t\tsigningTime: '2023-04-11',\r\n\t\t\t\t\t\tsigningAuditId: 1,\r\n\t\t\t\t\t\tsigningAuditName: '张三',\r\n\t\t\t\t\t\tinboundId: 1,\r\n\t\t\t\t\t\tinboundName: '张三',\r\n\t\t\t\t\t\ttype: 2\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}, 1000)\r\n\t\t},\r\n\t\tinit() {\r\n\t\t\tthis.setTestData()\r\n\t\t},\r\n\t\t// 展示纳排条件满足弹窗\r\n\t\ttoConditionDialog(row) {\r\n\t\t\tif (row.type && row.type == 2) {\r\n\t\t\t\t// 打开医联体推荐的受试者详情弹层\\\r\n\t\t\t\tthis.$emit('showYltDialog', {\r\n\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\tsrcEmpiid: row.srcEmpiid\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.$emit('showDialog', {\r\n\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\tsrcEmpiid: row.srcEmpiid\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 入组\r\n\t\tintoGroup(row) {\r\n\t\t\tthis.$confirm(`确定入组【${row.name}】到当前项目?`, this.$t('prompt.title'), {\r\n\t\t\t\tloading: true,\r\n\t\t\t\tconfirmButtonText: this.$t('confirm'),\r\n\t\t\t\tcancelButtonText: this.$t('cancel'),\r\n\t\t\t\ttype: 'warning'\r\n\t\t\t}).then(() => {\r\n\t\t\t\thisApiService\r\n\t\t\t\t\t.setPatIntoGroup({\r\n\t\t\t\t\t\tid: row.doctorRecommendId,\r\n\t\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\t\tsubProjectId: row.subProjectId\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\tmessage: this.$t('prompt.success'),\r\n\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\tduration: 500,\r\n\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\tthis.getDataList()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\treturnBack(row) {\r\n\t\t\t// 保存当前行数据和推荐信息\r\n\t\t\tthis.currentRow = row\r\n\t\t\tthis.recommendInfo = {\r\n\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\tempiid: row.empiid\r\n\t\t\t}\r\n\r\n\t\t\t// 重置表单\r\n\t\t\tthis.returnForm = {\r\n\t\t\t\treturnReason: '',\r\n\t\t\t\totherReason: ''\r\n\t\t\t}\r\n\r\n\t\t\t// 清除表单验证\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tif (this.$refs.returnForm) {\r\n\t\t\t\t\tthis.$refs.returnForm.clearValidate()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\r\n\t\t\t// 显示弹窗\r\n\t\t\tthis.visible = true\r\n\t\t},\r\n\t\t// 确认退回\r\n\t\tquitConfirm() {\r\n\t\t\tthis.$refs.returnForm.validate((valid) => {\r\n\t\t\t\tif (valid) {\r\n\t\t\t\t\t// 如果选择了\"其他原因\"但没有填写具体原因，需要验证\r\n\t\t\t\t\tif (this.returnForm.returnReason === '其他原因' && !this.returnForm.otherReason.trim()) {\r\n\t\t\t\t\t\tthis.$message.error('请输入具体原因')\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 调用接口\r\n\t\t\t\t\thisApiService\r\n\t\t\t\t\t\t.setPatBackGroup({\r\n\t\t\t\t\t\t\tsubProjectId: this.recommendInfo.subProjectId,\r\n\t\t\t\t\t\t\tempi: this.recommendInfo.empiid,\r\n\t\t\t\t\t\t\treason: this.returnForm.returnReason === '其他原因' ? this.returnForm.otherReason : this.returnForm.returnReason\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\tmessage: this.$t('prompt.success'),\r\n\t\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\t\tduration: 500,\r\n\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\tthis.visible = false\r\n\t\t\t\t\t\t\t\t\tthis.getDataList()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\t\tconsole.error('退回失败:', error)\r\n\t\t\t\t\t\t\tthis.$message.error('退回失败，请重试')\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetGender(gender) {\r\n\t\t\tif (gender == 1) {\r\n\t\t\t\treturn '女'\r\n\t\t\t} else if (gender == 0) {\r\n\t\t\t\treturn '男'\r\n\t\t\t} else {\r\n\t\t\t\treturn '保密'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcalculateAge(birthday) {\r\n\t\t\t// 根据日期算年龄\r\n\t\t\tif (birthday) {\r\n\t\t\t\tbirthday = birthday.split('-')\r\n\t\t\t\t// 新建日期对象\r\n\t\t\t\tconst date = new Date()\r\n\t\t\t\t// 今天日期，数组，同 birthday\r\n\t\t\t\tconst today = [date.getFullYear(), date.getMonth() + 1, date.getDate()]\r\n\t\t\t\t// 分别计算年月日差值\r\n\t\t\t\tconst age = today.map((val, index) => {\r\n\t\t\t\t\treturn val - birthday[index]\r\n\t\t\t\t})\r\n\t\t\t\t// 当天数为负数时，月减 1，天数加上月总天数\r\n\t\t\t\tif (age[2] < 0) {\r\n\t\t\t\t\t// 简单获取上个月总天数的方法，不会错\r\n\t\t\t\t\tconst lastMonth = new Date(today[0], today[1], 0)\r\n\t\t\t\t\tage[1]--\r\n\t\t\t\t\tage[2] += lastMonth.getDate()\r\n\t\t\t\t}\r\n\t\t\t\t// 当月数为负数时，年减 1，月数加上 12\r\n\t\t\t\tif (age[1] < 0) {\r\n\t\t\t\t\tage[0]--\r\n\t\t\t\t\tage[1] += 12\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// console.log(age[0] + '岁' + age[1] + '月' + age[2] + '天')\r\n\t\t\t\tif (age[0] < 1) {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn age[0]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 改变患者状态\r\n\t\tisReadChange(val, data) {\r\n\t\t\t// console.log(val, data, '219')\r\n\t\t\tthis.$axios.post(`pat/read/${data.doctorRecommendId}/${val}`).then(res => {\r\n\t\t\t\tthis.getDataList()\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n", {"version": 3, "sources": ["step-two.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "step-two.vue", "sourceRoot": "src/views/modules/components/recommend-list", "sourcesContent": ["<template>\r\n\t<div class=\"stept-two\">\r\n\t\t<div class=\"table-container\">\r\n\t\t\t<el-form :inline=\"true\" size=\"mini\" :model=\"dataForm\">\r\n\t\t\t\t<el-form-item label=\"状态\">\r\n\t\t\t\t\t<el-select v-model=\"dataForm.isRead\" clearable placeholder=\"状态\" style=\"width: 150px\">\r\n\t\t\t\t\t\t<el-option :label=\"$t('all')\" value=\"\" />\r\n\t\t\t\t\t\t<el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t<el-form-item label=\"推荐医院\">\r\n\t\t\t\t\t<el-select v-model=\"dataForm.medId\" clearable placeholder=\"推荐医院\" style=\"width: 200px\">\r\n\t\t\t\t\t\t<el-option :label=\"$t('all')\" value=\"\" />\r\n\t\t\t\t\t\t<el-option v-for=\"item in hospitalList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t<el-form-item>\r\n\t\t\t\t\t<el-input v-model=\"dataForm.inputQuery\" clearable placeholder=\"登记号或者姓名\" style=\"width: 150px\" />\r\n\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t<el-form-item>\r\n\t\t\t\t\t<el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getDataList(true)\">{{ $t('query') }}</el-button>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t<el-table v-loading=\"dataListLoading\" size=\"mini\" :data=\"dataList\">\r\n\t\t\t\t<el-table-column prop=\"regno\" label=\"登记号\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"name\" label=\"姓名\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"tel\" label=\"联系方式\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"gender\" label=\"性别\" header-align=\"center\" align=\"center\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t{{ getGender(scope.row.gender) }}\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"birthday\" label=\"年龄\" header-align=\"center\" align=\"center\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t{{ calculateAge(scope.row.birthday) }}\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"type\" label=\"推荐方式\" header-align=\"center\" align=\"center\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<span v-if=\"scope.row.type == 0\">系统推荐</span>\r\n\t\t\t\t\t\t<span v-else-if=\"scope.row.type == 1\">手动推荐</span>\r\n\t\t\t\t\t\t<span v-else-if=\"scope.row.type == 2\">医联体推荐</span>\r\n\t\t\t\t\t\t<span v-else>--</span>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"recommendHospitalName\" label=\"推荐医院\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"recommendName\" label=\"推荐医生\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"recommendTime\" label=\"推荐时间\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"isRead\" label=\"状态\" header-align=\"center\" align=\"center\" width=\"120px\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-select v-model=\"scope.row.isRead\" placeholder=\"请选择\" size=\"mini\" @change=\"isReadChange($event, scope.row)\">\r\n\t\t\t\t\t\t\t<el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column :label=\"$t('handle')\" header-align=\"center\" align=\"center\" min-width=\"200px\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-button size=\"mini\" @click=\"toConditionDialog(scope.row)\">查看</el-button>\r\n\t\t\t\t\t\t<el-button size=\"mini\" type=\"primary\" @click=\"intoGroup(scope.row)\">入组</el-button>\r\n\t\t\t\t\t\t<el-button size=\"mini\" type=\"danger\" @click=\"returnBack(scope.row)\">退回</el-button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t</div>\r\n\r\n\t\t<el-dialog :visible.sync=\"visible\" title=\"退回原因\" :close-on-click-modal=\"false\" :close-on-press-escape=\"false\" width=\"40%\">\r\n\t\t\t<el-form ref=\"returnForm\" :model=\"returnForm\" :rules=\"returnRules\" label-width=\"80px\">\r\n\t\t\t\t<el-form-item label=\"退回原因\" prop=\"returnReason\">\r\n\t\t\t\t\t<el-radio-group v-model=\"returnForm.returnReason\">\r\n\t\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"患者拒签\">患者拒签</el-radio></div>\r\n\t\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"不符合纳排标准\">不符合纳排标准</el-radio></div>\r\n\t\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"其他原因\">其他原因</el-radio></div>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item v-if=\"returnForm.returnReason === '其他原因'\" label=\"具体原因\" prop=\"otherReason\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t\tv-model=\"returnForm.otherReason\"\r\n\t\t\t\t\t\ttype=\"textarea\"\r\n\t\t\t\t\t\t:rows=\"3\"\r\n\t\t\t\t\t\tplaceholder=\"请输入具体原因\"\r\n\t\t\t\t\t\tmaxlength=\"200\"\r\n\t\t\t\t\t\tshow-word-limit\r\n\t\t\t\t\t/>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\r\n\t\t\t<template slot=\"footer\">\r\n\t\t\t\t<el-button size=\"mini\" @click=\"visible = false\">取消</el-button>\r\n\t\t\t\t<el-button size=\"mini\" type=\"primary\" @click=\"quitConfirm\">确定</el-button>\r\n\t\t\t</template>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 分页 -->\r\n\t\t<el-pagination\r\n\t\t\tslot=\"footer\"\r\n\t\t\t:current-page=\"page\"\r\n\t\t\t:page-sizes=\"[10, 20, 50, 100]\"\r\n\t\t\t:page-size=\"limit\"\r\n\t\t\t:total=\"total\"\r\n\t\t\tlayout=\"total, sizes, prev, pager, next, jumper,slot\"\r\n\t\t\t@size-change=\"pageSizeChangeHandle\"\r\n\t\t\t@current-change=\"pageCurrentChangeHandle\"\r\n\t\t>\r\n\t\t\t<i class=\"el-icon-refresh-right page-refresh-btn\" @click=\"refreshHandle\" />\r\n\t\t\t<span v-if=\"dataListSelections && dataListSelections.length > 0\" class=\"page-selected-total\">已选中 {{ dataListSelections.length }} 条数据</span>\r\n\t\t</el-pagination>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport mixinViewModule from '@/mixins/view-module'\r\nimport { hisApiService } from '@/views/d2admin/his/his-recommend-api.js'\r\nexport default {\r\n\tmixins: [mixinViewModule],\r\n\tprops: {\r\n\t\t// 推荐医院\r\n\t\thospitalList: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn []\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tforeignId: '',\r\n\t\t\tregNo: '',\r\n\t\t\tdoctorUserId: '', // 当前用户id\r\n\t\t\tpatEmpi: '',\r\n\t\t\tmixinViewModuleOptions: {\r\n\t\t\t\tactivatedIsNeed: true,\r\n\t\t\t\tgetDataListURL: '/pat/list',\r\n\t\t\t\tgetDataListIsPage: true\r\n\t\t\t},\r\n\t\t\tdataForm: {\r\n\t\t\t\tstatus: '1',\r\n\t\t\t\tprojectId: '',\r\n\t\t\t\tsubProjectId: '',\r\n\t\t\t\tisRead: '',\r\n\t\t\t\tinputQuery: '',\r\n\t\t\t\torder: 'desc', // 排序，asc／desc\r\n\t\t\t\torderField: 'recommendTime', // 排序，字段\r\n\t\t\t\tmedId: ''\r\n\t\t\t},\r\n\t\t\toptions: [\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 1,\r\n\t\t\t\t\tlabel: '已阅'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 0,\r\n\t\t\t\t\tlabel: '未阅'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tvisible: false,\r\n\t\t\tcurrentRow: null, // 当前操作的行数据\r\n\t\t\trecommendInfo: {}, // 推荐信息\r\n\t\t\treturnForm: {\r\n\t\t\t\treturnReason: '',\r\n\t\t\t\totherReason: ''\r\n\t\t\t},\r\n\t\t\treturnRules: {\r\n\t\t\t\treturnReason: [\r\n\t\t\t\t\t{ required: true, message: '请选择退回原因', trigger: 'change' }\r\n\t\t\t\t],\r\n\t\t\t\totherReason: [\r\n\t\t\t\t\t{ required: true, message: '请输入具体原因', trigger: 'blur' }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\twatch: {},\r\n\tcreated() {\r\n\t\tthis.init()\r\n\t},\r\n\tmethods: {\r\n\t\tsetTestData() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.dataList = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tprojectId: 64,\r\n\t\t\t\t\t\tsubProjectId: 3326,\r\n\t\t\t\t\t\tempiid: 121647588,\r\n\t\t\t\t\t\tvisitId: 1,\r\n\t\t\t\t\t\tuserId: 1,\r\n\t\t\t\t\t\tname: '张三',\r\n\t\t\t\t\t\tgender: 1,\r\n\t\t\t\t\t\tbirthday: '1992-04-11',\r\n\t\t\t\t\t\tidCard: '15555555555',\r\n\t\t\t\t\t\tinitials: 'zs',\r\n\t\t\t\t\t\ttel: '15555555555',\r\n\t\t\t\t\t\trecommendId: 1,\r\n\t\t\t\t\t\trecommendName: '张三',\r\n\t\t\t\t\t\trecommendReason: '',\r\n\t\t\t\t\t\trecommendTime: '2023-04-11',\r\n\t\t\t\t\t\toutReason: '',\r\n\t\t\t\t\t\tinboundTime: '2023-04-11',\r\n\t\t\t\t\t\tsigningTime: '2023-04-11',\r\n\t\t\t\t\t\tsigningAuditId: 1,\r\n\t\t\t\t\t\tsigningAuditName: '张三',\r\n\t\t\t\t\t\tinboundId: 1,\r\n\t\t\t\t\t\tinboundName: '张三',\r\n\t\t\t\t\t\ttype: 2\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}, 1000)\r\n\t\t},\r\n\t\tinit() {\r\n\t\t\tthis.setTestData()\r\n\t\t},\r\n\t\t// 展示纳排条件满足弹窗\r\n\t\ttoConditionDialog(row) {\r\n\t\t\tif (row.type && row.type == 2) {\r\n\t\t\t\t// 打开医联体推荐的受试者详情弹层\\\r\n\t\t\t\tthis.$emit('showYltDialog', {\r\n\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\tsrcEmpiid: row.srcEmpiid\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.$emit('showDialog', {\r\n\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\tsrcEmpiid: row.srcEmpiid\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 入组\r\n\t\tintoGroup(row) {\r\n\t\t\tthis.$confirm(`确定入组【${row.name}】到当前项目?`, this.$t('prompt.title'), {\r\n\t\t\t\tloading: true,\r\n\t\t\t\tconfirmButtonText: this.$t('confirm'),\r\n\t\t\t\tcancelButtonText: this.$t('cancel'),\r\n\t\t\t\ttype: 'warning'\r\n\t\t\t}).then(() => {\r\n\t\t\t\thisApiService\r\n\t\t\t\t\t.setPatIntoGroup({\r\n\t\t\t\t\t\tid: row.doctorRecommendId,\r\n\t\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\t\tsubProjectId: row.subProjectId\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\tmessage: this.$t('prompt.success'),\r\n\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\tduration: 500,\r\n\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\tthis.getDataList()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\treturnBack(row) {\r\n\t\t\t// 保存当前行数据和推荐信息\r\n\t\t\tthis.currentRow = row\r\n\t\t\tthis.recommendInfo = {\r\n\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\tempiid: row.empiid\r\n\t\t\t}\r\n\r\n\t\t\t// 重置表单\r\n\t\t\tthis.returnForm = {\r\n\t\t\t\treturnReason: '',\r\n\t\t\t\totherReason: ''\r\n\t\t\t}\r\n\r\n\t\t\t// 清除表单验证\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tif (this.$refs.returnForm) {\r\n\t\t\t\t\tthis.$refs.returnForm.clearValidate()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\r\n\t\t\t// 显示弹窗\r\n\t\t\tthis.visible = true\r\n\t\t},\r\n\t\t// 确认退回\r\n\t\tquitConfirm() {\r\n\t\t\tthis.$refs.returnForm.validate((valid) => {\r\n\t\t\t\tif (valid) {\r\n\t\t\t\t\t// 如果选择了\"其他原因\"但没有填写具体原因，需要验证\r\n\t\t\t\t\tif (this.returnForm.returnReason === '其他原因' && !this.returnForm.otherReason.trim()) {\r\n\t\t\t\t\t\tthis.$message.error('请输入具体原因')\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 调用接口\r\n\t\t\t\t\thisApiService\r\n\t\t\t\t\t\t.setPatBackGroup({\r\n\t\t\t\t\t\t\tsubProjectId: this.recommendInfo.subProjectId,\r\n\t\t\t\t\t\t\tempi: this.recommendInfo.empiid,\r\n\t\t\t\t\t\t\treason: this.returnForm.returnReason === '其他原因' ? this.returnForm.otherReason : this.returnForm.returnReason\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\tmessage: this.$t('prompt.success'),\r\n\t\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\t\tduration: 500,\r\n\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\tthis.visible = false\r\n\t\t\t\t\t\t\t\t\tthis.getDataList()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\t\tconsole.error('退回失败:', error)\r\n\t\t\t\t\t\t\tthis.$message.error('退回失败，请重试')\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetGender(gender) {\r\n\t\t\tif (gender == 1) {\r\n\t\t\t\treturn '女'\r\n\t\t\t} else if (gender == 0) {\r\n\t\t\t\treturn '男'\r\n\t\t\t} else {\r\n\t\t\t\treturn '保密'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcalculateAge(birthday) {\r\n\t\t\t// 根据日期算年龄\r\n\t\t\tif (birthday) {\r\n\t\t\t\tbirthday = birthday.split('-')\r\n\t\t\t\t// 新建日期对象\r\n\t\t\t\tconst date = new Date()\r\n\t\t\t\t// 今天日期，数组，同 birthday\r\n\t\t\t\tconst today = [date.getFullYear(), date.getMonth() + 1, date.getDate()]\r\n\t\t\t\t// 分别计算年月日差值\r\n\t\t\t\tconst age = today.map((val, index) => {\r\n\t\t\t\t\treturn val - birthday[index]\r\n\t\t\t\t})\r\n\t\t\t\t// 当天数为负数时，月减 1，天数加上月总天数\r\n\t\t\t\tif (age[2] < 0) {\r\n\t\t\t\t\t// 简单获取上个月总天数的方法，不会错\r\n\t\t\t\t\tconst lastMonth = new Date(today[0], today[1], 0)\r\n\t\t\t\t\tage[1]--\r\n\t\t\t\t\tage[2] += lastMonth.getDate()\r\n\t\t\t\t}\r\n\t\t\t\t// 当月数为负数时，年减 1，月数加上 12\r\n\t\t\t\tif (age[1] < 0) {\r\n\t\t\t\t\tage[0]--\r\n\t\t\t\t\tage[1] += 12\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// console.log(age[0] + '岁' + age[1] + '月' + age[2] + '天')\r\n\t\t\t\tif (age[0] < 1) {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn age[0]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 改变患者状态\r\n\t\tisReadChange(val, data) {\r\n\t\t\t// console.log(val, data, '219')\r\n\t\t\tthis.$axios.post(`pat/read/${data.doctorRecommendId}/${val}`).then(res => {\r\n\t\t\t\tthis.getDataList()\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.stept-two {\r\n\theight: 100%;\r\n\r\n\t.table-container {\r\n\t\theight: calc(100% - 35px);\r\n\r\n\t\t.el-table {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: calc(100% - 50px);\r\n\t\t}\r\n\t}\r\n}\r\n</style>"]}]}