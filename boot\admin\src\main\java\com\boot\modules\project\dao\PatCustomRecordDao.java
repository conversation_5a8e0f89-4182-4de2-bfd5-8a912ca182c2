package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.mobile.model.PatCustomDetailModel;
import com.boot.modules.project.entity.PatCustomRecordEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@CacheNamespace
public interface PatCustomRecordDao extends BaseMapper<PatCustomRecordEntity> {
    List<PatCustomDetailModel> getDetailInfo(@Param(Constants.WRAPPER) QueryWrapper<Object> qw);
}
