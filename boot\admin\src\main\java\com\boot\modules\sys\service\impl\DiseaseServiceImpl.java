package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.excel.model.ReadExcelListener;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.*;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.service.UserSubProjectService;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.dao.DiseaseDao;
import com.boot.modules.sys.dto.DiseaseDto;
import com.boot.modules.sys.entity.DiseaseEntity;
import com.boot.modules.sys.model.DieaseProjectModel;
import com.boot.modules.sys.service.DiseaseService;
import com.boot.modules.sys.vo.DiseaseProjectVo;
import com.boot.modules.sys.vo.DiseaseVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DiseaseServiceImpl extends ServiceImpl<DiseaseDao, DiseaseEntity> implements DiseaseService {

    @Resource
    private DiseaseDao diseaseDao;

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private ProjectService projectService;

    @Override
    public PageUtils getAllByPage(Map<String, Object> params) {
//        // 1.病种名称
//        String name = MapUtils.getValue(params, "name", String.class);
//        QueryWrapper<DiseaseVo> qw = new QueryWrapper<>();
//        if (!StringUtils.isEmpty(name)) {
//            qw.like("a.disease_name", name);
//        }
//
//        Integer limit = MapUtils.getValue(params, "limit", Integer.class);
//        IPage<DiseaseVo> page = new Page<>();
//
//        if (limit != null && limit > 0) {
//            IPage<DiseaseVo> pageFilter = new Query<DiseaseVo>().getPage(params);
//            page = diseaseDao.pageByQuery(pageFilter, qw);
//        }else {
//            List<DiseaseVo> records = diseaseDao.getByQuery(qw);
//            page.setRecords(records);
//        }
        Integer limit = MapUtils.getValue(params, "limit", Integer.class);
        String name = MapUtils.getValue(params, "name", String.class);
        QueryWrapper<DiseaseEntity> qw = new QueryWrapper<>();
        if (!StringUtils.isEmpty(name)) {
            qw.lambda().like(DiseaseEntity::getDiseaseName, name);
        }
        IPage<DiseaseEntity> page = new Page<>();
        if (limit != null && limit > 0) {
            IPage<DiseaseEntity> pageFilter = new Query<DiseaseEntity>().getPage(params);
            page = baseMapper.selectPage(pageFilter, qw);
        } else {
            List<DiseaseEntity> records = list(qw);
            page.setRecords(records);
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils pageByDept(Map<String, Object> params) {
        // 1.科室id
        QueryWrapper<DiseaseVo> qw = new QueryWrapper<>();
        Long deptId = MapUtils.getValue(params, "deptId", Long.class);

        // 2.获取相关病种
        if (deptId != null) {
            qw.eq("f.dept_id", deptId);
        }

        // 3.获取相关项目数
        IPage<DiseaseVo> pageFilter = new Query<DiseaseVo>().getPage(params);

        return new PageUtils(this.baseMapper.pageQuery(pageFilter, qw));
    }

    @Override
    public PageUtils pageByDoctor(Map<String, Object> params) {
        // 1.科室id
        QueryWrapper<DiseaseVo> qw = new QueryWrapper<>();
        // 2.用户id
        Long doctorId = MapUtils.getValue(params, "doctorId", Long.class);
        Long deptId = MapUtils.getValue(params, "deptId", Long.class);

        if (doctorId != null) {
            qw.eq("a.project_admin_id", doctorId);
        }

        if (deptId != null) {
            qw.eq("a.main_dept_id", deptId);
        }

        IPage<DiseaseVo> pageFilter = new Query<DiseaseVo>().getPage(params);

        return new PageUtils(this.baseMapper.pageQuery(pageFilter, qw));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(DiseaseEntity diseaseEntity) {
        String nowTimeStr = DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
        diseaseEntity.setCreateTime(nowTimeStr);
        return this.save(diseaseEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDisease(DiseaseEntity diseaseEntity) {
        return this.updateById(diseaseEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean excelImport(MultipartFile file) {
        List<DiseaseDto> list;
        try {
            //解析excel
            ReadExcelListener readExcelListener = new ReadExcelListener(DiseaseDto.class);
            list = ExcelUtils.readExcel(file, DiseaseDto.class, readExcelListener);
        } catch (Exception ex) {
            throw new BusinessException("读取excel文件出错，请检查文件格式");
        }
        //验证合法性
        List<DiseaseEntity> diseaseEntityList = new ArrayList<>();
        for (DiseaseDto diseaseDto : list) {
            //一个属性为空则跳过
//            if (StringUtils.isEmpty(diseaseDto.getDiseaseName()) || ObjectUtils.isEmpty(diseaseDto.getDeptId())) {
//                continue;
//            }
            if (StringUtils.isEmpty(diseaseDto.getDiseaseName())) {
                continue;
            }

            DiseaseEntity diseaseEntity = new DiseaseEntity();
            diseaseEntity.setDiseaseName(diseaseDto.getDiseaseName());
//            diseaseEntity.setDeptId(diseaseDto.getDeptId());
            diseaseEntity.setCreateTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            //判断唯一性
            QueryWrapper<DiseaseEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(DiseaseEntity::getDiseaseName, diseaseEntity.getDiseaseName());
//                    .eq(DiseaseEntity::getDeptId, diseaseEntity.getDeptId());
            int count = this.count(qw);
            //存在记录跳过
            if (count > 0) {
                continue;
            }
            diseaseEntityList.add(diseaseEntity);
        }
        if (CollectionUtils.isEmpty(diseaseEntityList)) {
            return true;
        }
        //去重
        diseaseEntityList = diseaseEntityList.stream().distinct().collect(Collectors.toList());
        return this.saveBatch(diseaseEntityList);
    }

    @Override
    public List<DieaseProjectModel> getDieaseByDept(List<Long> deptIdList) {
        List<DieaseProjectModel> ret = new ArrayList<>();
        QueryWrapper<DiseaseProjectVo> qw = new QueryWrapper<>();
//        qw.eq("a.dept_id", deptId);
        qw.in("d.dept_id", deptIdList);
        qw.eq("b.status",1);
        List<DiseaseProjectVo> diseaseProjectVos = baseMapper.getDieaseByDept(qw);
        Map<Long, List<DiseaseProjectVo>> groupByDiease = diseaseProjectVos.stream().collect(Collectors.groupingBy(o -> o.getDiseaseId()));
        for (Long diseaseId : groupByDiease.keySet()) {
            DieaseProjectModel dieaseProjectModel = new DieaseProjectModel();
            dieaseProjectModel.setDiseaseId(diseaseId);
            List<DiseaseProjectVo> diseaseProjectVoList = groupByDiease.get(diseaseId);
            if (CollectionUtils.isEmpty(diseaseProjectVoList)) {
                continue;
            }
            dieaseProjectModel.setDiseaseName(diseaseProjectVoList.get(0).getDiseaseName());
            dieaseProjectModel.setDeptId(diseaseProjectVoList.get(0).getDeptId());
            List<SubProjectVo> projectVoList = new ArrayList<>();
            for (DiseaseProjectVo projectVo : diseaseProjectVoList) {
                SubProjectVo subProjectVo = new SubProjectVo();
                subProjectVo.setProjectId(projectVo.getProjectId());
                subProjectVo.setProjectName(projectVo.getProjectName());
                subProjectVo.setId(projectVo.getId());
                subProjectVo.setName(projectVo.getName());
                subProjectVo.setContacts(projectVo.getContacts());
                subProjectVo.setContactsPhone(projectVo.getContactsPhone());
                subProjectVo.setProjectType(projectVo.getProjectType());
                projectVoList.add(subProjectVo);
            }
            dieaseProjectModel.setProjectList(projectVoList);
            ret.add(dieaseProjectModel);
        }
        return ret;
    }
}
