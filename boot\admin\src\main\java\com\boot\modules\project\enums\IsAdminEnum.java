package com.boot.modules.project.enums;

/**
 * <AUTHOR>
 * @desc : 是否是项目管理员枚举
 * @create 2021-04-01
 */
public enum IsAdminEnum {

    /**
     * 管理员
     */
    ADMIN(1),

    /**
     * 非管理员
     */
    NO_ADMIN(0),

    ;

    private Integer type;

    IsAdminEnum(int type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static IsAdminEnum getEnumByType(int type) {
        IsAdminEnum[] types = IsAdminEnum.values();
        for (IsAdminEnum a : types) {
            if (type == a.getType()) {
                return a;
            }
        }
        return null;
    }


}
