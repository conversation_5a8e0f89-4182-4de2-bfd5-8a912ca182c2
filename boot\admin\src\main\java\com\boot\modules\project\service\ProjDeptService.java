package com.boot.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.vo.ProjDeptVo;

import java.util.List;

/**
 * <AUTHOR>
 * @desc :项目中心
 * @create 2021-05-20
 */
public interface ProjDeptService extends IService<ProjDeptEntity> {

    /**
     * 根据项目id获取项目中心数据
     *
     * @param projectId
     * @return
     */
    List<ProjDeptEntity> getByProjectId(Long projectId);

    List<ProjDeptVo> getByQuery(Long projectId);

    /**
     * 通过subProjectId 查
     *
     * @param subProjectId
     * @return
     */
    List<ProjDeptEntity> getBySubProjectId(Long subProjectId);
}
