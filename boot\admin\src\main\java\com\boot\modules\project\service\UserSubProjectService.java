package com.boot.modules.project.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.UserSubProjectEntity;
import com.boot.modules.project.vo.UserSubProjectCenterVo;
import com.boot.modules.project.vo.UserSubProjectVo;
import com.boot.modules.sys.entity.SysDeptEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :项目用户服务
 * @create 2021-02-23
 */
public interface UserSubProjectService extends IService<UserSubProjectEntity> {

    /**
     * 项目用户 分页查询方法
     *
     * @param params
     * @return
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 获取当前子项目的所有项目用户 按中心分开
     *
     * @param params
     * @return
     */
    List<UserSubProjectCenterVo> getAllSubProjUser(Map<String, Object> params);


    /**
     * 获取当前项目还未关联的所有用户信息（不包括项目管理员、也要过滤掉系统管理员）
     *
     * @return
     */
//    PageUtils getNotAddUser(Map<String, Object> params);

    List<SysDeptEntity> getNotAddUser(Map<String, Object> params);
    /**
     * 获取某项目下的所以用户
     *
     * @param projectId
     * @return
     */
    List<UserSubProjectEntity> getByProjectId(Long projectId);

    /**
     * 获取当前用户在当前项目中的所属中心
     * @param subProjectId
     * @param projectId
     * @param userId
     * @return
     */
    Long getUserCenter(Long subProjectId, Long projectId, Long userId);

    SysDeptEntity getCurUserCenter(Long projectId, Long subProjectId, Long userId);

    /**
     * 新增项目用户
     *
     * @param
     * @return
     */
    boolean add(UserSubProjectEntity projUser);

    /**
     * 批量新增
     *
     * @param
     * @return
     */
    boolean addBatch(List<UserSubProjectEntity> projUsers);

    List<UserSubProjectVo> getByQuery(QueryWrapper<UserSubProjectVo> qwUser);
}
