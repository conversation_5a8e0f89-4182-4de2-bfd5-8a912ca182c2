package com.boot.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.entity.ProjectCenterConfigEntity;
import com.boot.modules.project.vo.ProjectCenterConfigVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :项目中心配置
 * @create 2021-09-08
 */
public interface ProjectCenterConfigService extends IService<ProjectCenterConfigEntity> {

    /**
     * 分页
     *
     * @param params
     * @return
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 在项目创建的时候 自动添加默认字段的配置
     *
     * @param projDeptList
     * @return
     */
    boolean autoSaveDefaultFields(Long subProjectId, List<ProjDeptEntity> projDeptList);

    List<ProjectCenterConfigEntity> getAllDefaultFields(Long subProjectId);

    List<ProjectCenterConfigEntity> getByQuery(Long subprojectId, Long centerId);

    /**
     * 新增 或者 修改中心配置
     *
     * @param configList
     * @return
     */
    boolean addOrUpdate(List<ProjectCenterConfigEntity> configList);

}
