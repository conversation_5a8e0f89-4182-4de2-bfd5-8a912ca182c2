package com.boot.modules.mobile.dto;

import com.boot.modules.patient.dto.RecommendDto;
import lombok.Data;

/**
 * 移动端患者DTO对象
 */
@Data
public class MobilePatientDTO {
    /**
     * 名字
     */
    private String name;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 民族
     */
    private String nation;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 医疗机构编码
     */
    private String medCode;
    /**
     * 推荐参数
     */
    private RecommendDto recommendDto;
    /**
     * 子项目ID
     */
    private Long subProjectId;

    /**
     * 医疗机构Id
     */
    private Long medId;

    private String empi;
}
