<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysDeptDao">
    <cache-ref namespace="com.boot.modules.sys.dao.SysDeptDao"/>
    <select id="queryList" resultType="com.boot.modules.sys.entity.SysDeptEntity">
        select t1.*,(select t2.name from rp_sys_dept t2 where t2.id=t1.pid)parentName from rp_sys_dept t1 where
        t1.del_flag = 0
        <if test="sql_filter != null">
            and ${sql_filter}
        </if>
        ORDER BY sort ASC
    </select>
    <select id="queryMaxSort" resultType="java.lang.Integer">
        SELECT MAX(sort) AS max_sort
            FROM rp_sys_dept
        ${ew.customSqlSegment}
    </select>
    <select id="getByQuery" resultType="com.boot.modules.sys.entity.SysDeptEntity">
        select t1.*,(select t2.name from rp_sys_dept t2 where t2.id=t1.pid)parentName from rp_sys_dept t1
            ${ew.customSqlSegment}
    </select>
    <select id="selectByQuery" resultType="com.boot.modules.sys.entity.SysDeptEntity">
        SELECT
            a.*
        FROM
            rp_sys_dept a
                LEFT JOIN rp_sys_user b ON a.id = b.dept_id
            ${ew.customSqlSegment}
    </select>

<!--    <select id="listCountDisease" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">-->
<!--        select-->
<!--            temp.deptId,-->
<!--            temp.deptName,-->
<!--            count(distinct(temp.diseaseId)) totalDisease-->
<!--        from-->
<!--            (select-->
<!--                a.id deptId,-->
<!--                a.name deptName,-->
<!--                b.id diseaseId-->
<!--            from-->
<!--                rp_sys_dept a-->
<!--            join-->
<!--                rp_sys_disease b-->
<!--            on-->
<!--                a.id = b.dept_id-->
<!--            ${ew.customSqlSegment}-->
<!--            ) as temp-->
<!--        group by temp.deptId,temp.deptName-->

<!--    </select>-->
<!--    <select id="listCount" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">-->
<!--        select-->
<!--            temp.deptId,-->
<!--            temp.deptName,-->
<!--            count(distinct(temp.diseaseId)) totalDisease,-->
<!--            count(distinct(temp.projectId)) totalProject-->
<!--        from-->
<!--            (select-->
<!--                a.id deptId,-->
<!--                a.name deptName,-->
<!--                c.disease_id diseaseId,-->
<!--                c.id projectId-->
<!--            from-->
<!--                rp_sys_dept a-->
<!--            join-->
<!--                rp_user_subproject g-->
<!--            on-->
<!--                a.id = g.project_dept_id-->
<!--            join-->
<!--                rp_sub_project d-->
<!--            on-->
<!--                d.id = g.sub_project_id-->
<!--            join-->
<!--                rp_project c-->
<!--            on-->
<!--                c.id = d.project_id-->
<!--            join-->
<!--                rp_sys_disease b-->
<!--            on-->
<!--                b.id = c.disease_id-->
<!--            ${ew.customSqlSegment}-->
<!--        ) as temp-->
<!--        group by temp.deptId,temp.deptName-->
<!--    </select>-->
    <select id="listCount" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">
        select
        temp.deptId,
        temp.deptName,
        count(distinct(temp.diseaseId)) totalDisease,
        count(distinct(temp.projectId)) totalProject
        from
        (select
        a.id deptId,
        a.name deptName,
        c.disease_id diseaseId,
        c.id projectId
        from
        rp_sys_dept a
        join
        rp_project_dept d
        on
        a.id = d.dept_id
        join
        rp_project c
        on
        d.project_id = c.id
        join
        rp_sys_disease b
        on
        b.id = c.disease_id
        ${ew.customSqlSegment}
        ) as temp
        group by temp.deptId,temp.deptName
    </select>
<!--    <select id="listCountProject" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">-->
<!--        select-->
<!--            temp.deptId,-->
<!--            temp.deptName,-->
<!--            count(distinct(temp.projectId)) totalProject-->
<!--        from-->
<!--            (select-->
<!--                a.id deptId,-->
<!--                a.name deptName,-->
<!--                b.project_id projectId-->
<!--            from-->
<!--                rp_sys_dept a-->
<!--            join-->
<!--                rp_project_dept b-->
<!--            on-->
<!--                a.id = b.dept_id-->
<!--            join-->
<!--                rp_project c-->
<!--            on-->
<!--                b.project_id = c.id-->
<!--            ${ew.customSqlSegment}-->
<!--        ) as temp-->
<!--        group by temp.deptId,temp.deptName-->
<!--    </select>-->
    <select id="getIdByCode" resultType="Long">
        SELECT u.id
        FROM
        (SELECT substring_index(substring_index(t.code, ',', b.help_topic_id + 1), ',', - 1) codeStr,t.id,t.name,t.code,t.pid
        FROM rp_sys_dept t
        JOIN mysql.help_topic b ON b.help_topic_id &lt; (LENGTH(t.code) - LENGTH(REPLACE(t.code, ',', '')) + 1)) AS u
        WHERE u.codeStr = #{code}
        LIMIT 1
    </select>

</mapper>