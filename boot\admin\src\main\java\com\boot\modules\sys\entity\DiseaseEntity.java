package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_disease")
public class DiseaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 项目id
     */
    @TableId
    private Long id;
    /**
     * 病种名称
     */
    @NotBlank(message = "病种名称不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private String diseaseName;

//    /**
//     * 关联科室Id
//     */
//    @NotNull(message = "关联科室不许为空", groups = {AddGroup.class, UpdateGroup.class})
//    private Long deptId;

    private String createTime;
}
