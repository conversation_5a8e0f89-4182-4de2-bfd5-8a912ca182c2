package com.boot.modules.sys.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.commons.utils.PageUtils;

import java.util.List;
import java.util.Map;


/**
 * 角色
 *
 * <AUTHOR>
 */
public interface SysRoleService extends IService<SysRoleEntity> {

    PageUtils queryPage(Map<String, Object> params);

    void saveRole(SysRoleEntity role);

    void update(SysRoleEntity role);

    void deleteBatch(Long[] roleIds);

    /**
     * 通过用户id，查询角色信息
     *
     * @param qwRole
     * @return
     */
    List<SysRoleEntity> getByQuery(QueryWrapper<SysRoleEntity> qwRole);

    /**
     * 获取指定范围的角色列表
     *
     * @param scope
     * @return
     */
    List<SysRoleEntity> getByScope(Integer scope);
}
