package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.excel.model.ReadExcelListener;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.WordToPinYin;
import com.boot.modules.sys.dao.SysDictItemDao;
import com.boot.modules.sys.entity.SysDictCateEntity;
import com.boot.modules.sys.entity.SysDictItemEntity;
import com.boot.modules.sys.service.SysDictCateService;
import com.boot.modules.sys.service.SysDictItemService;
import com.boot.modules.sys.vo.SysDicItemVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemDao, SysDictItemEntity> implements SysDictItemService {


    /**
     * 只能包含字母、数字、某些特殊字符 "^[a-zA-Z0-9:_@|^%&',;=?$]*$"
     */
    private final static String CODE_PATTERN = "^[a-zA-Z0-9:_@|^%&',;=?$]*$";

    @Resource
    private SysDictCateService dictCateService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        IPage<SysDictItemEntity> page;

        Long dictCateId = StringUtils.isNotBlank((String) params.get("dictCateId")) ? Long.parseLong((String) params.get("dictCateId")) : null;
        String dictName = StringUtils.isNotBlank((String) params.get("dictName")) ? params.get("dictName").toString() : null;

        QueryWrapper<SysDictItemEntity> qw = MapUtils.getWrapperByParams(params, "sort", "rp_sys_dict_item", SysDictItemEntity.class);

        page = this.page(
                new Query<SysDictItemEntity>().getPage(params),
                qw
                        .lambda()
                        .eq(dictCateId != null, SysDictItemEntity::getDictCateId, dictCateId)
                        .like(StringUtils.isNotBlank(dictName), SysDictItemEntity::getName, dictName)
        );

        return new PageUtils(page);
    }

    /**
     * 根据字典项分类编码 获取 该分类下的字典项
     *
     * @param code
     * @return
     */
    @Override
    public List<SysDictItemEntity> getByCateCode(String code) {

        SysDictCateEntity dictCate;
        QueryWrapper<SysDictCateEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(SysDictCateEntity::getCateCode, code);
        List<SysDictCateEntity> cateList = dictCateService.list(qw);
        if (cateList != null && cateList.size() > 0) {
            dictCate = cateList.get(0);
            QueryWrapper<SysDictItemEntity> qwItem = new QueryWrapper<>();
            qwItem.lambda()
                    .eq(SysDictItemEntity::getDictCateId, dictCate.getId())
                    .orderByAsc(SysDictItemEntity::getSort);
            return list(qwItem);
        }
        return null;
    }

    /**
     * 根据字典分类编码批量查询字典项信息
     * @param codes
     * @return
     */
    @Override
    public Map<String, List<SysDicItemVo>> getByCateCodes(String[] codes) {
        List<SysDicItemVo> list = this.baseMapper.getByQuery(
                new QueryWrapper<SysDicItemVo>().in("b.cate_code", codes));
        if (!CollectionUtils.isEmpty(list)){
            return list.stream().collect(Collectors.groupingBy(SysDicItemVo::getCateCode));
        }

        return null;
    }

    /**
     * 通过excel 批量导入字典项
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean excelImport(MultipartFile file) {

        try {
            ReadExcelListener readExcelListener = new ReadExcelListener(SysDictItemEntity.class);

            List<SysDictItemEntity> list = ExcelUtils.readExcel(file, SysDictItemEntity.class, readExcelListener);

            for (SysDictItemEntity item : list) {
                // 验证编码格式
                boolean inspect = Pattern.matches(CODE_PATTERN, item.getCode());

                if (!inspect) {
                    throw new BusinessException("code只能包含英文字母、数字、_@|^$&',;=?这些字符");
                }

                item.setPinyin(WordToPinYin.toHanyuPinyin(item.getName()));
                item.setFirstLetter(WordToPinYin.toFristChar(item.getName()));

                if (item.getSort() == null) {
                    item.setSort(1);
                }
            }

            return this.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

}


