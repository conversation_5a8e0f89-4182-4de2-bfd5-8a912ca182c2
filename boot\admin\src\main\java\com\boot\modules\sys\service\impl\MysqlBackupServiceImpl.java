package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.*;
import com.boot.modules.sys.dao.BackupTaskDao;
import com.boot.modules.sys.entity.BackupTaskEntity;
import com.boot.commons.enums.BackupTaskEnums;
import com.boot.modules.sys.service.MysqlBackupService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class MysqlBackupServiceImpl extends ServiceImpl<BackupTaskDao, BackupTaskEntity> implements MysqlBackupService {

    @Resource
    BackupTaskDao backupTaskDao;

    @Override
    @Async("bootExecutor")
    public void backup(String backupFolderPath,
                       String fileName,
                       String database,
                       String mysqlPath) {

        //新建导出实体类，并赋初值
        BackupTaskEntity backupTaskEntity = new BackupTaskEntity();
        backupTaskEntity.setFileName(fileName);
        backupTaskEntity.setBackupDatetime(new Date());
        backupTaskEntity.setFilePath(backupFolderPath);
        //调用备份服务工具类
        BackupTaskEnums enums = MySqlBackupRestoreUtils.backup(backupFolderPath, fileName, database, mysqlPath);
        //记录任务执行状态
        backupTaskEntity.setBackupStatus(enums.getCode());
        backupTaskEntity.setBackupErrorMessage(enums.getMessage());

        if (!backupFolderPath.endsWith(File.separator) && !backupFolderPath.endsWith("/")) {
            backupFolderPath = backupFolderPath + File.separator;
        }
        File file = new File(backupFolderPath + fileName);
        if (file.exists()) {
            backupTaskEntity.setFileLength(file.length());
        }
        backupTaskDao.insert(backupTaskEntity);
    }

    @Override
    public boolean restore(String restoreFilePath) {
        return MySqlBackupRestoreUtils.restore(restoreFilePath);
    }

    @Override
    public PageUtils searchBackupTaskRecords(Map<String, Object> params) {
        QueryWrapper<BackupTaskEntity> qw = MapUtils.getWrapperByParams(params, "backup_datetime", "edc_backup_task", BackupTaskEntity.class);
        IPage<BackupTaskEntity> page = this.page(
                new Query<BackupTaskEntity>().getPage(params),
                qw
                        .lambda()
                        .gt(BackupTaskEntity::getId, "0")
        );
        return new PageUtils(page);

    }

    @Override
    public boolean deleteBackupRecordById(Long id) {
//        if(BackupConstants.DEFAULT_BACKUP_NAME.equals(name)) {
//            return R.fail("系统默认备份无法删除!");
//        }
//        String restoreFilePath = BackupConstants.BACKUP_FOLDER + name;
        return true;
    }

    @Override
    public void autoDeleteBackupFile() {
        //获取指定日期
        String days = DateUtils.getDateLastDays(BootAdminProperties.days);
        //查出需要删除的记录
        List<BackupTaskEntity> taskEntityList = backupTaskDao.selectList(
                new UpdateWrapper<BackupTaskEntity>()
                        .lambda()
                        .le(BackupTaskEntity::getBackupDatetime, days)
        );

        for (BackupTaskEntity entity : taskEntityList) {
            try {
                //递归删除目录以及该目录下的所有文件
                MySqlBackupRestoreUtils.delFiles(new File(entity.getFilePath()));
                backupTaskDao.deleteById(entity.getId());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
