package com.boot.commons.utils;

import org.apache.commons.lang3.RandomStringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;


/**
 * 获取16位随机数
 * <p>
 * 13位时间戳+3位随机数
 */
public class IdUtils {
    public static void main(String[] args) {
        //调用生成id方法

        System.out.println(getEmpi());
    }

    public static String getEmpi() {
        Calendar c = Calendar.getInstance();
        String time = new SimpleDateFormat("yyyy-MM-ddHHmmss").format(c.getTime()).toString();
        StringBuffer s = new StringBuffer(time.substring(14, 16));
        long sys = System.currentTimeMillis();
        s.append(Long.toString(sys).substring(11, 13));
        double tm = Math.random() * 10000 + 1;
        s.append(Double.toString(tm).substring(Double.toString(tm).length() - 6, Double.toString(tm).length()));

        return s.toString();
    }
}

