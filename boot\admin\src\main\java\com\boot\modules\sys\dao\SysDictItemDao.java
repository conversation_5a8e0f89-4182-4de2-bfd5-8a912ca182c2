package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.sys.entity.SysDictItemEntity;
import com.boot.modules.sys.entity.SysMenuEntity;
import com.boot.modules.sys.vo.SysDicItemVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据字典
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysDictItemDao extends BaseMapper<SysDictItemEntity> {

    /**
     * 根据角色id 查询菜单列表
     *
     * @param qw
     * @return
     */
    List<SysDicItemVo> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysDicItemVo> qw);

}
