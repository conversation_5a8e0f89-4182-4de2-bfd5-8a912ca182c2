package com.boot.modules.cas.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * cas数据同步接口返回结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CasResult implements Serializable {
    /**
     * 信息
     */
    private String message;
    /**
     * 状态code
     */
    private String code;
    /**
     * 结果信息
     */
    private Object results;

    /**
     * success
     */
    private Boolean success;
}
