package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.WordToPinYin;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysDictItemEntity;
import com.boot.modules.sys.service.SysDictItemService;
import com.boot.modules.sys.vo.SysDicItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 数据字典项
 *
 * <AUTHOR>
 */
@Api(tags = "数据字典项")
@RestController
@RequestMapping("sys/dict/item")
public class SysDictItemController {
    @Resource
    private SysDictItemService sysDictItemService;

    /**
     * 列表
     */
    @ApiOperation(value = "字典项列表", notes = "字典项列表")
    @GetMapping("/list")
    @RequiresPermissions("sys:dictitem:list")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysDictItemService.queryPage(params);

        return R.success(page);
    }

    /**
     * 信息
     */
    @ApiOperation(value = "获取指定指定字典项信息", notes = "获取指定指定字典项信息")
    @GetMapping("/{id}")
    @RequiresPermissions("sys:dictitem:info")
    public Result info(@PathVariable("id") Long id) {
        SysDictItemEntity dict = sysDictItemService.getById(id);

        return R.success(dict);
    }

    @ApiOperation(value = "根据字典分类编码查询字典项信息", notes = "根据字典分类编码查询字典项信息")
    @GetMapping("/code")
    @RequiresPermissions("sys:dictitem:code")
    public Result getByCateCode(@RequestParam String code) {

        if (StringUtils.isEmpty(code)) {
            return R.success(null);
        }
        List<SysDictItemEntity> list = sysDictItemService.getByCateCode(code);

        return R.success(list);
    }

    @ApiOperation(value = "根据字典分类编码批量查询字典项信息", notes = "根据字典分类编码批量查询字典项信息")
    @PostMapping("/codes")
//    @RequiresPermissions("sys:dictitem:codes")
    public Result getByCodes(@RequestBody String[] codes) {

        if (codes == null || codes.length == 0) {
            return R.success(null);
        }
        Map<String, List<SysDicItemVo>> map = sysDictItemService.getByCateCodes(codes);

        return R.success(map);
    }


    /**
     * 保存
     */
    @ApiOperation(value = "保存字典项", notes = "保存字典项")
    @SysLog("保存字典项")
    @PostMapping()
    @RequiresPermissions("sys:dictitem:save")
    public Result save(@RequestBody SysDictItemEntity dict) {
        //校验类型
        ValidatorUtils.validateEntity(dict, AddGroup.class);

        //唯一性验证 字典项 所有的code都不能重复
        QueryWrapper<SysDictItemEntity> qwByCode = new QueryWrapper<>();
        qwByCode.lambda()
                .eq(SysDictItemEntity::getCode, dict.getCode());
        List<SysDictItemEntity> listByCode = sysDictItemService.list(qwByCode);
        if (listByCode != null && listByCode.size() > 0) {
            return R.fail("字典项编码已存在");
        }

        QueryWrapper<SysDictItemEntity> qwByName = new QueryWrapper<>();
        qwByName.lambda()
                .eq(SysDictItemEntity::getDictCateId, dict.getDictCateId())
                .eq(SysDictItemEntity::getName, dict.getName());
        List<SysDictItemEntity> listByName = sysDictItemService.list(qwByName);
        if (listByName != null && listByName.size() > 0) {
            return R.fail("字典项名称已存在");
        }

        String pinyin = WordToPinYin.toHanyuPinyin(dict.getName());
        String firstLetter = WordToPinYin.toFristChar(dict.getName());
        dict.setPinyin(pinyin);
        dict.setFirstLetter(firstLetter);

        sysDictItemService.save(dict);

        return R.success();
    }

    /**
     * 修改
     */
    @ApiOperation(value = "修改字典项", notes = "修改字典项")
    @SysLog("修改字典项")
    @PutMapping()
    @RequiresPermissions("sys:dictitem:update")
    public Result update(@RequestBody SysDictItemEntity dict) {
        //校验类型
        ValidatorUtils.validateEntity(dict, UpdateGroup.class);

        //唯一性验证 编码不能重复
        QueryWrapper<SysDictItemEntity> qwByCode = new QueryWrapper<>();
        qwByCode.lambda()
                .eq(SysDictItemEntity::getCode, dict.getCode())
                .ne(SysDictItemEntity::getId, dict.getId());
        List<SysDictItemEntity> listByCode = sysDictItemService.list(qwByCode);
        if (listByCode != null && listByCode.size() > 0) {
            return R.fail("字典项编码已存在");
        }
        // 名称不能重复
        QueryWrapper<SysDictItemEntity> qwByName = new QueryWrapper<>();
        qwByName.lambda()
                .eq(SysDictItemEntity::getDictCateId, dict.getDictCateId())
                .eq(SysDictItemEntity::getName, dict.getName())
                .ne(SysDictItemEntity::getId, dict.getId());
        List<SysDictItemEntity> listByName = sysDictItemService.list(qwByName);
        if (listByName != null && listByName.size() > 0) {
            return R.fail("字典项名称已存在");
        }
        dict.setPinyin(WordToPinYin.toHanyuPinyin(dict.getName()));
        dict.setFirstLetter(WordToPinYin.toFristChar(dict.getName()));
        sysDictItemService.updateById(dict);

        return R.success();
    }

    /**
     * 删除
     */
    @ApiOperation(value = "删除字典项", notes = "删除字典项")
    @SysLog("删除字典项")
    @DeleteMapping()
    @RequiresPermissions("sys:dictitem:delete")
    public Result delete(@RequestBody Long[] ids) {
        sysDictItemService.removeByIds(Arrays.asList(ids));

        return R.success();
    }

    @ApiOperation(value = "字典项excel导入", notes = "字典项excel导入")
    @PostMapping("/excel/import")
    @RequiresPermissions("sys:dictitem:excelimport")
    public Result excelImport(@RequestParam("file") MultipartFile file) {

        boolean res = true;
        if (file == null) {
            throw new BusinessException("上传的文件为空");
        }

        res = sysDictItemService.excelImport(file);

        return res ? R.success("导入成功") : R.fail("导入失败");
    }


    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    @PostMapping("/down/temp")
//    @RequiresPermissions("sys:dictitem:downtemp")
    public Result downImportTemp(HttpServletResponse response) throws NoSuchFieldException {
//        List<List<String>> heads = getHeads(SysDictItemEntity.class);
        ExcelUtils.wirteExcel(response, "字典项导入模板", SysDictItemEntity.class, new ArrayList<>());

        return R.success("下载成功", true);
    }


//    private List<List<String>> getHeads(Class<T> clazz) throws NoSuchFieldException {
//
//        List<List<String>> heads = new ArrayList<>();
//        Field[] fields = clazz.getDeclaredFields();
//        Field field;
//        for (int i = 0; i < fields.length; i++) {
//            List<String> column = new ArrayList<>();
//            field = clazz.getDeclaredField(fields[i].getName());
//            field.setAccessible(true);
//            //在实体类属性字段上加上@ExcelProperty注解
//            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
//            if (excelProperty != null) {
//                String[] values = excelProperty.value();
//                StringBuilder value = new StringBuilder();
//                for (String v : values) {
//                    value.append(v);
//                }
//                column.add(value.toString());
//                heads.add(column);
//            }
//        }
//        return heads;
//
//    }

}
