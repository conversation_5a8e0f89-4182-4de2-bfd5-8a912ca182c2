package com.boot.modules.sync.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 柯林查询接口的body实体
 *
 * <AUTHOR>
 * @date 2021/7/2 14:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KelinRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询的表名
     */
    private String tableName;

    /**
     * 请求的表字段
     */
    private List<String> columns;

    /**
     * 页宽-10000
     */
    private Integer limit;

    /**
     * 第几条
     */
    private Integer offset;

    /**
     * 查询条件
     */
    private Map<String, List<Object>> params;

    /**
     * 排序字段
     * chenwei 2022/11/4
     */
    private Map<String, String> sortFields;

    /**
     * 比对时间
     */
    private Map<String,List<String>> updateTime;

    /**
     * 大于等于
     */
    private Map<String,Object> ge;

    /**
     * 小于等于
     */
    private Map<String,Object> le;

    /**
     * 大于
     */
    private Map<String,Object> gt;

    /**
     * 小于
     */
    private Map<String,Object> lt;

}
