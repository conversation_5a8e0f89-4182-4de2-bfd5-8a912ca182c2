package com.boot.modules.mobile.controller;

import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.SexMapUtil;
import com.boot.modules.mobile.vo.MessageCountVO;
import com.boot.modules.patient.dto.InboundProcessDto;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.patient.vo.DoctorRecommendProjectDiseaseVo;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.service.SysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 移动端推荐记录调用接口
 */
@Api(tags = "移动端推荐记录调用接口")
@RestController
@RequestMapping("/h5/recommend")
@Slf4j
public class H5RecommendRecordController extends AbstractController {

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private PatientService patientService;

    @Resource
    private SysDeptService sysDeptService;

    @ApiOperation(value = "获取受试者消息数目", notes = "获取受试者消息数目")
    @GetMapping({"/message/count"})
    public Result getCount(@RequestParam Boolean isHx) {
        Long userId = getUserId();
        if (userId == null) {
            throw new BusinessException("当前用户为空");
        }
        MessageCountVO vo = new MessageCountVO();
        int count = 0;
        if (isHx) {
            //华西医生查看已推荐和拟入组
            //查询已推荐总数
            Map<String, Object> param = new HashMap<>();
            param.put("userId", userId);
            param.put("status", PatTypeEnum.RECOMMENDED.getCode());
            PageUtils result = patientService.listByUserId(param);
            int reCount = result == null ? 0 : result.getTotalCount();
            vo.setReCommendCount(reCount);
            //查询拟入组总数
            Map<String, Object> newMap = new HashMap<>();
            newMap.put("userId", userId);
            newMap.put("status", PatTypeEnum.PROPOSE_INBOUND.getCode());
            result = patientService.listByUserId(newMap);
            int inToCount = result == null ? 0 : result.getTotalCount();
            vo.setIntoCount(inToCount);

            count = inToCount + reCount;
            vo.setTotalCount(count);
        } else {
            //医联体医生查看推荐记录
            Map<String, Object> param = new HashMap<>();
            param.put("userId", userId);
            List<DoctorRecommendProjectDiseaseVo> list = doctorRecommendationService.getListByQuery(param);
            count = CollectionUtils.isEmpty(list) ? 0 : list.size();
            vo.setTotalCount(count);
        }
        return R.success(vo);
    }


    @ApiOperation(value = "获取医联体医生受试者推荐记录", notes = "获取医联体医生受试者推荐记录")
    @GetMapping({"/ylt-doctor/record"})
    public Result getRecommendRecord(@RequestParam Map<String, Object> param) {
        Long userId = getUserId();
        if (userId == null) {
            throw new BusinessException("当前用户为空");
        }
        param.put("userId", userId);
        PageUtils result = doctorRecommendationService.getByQuery(param);
        if (result != null && !CollectionUtils.isEmpty(result.getList())) {
            List<DoctorRecommendProjectDiseaseVo> records = (List<DoctorRecommendProjectDiseaseVo>) result.getList();
            records.forEach(p -> p.setGender(SexMapUtil.getIcoSex(p.getGender())));
            result.setList(records);
        }

        return R.success(result);
    }

    @ApiOperation(value = "获取华医医生已参加项目的受试者推荐记录", notes = "获取华医医生已参加项目的受试者推荐记录")
    @GetMapping({"/hx-doctor/record"})
    public Result getHxRecommendRecord(@RequestParam Map<String, Object> param) {
        Long userId = getUserId();
        if (userId == null) {
            throw new BusinessException("当前用户为空");
        }
        param.put("userId", userId);
        PageUtils result = patientService.listByUserId(param);
        if (result != null && !CollectionUtils.isEmpty(result.getList())) {
            List<InboundProcessDto> records = (List<InboundProcessDto>) result.getList();
            records.forEach(p -> p.setGender(SexMapUtil.getIcoSex(p.getGender())));
            result.setList(records);
        }

        return R.success(result);
    }

    @GetMapping("/parent/code/list")
    @ApiOperation(value = "获取推荐医院列表", notes = "获取推荐医院列表")
    public Result getParentDeptAndCodeNotNull() {
        List<SysDeptEntity> deptList = sysDeptService.getParentDeptAndCodeNotNull();
        return R.success(deptList);
    }


}
