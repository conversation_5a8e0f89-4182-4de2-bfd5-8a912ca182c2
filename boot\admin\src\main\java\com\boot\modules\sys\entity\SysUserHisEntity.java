package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc : 用户his公共关系表
 * 一个用户在一个机构中，只能有一个his工号
 * @create 2022-05-05
 */
@Data
@TableName("rp_sys_user_his")
public class SysUserHisEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @NotNull(message = "userId不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long userId;

    @NotNull(message = "deptId不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long deptId;

    @NotNull(message = "deptCode不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String deptCode;

    @NotNull(message = "his工号不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private String hisForeignId;
}
