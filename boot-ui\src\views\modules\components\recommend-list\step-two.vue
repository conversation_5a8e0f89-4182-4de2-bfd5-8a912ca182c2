<template>
	<div class="stept-two">
		<div class="table-container">
			<el-form :inline="true" size="mini" :model="dataForm">
				<el-form-item label="状态">
					<el-select v-model="dataForm.isRead" clearable placeholder="状态" style="width: 150px">
						<el-option :label="$t('all')" value="" />
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>

				<el-form-item label="推荐医院">
					<el-select v-model="dataForm.medId" clearable placeholder="推荐医院" style="width: 200px">
						<el-option :label="$t('all')" value="" />
						<el-option v-for="item in hospitalList" :key="item.id" :label="item.name" :value="item.id" />
					</el-select>
				</el-form-item>

				<el-form-item>
					<el-input v-model="dataForm.inputQuery" clearable placeholder="登记号或者姓名" style="width: 150px" />
				</el-form-item>

				<el-form-item>
					<el-button type="primary" icon="el-icon-search" @click="getDataList(true)">{{ $t('query') }}</el-button>
				</el-form-item>
			</el-form>
			<el-table v-loading="dataListLoading" size="mini" :data="dataList">
				<el-table-column prop="regno" label="登记号" header-align="center" align="center" />
				<el-table-column prop="name" label="姓名" header-align="center" align="center" />
				<el-table-column prop="tel" label="联系方式" header-align="center" align="center" />
				<el-table-column prop="gender" label="性别" header-align="center" align="center">
					<template slot-scope="scope">
						{{ getGender(scope.row.gender) }}
					</template>
				</el-table-column>
				<el-table-column prop="birthday" label="年龄" header-align="center" align="center">
					<template slot-scope="scope">
						{{ calculateAge(scope.row.birthday) }}
					</template>
				</el-table-column>
				<el-table-column prop="type" label="推荐方式" header-align="center" align="center">
					<template slot-scope="scope">
						<span v-if="scope.row.type == 0">系统推荐</span>
						<span v-else-if="scope.row.type == 1">手动推荐</span>
						<span v-else-if="scope.row.type == 2">医联体推荐</span>
						<span v-else>--</span>
					</template>
				</el-table-column>
				<el-table-column prop="recommendHospitalName" label="推荐医院" header-align="center" align="center" />
				<el-table-column prop="recommendName" label="推荐医生" header-align="center" align="center" />
				<el-table-column prop="recommendTime" label="推荐时间" header-align="center" align="center" />
				<el-table-column prop="isRead" label="状态" header-align="center" align="center" width="120px">
					<template slot-scope="scope">
						<el-select v-model="scope.row.isRead" placeholder="请选择" size="mini" @change="isReadChange($event, scope.row)">
							<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column :label="$t('handle')" header-align="center" align="center" min-width="200px">
					<template slot-scope="scope">
						<el-button size="mini" @click="toConditionDialog(scope.row)">查看</el-button>
						<el-button size="mini" type="primary" @click="intoGroup(scope.row)">入组</el-button>
						<el-button size="mini" type="danger" @click="returnBack(scope.row)">退回</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<el-dialog :visible.sync="visible" title="退回原因" :close-on-click-modal="false" :close-on-press-escape="false" width="40%">
			<el-form ref="returnForm" :model="returnForm" :rules="returnRules" label-width="80px">
				<el-form-item label="退回原因" prop="returnReason">
					<el-radio-group v-model="returnForm.returnReason">
						<div style="margin-bottom: 10px"><el-radio label="患者拒签">患者拒签</el-radio></div>
						<div style="margin-bottom: 10px"><el-radio label="不符合纳排标准">不符合纳排标准</el-radio></div>
						<div style="margin-bottom: 10px"><el-radio label="其他原因">其他原因</el-radio></div>
					</el-radio-group>
				</el-form-item>
				<el-form-item v-if="returnForm.returnReason === '其他原因'" label="具体原因" prop="otherReason">
					<el-input
						v-model="returnForm.otherReason"
						type="textarea"
						:rows="3"
						placeholder="请输入具体原因"
						maxlength="200"
						show-word-limit
					/>
				</el-form-item>
			</el-form>

			<template slot="footer">
				<el-button size="mini" @click="visible = false">取消</el-button>
				<el-button size="mini" type="primary" @click="quitConfirm">确定</el-button>
			</template>
		</el-dialog>

		<!-- 分页 -->
		<el-pagination
			slot="footer"
			:current-page="page"
			:page-sizes="[10, 20, 50, 100]"
			:page-size="limit"
			:total="total"
			layout="total, sizes, prev, pager, next, jumper,slot"
			@size-change="pageSizeChangeHandle"
			@current-change="pageCurrentChangeHandle"
		>
			<i class="el-icon-refresh-right page-refresh-btn" @click="refreshHandle" />
			<span v-if="dataListSelections && dataListSelections.length > 0" class="page-selected-total">已选中 {{ dataListSelections.length }} 条数据</span>
		</el-pagination>
	</div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import { hisApiService } from '@/views/d2admin/his/his-recommend-api.js'
export default {
	mixins: [mixinViewModule],
	props: {
		// 推荐医院
		hospitalList: {
			type: Array,
			default() {
				return []
			}
		}
	},
	data() {
		return {
			foreignId: '',
			regNo: '',
			doctorUserId: '', // 当前用户id
			patEmpi: '',
			mixinViewModuleOptions: {
				activatedIsNeed: true,
				getDataListURL: '/pat/list',
				getDataListIsPage: true
			},
			dataForm: {
				status: '1',
				projectId: '',
				subProjectId: '',
				isRead: '',
				inputQuery: '',
				order: 'desc', // 排序，asc／desc
				orderField: 'recommendTime', // 排序，字段
				medId: ''
			},
			options: [
				{
					value: 1,
					label: '已阅'
				},
				{
					value: 0,
					label: '未阅'
				}
			],
			visible: false,
			currentRow: null, // 当前操作的行数据
			recommendInfo: {}, // 推荐信息
			returnForm: {
				returnReason: '',
				otherReason: ''
			},
			returnRules: {
				returnReason: [
					{ required: true, message: '请选择退回原因', trigger: 'change' }
				],
				otherReason: [
					{ required: true, message: '请输入具体原因', trigger: 'blur' }
				]
			}
		}
	},
	watch: {},
	created() {
		this.init()
	},
	methods: {
		setTestData() {
			setTimeout(() => {
				this.dataList = [
					{
						projectId: 64,
						subProjectId: 3326,
						empiid: 121647588,
						visitId: 1,
						userId: 1,
						name: '张三',
						gender: 1,
						birthday: '1992-04-11',
						idCard: '15555555555',
						initials: 'zs',
						tel: '15555555555',
						recommendId: 1,
						recommendName: '张三',
						recommendReason: '',
						recommendTime: '2023-04-11',
						outReason: '',
						inboundTime: '2023-04-11',
						signingTime: '2023-04-11',
						signingAuditId: 1,
						signingAuditName: '张三',
						inboundId: 1,
						inboundName: '张三',
						type: 2
					}
				]
			}, 1000)
		},
		init() {
			this.setTestData()
		},
		// 展示纳排条件满足弹窗
		toConditionDialog(row) {
			if (row.type && row.type == 2) {
				// 打开医联体推荐的受试者详情弹层\
				this.$emit('showYltDialog', {
					projectId: row.projectId,
					subProjectId: row.subProjectId,
					empi: row.empiid,
					srcEmpiid: row.srcEmpiid
				})
			} else {
				this.$emit('showDialog', {
					projectId: row.projectId,
					subProjectId: row.subProjectId,
					empi: row.empiid,
					srcEmpiid: row.srcEmpiid
				})
			}
		},
		// 入组
		intoGroup(row) {
			this.$confirm(`确定入组【${row.name}】到当前项目?`, this.$t('prompt.title'), {
				loading: true,
				confirmButtonText: this.$t('confirm'),
				cancelButtonText: this.$t('cancel'),
				type: 'warning'
			}).then(() => {
				hisApiService
					.setPatIntoGroup({
						id: row.doctorRecommendId,
						empi: row.empiid,
						projectId: row.projectId,
						subProjectId: row.subProjectId
					})
					.then(res => {
						this.$message({
							message: this.$t('prompt.success'),
							type: 'success',
							duration: 500,
							onClose: () => {
								this.getDataList()
							}
						})
					})
			})
		},
		returnBack(row) {
			// 保存当前行数据和推荐信息
			this.currentRow = row
			this.recommendInfo = {
				subProjectId: row.subProjectId,
				empiid: row.empiid
			}

			// 重置表单
			this.returnForm = {
				returnReason: '',
				otherReason: ''
			}

			// 清除表单验证
			this.$nextTick(() => {
				if (this.$refs.returnForm) {
					this.$refs.returnForm.clearValidate()
				}
			})

			// 显示弹窗
			this.visible = true
		},
		// 确认退回
		quitConfirm() {
			this.$refs.returnForm.validate((valid) => {
				if (valid) {
					// 如果选择了"其他原因"但没有填写具体原因，需要验证
					if (this.returnForm.returnReason === '其他原因' && !this.returnForm.otherReason.trim()) {
						this.$message.error('请输入具体原因')
						return
					}

					// 调用接口
					hisApiService
						.setPatBackGroup({
							subProjectId: this.recommendInfo.subProjectId,
							empi: this.recommendInfo.empiid,
							reason: this.returnForm.returnReason === '其他原因' ? this.returnForm.otherReason : this.returnForm.returnReason
						})
						.then(res => {
							this.$message({
								message: this.$t('prompt.success'),
								type: 'success',
								duration: 500,
								onClose: () => {
									this.visible = false
									this.getDataList()
								}
							})
						})
						.catch(error => {
							console.error('退回失败:', error)
							this.$message.error('退回失败，请重试')
						})
				}
			})
		},
		getGender(gender) {
			if (gender == 1) {
				return '女'
			} else if (gender == 0) {
				return '男'
			} else {
				return '保密'
			}
		},
		calculateAge(birthday) {
			// 根据日期算年龄
			if (birthday) {
				birthday = birthday.split('-')
				// 新建日期对象
				const date = new Date()
				// 今天日期，数组，同 birthday
				const today = [date.getFullYear(), date.getMonth() + 1, date.getDate()]
				// 分别计算年月日差值
				const age = today.map((val, index) => {
					return val - birthday[index]
				})
				// 当天数为负数时，月减 1，天数加上月总天数
				if (age[2] < 0) {
					// 简单获取上个月总天数的方法，不会错
					const lastMonth = new Date(today[0], today[1], 0)
					age[1]--
					age[2] += lastMonth.getDate()
				}
				// 当月数为负数时，年减 1，月数加上 12
				if (age[1] < 0) {
					age[0]--
					age[1] += 12
				}

				// console.log(age[0] + '岁' + age[1] + '月' + age[2] + '天')
				if (age[0] < 1) {
					return 1
				} else {
					return age[0]
				}
			}
		},
		// 改变患者状态
		isReadChange(val, data) {
			// console.log(val, data, '219')
			this.$axios.post(`pat/read/${data.doctorRecommendId}/${val}`).then(res => {
				this.getDataList()
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.stept-two {
	height: 100%;

	.table-container {
		height: calc(100% - 35px);

		.el-table {
			width: 100%;
			height: calc(100% - 50px);
		}
	}
}
</style>