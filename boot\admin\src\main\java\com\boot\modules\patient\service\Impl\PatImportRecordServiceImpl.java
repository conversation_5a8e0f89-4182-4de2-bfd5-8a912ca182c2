package com.boot.modules.patient.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.patient.dao.PatImportRecordDao;
import com.boot.modules.patient.entity.PatImportRecordEntity;
import com.boot.modules.patient.service.PatImportRecordService;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class PatImportRecordServiceImpl extends ServiceImpl<PatImportRecordDao, PatImportRecordEntity>  implements PatImportRecordService {
    @Resource
    private ProjectService projectService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        Long curUserId = MapUtils.getValue(params, "userId", Long.class);
        //项目id 、子项目id 应该是必传
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        Integer status = MapUtils.getValue(params, "status", Integer.class);

        String startTime = MapUtils.getValue(params, "startTime", String.class);
        String endTime = MapUtils.getValue(params, "endTime", String.class);

        String orderField = MapUtils.getValue(params, "orderField", String.class);

        boolean isProjAdmin = false;
        if (projectId != null) {
            ProjectEntity project = projectService.getById(projectId);
            Long projectAdminId = project.getProjectAdminId();
            if (curUserId.equals(projectAdminId)) {
                isProjAdmin = true;
            }
        }

        QueryWrapper<PatImportRecordEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(curUserId != 0L && !isProjAdmin, PatImportRecordEntity::getUserId, curUserId)
                .eq(subProjectId != null, PatImportRecordEntity::getSubProjectId, subProjectId);

        qw
                .ge(!StringUtils.isEmpty(startTime), "a.start_time", startTime)
                .le(!StringUtils.isEmpty(endTime), "a.end_time", endTime + " 23:59:59")
                .eq(status != null, "a.status", status);

        if (StringUtils.isBlank(orderField)) {
            qw.orderByDesc("a.start_time", "a.id");
        }

        qw.lambda().groupBy(PatImportRecordEntity::getId);

        IPage<PatImportRecordEntity> pageFilter = new Query<PatImportRecordEntity>().getPage(params);
        IPage<PatImportRecordEntity> page = baseMapper.queryPage(pageFilter, qw);

        return new PageUtils(page);
    }
}
