package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @desc :回收站表对应entity对象
 * @create 2021-04-16
 */
@Data
@Accessors(chain = true)
@TableName("rp_recycle")
public class SysRecycleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * entity类名
     */
    @NotBlank(message = "entity类名不许为空", groups = {AddGroup.class})
    private String entityname;

    /**
     * imple bean
     */
    @NotBlank(message = "impl不许为空", groups = {AddGroup.class})
    private String implname;

    /**
     * 登录用户名
     */
    @NotBlank(message = "username不许为空", groups = {AddGroup.class})
    private String username;

    /**
     * 数据
     */
    @NotBlank(message = "数据不许为空", groups = {AddGroup.class})
    private String data;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date date;


    /**
     * 排序
     */
    @NotBlank(message = "排序不许为空", groups = {AddGroup.class})
    private Integer sort;
}
