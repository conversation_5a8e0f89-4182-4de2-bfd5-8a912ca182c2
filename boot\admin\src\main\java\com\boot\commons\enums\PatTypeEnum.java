package com.boot.commons.enums;

/**
 * 受试者状态枚举
 */
public enum PatTypeEnum {
    PROPOSE_RECOMMENDATION(0, "拟推荐", "未推荐之前状态"),
    RECOMMENDED(1, "已推荐", "患者已经被推荐给项目"),
    PROPOSE_INBOUND(2, "拟入组", "入组未签署知情同意书"),
    INBOUNDED(3, "已入组", "签署知情同意书后的状态"),
    EXIT_ABNORMALLY(4, "异常退出", "由于各种原因异常退出"),
    EXIT_NORMALLY(5, "正常退出", "由于项目完成后受试者正常退出"),
    REVERT(6, "退回", "拟入组界面退回"),
    REFUSE(7, "驳回", "推荐患者申请被驳回");

    private Integer code;

    private String name;

    private String desc;

    PatTypeEnum(Integer code, String name, String desc){
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static PatTypeEnum getByCode(Integer code){
        if (code != null) {
            for (PatTypeEnum patTypeEnum : PatTypeEnum.values()) {
                if (patTypeEnum.getCode().equals(code) ) {
                    return patTypeEnum;
                }
            }
        }
        return null;
    }
}
