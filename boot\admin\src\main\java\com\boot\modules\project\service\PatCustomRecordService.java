package com.boot.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.mobile.vo.PatientGroupDetailVO;
import com.boot.modules.project.entity.PatCustomRecordEntity;

public interface PatCustomRecordService extends IService<PatCustomRecordEntity> {
    /**
     * 获取详情
     * @param empi
     * @param subProjectId
     * @return
     */
    PatientGroupDetailVO getDetail(String empi, Long subProjectId);
}
