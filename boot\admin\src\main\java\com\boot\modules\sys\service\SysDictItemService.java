package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysDictItemEntity;
import com.boot.modules.sys.vo.SysDicItemVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 数据字典
 *
 * <AUTHOR>
 */
public interface SysDictItemService extends IService<SysDictItemEntity> {

    PageUtils queryPage(Map<String, Object> params);

    /**
     * 根据字典项分类编码 获取 该分类下的字典项
     * @param code
     * @return
     */
    List<SysDictItemEntity> getByCateCode(String code);

    /**
     * 根据字典分类编码批量查询字典项信息
     * @param codes
     * @return
     */
    Map<String, List<SysDicItemVo>> getByCateCodes(String[] codes);

    /**
     * 通过excel 批量导入字典项
     * @param file
     * @return
     */
    boolean excelImport(MultipartFile file);

}

