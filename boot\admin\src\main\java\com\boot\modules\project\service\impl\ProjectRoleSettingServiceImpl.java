package com.boot.modules.project.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.modules.project.dao.ProjectRoleSettingDao;
import com.boot.modules.project.entity.ProjectRoleSettingEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.service.ProjectRoleSettingService;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.service.SysRoleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @desc :项目角色配置
 * @create 2021-05-20
 */
@Service
public class ProjectRoleSettingServiceImpl extends
        ServiceImpl<ProjectRoleSettingDao, ProjectRoleSettingEntity> implements ProjectRoleSettingService {

    @Resource
    private SysRoleService roleService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectRoleSettingService projectRoleSettingService;

    @Override
    public List<ProjectRoleSettingEntity> query(Long subProjectId, Long roleId) {
        QueryWrapper<ProjectRoleSettingEntity> qw = new QueryWrapper<ProjectRoleSettingEntity>();
        qw.lambda().eq(ProjectRoleSettingEntity::getSubProjectId, subProjectId)
                .eq(roleId != null, ProjectRoleSettingEntity::getRoleId, roleId);

        List<ProjectRoleSettingEntity> list = queryList(qw);

        return list;
    }

    @Override
    public List<ProjectRoleSettingEntity> queryBySubProjectId(Long subProjectId, Integer projectCategory) {
        QueryWrapper<ProjectRoleSettingEntity> qw = new QueryWrapper<ProjectRoleSettingEntity>();
        qw.lambda().eq(ProjectRoleSettingEntity::getSubProjectId, subProjectId);

        List<ProjectRoleSettingEntity> list = queryList(qw, subProjectId, projectCategory);

        return list;
    }

    /**
     * 获取指定用户在指定子项目中的权限
     *
     * @param userId
     * @param subProjectId
     * @return
     */
    @Override
    public ProjectRoleSettingEntity getByUserIdAndSubProjId(Long userId, Long subProjectId) {
        // 项目管理员
        List<ProjectRoleSettingEntity> listAdmin = baseMapper.getByAdminUserIdAndSubProjId(subProjectId, userId);
        if (!CollectionUtils.isEmpty(listAdmin)) {
            return listAdmin.get(0);
        }

        // 项目成员
        List<ProjectRoleSettingEntity> list = baseMapper.getByUserIdAndSubProjId(subProjectId, userId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public boolean saveSetting(List<ProjectRoleSettingEntity> settings, Long subProjectId) {
        if (CollectionUtils.isEmpty(settings)) {
            return true;
        }
//        List<ProjectRoleSettingEntity> result = new ArrayList<ProjectRoleSettingEntity>();
//        for (ProjectRoleSettingEntity setting : settings) {
//            Integer dataAuth = setting.getDataAuth();
//            Long roleId = setting.getRoleId();
//            if (dataAuth == 0) {
//                QueryWrapper<ProjectRoleSettingEntity> qw = new QueryWrapper<ProjectRoleSettingEntity>();
//                qw.lambda()
//                        .eq(ProjectRoleSettingEntity::getRoleId, roleId)
//                        .eq(ProjectRoleSettingEntity::getSubProjectId, subProjectId);
//                if (projectRoleSettingService.count(qw) > 0) {
//                    projectRoleSettingService.remove(qw);   //删除已存在且需要重置的数据
//                }
//            } else {
//                result.add(setting); //将修改的数据添加到result数组里
//            }
//        }
//        this.saveOrUpdateBatch(result);

//        //先查询对应项目的配置
//        List<ProjectRoleSettingEntity> list = query(subProjectId, null);

//        if (CollectionUtils.isEmpty(list)) {
//            this.saveBatch(settings);
//        } else {
//            this.updateBatchById(settings);
//        }

        return this.saveOrUpdateBatch(settings);
    }

    private List<ProjectRoleSettingEntity> queryList(QueryWrapper<ProjectRoleSettingEntity> wq, Long subProjectId, Integer projectCategory) {
        List<ProjectRoleSettingEntity> list = this.baseMapper.selectList(wq);

        // 获取角色名称
        List<SysRoleEntity> roleLists = projectCategory == null ? null : roleService.getByScope(projectCategory);

        List<ProjectRoleSettingEntity> newList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleLists)) {
            for (SysRoleEntity roleEntity : roleLists) {
                //过滤掉项目管理员
//                if (roleEntity.getIsProjAdmin() != null && roleEntity.getIsProjAdmin().equals(1)) {
//                    continue;
//                }
                ProjectRoleSettingEntity roleSetting = null;
                if (!CollectionUtils.isEmpty(list)) {
                    for (ProjectRoleSettingEntity entity : list) {
                        if (entity.getRoleId().equals(roleEntity.getId())) {
                            roleSetting = entity;
                            roleSetting.setRoleName(roleEntity.getRoleName());
                            break;
                        }
                    }
                }

                if (roleSetting == null) {
                    roleSetting = new ProjectRoleSettingEntity();
                    roleSetting.setId(0L);
                    roleSetting.setRoleId(roleEntity.getId());
                    roleSetting.setRoleName(roleEntity.getRoleName());
                    roleSetting.setDataAuth(null);
                    roleSetting.setSubProjectId(subProjectId);
                }

                newList.add(roleSetting);
            }
        }

        return newList;
    }

    private List<ProjectRoleSettingEntity> queryList(QueryWrapper<ProjectRoleSettingEntity> wq) {
        List<ProjectRoleSettingEntity> list = this.baseMapper.selectList(wq);

        // 获取角色名称
        List<SysRoleEntity> roleLists = roleService.list();

        if (CollectionUtils.isEmpty(roleLists) && CollectionUtils.isEmpty(list)) {
            for (ProjectRoleSettingEntity entity : list) {
                List<SysRoleEntity> curRole = roleLists.stream().filter(f ->
                        f.getId().equals(entity.getRoleId())).collect(Collectors.toList());
                String roleName = curRole.size() == 0 ? null : curRole.get(0).getRoleName();

                entity.setRoleName(roleName);
            }
        }

        return list;
    }


    /**
     * 子项目新增添加默认配置
     * @param subProjectEntity
     * @return
     */
    @Override
    public boolean addConfigInit(SubProjectEntity subProjectEntity) {
        // 获取角色
        List<SysRoleEntity> roleLists = roleService.getByScope(2);

        // 设置初始值
        List<ProjectRoleSettingEntity> settings = new ArrayList<>();

        for (SysRoleEntity roleEntity : roleLists) {
            ProjectRoleSettingEntity projectRoleSettingEntity = new ProjectRoleSettingEntity();
            projectRoleSettingEntity.setIsTuomin(1);
            projectRoleSettingEntity.setRoleId(roleEntity.getId());
            projectRoleSettingEntity.setSubProjectId(subProjectEntity.getId());
            if (roleEntity.getIsProjAdmin().equals(1)) {
                // 项目管理员查所有
                projectRoleSettingEntity.setDataAuth(1);
            } else if (roleEntity.getIsInput().equals(1)){
                // 数据录入员查自己录入
                projectRoleSettingEntity.setDataAuth(3);
            } else{
                // 其他角色查自己中心的
                projectRoleSettingEntity.setDataAuth(2);
            }
            settings.add(projectRoleSettingEntity);
        }
        return this.saveBatch(settings);
    }

    /**
     * 子项目新增添加默认配置
     * @param subProjectEntity
     * @return
     */
    @Override
    public boolean updateConfigInit(SubProjectEntity subProjectEntity, List<ProjectRoleSettingEntity> projectRoleSettingEntityList) {
        // 获取角色
        List<SysRoleEntity> roleLists = roleService.getByScope(2);

        // 设置初始值
        List<ProjectRoleSettingEntity> settings = new ArrayList<>();

        for (SysRoleEntity roleEntity : roleLists) {
            List<ProjectRoleSettingEntity> exist = projectRoleSettingEntityList.stream().filter(p -> p.getRoleId().equals(roleEntity.getId())).collect(Collectors.toList());
            ProjectRoleSettingEntity projectRoleSettingEntity = new ProjectRoleSettingEntity();
            if (!CollectionUtils.isEmpty(exist)) {
                projectRoleSettingEntity = exist.get(0);
            } else {
                projectRoleSettingEntity.setRoleId(roleEntity.getId());
                projectRoleSettingEntity.setSubProjectId(subProjectEntity.getId());
                projectRoleSettingEntity.setDataAuth(0);
            }
            if (projectRoleSettingEntity.getDataAuth().equals(0)) {
                if (roleEntity.getIsProjAdmin().equals(1)) {
                    // 项目管理员查所有
                    projectRoleSettingEntity.setDataAuth(1);
                } else if (roleEntity.getIsInput().equals(1)) {
                    // 数据录入员查自己录入
                    projectRoleSettingEntity.setDataAuth(3);
                } else {
                    // 其他角色查自己中心的
                    projectRoleSettingEntity.setDataAuth(2);
                }
            }
            projectRoleSettingEntity.setIsTuomin(1);
            settings.add(projectRoleSettingEntity);
        }
        return this.saveOrUpdateBatch(settings);
    }
}
