package com.boot.modules.mobile.model;

import lombok.Data;

@Data
public class PatCustomDetailModel {
    /**
     * 患者名
     */
    private String patientName;
    /**
     * 机构名
     */
    private String medName;
    /**
     * 医生名
     */
    private String doctorName;
    /**
     * 推荐时间
     */
    private String recommendTime;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 备注
     */
    private String content;
    /**
     * 配置ID
     */
    private Long groupConfigId;

    /**
     * 推荐项目名
     */
    private String subProjectName;
    /**
     * 性别
     */
    private Integer gender;

    private String empiid;
    /**
     * 归一后的empiid，一般是和empi一致的，但是在某些情况下，例如移动端，可能会有差异，empiid是随机生成的，srcEmpiid是真实归一后的
     */
    private String srcEmpiid;

    private String birthday;

    private String regno;
}
