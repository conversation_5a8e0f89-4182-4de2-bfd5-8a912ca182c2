package com.boot.commons.aspect;

import com.boot.modules.sys.entity.SysRecycleEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysRecycleService;
import com.google.gson.Gson;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 回收站切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class RecycleAspect {

    @Resource
    private SysRecycleService sysRecycleService;

    /**
     * 切点
     */
    @Pointcut(value = "@annotation(com.boot.commons.annotation.Recycle)")
    public void recyclePoint() {
    }


    @Before("recyclePoint()")
    public void before(JoinPoint point) {
        Object[] args = point.getArgs();
        String curParams = new Gson().toJson(args[0]);
        Object aThis = point.getTarget();
        Class<?> aClass = aThis.getClass();
        String[] params=curParams.substring(1,curParams.length()-1).split(",");
        List<Long> numbers=new ArrayList<>();
        String preImpleName = String.valueOf(point.getTarget());
        String impleName = preImpleName.split("@")[0];
        for(int i=0;i<params.length;i++){
            numbers.add(Long.parseLong(params[i]));
        }
        Method selectById = null;
        try {
            selectById = aClass.getMethod("listByIds", Collection.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        List<?>  list = null;
        try {
            list = (List<?>)selectById.invoke(aThis, numbers);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        System.out.println("list是："+list);
        String data = new Gson().toJson(list);
        System.out.println("data是："+data);
        String entityName = list.get(0).getClass().getName();
        SysUserEntity user = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal());
        SysRecycleEntity sysRecycleEntity = new SysRecycleEntity();
        sysRecycleEntity.setEntityname(entityName)
                .setImplname(impleName)
                .setUsername(user.getUsername())
                .setData(data).setDate(new Date())
                .setSort(new Random().nextInt(10));
        sysRecycleService.insert(sysRecycleEntity);
    }
}
