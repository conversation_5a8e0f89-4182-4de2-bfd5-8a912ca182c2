package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.sys.dto.PassWordChangeDto;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.vo.UserAccessTokenVo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


/**
 * 系统用户
 *
 * <AUTHOR>
 */
public interface SysUserService extends IService<SysUserEntity> {

    PageUtils queryPage(Map<String, Object> params);

    /**
     * @param allCenterUserQw
     * @return
     */
    List<SysUserEntity> getByQuery(QueryWrapper<SysUserEntity> allCenterUserQw);

    /**
     * 查询用户的所有权限
     *
     * @param userId 用户ID
     */
    List<String> queryAllPerms(Long userId);

    /**
     * 查询用户的所有菜单ID
     */
    List<Long> queryAllMenuId(Long userId);

    /**
     * 按内外网查询用户的所有菜单ID
     */
    List<Long> queryAllMenuId(Long userId, Long type);

    /**
     * 分页获取指定机构中的用户
     *
     * @param params
     * @return
     */
    PageUtils queryByDeptIds(List<Long> deptIds, Map<String, Object> params);


    /**
     * @param deptIds
     * @param params
     * @param isReturnNoExpiredUser 是否返回没过期的用户 true 返回没过期的
     * @return
     */
    PageUtils queryByDeptIds(List<Long> deptIds, Map<String, Object> params, boolean isReturnNoExpiredUser);

    /**
     * 根据用户名，查询系统用户
     */
    SysUserEntity queryByUserName(String username);

    /**
     * 根据his工号，查询系统用户
     */
    SysUserEntity queryByUserCode(String userCode);

    /**
     * 获取指定his工号及机构的用户
     * @param foreignId his工号
     * @param deptCode 机构编码
     * @return
     */
    SysUserEntity getByForignAndDeptCode(String foreignId, String deptCode);

    /**
     * 根据用户手机号 和项目id查用户
     * @param telPhone
     * @param subProjectId
     * @return
     */
    SysUserEntity queryByMobile(String telPhone, Long subProjectId);

    /**
     * 根据用户手机号 和项目id查用户
     * @param telPhone
     * @return
     */
    List<SysUserEntity> queryByMobile(String telPhone);

    /**
     * 保存用户
     */
    void saveUser(SysUserEntity user);

    /**
     * 修改用户
     */
    void update(SysUserEntity user);

    /**
     * @param userId      用户ID
     * @param password    旧密码
     * @param newPassword 新密码
     * @return
     */
    boolean updatePassword(Long userId, String password, String newPassword);

    String createUserMessage(SysUserEntity user);

    @Async("bootExecutor")
    CompletableFuture<String> findUser(String user) throws InterruptedException;

    /**
     * 用户excel批量导入
     *
     * @param file
     * @return
     */
    boolean excelImport(MultipartFile file, Long userId);

    /**
     * 邮箱找回密码
     *
     * @param wordChangeDto
     * @return
     */
    boolean changePwd(PassWordChangeDto wordChangeDto);

    /**
     * 自动修改用户的状态
     */

    void autoUpdateUserStatus();

    Result getWatermark(SysUserEntity user);

    void asynUser();

    UserAccessTokenVo getAccessToken();

    /**
     * 获取指定机构下的所有用户
     * @param deptId
     * @return
     */
    List<Long> getUserIdByDeptId(List<Long> deptIdList);

    /**
     * 统计相关结构项目病种关联数据
     * @param deptName
     * @return
     */
    List<ReportDto> listCount(String doctorName, List<Long> deptId, Date startTime, Date endTime);
}
