package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.mobile.model.PatCustomDetailModel;
import com.boot.modules.mobile.vo.PatientGroupDetailVO;
import com.boot.modules.project.dao.PatCustomRecordDao;
import com.boot.modules.project.entity.PatCustomRecordEntity;
import com.boot.modules.project.service.PatCustomRecordService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class PatCustomRecordServiceImpl extends ServiceImpl<PatCustomRecordDao, PatCustomRecordEntity>
        implements PatCustomRecordService {
    @Override
    public PatientGroupDetailVO getDetail(String empi, Long subProjectId) {
        if (StringUtils.isBlank(empi)) {
            throw new BusinessException("患者唯一ID不能为空");
        }
        if (subProjectId == null) {
            throw new BusinessException("子项目ID不能为空");
        }
        PatientGroupDetailVO vo = new PatientGroupDetailVO();
        QueryWrapper<Object> qw = new QueryWrapper<>();
        qw.eq("a.empiid", empi)
                .eq("b.sub_project_id", subProjectId);
        List<PatCustomDetailModel> list = this.getBaseMapper().getDetailInfo(qw);
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        List<Long> ids = new ArrayList<>();
        PatCustomDetailModel start = list.get(0);
        vo.setRegno(start.getRegno());
        vo.setEmpi(start.getEmpiid());
        vo.setSrcEmpiid(start.getSrcEmpiid());
        vo.setBirthday(start.getBirthday());
        vo.setGender(start.getGender());
        vo.setSubProjectName(start.getSubProjectName());
        vo.setContent(start.getContent());
        vo.setPhone(start.getPhone());
        vo.setDoctorName(start.getDoctorName());
        vo.setMedName(start.getMedName());
        vo.setRecommendTime(start.getRecommendTime());
        vo.setPatientName(start.getPatientName());
        for (PatCustomDetailModel model : list) {
            ids.add(model.getGroupConfigId());
        }
        vo.setGroupConfigIds(ids);
        return vo;
    }
}
