package com.boot.commons.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.boot.commons.constants.ConfigConst;
import com.boot.commons.enums.GrantTypeEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单点登录工具类
 *
 * @Author: wangpenghui
 * @Date: 2021/6/17 18:54
 */
public class SSOUtil {
    private static final Logger log = LogManager.getLogger(SSOUtil.class);

    /**
     * 获取授权码url及传参
     * @return
     */
    public static String getAuthorizeUrl() {
        String authorizeUrl = ConfigProperties.getKey(ConfigConst.sso_authorize_url);
        String clientId = ConfigProperties.getKey(ConfigConst.sso_client_id);
        log.info("应用标识： " + clientId);
        String redirectUri = ConfigProperties.getKey(ConfigConst.sso_redirect_uri);
        return authorizeUrl + "?" + ConfigConst.sso_client_id + "=" + clientId +
                "&" + ConfigConst.sso_redirect_uri + "=" + redirectUri +
                "&" + ConfigConst.sso_response_type + "=code" +
                "&" + ConfigConst.sso_state + "=123";
    }

    /**
     * 通过授权码code获取access_token
     *
     * @param code
     * @return
     */
    public static String getAccessTokenByCode(String code) {
        log.info("======根据code获取access_token======");
        String url = ConfigProperties.getKey(ConfigConst.sso_access_token_url);
        String clientId = ConfigProperties.getKey(ConfigConst.sso_client_id);
        String clientSecret = ConfigProperties.getKey(ConfigConst.sso_client_secret);
        log.info("应用标识： " + clientId);
        log.info("密钥： " + clientSecret);
        String redirectUri = ConfigProperties.getKey(ConfigConst.sso_redirect_uri);
        String response = null;

        // 设置请求参数
        Map<String, Object> param = new HashMap<>();
        param.put(ConfigConst.sso_client_id, clientId);
        param.put(ConfigConst.sso_client_secret, clientSecret);
        param.put(ConfigConst.sso_code, code);
        param.put(ConfigConst.sso_redirect_uri, redirectUri);
        param.put(ConfigConst.sso_grant_type, GrantTypeEnum.AUTHORIZATION_CODE.getCode());

        response = HttpUtil.post(url, param);
        JSONObject jsonObject = JSONObject.parseObject(response);
        // 判断请求返回的状态值，是否请求成功
        if (1 == jsonObject.getIntValue(ConfigConst.sso_status)) {
            log.info("access_token： " + response);
        } else {
            log.error("获取access_token失败： " + jsonObject.get(ConfigConst.sso_error));
            response = "";
        }
        return response;
    }

    /**
     * 通过refresh_token刷新access_toke
     *
     * @param refreshToken
     * @return
     */
    public static String refreshToken(String refreshToken) {
        log.info("======刷新access_token======");
        String url = ConfigProperties.getKey(ConfigConst.sso_refresh_token_url);
        String response = null;

        Map<String, Object> param = new HashMap<>();
        param.put(ConfigConst.sso_refresh_token, refreshToken);
        response = HttpUtil.post(url, param);
        JSONObject jsonObject = JSONObject.parseObject(response);
        // 判断请求返回的状态值，是否请求成功
        if (1 == jsonObject.getIntValue(ConfigConst.sso_status)) {
            log.info("access_token： " + response);
        } else {
            log.error("刷新access_token失败： " + jsonObject.get(ConfigConst.sso_error));
            response = "";
        }
        return response;
    }

    /**
     * 通过access_token获取用户信息
     *
     * @param accessToken
     * @return
     */
    public static String getUserInfoByAccessToken(String accessToken) {
        log.info("======根据access_token获取用户信息======");
        String url = ConfigProperties.getKey(ConfigConst.sso_profile_url);
        String response = null;

        Map<String, Object> param = new HashMap<>();
        param.put(ConfigConst.sso_access_token, accessToken);
        response = HttpUtil.get(url, param);
        JSONObject jsonObject = JSONObject.parseObject(response);
        // 判断请求返回的状态值，是否请求成功
        if (1 == jsonObject.getIntValue(ConfigConst.sso_status)) {
            log.info("userInfo： " + response);
        } else {
            log.error("获取用户信息失败： " + jsonObject.get(ConfigConst.sso_error));
            response = "";
        }
        return response;
    }

    /**
     * 超级用户登录IP限制  检查
     */
    public static void adminLoginIpCheck(HttpServletRequest request) {
        String ip = getIpAddr(request);
        boolean isCheck = BootAdminProperties.isAdminLoginIpCheck;
        if (isCheck) {

            String allowIpStr = BootAdminProperties.adminLoginIpWhiteList;
            if (StringUtils.isNotBlank(allowIpStr)) {
                matchIp(allowIpStr, ip);
            }
        }

    }

    /**
     * 获取ip地址
     *
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        if (ip.split(",").length > 1) {
            ip = ip.split(",")[0];
        }
        return ip;
    }

    /**
     * 一个段一个段的匹配ip,匹配*
     *
     * @param ipWhiteLists
     * @param ip
     */
    public static void matchIp(String ipWhiteLists, String ip) {
        //设置一个数字，每次匹配成功一个段，则加一
        int result = 0;
        if (! StringUtils.isEmpty(ipWhiteLists) && ! StringUtils.isEmpty(ip)) {
            //设置的白名单中的每一个ip
            String[] ipList = ipWhiteLists.split(",");
            for (String ipSection : ipList) {
                //每次新的ip匹配，初始化
                result = 0;
                //设置的ip段 以.隔开
                List<String> ipStr = Arrays.asList(ipSection.split("\\."));
                if (ipStr.size() != 4) {
                    throw new BusinessException("设置的ip不符合四段式规定");
                }
                //当前ip的ip段
                List<String> curIpSection = Arrays.asList(ip.split("\\."));
                if (curIpSection.size() != 4) {
                    throw new BusinessException("当前ip不符合四段式规定");
                }
                for (int i = 0; i < ipStr.size(); i++) {
                    if (!"*".equals(ipStr.get(i))) {
                        if (!curIpSection.get(i).equals(ipStr.get(i))) {
                            break;
                        }
                    }
                    result += 1;
                }
                //说明有一个设置的ip和当前ip匹配上了
                if (result == 4) {
                    break;
                }
            }
            if (result != 4) {
                throw new BusinessException("当前IP地址不允许登录此账号");
            }
        }

    }

}
