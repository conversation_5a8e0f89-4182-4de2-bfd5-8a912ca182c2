package com.boot.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.modules.project.dao.EnrollDetailDao;
import com.boot.modules.project.entity.EnrollDetailEntity;
import com.boot.modules.project.redis.EnrollDetailRedis;
import com.boot.modules.project.service.EnrollDetailService;
import com.boot.modules.project.vo.EnrollDetailVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class EnrollDetailServiceImpl extends ServiceImpl<EnrollDetailDao, EnrollDetailEntity> implements EnrollDetailService {
    @Resource
    private EnrollDetailRedis enrollDetailRedis;

    public List<String> getMatchKey(String key) {
        return enrollDetailRedis.matchkey(key);
    }

    @Override
    public PageUtils getAllByPage(Map<String, Object> params) {
        //子项目Id
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        //记录id
        Long recordId = MapUtils.getValue(params, "recordId", Long.class);
        //带上条件查询
        QueryWrapper<EnrollDetailVo> qw = new QueryWrapper<>();
        qw.eq(subProjectId != null,"a.sub_project_id", subProjectId)
                .eq(recordId != null,"a.record_id", recordId);
        //分页查询
        IPage<EnrollDetailVo> pageFilter = new Query<EnrollDetailVo>().getPage(params);
        IPage<EnrollDetailVo> page = this.baseMapper.getByQuery(pageFilter,qw);
        return new PageUtils(page);
    }


}
