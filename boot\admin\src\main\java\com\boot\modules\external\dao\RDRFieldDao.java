package com.boot.modules.external.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boot.modules.external.entity.RDRFieldEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@CacheNamespace
public interface RDRFieldDao extends BaseMapper<RDRFieldEntity> {

    List<RDRFieldEntity> getByTableName(String tableName);
}
