package com.boot.commons.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc : 导出审核状态枚举类    审核状态。0等待审核，1审核通过，2审核不通过
 * @create 2021-03-05
 */
public enum ExportAuditStatusEnum {
    /**
     * 审核中
     */
    AWAIT(0, "await", "审核中", ""),
    /**
     * 审核通过
     */
    PASS(1, "pass", "审核通过", ""),
    /**
     * 审核不通过
     */
    NO_PASS(2, "no_pass", "审核不通过", ""),
    ;
    private int type;
    private String code;
    private String name;
    private String desc;

    ExportAuditStatusEnum(int type, String code, String name, String desc) {
        this.type = type;
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<String, Object> typeMap = new HashMap<>();
    public static Map<String, ExportAuditStatusEnum> typeEnumMap = new HashMap<>();

    static {
        ExportAuditStatusEnum[] types = ExportAuditStatusEnum.values();
        for (ExportAuditStatusEnum type : types) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", type.type);
            map.put("code", type.code);
            map.put("name", type.name);
            map.put("desc", type.desc);
            typeMap.put(String.valueOf(type.type), map);
            typeEnumMap.put(String.valueOf(type.type), type);
        }
    }

}
