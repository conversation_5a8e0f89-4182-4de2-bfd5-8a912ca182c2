<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.patient.dao.PatientDao">

    <select id="listDoctorRecommendationBySubProjectId" resultType="com.boot.modules.patient.dto.InboundProcessDto">
        select
            a.id as doctorRecommendId,
            a.status as recommendStatus,
            a.empiid as empiid,
            b.src_empiid as srcEmpiid,
            a.recommend_id as recommendId,
            a.recommend_reason as recommendReason,
<!--            LEFT(a.recommend_time,10) as recommendTime,-->
            a.recommend_time as recommendTime,
            a.type as type,
            a.project_id as projectId,
            a.sub_project_id as subProjectId,
            a.is_read as isRead,
            b.regno,
            b.name as name,
            b.gender as gender,
            b.birthday as birthday,
            b.id_card as idCard,
            b.initials as initials,
            a.recommend_dept_id,
            c.name as recommendHospitalName
        from
            rp_doctor_recommendation a
        join
            rp_patient b
        on
            a.empiid = b.empiid
        left join rp_sys_dept c on a.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}

    </select>

    <select id="listInboundProcessBySubProjectId" resultType="com.boot.modules.patient.dto.InboundProcessDto">
        select
            a.id as id,
            a.project_id as projectId,
            a.sub_project_id as subProjectId,
            a.empiid as empiid,
            b.src_empiid as srcEmpiid,
            c.recommend_id as recommendId,
            a.out_reason as outReason,
<!--            LEFT(a.out_time,10) as outTime,-->
<!--            LEFT(a.inbound_time,10) as inboundTime,-->
<!--            LEFT(a.signing_time,10) as signingTime,-->
            a.out_time as outTime,
            a.inbound_time as inboundTime,
            a.signing_time as signingTime,
            a.signing_audit_id as signingAuditId,
            a.inbound_id as inboundId,
            a.status as status,
            a.sign_expire_days as signExpireDays,
            b.name as name,
            b.regno,
            b.gender as gender,
            b.birthday as birthday,
            b.id_card as idCard,
            b.initials as initials,
            c.recommend_dept_id,
            c.type,
            d.name as recommendHospitalName        from
            rp_inbound_process a
        join
            rp_patient b
        on
            a.empiid = b.empiid
        join rp_doctor_recommendation c on c.id = a.doctor_recommend_id
        left join rp_sys_dept d on c.top_recommend_dept_id = d.id
        ${ew.customSqlSegment}
    </select>

    <select id="listDoctorRecommendation" resultType="com.boot.modules.patient.dto.InboundProcessDto">
        select
            a.id as id,
            a.type as type,
            a.is_read as isRead,
            a.recommend_id as recommendId,
            a.recommend_dept_id as recommendDeptId,
            e.status as status,
            a.sub_project_id,
            LEFT(a.recommend_time,10) as recommendTime,
            b.name as name,
            b.regno as regno,
            b.tel,
            b.src_empiid as srcEmpiid,
            c.id as projectId,
            c.project_name as projectName,
            c.project_admin_id as projectAdminId,
            d.disease_name as diseaseName
        from
            rp_doctor_recommendation a
        join
            rp_patient b
        on
            a.empiid = b.empiid
        join
            rp_project c
        on
            a.project_id = c.id
        join
            rp_sys_disease d
        on
            c.disease_id = d.id
        left join
            rp_inbound_process e
        on
            e.doctor_recommend_id = a.id
        ${ew.customSqlSegment}
    </select>


    <select id="listDoctorRecommendationMobile" resultType="com.boot.modules.patient.dto.InboundProcessDto">
        select
        a.id as doctorRecommendId,
        a.status as recommendStatus,
        a.empiid as empiid,
        b.src_empiid as srcEmpiid,
        a.recommend_id as recommendId,
        a.recommend_reason as recommendReason,
        a.recommend_time as recommendTime,
        a.type as type,
        a.project_id as projectId,
        a.sub_project_id as subProjectId,
        a.is_read as isRead,
        b.regno,
        b.name as name,
        b.gender as gender,
        b.birthday as birthday,
        b.id_card as idCard,
        b.initials as initials,
        b.tel as tel,
        b.nation,
        c.name as recommendHospitalName,
        d.name as subProjectName
        from
        rp_doctor_recommendation a
        join
        rp_patient b
        on
        a.empiid = b.empiid
        join
        rp_sub_project d
        on
        a.sub_project_id = d.id
        join
        rp_sys_user e
        on
        a.recommend_id = e.id
        left join
        rp_sys_dept c on
        a.top_recommend_dept_id = c.id
        ${ew.customSqlSegment}

    </select>

    <select id="listInboundProcessMobile" resultType="com.boot.modules.patient.dto.InboundProcessDto">
        select
        a.id as id,
        a.project_id as projectId,
        a.sub_project_id as subProjectId,
        a.empiid as empiid,
        b.src_empiid as srcEmpiid,
        c.recommend_id as recommendId,
        a.out_reason as outReason,
        a.out_time as outTime,
        a.inbound_time as inboundTime,
        a.signing_time as signingTime,
        a.signing_audit_id as signingAuditId,
        a.inbound_id as inboundId,
        a.status as status,
        a.sign_expire_days as signExpireDays,
        b.name as name,
        b.regno,
        b.gender as gender,
        b.birthday as birthday,
        b.id_card as idCard,
        b.initials as initials,
        b.tel as tel,
        b.nation,
        f.name as recommendHospitalName,
        d.name as subProjectName
        from
        rp_inbound_process a
        join
        rp_patient b
        on
        a.empiid = b.empiid
        join rp_doctor_recommendation c
        on
        c.id = a.doctor_recommend_id
        join
        rp_sub_project d
        on
        c.sub_project_id = d.id
        join
        rp_sys_user e
        on
        c.recommend_id = e.id
        left join rp_sys_dept f on c.top_recommend_dept_id = f.id
        ${ew.customSqlSegment}
    </select>

</mapper>