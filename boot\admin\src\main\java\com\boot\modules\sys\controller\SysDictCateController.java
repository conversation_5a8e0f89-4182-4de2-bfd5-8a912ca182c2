package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysDictCateEntity;
import com.boot.modules.sys.service.SysDictCateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc : 字典分类管理controller
 * @create 2021-02-25
 */
@Api(tags = "数据字典分类")
@RestController
@RequestMapping("sys/dict/cate")
public class SysDictCateController extends AbstractController {

    @Resource
    private SysDictCateService dictCateService;

    @ApiOperation(value = "字典分类列表", notes = "字典分类列表")
    @GetMapping("/list")
    @RequiresPermissions("sys:dictcate:list")
    public Result list(@RequestParam Map<String, Object> params) {

        PageUtils page = dictCateService.queryPage(params);
        return R.success(page);
    }

    @ApiOperation(value = "获取指定字典分类信息", notes = "获取指定分类字典信息")
    @GetMapping("/{id}")
    @RequiresPermissions("sys:dictcate:info")
    public Result info(@PathVariable("id") Long id) {

        SysDictCateEntity dictCate = dictCateService.selectById(id);

        return R.success(dictCate);
    }

    @ApiOperation(value = "保存字典分类", notes = "保存字典分类")
    @SysLog("保存字典分类")
    @PostMapping()
    @RequiresPermissions("sys:dictcate:save")
    public Result save(@RequestBody SysDictCateEntity dictCate) {
        ValidatorUtils.validateEntity(dictCate, AddGroup.class);

        //唯一性验证，字典分类的编码和名称不能重复
        QueryWrapper<SysDictCateEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(SysDictCateEntity::getCateName, dictCate.getCateName());
        List<SysDictCateEntity> listByName = dictCateService.list(qw);
        if (listByName != null && listByName.size() > 0) {
            return R.fail("字典分类名称不能重复");
        }

        QueryWrapper<SysDictCateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysDictCateEntity::getCateCode, dictCate.getCateCode());
        List<SysDictCateEntity> listByCode = dictCateService.list(queryWrapper);
        if (listByCode != null && listByCode.size() > 0) {
            return R.fail("字典分类编码不能重复");
        }

        return R.success(dictCateService.save(dictCate));
    }

    @SysLog("修改字典分类")
    @ApiOperation(value = "修改字典分类", notes = "修改字典分类")
    @PutMapping()
    @RequiresPermissions("sys:dictcate:update")
    public Result update(@RequestBody SysDictCateEntity dictCate) {

        ValidatorUtils.validateEntity(dictCate, UpdateGroup.class);

        // 唯一性验证，字典分类的编码和名称不能重复
        QueryWrapper<SysDictCateEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(SysDictCateEntity::getCateName, dictCate.getCateName())
                .ne(SysDictCateEntity::getId, dictCate.getId());
        List<SysDictCateEntity> listByName = dictCateService.list(qw);
        if (listByName != null && listByName.size() > 0) {
            return R.fail("字典分类名称不能重复");
        }
        QueryWrapper<SysDictCateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysDictCateEntity::getCateCode, dictCate.getCateCode())
                .ne(SysDictCateEntity::getId, dictCate.getId());
        List<SysDictCateEntity> listByCode = dictCateService.list(queryWrapper);
        if (listByCode != null && listByCode.size() > 0) {
            return R.fail("字典分类编码不能重复");
        }

        return R.success(dictCateService.updateById(dictCate));
    }

    @ApiOperation(value = "删除字典分类", notes = "删除字典分类")
    @SysLog("删除字典分类")
    @DeleteMapping()
    @RequiresPermissions("sys:dictcate:delete")
    public Result delete(@RequestBody Long[] ids) {

        return R.success(dictCateService.deleteByIds(Arrays.asList(ids)));
    }

    @ApiOperation(value = "字典分类excel导入", notes = "字典分类excel导入")
    @PostMapping("/excel/import")
    @RequiresPermissions("sys:dictcate:excelimport")
    public Result excelImport(@RequestParam("file") MultipartFile file) {

        boolean res = true;
        if (file == null) {
            throw new BusinessException("上传的文件为空");
        }

        res = dictCateService.excelImport(file);

        return res ? R.success("导入成功") : R.fail("导入失败");
    }

    @ApiOperation(value = "下载字典分类导入模板", notes = "下载导入模板")
    @PostMapping("/down/temp")
//    @RequiresPermissions("sys:dictitem:downtemp")
    public Result downImportTemp(HttpServletResponse response) throws NoSuchFieldException {
//        List<List<String>> heads = getHeads(SysDictItemEntity.class);
        ExcelUtils.wirteExcel(response, "字典项导入模板", SysDictCateEntity.class, new ArrayList<>());

        return R.success("下载成功", true);
    }


}
