package com.boot.modules.patient.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.constant.HxConst;
import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.external.service.HisService;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dao.InboundProcessDao;
import com.boot.modules.patient.dto.IcoReportDto;
import com.boot.modules.patient.dto.InboundProcessDto;
import com.boot.modules.patient.dto.PIReportDto;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.ReportService;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.sys.entity.DiseaseEntity;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.service.DiseaseService;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    private SysDeptService deptService;

    @Resource
    private SysUserService userService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private DiseaseService diseaseService;

    @Resource
    private ProjectService projectService;

    @Resource
    private HisService hisService;

    @Resource
    private DoctorRecommendationDao recommendationDao;

    @Resource
    private InboundProcessDao inboundProcessDao;

    @Override
    public Map<String, Object> proManagement(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        List<ReportDto> list;

        // 1.获取相关前端传参
        String deptName = MapUtils.getValue(params, "deptName", String.class);
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        Date startDate = null;
        Date endDate = null;
        String startDateString = null;
        String endDateString = null;

        // type为null或startDate为空，查所有
        if (type == null || type == 0) {
            startDateString = MapUtils.getValue(params, "startDate", String.class);
            if (StringUtils.isEmpty(startDateString)) {
                throw new BusinessException("请选择查询时间范围");
            }
            endDateString = MapUtils.getValue(params, "endDate", String.class);
        } else if (type != null && type == 1) {
            String year = MapUtils.getValue(params, "year", String.class);
            if (!StringUtils.isEmpty(year)) {
                startDateString = year + "-01-01";
                try {
                    endDateString = DateUtils.getAfterYear(startDateString, "1");
                    endDateString = DateUtils.getAfterDay(endDateString, "-1");
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        if (!StringUtils.isEmpty(startDateString)) {
            startDate = DateUtils.getByDateStr(startDateString);
            endDate = DateUtils.addDateDays(DateUtils.getByDateStr(endDateString), 1);
        }

        // 2.设置机构病种、项目统计
        list = deptService.listCount(MapUtils.getValue(params, "deptIdList", List.class), deptName, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        // 3.设置出院人数
        AtomicInteger totalDischargesCount = new AtomicInteger();
        List<Long> hxDeptIds = deptService.getSubDeptIdList(15L);
        List<Long> deptIdList = list.stream().filter(p -> p.getDeptId() != null && hxDeptIds.contains(p.getDeptId())).map(ReportDto::getDeptId).collect(Collectors.toList());
        List<SysDeptEntity> allDept = deptService.listByIds(deptIdList);
        Map<Long, String> deptIdCodeMap = allDept.stream().collect(Collectors.toMap(SysDeptEntity::getId, SysDeptEntity::getCode));

        List<String> codeList = allDept.stream().map(p -> p.getCode()).distinct().collect(Collectors.toList());
        String codeString = Strings.join(codeList, ',');
//        // todo cx 测试数据
//        Map<String, Integer> dischargesCountMap = new LinkedHashMap<>();
        Map<String, Integer> dischargesCountMap = hisService.getDisChgCount(startDateString, DateUtils.format(endDate), codeString);
        list.forEach(reportDto -> {
            String[] deptIdStringList = deptIdCodeMap.get(reportDto.getDeptId()) != null ? deptIdCodeMap.get(reportDto.getDeptId()).split(",") : null;
            int dischargesCount = 0;

            if (deptIdStringList != null) {
                for (String deptIdString : deptIdStringList) {
                    Integer count = dischargesCountMap.get(deptIdString);
                    dischargesCount += count == null ? 0 : count;
                }
            }
            if (reportDto.getTotalProject() == null) {
                reportDto.setTotalProject(0);
                reportDto.setAddProject(0);
            }

            reportDto.setDischargesCount(dischargesCount);
            reportDto.setTotalRecommend(0);
            reportDto.setTotalInbound(0);
            reportDto.setAddRecommend(0);
            reportDto.setAddInbound(0);
            reportDto.setRecommendRatio("0.00%");
            reportDto.setInboundRatio("0.00%");
            reportDto.setTargetInboundCompeteRatio("0.00%");
            reportDto.setTargetRecommendCompeteRatio("0.00%");
            reportDto.setTargetRecommend(new Double(Math.ceil(dischargesCount * 0.6)).longValue());
            reportDto.setTargetInbound(new Double(Math.ceil(dischargesCount * 0.2)).longValue());

            totalDischargesCount.addAndGet(dischargesCount);
        });

        // 3. 设置推荐相关统计参数
        doctorRecommendationService.setRecommendCount(list, startDate, endDate);

        // 4. 设置入组相关统计
        inboundProcessService.setInboundCount(list, startDate, endDate);

        // 5.按需求排序
        String orderField = MapUtils.getValue(params, "orderField", String.class);
        String order = MapUtils.getValue(params, "order", String.class);
        if (!StringUtils.isEmpty(orderField) && !StringUtils.isEmpty(order)) {
            sortByField(list, orderField, order.equals("ascending"));
        }

        // 6.对合计项进行统计
        ReportDto total = new ReportDto();
        total.setDeptName("合计");
        total.setTotalDisease(projectService.count(new QueryWrapper<ProjectEntity>().select("DISTINCT disease_id")));

        Integer addProjectCount = 0;
        Integer addDieaseCount = 0;
        if (startDate != null) {
            addProjectCount = projectService.count(new QueryWrapper<ProjectEntity>().lambda().ge(ProjectEntity::getCreateTime, startDate).lt(ProjectEntity::getCreateTime, endDate));
            addDieaseCount = diseaseService.count(new QueryWrapper<DiseaseEntity>().lambda()
                    .ge(DiseaseEntity::getCreateTime, startDate).lt(DiseaseEntity::getCreateTime, endDate));
        }
        total.setAddDisease(startDate == null ? total.getTotalDisease() : addDieaseCount);
        total.setTotalProject(projectService.count());

        total.setAddProject(startDate == null ? total.getTotalProject() : addProjectCount);
        int dischargesCount = totalDischargesCount.get();
        total.setDischargesCount(dischargesCount);
        total.setTargetRecommend(Math.round(dischargesCount * 0.6));
        total.setTargetInbound(Math.round(dischargesCount * 0.2));

        doctorRecommendationService.totalRecommend(total, startDate, endDate);
        inboundProcessService.setTotalInboundCount(total, startDate, endDate);

        result.put(HxConst.TOTAL_KEY, total);
        result.put(HxConst.LIST_KEY, list);

        return result;
    }

    @Override
    public Map<String, Object> deptManagement(Map<String, Object> params, boolean isPro) {
        Map<String, Object> result = new HashMap<>();
        List<ReportDto> list;

        // 1.获取相关前端传参
        String doctorName = MapUtils.getValue(params, "doctorName", String.class);
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        Date startDate = null;
        Date endDate = null;
        String startDateString = null;
        String endDateString = null;

        // type为null或startDate为空，查所有
        if (type == null || type == 0) {
            startDateString = MapUtils.getValue(params, "startDate", String.class);
            if (StringUtils.isEmpty(startDateString)) {
                throw new BusinessException("请选择查询时间范围");
            }
            endDateString = MapUtils.getValue(params, "endDate", String.class);
        } else if (type != null && type == 1) {
            String year = MapUtils.getValue(params, "year", String.class);
            if (!StringUtils.isEmpty(year)) {
                startDateString = year + "-01-01";
                try {
                    endDateString = DateUtils.getAfterYear(startDateString, "1");
                    endDateString = DateUtils.getAfterDay(endDateString, "-1");
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        if (!StringUtils.isEmpty(startDateString)) {
            startDate = DateUtils.getByDateStr(startDateString);
            endDate = DateUtils.addDateDays(DateUtils.getByDateStr(endDateString), 1);
        }

        // 2.设置病种、项目统计
        Object deptId = params.get("deptId");
        if (deptId == null) {
            list = userService.listCount(doctorName, null, startDate, endDate);
        } else if (deptId instanceof List) {
            list = userService.listCount(doctorName, (List<Long>) deptId, startDate, endDate);
            params.put("deptIdList", deptId);
        } else {
            list = userService.listCount(doctorName, Arrays.asList(Long.parseLong(deptId.toString())), startDate, endDate);
            params.put("deptIdList", Arrays.asList(Long.parseLong(deptId.toString())));
        }

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        // 设置所属科室
        List<SysDeptEntity> allDept = deptService.list();
        Map<Long, String> deptIdNameMap = allDept.stream().collect(Collectors.toMap(SysDeptEntity::getId, SysDeptEntity::getName));

        list.forEach(reportDto -> {
            if (reportDto.getTotalProject() == null) {
                reportDto.setTotalProject(0);
                reportDto.setAddProject(0);
            }
            reportDto.setTotalRecommend(0);
            reportDto.setTotalInbound(0);
            reportDto.setAddRecommend(0);
            reportDto.setAddInbound(0);
            reportDto.setDeptName(deptIdNameMap.get(reportDto.getDeptId()));
        });

        // 3. 设置推荐相关统计参数
        doctorRecommendationService.setPerRecommendCount(list, startDate, endDate);
        // 4. 设置入组相关统计
        inboundProcessService.setPerInboundCount(list, startDate, endDate);
        // 5.按需求排序
        String orderField = MapUtils.getValue(params, "orderField", String.class);
        String order = MapUtils.getValue(params, "order", String.class);

        if (!StringUtils.isEmpty(orderField) && !StringUtils.isEmpty(order)) {
            sortByField(list, orderField, order.equals("ascending"));
        }

        // 计算合计
        String isAll = MapUtils.getValue(params, "isAll", String.class);
        Map<String, Object> objectMap = this.proManagement(params);
        List<ReportDto> totalList;

        if (isAll != null && isAll.equals("true")) {
            totalList = Arrays.asList((ReportDto) objectMap.get(HxConst.TOTAL_KEY));
        } else {
            totalList = (List) objectMap.get(HxConst.LIST_KEY);
        }

        if (isPro) {
            totalList.get(0).setDeptName("合计");
        }

        result.put(HxConst.TOTAL_KEY, totalList);
        result.put(HxConst.LIST_KEY, list);
        return result;
    }

    @Override
    public void setDeptManageData(Map<String, List<List<String>>> headMap,
                                  Map<String, List<List<Object>>> dataMap,
                                  String key,
                                  List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("科室"));
        heads.add(Collections.singletonList("医生"));
        heads.add(Collections.singletonList("作为PI的总病种数"));
        heads.add(Collections.singletonList("作为PI的新增病种数"));
        heads.add(Collections.singletonList("作为PI的总项目数"));
        heads.add(Collections.singletonList("作为PI的新增项目数"));
        heads.add(Collections.singletonList("累计推荐入组人数"));
        heads.add(Collections.singletonList("新增推荐入组人数"));
        heads.add(Collections.singletonList("累计实际入组人数"));
        heads.add(Collections.singletonList("新增实际入组人数"));
        heads.add(Collections.singletonList("累计实际加权入组人数"));
        heads.add(Collections.singletonList("新增实际加权入组人数"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("deptName"));
            data.add(export.get("doctorName"));
            data.add(export.get("totalDisease"));
            data.add(export.get("addDisease"));
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("totalInbound"));
            data.add(export.get("addInbound"));
            data.add(export.get("totalWeightInbound"));
            data.add(export.get("addWeightInbound"));
            dataList.add(data);
        });
    }

    @Override
    public void setDoctorData(Map<String, List<List<String>>> headMap,
                              Map<String, List<List<Object>>> dataMap,
                              String key,
                              List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("科室"));
        heads.add(Collections.singletonList("医生"));
        heads.add(Collections.singletonList("作为PI的总病种数"));
        heads.add(Collections.singletonList("作为PI的新增病种数"));
        heads.add(Collections.singletonList("作为PI的总项目数"));
        heads.add(Collections.singletonList("作为PI的新增项目数"));
        heads.add(Collections.singletonList("累计推荐入组人数"));
        heads.add(Collections.singletonList("累计门诊推荐入组人数"));
        heads.add(Collections.singletonList("累计住院推荐入组人数"));
        heads.add(Collections.singletonList("累计急诊推荐入组人数"));
        heads.add(Collections.singletonList("新增推荐入组人数"));
        heads.add(Collections.singletonList("新增门诊推荐入组人数"));
        heads.add(Collections.singletonList("新增住院推荐入组人数"));
        heads.add(Collections.singletonList("新增急诊推荐入组人数"));
        heads.add(Collections.singletonList("累计实际入组人数"));
        heads.add(Collections.singletonList("累计门诊实际入组人数"));
        heads.add(Collections.singletonList("累计住院实际入组人数"));
        heads.add(Collections.singletonList("累计急诊实际入组人数"));
        heads.add(Collections.singletonList("新增实际入组人数"));
        heads.add(Collections.singletonList("新增门诊实际入组人数"));
        heads.add(Collections.singletonList("新增住院实际入组人数"));
        heads.add(Collections.singletonList("新增急诊实际入组人数"));
        heads.add(Collections.singletonList("累计实际加权入组人数"));
        heads.add(Collections.singletonList("新增实际加权入组人数"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("deptName"));
            data.add(export.get("doctorName"));
            data.add(export.get("totalDisease"));
            data.add(export.get("addDisease"));
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("totalORecommend"));
            data.add(export.get("totalIRecommend"));
            data.add(export.get("totalERecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("addORecommend"));
            data.add(export.get("addIRecommend"));
            data.add(export.get("addERecommend"));
            data.add(export.get("totalInbound"));
            data.add(export.get("totalOInbound"));
            data.add(export.get("totalIInbound"));
            data.add(export.get("totalEInbound"));
            data.add(export.get("addInbound"));
            data.add(export.get("addOInbound"));
            data.add(export.get("addIInbound"));
            data.add(export.get("addEInbound"));
            data.add(export.get("totalWeightInbound"));
            data.add(export.get("addWeightInbound"));
            dataList.add(data);
        });
    }

    @Override
    public void setDeptData(Map<String, List<List<String>>> headMap,
                            Map<String, List<List<Object>>> dataMap,
                            String key,
                            List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("科室"));
        heads.add(Collections.singletonList("出院人数"));
        heads.add(Collections.singletonList("总病种数"));
        heads.add(Collections.singletonList("新增病种数"));
        heads.add(Collections.singletonList("总项目数"));
        heads.add(Collections.singletonList("新增项目数"));
        heads.add(Collections.singletonList("累计推荐入组人数"));
        heads.add(Collections.singletonList("累计门诊推荐入组人数"));
        heads.add(Collections.singletonList("累计住院推荐入组人数"));
        heads.add(Collections.singletonList("累计急诊推荐入组人数"));
        heads.add(Collections.singletonList("新增推荐入组人数"));
        heads.add(Collections.singletonList("新增门诊推荐入组人数"));
        heads.add(Collections.singletonList("新增住院推荐入组人数"));
        heads.add(Collections.singletonList("新增急诊推荐入组人数"));
        heads.add(Collections.singletonList("推荐入组率"));
        heads.add(Collections.singletonList("累计实际入组人数"));
        heads.add(Collections.singletonList("累计门诊实际入组人数"));
        heads.add(Collections.singletonList("累计住院实际入组人数"));
        heads.add(Collections.singletonList("累计急诊实际入组人数"));
        heads.add(Collections.singletonList("新增实际入组人数"));
        heads.add(Collections.singletonList("新增门诊实际入组人数"));
        heads.add(Collections.singletonList("新增住院实际入组人数"));
        heads.add(Collections.singletonList("新增急诊实际入组人数"));
        heads.add(Collections.singletonList("实际入组率"));
        heads.add(Collections.singletonList("累计实际加权入组人数"));
        heads.add(Collections.singletonList("新增实际加权入组人数"));
        heads.add(Collections.singletonList("实际加权入组率"));
        heads.add(Collections.singletonList("目标推荐人数")); // 推荐目标数量
        heads.add(Collections.singletonList("目标推荐完成率"));
        heads.add(Collections.singletonList("目标入组人数"));//入组目标数量
        heads.add(Collections.singletonList("目标入组完成率"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("deptName"));
            data.add(export.get("dischargesCount"));
            data.add(export.get("totalDisease"));
            data.add(export.get("addDisease"));
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("totalORecommend"));
            data.add(export.get("totalIRecommend"));
            data.add(export.get("totalERecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("addORecommend"));
            data.add(export.get("addIRecommend"));
            data.add(export.get("addERecommend"));
            data.add(export.get("recommendRatio"));
            data.add(export.get("totalInbound"));
            data.add(export.get("totalOInbound"));
            data.add(export.get("totalIInbound"));
            data.add(export.get("totalEInbound"));
            data.add(export.get("addInbound"));
            data.add(export.get("addOInbound"));
            data.add(export.get("addIInbound"));
            data.add(export.get("addEInbound"));
            data.add(export.get("inboundRatio"));
            data.add(export.get("totalWeightInbound"));
            data.add(export.get("addWeightInbound"));
            data.add(export.get("weightInboundRatio"));
            data.add(export.get("targetRecommend"));
            data.add(export.get("targetRecommendCompeteRatio"));
            data.add(export.get("targetInbound"));
            data.add(export.get("targetInboundCompeteRatio"));

            dataList.add(data);
        });
    }

    @Override
    public void setDoctorHeaderData(Map<String, List<List<String>>> headMap,
                                    Map<String, List<List<Object>>> dataMap,
                                    String key,
                                    List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("科室"));
        heads.add(Collections.singletonList("出院人数"));
        heads.add(Collections.singletonList("总病种数"));
        heads.add(Collections.singletonList("新增病种数"));
        heads.add(Collections.singletonList("总项目数"));
        heads.add(Collections.singletonList("新增项目数"));
        heads.add(Collections.singletonList("累计推荐入组人数"));
        heads.add(Collections.singletonList("累计门诊推荐入组人数"));
        heads.add(Collections.singletonList("累计住院推荐入组人数"));
        heads.add(Collections.singletonList("累计急诊推荐入组人数"));
        heads.add(Collections.singletonList("新增推荐入组人数"));
        heads.add(Collections.singletonList("新增门诊推荐入组人数"));
        heads.add(Collections.singletonList("新增住院推荐入组人数"));
        heads.add(Collections.singletonList("新增急诊推荐入组人数"));
        heads.add(Collections.singletonList("推荐入组率"));
        heads.add(Collections.singletonList("累计实际入组人数"));
        heads.add(Collections.singletonList("累计门诊实际入组人数"));
        heads.add(Collections.singletonList("累计住院实际入组人数"));
        heads.add(Collections.singletonList("累计急诊实际入组人数"));
        heads.add(Collections.singletonList("新增实际入组人数"));
        heads.add(Collections.singletonList("新增门诊实际入组人数"));
        heads.add(Collections.singletonList("新增住院实际入组人数"));
        heads.add(Collections.singletonList("新增急诊实际入组人数"));
        heads.add(Collections.singletonList("实际入组率"));
        heads.add(Collections.singletonList("累计实际加权入组人数"));
        heads.add(Collections.singletonList("新增实际加权入组人数"));
        heads.add(Collections.singletonList("实际加权入组率"));
//        heads.add(Collections.singletonList("目标推荐人数"));
//        heads.add(Collections.singletonList("目标推荐完成率"));
//        heads.add(Collections.singletonList("目标入组人数"));
//        heads.add(Collections.singletonList("目标入组完成率"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("deptName"));
            data.add(export.get("dischargesCount"));
            data.add(export.get("totalDisease"));
            data.add(export.get("addDisease"));
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("totalORecommend"));
            data.add(export.get("totalIRecommend"));
            data.add(export.get("totalERecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("addORecommend"));
            data.add(export.get("addIRecommend"));
            data.add(export.get("addERecommend"));
            data.add(export.get("recommendRatio"));
            data.add(export.get("totalInbound"));
            data.add(export.get("totalOInbound"));
            data.add(export.get("totalIInbound"));
            data.add(export.get("totalEInbound"));
            data.add(export.get("addInbound"));
            data.add(export.get("addOInbound"));
            data.add(export.get("addIInbound"));
            data.add(export.get("addEInbound"));
            data.add(export.get("inboundRatio"));
            data.add(export.get("totalWeightInbound"));
            data.add(export.get("addWeightInbound"));
            data.add(export.get("weightInboundRatio"));
//            data.add(export.get("targetRecommend"));
//            data.add(export.get("targetRecommendCompeteRatio"));
//            data.add(export.get("targetInbound"));
//            data.add(export.get("targetInboundCompeteRatio"));

            dataList.add(data);
        });
    }

    @Override
    public void setDeptManageHeaderData(Map<String, List<List<String>>> headMap,
                                        Map<String, List<List<Object>>> dataMap,
                                        String key,
                                        List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("科室"));
        heads.add(Collections.singletonList("出院人数"));
        heads.add(Collections.singletonList("总病种数"));
        heads.add(Collections.singletonList("新增病种数"));
        heads.add(Collections.singletonList("总项目数"));
        heads.add(Collections.singletonList("新增项目数"));
        heads.add(Collections.singletonList("累计推荐入组人数"));
        heads.add(Collections.singletonList("新增推荐入组人数"));
        heads.add(Collections.singletonList("推荐入组率"));
        heads.add(Collections.singletonList("累计实际入组人数"));
        heads.add(Collections.singletonList("新增实际入组人数"));
        heads.add(Collections.singletonList("实际入组率"));
        heads.add(Collections.singletonList("累计实际加权入组人数"));
        heads.add(Collections.singletonList("新增实际加权入组人数"));
        heads.add(Collections.singletonList("实际加权入组率"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("deptName"));
            data.add(export.get("dischargesCount"));
            data.add(export.get("totalDisease"));
            data.add(export.get("addDisease"));
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("recommendRatio"));
            data.add(export.get("totalInbound"));
            data.add(export.get("addInbound"));
            data.add(export.get("inboundRatio"));
            data.add(export.get("totalWeightInbound"));
            data.add(export.get("addWeightInbound"));
            data.add(export.get("weightInboundRatio"));

            dataList.add(data);
        });
    }

    @Override
    public void setIcoData(Map<String, List<List<String>>> headMap,
                           Map<String, List<List<Object>>> dataMap,
                           String key,
                           List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("医联体单位"));
        heads.add(Collections.singletonList("参与项目数"));
        heads.add(Collections.singletonList("新增参与项目数"));
        heads.add(Collections.singletonList("累积推荐人数"));
        heads.add(Collections.singletonList("新增推荐人数"));
        heads.add(Collections.singletonList("实际入组人数"));
        heads.add(Collections.singletonList("实际新增入组人数"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("medName"));
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("totalInbound"));
            data.add(export.get("addInbound"));

            dataList.add(data);
        });
    }

    @Override
    public void setPatientData(Map<String, List<List<String>>> headMap,
                               Map<String, List<List<Object>>> dataMap,
                               String key,
                               List<InboundProcessDto> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("登记号"));
        heads.add(Collections.singletonList("受试者姓名"));
        heads.add(Collections.singletonList("推荐项目"));
        heads.add(Collections.singletonList("CRC信息"));
        heads.add(Collections.singletonList("所属病种"));
        heads.add(Collections.singletonList("推荐方式"));
        heads.add(Collections.singletonList("入组状态"));
        heads.add(Collections.singletonList("推荐日期"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.getRegno());
            data.add(export.getName());
            data.add(export.getProjectName());
            data.add(export.getCrc());
            data.add(export.getDiseaseName());
            //推荐类型 0-系统推荐 1-手动推荐
            String typeStr = "";
            if (export.getType() != null && export.getType() == 0) {
                typeStr = "系统推荐";
            } else if (export.getType() != null && export.getType() == 1) {
                typeStr = "手动推荐";
            }
            data.add(typeStr);

            String statusStr = PatTypeEnum.getByCode(export.getStatus()) != null ? PatTypeEnum.getByCode(export.getStatus()).getName() : "";
            data.add(statusStr);
            data.add(export.getRecommendTime());

            dataList.add(data);
        });
    }

    @Override
    public void setYltPatientData(Map<String, List<List<String>>> headMap,
                               Map<String, List<List<Object>>> dataMap,
                               String key,
                               List<InboundProcessDto> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("登记号"));
        heads.add(Collections.singletonList("受试者姓名"));
        heads.add(Collections.singletonList("电话号码"));
        heads.add(Collections.singletonList("推荐医生"));
        heads.add(Collections.singletonList("推荐医生科室"));
        heads.add(Collections.singletonList("推荐项目"));
        heads.add(Collections.singletonList("项目PI"));
        heads.add(Collections.singletonList("临床科室"));
        heads.add(Collections.singletonList("CRC信息"));
        heads.add(Collections.singletonList("所属病种"));
        heads.add(Collections.singletonList("入组状态"));
        heads.add(Collections.singletonList("推荐日期"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.getRegno());
            data.add(export.getName());
            data.add(export.getTel());
            data.add(export.getRecommendName());
            data.add(export.getRecommendDeptName());
            data.add(export.getProjectName());
            data.add(export.getProjectAdminName());
            data.add(export.getProjectDeptName());
            data.add(export.getCrc());
            data.add(export.getDiseaseName());

            String statusStr = PatTypeEnum.getByCode(export.getStatus()) != null ? PatTypeEnum.getByCode(export.getStatus()).getName() : "";
            data.add(statusStr);
            data.add(export.getRecommendTime());

            dataList.add(data);
        });
    }

    @Override
    public void setPIPatientData(Map<String, List<List<String>>> headMap,
                               Map<String, List<List<Object>>> dataMap,
                               String key,
                               List<InboundProcessDto> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("登记号"));
        heads.add(Collections.singletonList("受试者姓名"));
        heads.add(Collections.singletonList("电话号码"));
        heads.add(Collections.singletonList("推荐项目"));
        heads.add(Collections.singletonList("CRC信息"));
        heads.add(Collections.singletonList("所属病种"));
        heads.add(Collections.singletonList("推荐方式"));
        heads.add(Collections.singletonList("入组状态"));
        heads.add(Collections.singletonList("推荐日期"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.getRegno());
            data.add(export.getName());
            data.add(export.getTel());
            data.add(export.getProjectName());
            data.add(export.getCrc());
            data.add(export.getDiseaseName());
            //推荐类型 0-系统推荐 1-手动推荐
            String typeStr = "";
            if (export.getType() != null && export.getType() == 0) {
                typeStr = "系统推荐";
            } else if (export.getType() != null && export.getType() == 1) {
                typeStr = "手动推荐";
            }
            data.add(typeStr);

            String statusStr = PatTypeEnum.getByCode(export.getStatus()) != null ? PatTypeEnum.getByCode(export.getStatus()).getName() : "";
            data.add(statusStr);
            data.add(export.getRecommendTime());

            dataList.add(data);
        });
    }
    /**
     * 通过字段名进行排序
     *
     * @param list
     * @param field
     * @param isAsc
     */
    private void sortByField(List<ReportDto> list, final String field, final boolean isAsc) {
        Collections.sort(list, (arg1, arg2) -> {
            int result = 0;

            try {
                Field declaredField = ReportDto.class.getDeclaredField(field);
                declaredField.setAccessible(true);
                Object value1 = declaredField.get(arg1);
                Object value2 = declaredField.get(arg2);
                if (value1 instanceof String) {
                    String s = value1.toString();
                    // 百分数
                    if (s.endsWith("%")) {
                        double v1 = Double.parseDouble(s.substring(0, s.lastIndexOf("%")));
                        double v2 = Double.parseDouble(value2.toString().substring(0, value2.toString().lastIndexOf("%")));
                        result = v1 > v2 ? 1 : v1 == v2 ? 0 : -1;
                    } else {
                        // 字符串
                        result = s.compareTo(value2.toString());
                    }
                } else if (value1 instanceof Integer) {
                    // 整型（Method的返回参数可以是int的，因为JDK1.5之后，Integer与int可以自动转换了）
                    result = (Integer) value1 - (Integer) value2;
                } else {
                    // 目前尚不支持的对象，直接转换为String，然后比较，后果未知
                    result = value1.toString().compareTo(value1.toString());

                    System.err.println("MySortList.sortByMethod方法接受到不可识别的对象类型，转换为字符串后比较返回...");
                }

                if (!isAsc) {
                    // 倒序
                    result = -result;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            return result;
        });
    }

    @Override
    public Map<String, Object> icoManagement(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        Date startDate = null;
        Date endDate = null;
        String startDateString = null;
        String endDateString = null;
        List<Long> deptId = MapUtils.getValue(params, "deptId", ArrayList.class);

        // type为null或startDate为空，查所有
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        if (type == null || type == 0) {
            startDateString = MapUtils.getValue(params, "startDate", String.class);
            if (StringUtils.isEmpty(startDateString)) {
                throw new BusinessException("请选择查询时间范围");
            }
            endDateString = MapUtils.getValue(params, "endDate", String.class);
        } else if (type != null && type == 1) {
            String year = MapUtils.getValue(params, "year", String.class);
            if (!StringUtils.isEmpty(year)) {
                startDateString = year + "-01-01";
                try {
                    endDateString = DateUtils.getAfterYear(startDateString, "1");
                    endDateString = DateUtils.getAfterDay(endDateString, "-1");
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        if (!StringUtils.isEmpty(startDateString)) {
            startDate = DateUtils.getByDateStr(startDateString);
            endDate = DateUtils.addDateDays(DateUtils.getByDateStr(endDateString), 1);
        }

        // 1.总表
        IcoReportDto total = total(deptId, startDate, endDate);
        result.put(HxConst.TOTAL_KEY, total);

        // 2.分表
        String medName = MapUtils.getValue(params, "medName", String.class);
        List<IcoReportDto> ico = ico(deptId, medName, startDate, endDate);
        result.put(HxConst.LIST_KEY, ico);
        return result;
    }

    private IcoReportDto total(List<Long> deptId, Date startDate, Date endDate) {
        IcoReportDto total = new IcoReportDto();
        // 1.1 所有开通医联体权限的项目数
        Integer allProject = projectService.count(new QueryWrapper<ProjectEntity>()
                .lambda().eq(ProjectEntity::getEnableMedConst, 1));
        total.setAllProject(allProject);

        QueryWrapper<DoctorRecommendationEntity> qw = new QueryWrapper<>();
        qw.ne("b.top_recommend_dept_id", 15L);
        if (!CollectionUtils.isEmpty(deptId)) {
            qw.in("b.recommend_dept_id", deptId);
        }

        // 1.1参与项目数
        Integer totalProject = recommendationDao.icoProjectCount(qw);
        total.setTotalProject(totalProject);

        // 1.2新增参与项目数
        if (startDate != null) {
            qw.ge("b.recommend_time", startDate)
                    .lt("b.recommend_time", endDate);
            Integer addProject = recommendationDao.icoProjectCount(qw);
            total.setAddProject(addProject);
        } else {
            total.setAddProject(totalProject);
        }

        QueryWrapper<ReportDto> qwRecTotal = new QueryWrapper<>();
        qwRecTotal.ne("a.top_recommend_dept_id", 15L);
        if (!CollectionUtils.isEmpty(deptId)) {
            qwRecTotal.in("a.recommend_dept_id", deptId);
        }
        // 1.3推荐病例数
        Integer recommendCount = recommendationDao.totalRecommend(qwRecTotal);
        total.setTotalRecommend(recommendCount);

        // 1.4新增推荐病例数
        if (startDate != null) {
            qwRecTotal.ge("a.recommend_time", startDate)
                    .lt("a.recommend_time", endDate);
            Integer addRecommendCount = recommendationDao.totalRecommend(qwRecTotal);
            total.setAddRecommend(addRecommendCount);
        } else {
            total.setAddRecommend(recommendCount);
        }

        QueryWrapper<ReportDto> qwInboundTotal = new QueryWrapper<>();
        qwInboundTotal.ne("c.top_recommend_dept_id", 15L);
        qwInboundTotal.isNotNull("a.signing_op_time");
        if (!CollectionUtils.isEmpty(deptId)) {
            qwRecTotal.in("c.recommend_dept_id", deptId);
        }
        // 1.5实际入组病例数
        Integer inboundCount = inboundProcessDao.icoTotalInbound(qwInboundTotal);
        total.setTotalInbound(inboundCount);

        // 1.6新增实际入组病例数
        if (startDate != null) {
            qwInboundTotal.ge("a.signing_op_time", startDate)
                    .lt("a.signing_op_time", endDate);
            Integer addInboundCount = inboundProcessDao.icoTotalInbound(qwInboundTotal);
            total.setAddInbound(addInboundCount);
        } else {
            total.setAddInbound(inboundCount);
        }
        return total;
    }

    private List<IcoReportDto> ico(List<Long> deptId, String medName, Date startDate, Date endDate) {
        // 2.1 医联体参与项目
        QueryWrapper<IcoReportDto> qwProject = new QueryWrapper<>();
        qwProject.ne("b.top_recommend_dept_id", 15L);
        if (!CollectionUtils.isEmpty(deptId)) {
            qwProject.in("b.recommend_dept_id", deptId);
        }
        if (StringUtils.isNotBlank(medName)) {
            qwProject.like("c.name", medName);
        }
        List<IcoReportDto> project = recommendationDao.groupIcoProject(qwProject);
        if (CollectionUtils.isEmpty(project)) {
            return null;
        }

        // 2.2 新增医联体参与项目
        Map<Long, Integer> addProjectMap = new LinkedHashMap<>();
        if (startDate != null) {
            qwProject.ge("b.recommend_time", startDate)
                    .lt("b.recommend_time", endDate);
            List<IcoReportDto> addProject = recommendationDao.groupIcoProject(qwProject);
            addProjectMap = addProject.stream().collect(Collectors.toMap(IcoReportDto::getMedId, IcoReportDto::getTotalProject));
        }

        // 2.3 医联体推荐病例数
        QueryWrapper<IcoReportDto> qwRec = new QueryWrapper<>();
        qwRec.ne("b.top_recommend_dept_id", 15L);
        if (!CollectionUtils.isEmpty(deptId)) {
            qwRec.in("b.recommend_dept_id", deptId);
        }
        if (StringUtils.isNotBlank(medName)) {
            qwRec.like("a.project_name", medName);
        }
        List<IcoReportDto> recommend = recommendationDao.groupIcoRecommend(qwRec);
        Map<Long, Integer> recommendMap = recommend.stream().collect(Collectors.toMap(IcoReportDto::getMedId, IcoReportDto::getTotalRecommend));

        // 2.4 新增医联体推荐病例数
        Map<Long, Integer> addRecommendMap = new LinkedHashMap<>();
        if (startDate != null) {
            qwRec.ge("b.recommend_time", startDate)
                    .lt("b.recommend_time", endDate);
            List<IcoReportDto> addRecommend = recommendationDao.groupIcoRecommend(qwRec);
            addRecommendMap = addRecommend.stream().collect(Collectors.toMap(IcoReportDto::getMedId, IcoReportDto::getTotalRecommend));
        }

        // 2.5 医联体推荐病例数
        QueryWrapper<IcoReportDto> qwInbound = new QueryWrapper<>();
        qwInbound.ne("b.top_recommend_dept_id", 15L);
        qwInbound.isNotNull("c.signing_op_time");
        if (!CollectionUtils.isEmpty(deptId)) {
            qwInbound.in("b.recommend_dept_id", deptId);
        }
        if (StringUtils.isNotBlank(medName)) {
            qwInbound.like("a.project_name", medName);
        }
        List<IcoReportDto> inbound = inboundProcessDao.groupIcoInbound(qwInbound);
        Map<Long, Integer> inboundMap = inbound.stream().collect(Collectors.toMap(IcoReportDto::getMedId, IcoReportDto::getTotalInbound));

        // 2.6 新增医联体推荐病例数
        Map<Long, Integer> addInboundMap = new LinkedHashMap<>();
        if (startDate != null) {
            qwInbound.ge("c.signing_op_time", startDate)
                    .lt("c.signing_op_time", endDate);
            List<IcoReportDto> addInbound = inboundProcessDao.groupIcoInbound(qwInbound);
            addInboundMap = addInbound.stream().collect(Collectors.toMap(IcoReportDto::getMedId, IcoReportDto::getTotalInbound));
        }

        for (IcoReportDto dto : project) {
            Integer toatlRec = recommendMap != null && recommendMap.containsKey(dto.getMedId()) ? recommendMap.get(dto.getMedId()) : 0;
            dto.setTotalRecommend(toatlRec);

            Integer totalInb = inboundMap != null && inboundMap.containsKey(dto.getMedId()) ? inboundMap.get(dto.getMedId()) : 0;
            dto.setTotalInbound(totalInb);

            // 增量需要区分是否有起止日期
            if (startDate != null) {
                Integer addProj = addProjectMap != null && addProjectMap.containsKey(dto.getMedId()) ? addProjectMap.get(dto.getMedId()) : 0;
                dto.setAddProject(addProj);
                Integer addRec = addRecommendMap != null && addRecommendMap.containsKey(dto.getMedId()) ? addRecommendMap.get(dto.getMedId()) : 0;
                dto.setAddRecommend(addRec);
                Integer addInb = addInboundMap != null && addInboundMap.containsKey(dto.getMedId()) ? addInboundMap.get(dto.getMedId()) : 0;
                dto.setAddInbound(addInb);
            } else {
                dto.setAddProject(dto.getTotalProject());
                dto.setAddRecommend(dto.getTotalRecommend());
                dto.setAddInbound(dto.getAddInbound());
            }
        }
        return project;
    }

    @Override
    public Map<String, Object> pi(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        Date startDate = null;
        Date endDate = null;
        String startDateString = null;
        String endDateString = null;
        Long userId = MapUtils.getValue(params, "userId", Long.class);

        // type为null或startDate为空，查所有
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        if (type == null || type == 0) {
            startDateString = MapUtils.getValue(params, "startDate", String.class);
            if (StringUtils.isEmpty(startDateString)) {
                throw new BusinessException("请选择查询时间范围");
            }
            endDateString = MapUtils.getValue(params, "endDate", String.class);
        } else if (type != null && type == 1) {
            String year = MapUtils.getValue(params, "year", String.class);
            if (!StringUtils.isEmpty(year)) {
                startDateString = year + "-01-01";
                try {
                    endDateString = DateUtils.getAfterYear(startDateString, "1");
                    endDateString = DateUtils.getAfterDay(endDateString, "-1");
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        if (!StringUtils.isEmpty(startDateString)) {
            startDate = DateUtils.getByDateStr(startDateString);
            endDate = DateUtils.addDateDays(DateUtils.getByDateStr(endDateString), 1);
        }

        // 1.总表
        PIReportDto total = totalPI(userId, startDate, endDate);
        result.put(HxConst.TOTAL_KEY, total);

        // 2.分表
        String projectName = MapUtils.getValue(params, "projectName", String.class);
        List<PIReportDto> pi = pi(userId, projectName, startDate, endDate);
        result.put(HxConst.LIST_KEY, pi);
        return result;
    }


    private PIReportDto totalPI(Long userId, Date startDate, Date endDate) {
        PIReportDto total = new PIReportDto();
        QueryWrapper<ProjectEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjectEntity::getProjectAdminId, userId);

        // 1.1参与项目数
        Integer totalProject = projectService.count(qw);
        total.setTotalProject(totalProject);

        // 1.2新增参与项目数
        if (startDate != null) {
            qw.lambda().ge(ProjectEntity::getCreateTime, startDate)
                    .lt(ProjectEntity::getCreateTime, endDate);
            Integer addProject = projectService.count(qw);
            total.setAddProject(addProject);
        } else {
            total.setAddProject(totalProject);
        }

        QueryWrapper<ReportDto> qwRecTotal = new QueryWrapper<>();
        qwRecTotal.eq("b.project_admin_id", userId);
        // 1.3推荐病例数
        Integer recommendCount = recommendationDao.totalRecommend(qwRecTotal);
        total.setTotalRecommend(recommendCount);

        // 1.4新增推荐病例数
        if (startDate != null) {
            qwRecTotal.ge("a.recommend_time", startDate)
                    .lt("a.recommend_time", endDate);
            Integer addRecommendCount = recommendationDao.totalRecommend(qwRecTotal);
            total.setAddRecommend(addRecommendCount);
        } else {
            total.setAddRecommend(recommendCount);
        }

        QueryWrapper<ReportDto> qwInboundTotal = new QueryWrapper<>();
        qwInboundTotal.eq("b.project_admin_id", userId);
        qwInboundTotal.isNotNull("a.signing_op_time");
        // 1.5实际入组病例数
        Integer inboundCount = inboundProcessDao.totalInbound(qwInboundTotal);
        total.setTotalInbound(inboundCount);

        // 1.6新增实际入组病例数
        if (startDate != null) {
            qwInboundTotal.ge("a.signing_op_time", startDate)
                    .lt("a.signing_op_time", endDate);
            Integer addInboundCount = inboundProcessDao.totalInbound(qwInboundTotal);
            total.setAddInbound(addInboundCount);
        } else {
            total.setAddInbound(inboundCount);
        }
        return total;
    }

    private List<PIReportDto> pi(Long userId, String projectName, Date startDate, Date endDate) {
        // 2.1 项目推荐病例数
        QueryWrapper<PIReportDto> qwRec = new QueryWrapper<>();
        qwRec.eq("a.project_admin_id", userId);
        if (StringUtils.isNotBlank(projectName)) {
            qwRec.like("a.project_name", projectName);
        }
        List<PIReportDto> recommend = recommendationDao.groupPIRecommend(qwRec);

        // 2.2 新增项目推荐病例数
        Map<Long, Integer> addRecommendMap = new LinkedHashMap<>();
        if (startDate != null) {
            qwRec.ge("b.recommend_time", startDate)
                    .lt("b.recommend_time", endDate);
            List<PIReportDto> addRecommend = recommendationDao.groupPIRecommend(qwRec);
            addRecommendMap = addRecommend.stream().collect(Collectors.toMap(PIReportDto::getProjectId, PIReportDto::getTotalRecommend));
        }

        // 2.3 项目实际入组病例数
        QueryWrapper<PIReportDto> qwInbound = new QueryWrapper<>();
        qwInbound.eq("a.project_admin_id", userId);
        qwInbound.isNotNull("c.signing_op_time");
        if (StringUtils.isNotBlank(projectName)) {
            qwInbound.like("a.project_name", projectName);
        }
        List<PIReportDto> inbound = inboundProcessDao.groupPIInbound(qwInbound);
        Map<Long, Integer> inboundMap = inbound.stream().collect(Collectors.toMap(PIReportDto::getProjectId, PIReportDto::getTotalInbound));

        // 2.4 新增项目实际入组病例数
        Map<Long, Integer> addInboundMap = new LinkedHashMap<>();
        if (startDate != null) {
            qwInbound.ge("c.signing_op_time", startDate)
                    .lt("c.signing_op_time", endDate);
            List<PIReportDto> addInbound = inboundProcessDao.groupPIInbound(qwInbound);
            addInboundMap = addInbound.stream().collect(Collectors.toMap(PIReportDto::getProjectId, PIReportDto::getTotalInbound));
        }

        for (PIReportDto dto : recommend) {
            if (inboundMap != null) {
                Integer totalInb = inboundMap.containsKey(dto.getProjectId()) ? inboundMap.get(dto.getProjectId()) : 0;
                dto.setTotalInbound(totalInb);
            }

            // 增量需要区分是否有起止日期
            if (startDate != null) {
                if (addRecommendMap != null) {
                    Integer addRec = addRecommendMap.containsKey(dto.getProjectId()) ? addRecommendMap.get(dto.getProjectId()) : 0;
                    dto.setAddRecommend(addRec);
                }
                if (addInboundMap != null) {
                    Integer addInb = addInboundMap.containsKey(dto.getProjectId()) ? addInboundMap.get(dto.getProjectId()) : 0;
                    dto.setAddInbound(addInb);
                }
            } else {
                dto.setAddRecommend(dto.getTotalRecommend());
                dto.setAddInbound(dto.getAddInbound());
            }
        }
        return recommend;
    }

    @Override
    public void setPIData(Map<String, List<List<String>>> headMap,
                          Map<String, List<List<Object>>> dataMap,
                          String key,
                          List<Map<String, Object>> list) {

        List<List<String>> heads = headMap.get(key);
        heads.add(Collections.singletonList("牵头项目数"));
        heads.add(Collections.singletonList("新增牵头项目数"));
        heads.add(Collections.singletonList("项目名称"));
        heads.add(Collections.singletonList("累积被推荐人数"));
        heads.add(Collections.singletonList("新增被推荐人数"));
        heads.add(Collections.singletonList("实际入组人数"));
        heads.add(Collections.singletonList("实际新增入组人数"));

        List<List<Object>> dataList = dataMap.get(key);
        list.forEach(export -> {
            List<Object> data = new ArrayList<>();
            data.add(export.get("totalProject"));
            data.add(export.get("addProject"));
            data.add(export.get("projectName"));
            data.add(export.get("totalRecommend"));
            data.add(export.get("addRecommend"));
            data.add(export.get("totalInbound"));
            data.add(export.get("addInbound"));

            dataList.add(data);
        });
    }
}
