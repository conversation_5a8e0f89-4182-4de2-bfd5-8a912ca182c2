package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * 系统应用管理
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_application")
public class SysApplicationEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    @TableId
    private Long id;

    /**
     * 应用名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String appName;

    /**
     * 应用编码
     */
    @NotBlank(message = "编码不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "编码只能由英文字母和数字构成", groups = {AddGroup.class, UpdateGroup.class})
    private String appCode;


    /**
     * 是否默认应用， 1：是 0：否， 默认值0，
     * 如果为默认应用， 登录时默认大概对应应用菜单， 只能设置一个应用为默认
     */
    private Integer isDefault;

    /**
     * 应用说明
     */
    private String appDesc;

    /**
     * 是否是外部应用  1：是--外部应用    0：否--本系统应用 默认是0
     */
    private Integer external;

    /**
     * 外部应用链接
     */
    private String externalLink;


    /**
     * 是否新窗口打开， 1：是， 0 否
     */
    private Integer isNewWindowsOpen;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否是项目应用标识0：否，1：是
     */
    private Integer isProjectApp;

    @TableField(exist = false)
    private List<SysMenuEntity> menus;
}
