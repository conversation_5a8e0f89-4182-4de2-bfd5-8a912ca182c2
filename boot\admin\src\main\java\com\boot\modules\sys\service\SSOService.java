package com.boot.modules.sys.service;

import com.boot.modules.sys.entity.SysUserEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 单点登录业务接口类
 * <AUTHOR>
 * @createTime 2022年2月09日 18:21:00
 */
public interface SSOService {

    /**
     * 单点登录方法
     * @param request
     * @param response
     * @param code
     * @return
     * @throws Exception
     */
    SysUserEntity ssoLogin(HttpServletRequest request, HttpServletResponse response, String code) throws Exception;

    /**
     * 单点登出
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    boolean ssoLogout(HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 模拟shiro登录，生成token
     */
    String createTokenByUserName(String userName, HttpServletRequest request, HttpServletResponse response) throws IOException;

}