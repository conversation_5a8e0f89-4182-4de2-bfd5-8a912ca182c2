package com.boot.modules.sys.controller;


import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.constant.CacheConst;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.*;
import com.boot.modules.mobile.model.HytParam;
import com.boot.modules.mobile.utils.HytUtil;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.mapper.SSOServiceMapper;
import com.boot.modules.sys.service.SSOService;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserHisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.Principal;
import java.util.List;
import java.util.Map;

/**
 * SSO单点登录接口管理
 * <AUTHOR>
 * @createTime 2022年2月09日 18:21:00
 */
@Api(tags = "SSO单点登录管理")
@RequestMapping("/sso")
@Controller
public class SSOLoginController extends AbstractController {


    @Resource
    private SSOConfigurationProperties ssoConfigurationProperties;

    @Resource
    private SysUserHisService userHisService;

    @Resource
    private SysDeptService deptService;

    /**
     * sso单点登录
     *
     * @param response HttpServletResponse
     * @throws IOException 异常
     */
    @ApiOperation(
            value = "sso单点登录",
            notes = "sso单点登录")
    @PostMapping("/login")
    @ResponseBody
    public Result appLogin(HttpServletRequest request,
                           HttpServletResponse response,
                           @RequestBody Map<String, Object> params) throws Exception {

        if (CollectionUtils.isEmpty(params)) {
            return R.success();
        }
        String code = params.get("code") == null ? "" : params.get("code").toString();
        if (StringUtils.isEmpty(code)) {
            return R.success();
        }

        //执行单点登录逻辑，通过授权码获取登录用户信息
        //判断单点登录医院模式
        SysUserEntity userEntity;
        SSOService ssoService = SSOServiceMapper.getMapper(SpringContextUtils.applicationContext);
        userEntity = ssoService.ssoLogin(request, response, code);
        String token = "";
        if (userEntity != null) {
            //生成token信息
            token = ssoService.createTokenByUserName(userEntity.getUsername(), request, response);
            userEntity.setToken(token);
        }

        if (StringUtils.isEmpty(token)) {
            return R.fail("单点登录失败");
        } else {
            // 受试者返回相关字段
            Integer hyt = params.get("hyt") == null ? 0 : Integer.parseInt(params.get("hyt").toString());
            if (hyt != null && hyt.equals(1)) {
                String medCode = params.get("medCode") == null ? "" : params.get("medCode").toString();
                String doctorCode = params.get("doctorCode") == null ? "" : params.get("doctorCode").toString();
                userEntity.setMedCode(medCode);
                userEntity.setDoctorCode(doctorCode);
                if (!StringUtils.isEmpty(medCode)) {
                    SysDeptEntity sysDeptEntity = deptService.getTopDeptByCode(medCode);
                    userEntity.setMedId(ObjectUtils.isEmpty(sysDeptEntity) ? 0L : sysDeptEntity.getId());
                }
            }

            Result result = R.success();
            result.setData(userEntity);
            result.setToken(token);
            result.setExpire(3600 * 12);
            return result;
        }
    }

    @SneakyThrows
    @GetMapping(value = "/home")
    public void home(HttpServletRequest request, HttpServletResponse response) {
        Principal userPrincipal = request.getUserPrincipal();
        if (userPrincipal == null) {
            return;
        }
        String name = userPrincipal.getName();
        if (name.contains("SysUserEntity(id=")) {
            name = name.split("username=")[1].split(", password")[0];
        }
        response.sendRedirect(ssoConfigurationProperties.getFrontLoginUrl() + "?code=" + name);
    }

    @SneakyThrows
    @GetMapping(value = "/his")
    public void his(HttpServletRequest request, HttpServletResponse response) {
        String foreignId = request.getParameter("foreignId") == null ? "" : request.getParameter("foreignId");
        String deptCode = request.getParameter("medCode") == null ? "" : request.getParameter("medCode");

        List<SysUserEntity> sysUserEntityList = userHisService.getByForeignAndDept(foreignId, deptCode);
        String backUrl = "/his-recommend-detail?" + request.getQueryString();

        if (CollectionUtils.isEmpty(sysUserEntityList)) {
            logger.error("用户在受试者入组关联系统中不存在 请检查：" + foreignId + "///" + deptCode);
            response.sendRedirect(ssoConfigurationProperties.getServerLoginUrl());
            return;
        }
        SysUserEntity sysUserEntity = sysUserEntityList.get(0);
        EhCacheUtil.getInstance().put(CacheConst.CACHE_HIS_USER_KEY, sysUserEntity.getId().toString(), true);
        response.sendRedirect(ssoConfigurationProperties.getFrontLoginUrl() + "?code=" + sysUserEntity.getUsername() + "&backUrl=" + URLEncoder.encode(backUrl, "UTF-8"));
    }

    /**
     * 是否开启sso单点登录
     */
    @ApiOperation(
            value = "是否开启sso单点登录",
            notes = "是否开启sso单点登录")
    @GetMapping("/enable")
    @ResponseBody
    public Result enable() {
        return R.success(ssoConfigurationProperties.getEnable());
    }

    @SneakyThrows
    @ApiOperation(
            value = "华医通请求",
            notes = "华医通请求")
    @PostMapping(value = "/hyt")
    @ResponseBody
    public Result hyt(HttpServletRequest request, HttpServletResponse response, @RequestBody Map<String, Object> params) {
        // 先认证是否从华医通跳转过来
        String digest = MapUtils.getValue(params, "digest", String.class);
        HytParam hytParam = HytUtil.getHytParam(digest);
        if (hytParam == null || StringUtils.isBlank(hytParam.getDoctorCode()) || StringUtils.isBlank(hytParam.getOrganCode())) {
            logger.error("该请求华医通认证信息缺失：" + digest);
            return R.fail("该请求华医通认证信息缺失，请确认！");
        }

        List<SysUserEntity> sysUserEntityList = userHisService.getByForeignAndDept(hytParam.getDoctorCode(), hytParam.getOrganCode());
        if (CollectionUtils.isEmpty(sysUserEntityList)) {
            logger.error("用户不存在：" + digest + "///" + hytParam.getDoctorCode() + "///" + hytParam.getOrganCode());
            return R.fail("用户不存在，请确认！");
        }
        SysUserEntity sysUserEntity = sysUserEntityList.get(0);
        EhCacheUtil.getInstance().put(CacheConst.CACHE_HYT_USER_KEY, sysUserEntity.getId().toString(), true);
        params.put("code", sysUserEntity.getUsername());
        params.put("medCode", hytParam.getOrganCode());
        params.put("doctorCode", hytParam.getDoctorCode());
        params.put("hyt", 1);
        return appLogin(request, response, params);
    }

}
