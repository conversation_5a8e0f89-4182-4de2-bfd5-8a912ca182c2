package com.boot.modules.patient.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boot.commons.constant.HxConst;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.patient.dto.InboundProcessDto;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.patient.service.ReportService;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.service.DiseaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 统计报表管理
 */
@Api(tags = "统计报表管理")
@RestController
@RequestMapping("/report")
public class ReportController extends AbstractController {

    @Resource
    private DoctorRecommendationService recommendationService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private ReportService reportService;

    @Resource
    private DiseaseService diseaseService;

    @Resource
    private PatientService patientService;

    @ApiOperation(value = "每位医生推荐的患者量接口", notes = "每位医生推荐的患者量接口")
    @GetMapping("/recommend/personal")
    public Result personalRecommendData(@RequestParam(required = false) String date) {
        return R.success(recommendationService.personalRecommendData(date));
    }
//
//    @ApiOperation(value = "科室推荐的患者量接口", notes = "科室推荐的患者量接口")
//    @GetMapping("/recommend/department")
//    public Result departmentRecommendData(@RequestParam(required = false) String date){
//        return R.success(recommendationService.departmentRecommendData(date));
//    }

    @ApiOperation(value = "医生推荐的患者实际入组量（签订知情同意书）接口", notes = "医生推荐的患者实际入组量（签订知情同意书）接口")
    @GetMapping("/reality/personal")
    public Result realityPersonalData(@RequestParam(required = false) String date) {
        return R.success(inboundProcessService.realityPersonalData(date));
    }

//    @ApiOperation(value = "医生推荐的患者实际入组量（签订知情同意书）接口", notes = "医生推荐的患者实际入组量（签订知情同意书）接口")
//    @GetMapping("/reality/department")
//    public Result realityDepartmentData(@RequestParam(required = false) String date){
//        return R.success(inboundProcessService.realityDepartmentData(date));
//    }

    /**
     * 临管部管理者-科室报表
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "临管部管理员统计页面接口", notes = "临管部管理员统计页面接口")
    @GetMapping("/pro/management")
    public Result proManagement(@RequestParam Map<String, Object> params) {
        return R.success(reportService.proManagement(params));
    }

    /**
     * 临管部管理者医生报表、科室管理者医生报表
     * isAll为true 临管部管理者医生报表
     * deptId非空 临管部管理者科室报表，点击科室跳转
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "科室管理员统计页面接口", notes = "科室管理员统计页面接口")
    @GetMapping("/dept/management")
    public Result deptManagement(@RequestParam Map<String, Object> params) {
        String isAll = MapUtils.getValue(params, "isAll", String.class);
        Object deptId = params.get("deptId");

        boolean a = isAll != null && isAll.equals("true");
        boolean b = deptId == null;

        if ((!a) && (!b)) {
            return R.success(reportService.deptManagement(params, true));
        } else {
            if (b) {
                params.put("deptId", getAdminDeptId());
            }
            return R.success(reportService.deptManagement(params, false));
        }

    }

    /**
     * 临管部管理者医联体报表、科室管理者医联体报表（只看本科室的项目）
     * roleType 1临管部管理者；2科室管理者
     * type 0按日期（默认）；1按年份
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "医联体报表", notes = "医联体报表")
    @GetMapping("/ico/management")
    public Result icoManagement(@RequestParam Map<String, Object> params) {
        Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
        if (roleType != null && roleType == 2) {
            // 科室管理者
            params.put("deptId", getAdminDeptId());
        }
        return R.success(reportService.icoManagement(params));
    }


    /**
     * 统计当前用户作为PI的项目数
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "PI报表", notes = "PI报表")
    @GetMapping("/pi")
    public Result pi(@RequestParam Map<String, Object> params) {
        params.put("userId", getUserId());
        return R.success(reportService.pi(params));
    }

    @ApiOperation(value = "数据导出", notes = "数据导出")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody Map<String, Object> requireMap) {

        String fileName = "reportExport" + DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);

        Object type = requireMap.get(HxConst.TYPE);

        Map<String, List<List<String>>> headMap = new HashMap<>();
        Map<String, List<List<Object>>> dataMap = new HashMap<>();
        headMap.put("合计", new ArrayList<>());
        headMap.put("分项", new ArrayList<>());
        dataMap.put("合计", new ArrayList<>());
        dataMap.put("分项", new ArrayList<>());
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) requireMap.get(HxConst.LIST_KEY);
        List<Map<String, Object>> totalList = (List<Map<String, Object>>) requireMap.get(HxConst.TOTAL_KEY);

        switch ((Integer) type) {
            case 0:
                //科室管理者报表
                if (!CollectionUtils.isEmpty(dataList)) {
                    reportService.setDeptManageData(headMap, dataMap, "分项", dataList);
                }
                if (!CollectionUtils.isEmpty(totalList)) {
                    reportService.setDeptManageHeaderData(headMap, dataMap, "合计", totalList);
                }
                break;
            case 1:
                //临管部管理者-医生报表
                if (!CollectionUtils.isEmpty(dataList)) {
                    reportService.setDoctorData(headMap, dataMap, "分项", dataList);
                }
                if (!CollectionUtils.isEmpty(totalList)) {
                    reportService.setDoctorHeaderData(headMap, dataMap, "合计", totalList);
                }
                break;
            case 2:
                //临管部管理者-科室报表
                if (!CollectionUtils.isEmpty(dataList)) {
                    reportService.setDeptData(headMap, dataMap, "分项", dataList);
                }
                if (!CollectionUtils.isEmpty(totalList)) {
                    reportService.setDeptData(headMap, dataMap, "合计", totalList);
                }
                break;
            case 3:
                // 医联体报表
                if (!CollectionUtils.isEmpty(dataList)) {
                    reportService.setIcoData(headMap, dataMap, "分项", dataList);
                }
                if (!CollectionUtils.isEmpty(totalList)) {
                    reportService.setIcoData(headMap, dataMap, "合计", totalList);
                }
                break;
            case 4:
                // PI报表
                if (!CollectionUtils.isEmpty(dataList)) {
                    reportService.setPIData(headMap, dataMap, "分项", dataList);
                }
                if (!CollectionUtils.isEmpty(totalList)) {
                    reportService.setPIData(headMap, dataMap, "合计", totalList);
                }
                break;
            default:
                if (!CollectionUtils.isEmpty(dataList)) {
                    reportService.setDeptData(headMap, dataMap, "分项", dataList);
                }
                if (!CollectionUtils.isEmpty(totalList)) {
                    reportService.setDeptData(headMap, dataMap, "合计", totalList);
                }
                break;
        }

        ExcelUtils.wirteExcel(response, fileName, headMap, dataMap);
    }

    @ApiOperation(value = "获取当前医生所属科室的病种及项目", notes = "获取当前医生所属科室的项目及系统推荐")
    @GetMapping("/dept/subProject/list/{userId}/{deptId}")
    public Result deptproject(@PathVariable() Long userId, @PathVariable() Long deptId) {
        return R.success(diseaseService.getDieaseByDept(Arrays.asList(deptId)));
    }

    @ApiOperation(value = "受试者信息列表", notes = "受试者信息列表")
    @GetMapping("/pat/list")
    public Result list(@RequestParam Map<String, Object> params) {
        return R.success(patientService.patList(params));
    }

    @ApiOperation(value = "导出受试者信息列表", notes = "导出受试者信息列表")
    @PostMapping("/pat/list/export")
    public void exportPat(HttpServletResponse response, @RequestBody Map<String, Object> params) {
        String fileName = "reportExport" + DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
        List<InboundProcessDto> dataList = new ArrayList<>();
        Map<String, List<List<String>>> headMap = new HashMap<>();
        Map<String, List<List<Object>>> dataMap = new HashMap<>();
        headMap.put("受试者信息", new ArrayList<>());
        dataMap.put("受试者信息", new ArrayList<>());
        // 查受试者患者信息
        Integer page = 1;
        Integer limit = 1000;
        params.put("page", page.toString());
        params.put("limit", limit.toString());
        PageUtils onePage = patientService.patList(params);
        if (onePage != null && !CollectionUtils.isEmpty(onePage.getList())) {
            List<InboundProcessDto> firstPage = (List<InboundProcessDto>) onePage.getList();
            dataList.addAll(firstPage);
            Integer totalPage = onePage.getTotalPage();
            for (Integer i = 2; i <= totalPage; i++) {
                params.put("page", i.toString());
                PageUtils nextPage = patientService.patList(params);
                if (nextPage != null && !CollectionUtils.isEmpty(nextPage.getList())) {
                    List<InboundProcessDto> curPage = (List<InboundProcessDto>) nextPage.getList();
                    dataList.addAll(curPage);
                }
            }
            Integer isFromYlt = MapUtils.getValue(params, "isFromYlt", Integer.class);
            Integer isFromPi = MapUtils.getValue(params, "isFromPi", Integer.class);
            if (isFromYlt != null && isFromYlt.equals(1)) {
                reportService.setYltPatientData(headMap, dataMap, "受试者信息", dataList);
            } else if (isFromPi != null && isFromPi.equals(1)) {
                reportService.setPIPatientData(headMap, dataMap, "受试者信息", dataList);
            } else {
                reportService.setPatientData(headMap, dataMap, "受试者信息", dataList);
            }
        }
        ExcelUtils.wirteExcel(response, fileName, headMap, dataMap);
    }
}
