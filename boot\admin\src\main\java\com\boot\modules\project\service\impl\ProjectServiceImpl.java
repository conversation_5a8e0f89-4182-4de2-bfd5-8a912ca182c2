package com.boot.modules.project.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.excel.model.ReadExcelListener;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.*;
import com.boot.commons.utils.file.FileHelper;
import com.boot.modules.openApi.service.EdcService;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.project.dao.ProjectDao;
import com.boot.modules.project.entity.*;
import com.boot.modules.project.enums.IsAdminEnum;
import com.boot.modules.project.enums.ProjectCategoryEnum;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.project.enums.ProjectTypeEnum;
import com.boot.modules.project.redis.EnrollDetailRedis;
import com.boot.modules.project.service.*;
import com.boot.modules.project.vo.*;
import com.boot.modules.sync.entity.KelinConfigEntity;
import com.boot.modules.sync.service.KelinConfigService;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysRoleService;
import io.swagger.models.auth.In;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.boot.modules.project.redis.EnrollDetailRedis.REDIS_CONFIG;
import static com.boot.modules.project.redis.EnrollDetailRedis.UNDER_SCORE;

/**
 * <AUTHOR>
 * @desc :项目接口实现类
 * @create 2021-02-23
 */
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectDao, ProjectEntity> implements ProjectService {


    @Resource
    private SubProjectService subProjectService;

    @Resource
    private SysRoleService roleService;

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private ProjDeptService projDeptService;

    @Resource
    private SysDeptService deptService;

    @Resource
    private ProjectSettingService projectSettingService;

    @Resource
    private ProjectDeptPermissionService projectDeptPermissionService;

    @Resource
    private KelinConfigService kelinConfigService;

    @Resource
    private EnrollDetailRedis enrollDetailRedis;

    @Resource
    private ProjectRoleSettingService projectRoleSettingService;

    @Resource
    private ProjectCenterConfigService projectCenterConfigService;

    @Resource
    private GroupConfigService groupConfigService;

    @Resource
    private EnrollRecordService enrollRecordService;

    @Resource
    private EnrollDetailService enrollDetailService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private PatientService patientService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private EdcService edcService;

    @Resource
    private DoctorRecommendationDao recommendationDao;

    /**
     * 分页查询当前用户的所有项目 支持项目名称的模糊查询
     *
     * @param params
     * @param isReturnOwnCreate 是否只返回自己创建
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params,
                               SysUserEntity userEntity,
                               boolean isReturnOwnCreate, boolean isReturnStatus) {
        Long userId = userEntity == null ? null : userEntity.getId();

        String projectName = MapUtils.getValue(params, "projectName", String.class);
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        //单病钟还是多病钟
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        Integer projectType = MapUtils.getValue(params, "projectType", Integer.class);
        //单中心 还是多中心
        Integer category = MapUtils.getValue(params, "category", Integer.class);
        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);
        QueryWrapper<ProjectVo> qw = MapUtils.getWrapperByParams(params, "create_time", "a", ProjectVo.class);

        qw.lambda()
                .like(StringUtils.isNotBlank(projectName), ProjectEntity::getProjectName, projectName)
                .eq(type != null, ProjectEntity::getType, type)
                .eq(category != null, ProjectEntity::getProjectCategory, category);
        qw.eq(status != null, "a.status", status);

        qw.eq(projectType != null, "a.project_type", projectType);
        qw.eq(diseaseId != null, "a.disease_id", diseaseId);

        if (isReturnOwnCreate) {
            // 自己创建的用户
            qw.eq(userId != null && userId != 0L, "a.create_user_id", userId);
        } else {
            qw.and(userId != null && userId != 0L, q ->
                    q.eq("c.user_id", userId)
                            .or()
                            .eq("a.create_user_id", userId)
                            .or()
                            .eq("a.project_admin_id", userId)
            );

        }

        qw.eq(isReturnStatus, "a.status", 1)
                .groupBy("a.id");

        IPage<ProjectVo> pageFilter = new Query<ProjectVo>().getPage(params);

        IPage<ProjectVo> page = this.baseMapper.queryPage(pageFilter, qw);

        //查询所以项目中心
        List<ProjDeptEntity> projectDeptCenterList = projDeptService.list();
        List<ProjectVo> records = page.getRecords();
        getProjCenter(records);
        for (ProjectVo record : records) {
            joinProjectCenter(projectDeptCenterList, record);
        }
        page.setRecords(records);
        return new PageUtils(page);
    }

    /**
     * 获取指定用户的全部项目
     *
     * @param isReturnOwnCreate 是否只返回自己创建
     * @param isReturnStatus    是否只返回启用状态的项目
     * @return
     */
    @Override
    public List<ProjectVo> queryList(Map<String, Object> params,
                                     SysUserEntity userEntity,
                                     boolean isReturnOwnCreate,
                                     boolean isReturnStatus) {
        Long userId = userEntity == null ? null : userEntity.getId();

        Integer projectCategory = MapUtils.getValue(params, "projectCategory", Integer.class);
        Integer type = MapUtils.getValue(params, "type", Integer.class);
        Integer projectType = MapUtils.getValue(params, "projectType", Integer.class);
        Integer status = MapUtils.getValue(params, "status", Integer.class);

        String name = MapUtils.getValue(params, "name", String.class);

        String projectName = MapUtils.getValue(params, "projectName", String.class);

        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);

        Long deptId = MapUtils.getValue(params, "deptId", Long.class);

        QueryWrapper<ProjectVo> qw = new QueryWrapper<ProjectVo>();
        // 华医通时，需要对项目进行过滤
        Integer enableHyt = MapUtils.getValue(params, "enableHyt", Integer.class);
        if (enableHyt != null && enableHyt == 1) {
            qw.eq("a.enable_hyt", 1);
        }
        if (isReturnOwnCreate) {
            // 自己创建的用户
            qw.eq(userId != null && userId != 0L, "a.create_user_id", userId);
        } else {
            qw.and(userId != null && userId != 0L, q ->
                    q.eq("c.user_id", userId)
                            .or()
                            .eq("a.create_user_id", userId)
                            .or()
                            .eq("a.project_admin_id", userId)
            );
        }
        qw.eq(status != null, "a.status", status);
        qw.eq(projectType != null, "a.project_type", projectType);
        qw.and(StringUtils.isNotBlank(name), q ->
                q.like("a.project_name", name)
                        .or()
                        .like("b.name", name));

        qw.like(StringUtils.isNotEmpty(projectName), "a.project_name", projectName);

        qw.eq(diseaseId != null, "a.disease_id", diseaseId);

        qw.eq(deptId != null, "a.main_dept_id", deptId);

        List<ProjectVo> result = this.baseMapper.getByQuery(qw);

        List<ProjDeptEntity> allProjDept = projDeptService.list();

        Map<Long, List<ProjDeptEntity>> projectDeptMap = allProjDept.stream().collect(Collectors.groupingBy(ProjDeptEntity::getProjectId));

        for (ProjectVo projectVo : result) {
            List<ProjDeptEntity> list = projectDeptMap.get(projectVo.getId());
            if (!CollectionUtils.isEmpty(list)) {
                joinProjectCenter(list, projectVo);
            }
        }
        getProjCenter(result);

        getAttachInfo(result, userId, name);
        getRoleInfo(result, userId);

        if (projectCategory != null) {
            result = result.stream().filter(vo -> vo.getProjectCategory().equals(projectCategory)).collect(Collectors.toList());
        }
        if (type != null) {
            result = result.stream().filter(vo -> vo.getType().equals(type)).collect(Collectors.toList());
        }

        getSubProjSetting(result);
        return result;
    }

    @Override
    public List<ProjectVo> listAll() {
        return this.baseMapper.getByQuery(new QueryWrapper<ProjectVo>()
                .eq("a.enable_sync", 1)
                .isNull("a.edc_project_id"));
    }

    @Override
    public PageUtils page(Map<String, Object> params) {
        // 1.自定义查询参数
        QueryWrapper<ProjectVo> qw = new QueryWrapper<>();
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        Integer projectType = MapUtils.getValue(params, "projectType", Integer.class);
        Date startDate = MapUtils.getValue(params, "startDate", Date.class);
        Date endDate = MapUtils.getValue(params, "endDate", Date.class);

        if (status != null) {
            qw.eq("a.status", status);
        }

        if (projectType != null) {
            qw.eq("a.project_type", projectType);
        }

//        if (startDate != null) {
//            qw.ge("a.create_time", startDate);
//        }
//
//        if (endDate != null) {
//            qw.le("a.create_time", endDate);
//        }

        // 拼接有新增入组的项目的条件
        if (startDate != null && endDate != null) {
            QueryWrapper<InboundProcessEntity> qwInbound = new QueryWrapper<>();
            qwInbound.lambda().ge(InboundProcessEntity::getSigningOpTime, startDate);
            endDate = DateUtils.addDateDays(endDate, 1);
            qwInbound.lambda().le(InboundProcessEntity::getSigningOpTime, endDate);
            // 查有新增入组的项目ID
            List<Long> addPatProjectIds = this.baseMapper.getAddPatProjectIdList(qwInbound);
            if (CollectionUtils.isEmpty(addPatProjectIds)) {
                return null;
            }
            qw.in("a.id", addPatProjectIds);
        }

        // 2.获取分页参数对象
        IPage<ProjectVo> pageFilter = new Query<ProjectVo>().getPage(params);

        // 3.按科室id获取结果
        Long deptId = MapUtils.getValue(params, "deptId", Long.class);

        if (deptId != null) {
            qw.eq("a.main_dept_id", deptId);
//            qw.eq("g.project_dept_id", deptId);
        }
        // 4.用户id
        Long doctorId = MapUtils.getValue(params, "doctorId", Long.class);

        if (doctorId != null) {
            qw.eq("a.project_admin_id", doctorId);
        }

        // 5.病种id
        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);

        if (diseaseId != null) {
            qw.eq("a.disease_id", diseaseId);
        }

        // 6.医联体报表跳转过来
        Integer isYlt = MapUtils.getValue(params, "isYlt", Integer.class);
        if (isYlt != null && isYlt.equals(1)) {
            // 7.医疗机构ID
            Long medId = MapUtils.getValue(params, "medId", Long.class);
            if (medId != null) {
                // 查指定推荐医疗机构的项目
                QueryWrapper<DoctorRecommendationEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(DoctorRecommendationEntity::getTopRecommendDeptId, medId);
                List<Long> projectIds = recommendationDao.getProjectIds(queryWrapper);
                if (CollectionUtils.isEmpty(projectIds)) {
                    return null;
                }
                qw.in("a.id", projectIds);
            } else {
                Integer isAll = MapUtils.getValue(params, "isAll", Integer.class);
                if (isAll != null && isAll == 1) {
                    // 总项目数，查开通医联体权限的项目数
                    List<ProjectEntity> projectEntityList = this.list(new QueryWrapper<ProjectEntity>()
                            .lambda().eq(ProjectEntity::getEnableMedConst, 1).select(ProjectEntity::getId));
                    List<Long> projectIds = projectEntityList.stream().map(p -> p.getId()).collect(Collectors.toList());
                    qw.in("a.id", projectIds);
                } else {
                    Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
                    switch (roleType) {
                        case 1:
                            // 临管部管理者-推荐机构不是华西本院的
                            QueryWrapper<DoctorRecommendationEntity> queryWrapper = new QueryWrapper<>();
                            queryWrapper.lambda().ne(DoctorRecommendationEntity::getTopRecommendDeptId, 15L);
                            List<Long> projectIds = recommendationDao.getProjectIds(queryWrapper);
                            if (CollectionUtils.isEmpty(projectIds)) {
                                return null;
                            }
                            qw.in("a.id", projectIds);
                            break;
                        case 2:
                            // 科室管理者-推荐科室为主管科室的
                            List<Long> mainDeptId = MapUtils.getValue(params, "mainDeptId", ArrayList.class);
                            QueryWrapper<DoctorRecommendationEntity> qwDept = new QueryWrapper<>();
                            qwDept.lambda().in(DoctorRecommendationEntity::getRecommendDeptId, mainDeptId);
                            List<Long> mainDeptProjectIds = recommendationDao.getProjectIds(qwDept);
                            if (CollectionUtils.isEmpty(mainDeptProjectIds)) {
                                return null;
                            }
                            qw.in("a.id", mainDeptProjectIds);
                            break;
                    }
                }
            }
        }

        IPage<ProjectVo> page = this.baseMapper.pageByDept(pageFilter, qw);
        List<ProjectVo> records = page.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return null;
        }

        // 6.设置项目入组数
        inboundProcessService.setProjectInboundCount(records, startDate, endDate);

        return new PageUtils(page);
    }


    /**
     * 获取当前用户为项目管理员的所有项目列表
     * 如果是系统管理员 返回所有项目
     *
     * @param user
     * @return
     */
    @Override
    public List<ProjectVo> queryAdminProjectByUser(SysUserEntity user) {
        List<ProjectVo> result = new ArrayList<>();

        QueryWrapper<ProjectEntity> qw = new QueryWrapper<>();
        if (!((user.getIsSuperAdmin() != null && user.getIsSuperAdmin().equals(1)) //超级用户
                || (user.getIsSysAdmin() != null && user.getIsSysAdmin().equals(1)) //系统用户
                || user.getId() == 0L)) {
            qw.lambda().eq(ProjectEntity::getProjectAdminId, user.getId());
        }
        //系统管理员 返回所有项目
        qw.lambda()
                .eq(ProjectEntity::getStatus, 1)
                .select(ProjectEntity::getId, ProjectEntity::getProjectName);
        List<ProjectEntity> list = this.list(qw);

        List<Long> projectIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (ProjectEntity project : list) {
                ProjectVo projectVo = new ProjectVo();
                BeanUtils.copyProperties(project, projectVo);
                result.add(projectVo);
                projectIds.add(project.getId());
            }
        }

        if (!CollectionUtils.isEmpty(projectIds)) {
            QueryWrapper<SubProjectEntity> qwSub = new QueryWrapper<>();

            qwSub.lambda()
                    .in(SubProjectEntity::getProjectId, projectIds)
                    .select(SubProjectEntity::getId,
                            SubProjectEntity::getName,
                            SubProjectEntity::getProjectId);
            List<SubProjectEntity> subProjectList = subProjectService.list(qwSub);

            if (!CollectionUtils.isEmpty(subProjectList)) {
                List<SubProjectVo> subProjectVoList = new ArrayList<>();
                for (SubProjectEntity subProjectEntity : subProjectList) {
                    SubProjectVo subProjectVo = new SubProjectVo();
                    BeanUtils.copyProperties(subProjectEntity, subProjectVo);
                    subProjectVoList.add(subProjectVo);
                }

                Map<Long, List<SubProjectVo>> listMap = subProjectVoList.stream().collect(Collectors.groupingBy(SubProjectVo::getProjectId));

                for (ProjectVo projectVo : result) {
                    if (projectVo.getId() == null) {
                        continue;
                    }

                    projectVo.setSubProjList(listMap.get(projectVo.getId()));
                }
            }
        }


        return result;
    }

    @Override
    public ProjectVo getInfoById(Long projectId) {

//        ProjectEntity project = this.getById(projectId);
        QueryWrapper<ProjectVo> qw = new QueryWrapper<>();
        qw.eq("a.id", projectId);
        List<ProjectVo> projectVoList = baseMapper.getByQuery(qw);
        if (CollectionUtils.isEmpty(projectVoList)) {
            return null;
        }
        ProjectVo projectVo = projectVoList.get(0);

        List<ProjDeptEntity> projDeptEntityList = projDeptService.getByProjectId(projectId);
        if (!CollectionUtils.isEmpty(projDeptEntityList)) {
            joinProjectCenter(projDeptEntityList, projectVo);
        }

        return projectVo;
    }

    /**
     * 新增项目的同时  将项目业务表添加记录
     *
     * @param project
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(ProjectVo project) {

        long start0 = System.currentTimeMillis();
        boolean res;
        //
        String projectName = project.getProjectName();
        if (projectName.length() > 200) {
            projectName = project.getProjectName().substring(0, 200);
        }
        project.setProjectName(projectName);
        res = this.save(project);

        //新增项目的时候 处理项目中心关联数据
        List<ProjDeptEntity> projDeptList = getProjDeptList(project.getId(), project.getPrimaryCenter(), project.getBranchCenter());

        if (!CollectionUtils.isEmpty(projDeptList)) {
            res = res && projDeptService.saveBatch(projDeptList);
        }
        //  如果是单病种项目， 同时生成当前单病种下的子项目，且只能生成一个
        SubProjectEntity subProjectEntity = new SubProjectEntity();
        if (res && project.getType().equals(ProjectTypeEnum.SINGLE_DISEASE.getType())) {
            // 单病种
            subProjectEntity.setName(projectName);
            subProjectEntity.setProjectId(project.getId());
            subProjectEntity.setCreateTime(new Date());
            subProjectEntity.setCreateUserId(project.getCreateUserId());
            subProjectEntity.setEnabled(1);
            subProjectEntity.setEnableAudit(project.getEnableAudit());
            subProjectEntity.setEnableApi(project.getEnableApi());
            subProjectEntity.setStatus(0);
            res = subProjectService.add(subProjectEntity, project);

            //新增数据权限默认配置
            res = res && projectRoleSettingService.addConfigInit(subProjectEntity);

        }

        // 若项目所属中心的一级机构在数据中心，需要增加一条项目数据权限关联记录
        List<ProjDeptPermissionEntity> projDeptPermissionEntityList = new ArrayList<>();
        List<Long> deptIds = projDeptList.stream().map(p -> p.getDeptId()).distinct().collect(Collectors.toList());
        // 获取所有机构信息
        List<SysDeptEntity> deptEntities = deptService.getAll();
        Long topDeptId = 0L;
        for (Long deptId : deptIds) {
            // 递归获取最上层科室信息
            SysDeptEntity sysDeptEntity = deptService.getTopDeptById(deptEntities, deptId);

            // 非数据中心的不处理
            if (sysDeptEntity == null || !sysDeptEntity.getIsDataCenter().equals(1)) {
                continue;
            }
            // 主中心的上层科室信息
            List<ProjDeptEntity> primary = projDeptList.stream().filter(p -> p.getDeptId().equals(deptId)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(primary)) {
                topDeptId = sysDeptEntity.getId();
            }
            ProjDeptPermissionEntity projDeptPermissionEntity = new ProjDeptPermissionEntity();
            projDeptPermissionEntity.setDeptCode(sysDeptEntity.getCode());
            projDeptPermissionEntity.setDeptId(sysDeptEntity.getId());
            projDeptPermissionEntity.setProjectId(project.getId());
            projDeptPermissionEntityList.add(projDeptPermissionEntity);
        }
        if (projDeptPermissionEntityList.size() > 0) {
            res = res && projectDeptPermissionService.saveBatch(projDeptPermissionEntityList);
        }

        // 更新项目管理员记录
        Long projectAdminId = project.getProjectAdminId();
        UserSubProjectEntity userSubProject = new UserSubProjectEntity();
        userSubProject.setUserId(projectAdminId);
        userSubProject.setSubProjectId(subProjectEntity.getId());
        // 设置用户对应机构
        List<ProjDeptEntity> collect = projDeptList
                .stream().filter(projDeptEntity -> projDeptEntity.getIsPrimary().equals(1)).collect(Collectors.toList());
        userSubProject.setProjectDeptId(collect.get(0).getDeptId());
        userSubProject.setTopDeptId(topDeptId);
        // 设置用户角色id
        List<SysRoleEntity> list = roleService.list(new QueryWrapper<SysRoleEntity>().lambda().eq(SysRoleEntity::getIsProjAdmin, 1));
        userSubProject.setProjRoleId(list.get(0).getId());

        res = res && userSubProjectService.save(userSubProject);
        if (!res) {
            throw new BusinessException("创建项目失败");
        }
        long start = System.currentTimeMillis();
        if (project.getEnableSync() != null && project.getEnableSync() == 1) {
            edcService.addProject(project);
        }
        log.error("调用EDC耗时" + (System.currentTimeMillis() - start));
        log.error("总耗时" + (System.currentTimeMillis() - start0));
        return res;
    }

    /**
     * 修改项目，包含可修改项目中心
     *
     * @param projectVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProject(ProjectVo projectVo) {
        boolean res = true;
        ProjectVo oldProject = this.getInfoById(projectVo.getId());
        if (!oldProject.getProjectName().equals(projectVo.getProjectName())) {
            //修改了项目名称
            QueryWrapper<ProjectEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(ProjectEntity::getProjectName, projectVo.getProjectName());
            List<ProjectEntity> list = this.list(qw);
            if (list != null && list.size() > 0) {
                throw new BusinessException("当前项目已存在");
            }
        }

        List<SubProjectEntity> subProjectEntityList = subProjectService.list(
                new QueryWrapper<SubProjectEntity>().lambda().eq(SubProjectEntity::getProjectId, projectVo.getId())
        );
        if (!CollectionUtils.isEmpty(subProjectEntityList)) {
            if (projectVo.getType().equals(1)
                    && !subProjectEntityList.get(0).getEnableApi().equals(projectVo.getEnableApi())) {
                subProjectEntityList.get(0).setEnableApi(projectVo.getEnableApi());
                subProjectEntityList.get(0).setUpdateTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                subProjectService.updateById(subProjectEntityList.get(0));
            }
        }

        //判断是否修改了状态
        if ((oldProject.getStatus() == null && projectVo.getStatus() != null)
                || (!oldProject.getStatus().equals(projectVo.getStatus()))) {
            if (!CollectionUtils.isEmpty(subProjectEntityList)) {
                for (SubProjectEntity subProjectEntity : subProjectEntityList) {
                    subProjectEntity.setStatus(projectVo.getStatus());
                    subProjectEntity.setUpdateTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                    // 释放受试者
                    if (projectVo.getStatus().equals(2)) {
                        Boolean isLockPrj = projectVo.getProjectType() != null
                                && (projectVo.getProjectType().equals(ProjectStudyTypeEnum.GCP.getCode())
                                || projectVo.getProjectType().equals(ProjectStudyTypeEnum.INTERVENTION_IIT.getCode()));
                        res = res && clearPatient(subProjectEntity.getId(), isLockPrj);
                    }
                }
                subProjectService.updateBatchById(subProjectEntityList);
            }
        }

        //判断项目是否修改了项目管理员
        if ((oldProject.getProjectAdminId() == null && projectVo.getProjectAdminId() != null)
                || !oldProject.getProjectAdminId().equals(projectVo.getProjectAdminId())) {

            //判断修改后的项目管理员是否在项目用户中
            List<SysRoleEntity> roleList = roleService.list(new QueryWrapper<SysRoleEntity>().lambda().eq(SysRoleEntity::getIsProjAdmin, 1));
            Long adminRoleId = roleList.get(0).getId();
            List<UserSubProjectEntity> list = userSubProjectService.getByProjectId(projectVo.getId());
            for (UserSubProjectEntity user : list) {
                if (user == null) {
                    continue;
                }
                // 非项目管理员成员信息
                if (user.getUserId().equals(projectVo.getProjectAdminId()) && !user.getProjRoleId().equals(adminRoleId)) {
                    throw new BusinessException("该用户已在项目中存在其他角色,请先删除此用户");
                }
            }
            // 项目管理员成员信息更新
            List<UserSubProjectEntity> adminList = list.stream().filter(p -> p.getProjRoleId().equals(adminRoleId)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(adminList)) {
                adminList.forEach(p -> p.setUserId(projectVo.getProjectAdminId()));
                res = res && userSubProjectService.updateBatchById(adminList);
            }
        }
        //判断是否对项目中心做了修改
        if ((projectVo.getPrimaryCenter() != null && !projectVo.getPrimaryCenter().equals(oldProject.getPrimaryCenter()))
                ||
                (StringUtils.isNotBlank(projectVo.getBranchCenter()) && !projectVo.getBranchCenter().equals(oldProject.getBranchCenter()))) {

            //如果对主中心做了修改，因为主中心只有一个，则删除主中心的数据，重新添加
            //如果对分中心做了修改，比如是新增、或者删除分中心，则只对修改了的中心数据做修改
            res = res && afterUpdateProjCenter(projectVo, oldProject);
        }
        //先做项目的修改操作

        res = this.updateById(projectVo);

        if (!res) {
            throw new BusinessException("项目修改失败");
        }
        return res;
    }

    /**
     * 释放受试者
     *
     * @param subProjectId
     * @return
     */
    private Boolean clearPatient(Long subProjectId, Boolean isLockPrj) {
        Boolean res = true;
        // 查本项目的推荐患者
        List<String> empiList = doctorRecommendationService.getEmpiBySubProjectId(subProjectId);

        if (CollectionUtils.isEmpty(empiList)) {
            return true;
        }
        // 更新推荐列表
        UpdateWrapper<DoctorRecommendationEntity> updateDoc = new UpdateWrapper<>();
        updateDoc.lambda().set(DoctorRecommendationEntity::getStatus, PatTypeEnum.EXIT_NORMALLY.getCode());
        updateDoc.lambda().in(DoctorRecommendationEntity::getEmpiid, empiList);
        res = doctorRecommendationService.update(updateDoc);
        // 更新入组患者
        List<String> inboundEmpiList = inboundProcessService.getEmpiByProjectId(subProjectId);
        if (!CollectionUtils.isEmpty(inboundEmpiList)) {
            UpdateWrapper<InboundProcessEntity> updateInbound = new UpdateWrapper<>();
            updateInbound.lambda().set(InboundProcessEntity::getStatus, PatTypeEnum.EXIT_NORMALLY.getCode());
            updateInbound.lambda().in(InboundProcessEntity::getEmpiid, inboundEmpiList);
            res = res && inboundProcessService.update(updateInbound);
        }


        if (isLockPrj) {
            // 干预性项目，解锁患者信息表
            UpdateWrapper<PatientEntity> updatePat = new UpdateWrapper<>();
            updatePat.lambda().set(PatientEntity::getStatus, 0);
            updatePat.lambda().in(PatientEntity::getEmpiid, empiList);
            res = res && patientService.update(updatePat);
        }
        return res;
    }

    /**
     * 获取project子项目的setting属性
     */
    private void getSubProjSetting(List<ProjectVo> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        // 查询获取项目配置对象list
        List<ProjectSettingEntity> projectSettingEntityList = projectSettingService.list();
        if (CollectionUtils.isEmpty(projectSettingEntityList)) {
            return;
        }
        // 遍历获取当前Project对象
        for (ProjectVo projectVo : result) {

            if (CollectionUtils.isEmpty(projectVo.getSubProjList())) {
                continue;
            }

            // 获取project子项目列表，进行遍历
            for (SubProjectVo subProjectVo : projectVo.getSubProjList()) {
                //遍历配置信息，给子项目匹配赋值
                for (ProjectSettingEntity settingEntity : projectSettingEntityList) {
                    if (subProjectVo.getId().equals(settingEntity.getSubProjectId())) {
                        subProjectVo.setSubprojectSetting(settingEntity);
                        break;
                    }
                }
            }

        }

    }


    /**
     * 修改项目中心之后
     * 如果对主中心做了修改，因为主中心只有一个，则删除主中心的数据，重新添加
     * 如果对分中心做了修改，比如是新增、或者删除分中心，则只对修改了的中心数据做修改
     *
     * @param projectVo
     * @param oldProject
     * @return
     */
    private boolean afterUpdateProjCenter(ProjectVo projectVo, ProjectVo oldProject) {

        boolean flag = true;

        List<Long> deleteCenterIds = new ArrayList<>();
        List<Long> needAddCenterIds = new ArrayList<>();
        List<ProjDeptEntity> needProjDeptList = new ArrayList<>();

        List<SysDeptEntity> allDeptList = deptService.getAll();
        //如果修改后的项目的主中心和旧项目的主中心不同
        if (!projectVo.getPrimaryCenter().equals(oldProject.getPrimaryCenter())) {
            deleteCenterIds.add(oldProject.getPrimaryCenter());
            // 查主中心top级科室ID
            SysDeptEntity topPrimaryDept = deptService.getTopDeptById(allDeptList, projectVo.getPrimaryCenter());
            needProjDeptList.add(setProjDeptObj(projectVo.getId(), topPrimaryDept.getId(), projectVo.getPrimaryCenter(), IsAdminEnum.ADMIN.getType()));
        }
        if (projectVo.getProjectCategory().equals(ProjectCategoryEnum.MULTIPLE_CENTER.getType())) {

            if (!projectVo.getBranchCenter().equals(oldProject.getBranchCenter())) {
                //如果分中心做了修改
                List<Long> oldCenterIds = new ArrayList<>();
                List<Long> newCenterIds = new ArrayList<>();
                if (StringUtils.isNotBlank(oldProject.getBranchCenter())) {
                    //如果前项目的分中心不为空
                    String[] oldCenterStrs = oldProject.getBranchCenter().split(",");
                    for (String oldCenterStr : oldCenterStrs) {
                        Long branchCenter = null;
                        try {
                            branchCenter = Long.parseLong(oldCenterStr);
                            oldCenterIds.add(branchCenter);
                        } catch (Exception ignored) {
                        }
                    }
                }
                if (StringUtils.isNotBlank(projectVo.getBranchCenter())) {
                    String[] newCenterStrs = projectVo.getBranchCenter().split(",");
                    for (String newCenterStr : newCenterStrs) {
                        Long branchCenter = null;
                        try {
                            branchCenter = Long.parseLong(newCenterStr);
                            newCenterIds.add(branchCenter);
                        } catch (Exception ignored) {
                        }
                    }
                }

                if (CollectionUtils.isEmpty(oldCenterIds)) {
                    needAddCenterIds.addAll(newCenterIds);
                } else if (CollectionUtils.isEmpty(newCenterIds)) {
                    deleteCenterIds.addAll(oldCenterIds);
                } else {
                    //两者都不为空，且做了修改 需要对比两者的区别
                    List<Long> addListThanList = ListUtils.getAddListThanList(newCenterIds, oldCenterIds);
                    List<Long> reduceListThanList = ListUtils.getReduceListThanList(newCenterIds, oldCenterIds);
                    needAddCenterIds.addAll(addListThanList);
                    deleteCenterIds.addAll(reduceListThanList);
                }

            }
        }

        if (!CollectionUtils.isEmpty(needAddCenterIds)) {
            //需要增加的中心
            for (Long needAddCenterId : needAddCenterIds) {
                SysDeptEntity topNeedAddDept = deptService.getTopDeptById(allDeptList, needAddCenterId);
                needProjDeptList.add(setProjDeptObj(projectVo.getId(), topNeedAddDept.getId(), needAddCenterId, IsAdminEnum.NO_ADMIN.getType()));
            }
        }
        if (!CollectionUtils.isEmpty(deleteCenterIds)) {
            //需要删除的中心
            QueryWrapper<ProjDeptEntity> qw = new QueryWrapper<>();
            qw.lambda()
                    .eq(ProjDeptEntity::getProjectId, projectVo.getId())
                    .in(ProjDeptEntity::getDeptId, deleteCenterIds);

            flag = flag && projDeptService.remove(qw);
        }
        if (!CollectionUtils.isEmpty(needProjDeptList)) {
            flag = flag && projDeptService.saveBatch(needProjDeptList);
        }
        return flag;
    }

    @Override
    public void getRoleInfo(List<ProjectVo> result, Long userId) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        if (userId != null && userId.equals(0L)) {
            return;
        }

        // 全部角色信息
        List<SysRoleEntity> allRole = roleService.list();
        // 查询当前用户在所有子项目的
        List<UserSubProjectEntity> allUserSubProject = userSubProjectService.list(
                new QueryWrapper<UserSubProjectEntity>().lambda().eq(UserSubProjectEntity::getUserId, userId)
        );

        if (CollectionUtils.isEmpty(allRole)) {
            allRole = new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(allUserSubProject)) {
            allUserSubProject = new ArrayList<>();
        }

        // 获取当前用户在子项目中的角色信息
        for (ProjectVo project : result) {
            ProjectRoleInfoVo roleInfo = new ProjectRoleInfoVo();
            boolean isProjectAdminId = project.getProjectAdminId() != null && project.getProjectAdminId().equals(userId);

            boolean isProjectCreator = project.getCreateUserId() != null && project.getCreateUserId().equals(userId);
            if (isProjectAdminId || isProjectCreator) {
                // 创建者和项目管理员默认统一处理
                roleInfo.setProjectAdmin(true);

                for (SysRoleEntity role : allRole) {
                    if (role.getIsProjAdmin() != null && role.getIsProjAdmin().equals(1)) {
                        roleInfo.setRoleId(role.getId());
                        roleInfo.setRoleName(role.getRoleName());
                        break;
                    }
                }

                if (isProjectCreator) {
                    roleInfo.setRoleName("项目创建者");
                }

            } else {
                roleInfo.setProjectAdmin(false);
                roleInfo.setRoleName("项目成员");
            }

            project.setRoleInfo(roleInfo);

            // 循环子项目信息
            List<SubProjectVo> curSubs = project.getSubProjList();

            if (CollectionUtils.isEmpty(curSubs)) {
                continue;
            }

            for (SubProjectVo sub : curSubs) {
                // 设置用户在子项目的所属医疗单元
                List<UserSubProjectEntity> curSubGroup = allUserSubProject.stream().filter(p -> p.getSubProjectId().equals(sub.getId()) && p.getUserId().equals(userId)).collect(Collectors.toList());
                Long userSubProjectDeptId = CollectionUtils.isEmpty(curSubGroup) ? 0L : curSubGroup.get(0).getProjectDeptId();
                sub.setUserSubProjectDeptId(userSubProjectDeptId);
                ProjectRoleInfoVo subRoleInfo = new ProjectRoleInfoVo();
                if (isProjectAdminId || isProjectCreator) {
                    subRoleInfo.setProjectAdmin(true);
                    for (SysRoleEntity role : allRole) {
                        if (role.getIsProjAdmin() != null && role.getIsProjAdmin().equals(1)) {
                            subRoleInfo.setRoleId(role.getId());
                            subRoleInfo.setRoleName(role.getRoleName());
                            break;
                        }
                    }

                    if (isProjectCreator) {
                        roleInfo.setRoleName("项目创建者");
                    }
                } else {
                    subRoleInfo.setProjectAdmin(false);
                    //过滤当前用户在当前子项目中的角色
                    for (UserSubProjectEntity entity : allUserSubProject) {
                        if (entity.getSubProjectId().equals(sub.getId())) {
                            //存在设置了用户 但没有设置项目用户角色的情况
                            if (entity.getProjRoleId() != null) {
                                long roleId = entity.getProjRoleId();

                                for (SysRoleEntity role : allRole) {
                                    if (role.getId().equals(roleId)) {
                                        subRoleInfo.setRoleId(roleId);
                                        subRoleInfo.setRoleName(role.getRoleName());
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }

                //反回当前用户所属研究中心
                sub.setRoleInfo(subRoleInfo);
            }

            project.setSubProjList(curSubs);
        }
    }

    /**
     * 修改过期项目的项目状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExpireProjects() {
        try {
            List<ProjectEntity> allProject = this.list(
                    new QueryWrapper<ProjectEntity>()
                            .lambda().eq(ProjectEntity::getStatus, 1)
            );

            List<ProjectEntity> expiredProject = new ArrayList<>();
            for (ProjectEntity project : allProject) {
                Date endDate = project.getEndDate();
                if (endDate == null) {
                    continue;
                }
                //判断是否过期
                Date now = new Date();
                if (now.getTime() >= endDate.getTime()) {
                    expiredProject.add(project);
                }
            }
            if (!CollectionUtils.isEmpty(expiredProject)) {
                for (ProjectEntity entity : expiredProject) {
                    entity.setStatus(0);
                }

                this.updateBatchById(expiredProject);
            }
        } catch (Exception e) {

            throw new BusinessException("更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeProjects(List<Long> idList) {
        // 1.删除所有相关项目
        List<ProjectEntity> projectEntityList = this.listByIds(idList);
        boolean flag = this.removeByIds(idList);

        // 2. 删除所有子项目
        List<SubProjectEntity> subProjectEntityList = subProjectService.list(new QueryWrapper<SubProjectEntity>().lambda()
                .in(SubProjectEntity::getProjectId, idList));

        if (CollectionUtils.isEmpty(subProjectEntityList)) {
            return flag;
        }

        List<Long> subprojectIdList = subProjectEntityList.stream().map(SubProjectEntity::getId).collect(Collectors.toList());

        flag = flag && subProjectService.removeByIds(subprojectIdList);
        // 3. 删除所有项目用户关联数据
        flag = flag && userSubProjectService.remove(new QueryWrapper<UserSubProjectEntity>().lambda()
                .in(UserSubProjectEntity::getSubProjectId, subprojectIdList));
        // 4. 删除所有setting
        flag = flag && projectSettingService.remove(new QueryWrapper<ProjectSettingEntity>().lambda()
                .in(ProjectSettingEntity::getSubProjectId, subprojectIdList));
        // 5. 删除所有roleSetting
        flag = flag && projectRoleSettingService.remove(new QueryWrapper<ProjectRoleSettingEntity>().lambda()
                .in(ProjectRoleSettingEntity::getSubProjectId, subprojectIdList));
        // 6. 删除所有projectDept
        flag = flag && projDeptService.remove(new QueryWrapper<ProjDeptEntity>().lambda()
                .in(ProjDeptEntity::getProjectId, idList));
//        // 7. 删除所有projectDeptPermission
//        flag = flag && projectDeptPermissionService.remove(new QueryWrapper<ProjDeptPermissionEntity>().lambda()
//                .in(ProjDeptPermissionEntity::getProjectId, idList));
        // 8. 删除所有项目中心配置
        flag = flag && projectCenterConfigService.remove(new QueryWrapper<ProjectCenterConfigEntity>().lambda()
                .in(ProjectCenterConfigEntity::getSubprojectId, subprojectIdList));
        // 9. 删除所有纳排入组配置
        List<GroupConfigEntity> configEntityList = groupConfigService.list(new QueryWrapper<GroupConfigEntity>().lambda()
                .in(GroupConfigEntity::getProjectId, idList));

        List<String> keyList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(configEntityList)) {
            List<Long> configIdList = configEntityList.stream().map(GroupConfigEntity::getId).collect(Collectors.toList());
            flag = flag && groupConfigService.removeByIds(configIdList);

            configEntityList.forEach(configEntity -> {
                String key = REDIS_CONFIG + configEntity.getSubProjectId() + UNDER_SCORE + configEntity.getId();
                keyList.add(key);
            });

            // 10. 删除所有柯林检索任务
            flag = flag && kelinConfigService.remove(new QueryWrapper<KelinConfigEntity>().lambda()
                    .in(KelinConfigEntity::getGroupConfigId, configIdList));

            // 11. 删除所有执行记录
            flag = flag && enrollRecordService.remove(new QueryWrapper<EnrollRecordEntity>().lambda()
                    .in(EnrollRecordEntity::getProjectId, idList));

            // 12. 删除所有执行详情
            flag = flag && enrollDetailService.remove(new QueryWrapper<EnrollDetailEntity>().lambda()
                    .in(EnrollDetailEntity::getProjectId, idList));

            // 13. 删除所有受试者推荐记录
            flag = flag && inboundProcessService.remove(new QueryWrapper<InboundProcessEntity>().lambda()
                    .in(InboundProcessEntity::getProjectId, idList));

            // 14. 删除所有医生推荐记录
            flag = flag && doctorRecommendationService.remove(new QueryWrapper<DoctorRecommendationEntity>().lambda()
                    .in(DoctorRecommendationEntity::getProjectId, idList));
        }

        if (!flag) {
            throw new BusinessException("删除项目失败");
        }

        // 15. 删除对应redis中记录
        if (!CollectionUtils.isEmpty(keyList)) {
            enrollDetailRedis.deleteBatch(keyList);
        }

        return flag;


    }

    @Override
    public void excelImport(MultipartFile file, Long userId) {
        try {
            ReadExcelListener readExcelListener = new ReadExcelListener(ExcelProjectVo.class);
            List<ExcelProjectVo> list = ExcelUtils.readExcel(file, ExcelProjectVo.class, readExcelListener);
            System.out.println(JSON.toJSONString(list));
            Date now = new Date();
            List<ExcelProjectVo> errorList = new ArrayList<>();
            for (ExcelProjectVo projectVo : list) {
                try {
                    ProjectVo project = new ProjectVo();
                    project.setDiseaseId(projectVo.getDiseaseId());
                    project.setMainDeptId(projectVo.getPrimaryCenter());
                    project.setPrimaryCenter(projectVo.getPrimaryCenter());
                    project.setProjectAdminId(projectVo.getProjectAdminId());
                    project.setProjectCategory(projectVo.getProjectCategory());
                    project.setProjectName(projectVo.getProjectName().replaceAll("\n", ""));
                    project.setSignDateline(projectVo.getSignDateline());
                    project.setEnrollmentsEstimatedCount(projectVo.getEnrollmentsEstimatedCount());
                    project.setEnableHyt(projectVo.getEnableHyt());
                    project.setEnableSync(projectVo.getEnableSync());
                    project.setEnableAudit(projectVo.getEnableAudit());
                    project.setEnableDownloadImage(projectVo.getEnableDownloadImage());
                    if (!StringUtils.isEmpty(projectVo.getStartDate())) {
                        //验证日期合法性
                        String startDate;
                        try {
                            Date date = DateUtils.deserialize(projectVo.getStartDate());
                            startDate = DateUtils.getStr(date);
                        } catch (Exception ex) {
                            log.error("开始日期格式错误" + project.getProjectName());
                            projectVo.setErrorInfo("开始日期格式错误");
                            errorList.add(projectVo);
                            continue;
                        }
                        project.setStartDate(DateUtils.stringToDate(startDate, DateUtils.DATE_PATTERN));
                    }

                    if (!StringUtils.isEmpty(projectVo.getEndDate())) {
                        //验证日期合法性
                        String endDate;
                        try {
                            Date date = DateUtils.deserialize(projectVo.getEndDate());
                            endDate = DateUtils.getStr(date);
                        } catch (Exception ex) {
                            log.error("截止日期格式错误" + project.getProjectName());
                            projectVo.setErrorInfo("截止日期格式错误");
                            errorList.add(projectVo);
                            continue;
                        }
                        project.setEndDate(DateUtils.stringToDate(endDate, DateUtils.DATE_PATTERN));
                    }

                    project.setStatus(projectVo.getStatus());
                    project.setBranchCenter(projectVo.getBranchCenter());
                    project.setType(1);
                    project.setCreateTime(now);
                    project.setCreateUserId(userId);
                    project.setProjectType(projectVo.getProjectType());
                    project.setApprovalYear(projectVo.getApprovalYear());
                    project.setApprovalNumber(projectVo.getApprovalNumber());
                    project.setContacts(projectVo.getContacts());
                    project.setContactsPhone(projectVo.getContactsPhone());
                    project.setMulticenterType(projectVo.getMulticenterType());
                    project.setMulticenterDescription(projectVo.getMulticenterDescription());
                    project.setIsImport(true);
                    Boolean res = this.add(project);
                    if (!res) {
                        projectVo.setErrorInfo("新增失败");
                    }
                } catch (Exception ex) {
                    projectVo.setErrorInfo(ex.getMessage());
                    errorList.add(projectVo);
                    continue;
                }
            }
            // 写错误日志
            if (!CollectionUtils.isEmpty(errorList)) {
                generateErrorExcel(errorList);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Collection<TotalProjectVo> total(Map<String, Object> params) {
        QueryWrapper<ProjectVo> qw = getQueryWrapper(params);
        Date startDate = MapUtils.getValue(params, "startDate", Date.class);
        Date endDate = MapUtils.getValue(params, "endDate", Date.class);
        Integer isYlt = MapUtils.getValue(params, "isYlt", Integer.class);
        return total(qw, startDate, endDate, isYlt != null && isYlt.equals(1));
    }

    @Override
    public void export(HttpServletResponse response, Map<String, Object> params) {
        String fileName = "projectExport" + DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
        Map<String, List<List<String>>> headMap = new HashMap<>();
        Map<String, List<List<Object>>> dataMap = new HashMap<>();

        List<List<String>> totalHead = new ArrayList<>();
        totalHead.add(Arrays.asList("项目类型名"));
        totalHead.add(Arrays.asList("项目数"));
        totalHead.add(Arrays.asList("新增项目数"));
        totalHead.add(Arrays.asList("入组数"));
        totalHead.add(Arrays.asList("加权入组数"));
        totalHead.add(Arrays.asList("新增入组数"));
        totalHead.add(Arrays.asList("新增加权入组数"));
        headMap.put("合计", totalHead);

        List<List<String>> head = new ArrayList<>();
        head.add(Arrays.asList("项目名称"));
        head.add(Arrays.asList("项目类型"));
        head.add(Arrays.asList("项目状态"));
        head.add(Arrays.asList("科室"));
        head.add(Arrays.asList("PI"));
        head.add(Arrays.asList("开始时间"));
        head.add(Arrays.asList("结束时间"));
        head.add(Arrays.asList("预计入组数"));
        head.add(Arrays.asList("入组数"));
        head.add(Arrays.asList("新增入组数"));
        head.add(Arrays.asList("加权入组数"));
        headMap.put("分项", head);

        QueryWrapper<ProjectVo> qw = getQueryWrapper(params);

//        // 1.获取所有项目数据
//        QueryWrapper<ProjectVo> qw = new QueryWrapper<>();
//        Long deptId = MapUtils.getValue(params, "deptId", Long.class);
//
//        if (deptId != null) {
//            qw.eq("a.main_dept_id", deptId);
//        }
//        // 4.用户id
//        Long doctorId = MapUtils.getValue(params, "doctorId", Long.class);
//
//        if (doctorId != null) {
//            qw.eq("a.project_admin_id", doctorId);
//        }
//
//        // 5.病种id
//        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);
//
//        if (diseaseId != null) {
//            qw.eq("a.disease_id", diseaseId);
//        }

        Date startDate = MapUtils.getValue(params, "startDate", Date.class);
        Date endDate = MapUtils.getValue(params, "endDate", Date.class);
        Integer isYlt = MapUtils.getValue(params, "isYlt", Integer.class);
        Collection<TotalProjectVo> total = total(qw, startDate, endDate, isYlt != null && isYlt.equals(1));

        if (CollectionUtils.isEmpty(total)) {
            return;
        }

        List<List<Object>> TotalDataList = new ArrayList<>();
        total.forEach(entity -> {
            List<Object> list = new ArrayList<>();
            list.add(entity.getName());
            list.add(entity.getProject());
            list.add(entity.getAddProject());
            list.add(entity.getInbound());
            list.add(entity.getWeightInbound());
            list.add(entity.getAddInbound());
            list.add(entity.getAddWeightInbound());
            TotalDataList.add(list);
        });
        dataMap.put("合计", TotalDataList);

        Integer status = MapUtils.getValue(params, "status", Integer.class);
        Integer projectType = MapUtils.getValue(params, "projectType", Integer.class);

        if (status != null) {
            qw.eq("a.status", status);
        }

        if (projectType != null) {
            qw.eq("a.project_type", projectType);
        }

        List<ProjectVo> totalProject = this.baseMapper.allByDept(qw);
        if (!CollectionUtils.isEmpty(totalProject)) {
            inboundProcessService.setProjectInboundCount(totalProject, startDate, endDate);
            List<List<Object>> dataList = new ArrayList<>();
            totalProject.forEach(entity -> {
                List<Object> list = new ArrayList<>();
                list.add(entity.getProjectName());
                list.add(ProjectStudyTypeEnum.getByCode(entity.getProjectType()).getName());
                Integer status1 = entity.getStatus();
                list.add(status1 == 0 ? "立项" : status1 == 1 ? "在研" : status1 == 2 ? "结束" : "入组完成");
                list.add(entity.getDept());
                list.add(entity.getProjectAdminName());
                list.add(entity.getStartDate());
                list.add(entity.getEndDate());
                list.add(entity.getEnrollmentsEstimatedCount());
                list.add(entity.getInboundCount());
                list.add(entity.getAddInboundCount());
                list.add(entity.getTotalWeightInbound());
                dataList.add(list);
            });
            dataMap.put("分项", dataList);

        }
        ExcelUtils.wirteExcel(response, fileName, headMap, dataMap);
    }

    private QueryWrapper<ProjectVo> getQueryWrapper(Map<String, Object> params) {
        // 1.获取所有项目数据
        QueryWrapper<ProjectVo> qw = new QueryWrapper<>();
        Long deptId = MapUtils.getValue(params, "deptId", Long.class);

        if (deptId != null) {
            qw.eq("a.main_dept_id", deptId);
        }
        // 4.用户id
        Long doctorId = MapUtils.getValue(params, "doctorId", Long.class);

        if (doctorId != null) {
            qw.eq("a.project_admin_id", doctorId);
        }

        // 5.病种id
        Long diseaseId = MapUtils.getValue(params, "diseaseId", Long.class);

        if (diseaseId != null) {
            qw.eq("a.disease_id", diseaseId);
        }

        // 6.医联体报表跳转过来
        Integer isYlt = MapUtils.getValue(params, "isYlt", Integer.class);
        if (isYlt != null && isYlt.equals(1)) {
            // 7.医疗机构ID
            Long medId = MapUtils.getValue(params, "medId", Long.class);
            if (medId != null) {
                // 查指定推荐医疗机构的项目
                QueryWrapper<DoctorRecommendationEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(DoctorRecommendationEntity::getTopRecommendDeptId, medId);
                List<Long> projectIds = recommendationDao.getProjectIds(queryWrapper);
                if (CollectionUtils.isEmpty(projectIds)) {
                    return null;
                }
                qw.in("a.id", projectIds);
            } else {
                Integer isAll = MapUtils.getValue(params, "isAll", Integer.class);
                if (isAll != null && isAll == 1) {
                    // 总项目数，查开通医联体权限的项目数
                    List<ProjectEntity> projectEntityList = this.list(new QueryWrapper<ProjectEntity>()
                            .lambda().eq(ProjectEntity::getEnableMedConst, 1).select(ProjectEntity::getId));
                    List<Long> projectIds = projectEntityList.stream().map(p -> p.getId()).collect(Collectors.toList());
                    qw.in("a.id", projectIds);
                } else {
                    Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
                    switch (roleType) {
                        case 1:
                            // 临管部管理者-推荐机构不是华西本院的
                            QueryWrapper<DoctorRecommendationEntity> queryWrapper = new QueryWrapper<>();
                            queryWrapper.lambda().ne(DoctorRecommendationEntity::getTopRecommendDeptId, 15L);
                            List<Long> projectIds = recommendationDao.getProjectIds(queryWrapper);
                            if (CollectionUtils.isEmpty(projectIds)) {
                                return null;
                            }
                            qw.in("a.id", projectIds);
                            break;
                        case 2:
                            // 科室管理者-推荐科室为主管科室的
                            List<Long> mainDeptId = MapUtils.getValue(params, "mainDeptId", ArrayList.class);
                            QueryWrapper<DoctorRecommendationEntity> qwDept = new QueryWrapper<>();
                            qwDept.lambda().in(DoctorRecommendationEntity::getRecommendDeptId, mainDeptId);
                            List<Long> mainDeptProjectIds = recommendationDao.getProjectIds(qwDept);
                            if (CollectionUtils.isEmpty(mainDeptProjectIds)) {
                                return null;
                            }
                            qw.in("a.id", mainDeptProjectIds);
                            break;
                    }
                }
            }
        }
        return qw;
    }

    /**
     * 将错误信息放入到excel中去
     *
     * @param errInfoList
     * @return
     */
    private String generateErrorExcel(List<ExcelProjectVo> errInfoList) throws IOException {
        if (CollectionUtils.isEmpty(errInfoList)) {
            return null;
        }
        try {
            List<List<String>> heads = ExcelUtils.getHeads(ExcelProjectVo.class);
            Field[] fields = ExcelProjectVo.class.getDeclaredFields();
            Map<String, String> fieldMap = new HashMap<>();
            for (Field field : fields) {
                fieldMap.put(field.getName(), field.getAnnotation(ExcelProperty.class).value()[0]);
            }
            List<Map<String, String>> dataList = JSONObject.parseObject(JSON.toJSONString(errInfoList), ArrayList.class);
            List<Map<String, String>> dataListNew = new ArrayList<>();
            for (Map<String, String> data : dataList) {
                Map<String, String> dataNew = new HashMap<>();
                for (String key : data.keySet()) {
                    dataNew.put(fieldMap.get(key), data.get(key));
                }
                dataListNew.add(dataNew);
            }
            String fileName = IdUtils.getEmpi() + ".xls";
            ExcelUtils.createExcel(FileHelper.getImportProjectErrorInfoPath(), fileName, heads, JSONArray.parseArray(JSON.toJSONString(dataListNew)));
            return fileName;
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
            throw new BusinessException("创建excel失败");
        }
    }

    /**
     * 生成 当前项目的项目中心 数据
     *
     * @param projectId       项目id
     * @param primaryCenter   主中心id
     * @param branchCenterStr 分中心id组成的字符串 以“,”隔开
     * @return
     */
    private List<ProjDeptEntity> getProjDeptList(Long projectId, Long primaryCenter, String branchCenterStr) {

        List<ProjDeptEntity> list = new ArrayList<>();
        List<SysDeptEntity> allDeptList = deptService.getAll();
        // 查主中心、分中心top级科室ID
        SysDeptEntity topPrimaryDept = deptService.getTopDeptById(allDeptList, primaryCenter);
        //主中心是必有的，添加主中心
        ProjDeptEntity primaryDept = setProjDeptObj(projectId, topPrimaryDept.getId(), primaryCenter, IsAdminEnum.ADMIN.getType());
        list.add(primaryDept);
        //分解分中心
        if (StringUtils.isNotBlank(branchCenterStr)) {
            String[] branchCenterStrs = branchCenterStr.split(",");
            for (String str : branchCenterStrs) {
                Long branchCenter = null;
                try {
                    branchCenter = Long.parseLong(str);
                    SysDeptEntity topCenterDept = deptService.getTopDeptById(allDeptList, branchCenter);
                    ProjDeptEntity branchDept = setProjDeptObj(projectId, topCenterDept.getId(), branchCenter, IsAdminEnum.NO_ADMIN.getType());
                    list.add(branchDept);
                } catch (Exception ignored) {
                }
            }
        }
        return list;
    }

    /**
     * 拼接项目的中心
     *
     * @param projectDeptCenterList 项目中心list
     * @param projectVo             项目VO对象
     * @return
     */
    private ProjectVo joinProjectCenter(List<ProjDeptEntity> projectDeptCenterList, ProjectVo projectVo) {
        StringBuilder branchCenterStr = new StringBuilder();
        for (ProjDeptEntity projDeptEntity : projectDeptCenterList) {
            if (projectVo.getId().equals(projDeptEntity.getProjectId())) {
                if (projDeptEntity.getIsPrimary().equals(IsAdminEnum.ADMIN.getType())) {
                    projectVo.setPrimaryCenter(projDeptEntity.getDeptId());
                } else {
                    branchCenterStr.append(projDeptEntity.getDeptId().toString()).append(",");
                }
            }
        }
        if (StringUtils.isNotBlank(branchCenterStr.toString())) {
            branchCenterStr = new StringBuilder(branchCenterStr.substring(0, branchCenterStr.length() - 1));
            projectVo.setBranchCenter(branchCenterStr.toString());
        }
        return projectVo;
    }

    /**
     * 获取项目中心的名称
     *
     * @param result
     */
    private void getProjCenter(List<ProjectVo> result) {

        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<SysDeptEntity> allDept = deptService.list();

        for (ProjectVo projectVo : result) {
            for (SysDeptEntity dept : allDept) {
                // 获取科室信息
                if (projectVo.getMainDeptId() != null && projectVo.getMainDeptId().equals(dept.getId())) {
                    projectVo.setMainDeptName(dept.getName());
                }
                if (projectVo.getPrimaryCenter() != null && projectVo.getPrimaryCenter().equals(dept.getId())) {
                    projectVo.setPrimaryCenterName(dept.getName());
                    break;
                }
            }

            String branchCenterStr = projectVo.getBranchCenter();
            if (StringUtils.isNotBlank(branchCenterStr)) {
                List<String> branchCenterName = new ArrayList<>();
                String[] branchCenterStrs = branchCenterStr.split(",");
                List<Long> centerIds = new ArrayList<>();
                for (String str : branchCenterStrs) {
                    Long branchCenter = null;
                    try {
                        branchCenter = Long.parseLong(str);
                        centerIds.add(branchCenter);
                    } catch (Exception ignored) {
                    }
                }
                for (Long centerId : centerIds) {
                    for (SysDeptEntity dept : allDept) {
                        if (centerId.equals(dept.getId())) {
                            branchCenterName.add(dept.getName());
                            break;
                        }
                    }
                }
                projectVo.setBranchCenterNames(branchCenterName);
            }


        }

    }


    /**
     * 拼接projDept 的属性
     *
     * @param projectId       项目id
     * @param deptId          中心id
     * @param isPrimaryCenter 是否是主中心
     * @return
     */
    private ProjDeptEntity setProjDeptObj(Long projectId, Long topDeptId, Long deptId, Integer isPrimaryCenter) {

        ProjDeptEntity projDeptEntity = new ProjDeptEntity();
        projDeptEntity.setProjectId(projectId);
        projDeptEntity.setDeptId(deptId);
        projDeptEntity.setIsPrimary(isPrimaryCenter);
        projDeptEntity.setTopDeptId(topDeptId);
        return projDeptEntity;
    }

    /**
     * 获取附加的其他信息
     *
     * @param result
     * @param userId
     */
    private void getAttachInfo(List<ProjectVo> result, Long userId, String subProjName) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        QueryWrapper<SubProjectVo> qw = new QueryWrapper<>();
        qw.lambda().like(StringUtils.isNotBlank(subProjName), SubProjectEntity::getName, subProjName);
        List<SubProjectVo> subProjectEntities = subProjectService.getByProjectId(null, userId, qw);

        for (ProjectVo project : result) {
            if (!CollectionUtils.isEmpty(subProjectEntities)) {
                for (SubProjectVo entity : subProjectEntities) {
                    if (entity.getProjectId().equals(project.getId())) {
                        List<SubProjectVo> subs = project.getSubProjList();
                        if (subs == null) {
                            subs = new ArrayList<>();
                        }
                        subs.add(entity);
                        project.setSubProjList(subs);
                    }
                }
            }
        }

        // 计算项目总病例数
        for (ProjectVo project : result) {
            List<SubProjectVo> subs = project.getSubProjList();
            int patCount = 0;
            int recommendCount = 0;
            int inboundCount = 0;
            int signCount = 0;
            int exitCount = 0;
            if (!CollectionUtils.isEmpty(subs)) {
                for (SubProjectVo entity : subs) {
                    patCount += entity.getPatCount() == null ? 0 : entity.getPatCount();
                    recommendCount += entity.getRecommendCount() == null ? 0 : entity.getRecommendCount();
                    inboundCount += entity.getInboundCount() == null ? 0 : entity.getInboundCount();
                    signCount += entity.getSignCount() == null ? 0 : entity.getSignCount();
                    exitCount += entity.getExitCount() == null ? 0 : entity.getExitCount();
                }
            }

            project.setPatCount(patCount);
            project.setRecommendCount(recommendCount);
            project.setInboundCount(inboundCount);
            project.setSignCount(signCount);
            project.setExitCount(exitCount);
        }
    }

    private Collection<TotalProjectVo> total(QueryWrapper<ProjectVo> qw, Date startDate, Date endDate, Boolean isYlt) {
        List<ProjectVo> totalProject = this.baseMapper.allByDept(qw);
        if (CollectionUtils.isEmpty(totalProject)) {
            return new ArrayList<>();
        }

        if (startDate != null && endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
            if (isYlt) {
                // 医联体报表时，拼接有新增入组的项目的条件
                QueryWrapper<InboundProcessEntity> qwInbound = new QueryWrapper<>();
                qwInbound.lambda().ge(InboundProcessEntity::getSigningOpTime, startDate)
                        .le(InboundProcessEntity::getSigningOpTime, endDate);
                // 查有新增入组的项目ID
                List<Long> addPatProjectIds = this.baseMapper.getAddPatProjectIdList(qwInbound);
                if (CollectionUtils.isEmpty(addPatProjectIds)) {
                    return null;
                }
                qw.in("a.id", addPatProjectIds);
            } else {
                // 新增项目数按项目创建时间筛选
                qw.ge("a.create_time", startDate)
                        .le("a.create_time", endDate);
            }

        }

        List<ProjectVo> addProject = this.baseMapper.allByDept(qw);
        Map<Integer, List<ProjectVo>> map = totalProject.stream().collect(Collectors.groupingBy(ProjectVo::getProjectType));
        List<Long> projectIdList = totalProject.stream().map(ProjectVo::getId).collect(Collectors.toList());
        Map<String, TotalProjectVo> resultMap = inboundProcessService.groupInboundCountByType(projectIdList, startDate, endDate);

        for (Map.Entry<Integer, List<ProjectVo>> entry : map.entrySet()) {
            ProjectStudyTypeEnum projectType = ProjectStudyTypeEnum.getByCode(entry.getKey());
            String name = projectType.getName();
            // 新增项目
            List<ProjectVo> curTypeAddProject = CollectionUtils.isEmpty(addProject) ? null : addProject.stream().filter(p -> p.getProjectType().equals(entry.getKey())).collect(Collectors.toList());

            if (resultMap.containsKey(name)) {
                resultMap.get(name).setProject(entry.getValue().size());
                resultMap.get(name).setAddProject(CollectionUtils.isEmpty(curTypeAddProject) ? 0 : curTypeAddProject.size());
            } else {
                TotalProjectVo totalProjectVo = new TotalProjectVo();
                totalProjectVo.setName(name);
                totalProjectVo.setProject(entry.getValue().size());
                totalProjectVo.setAddProject(CollectionUtils.isEmpty(curTypeAddProject) ? 0 : curTypeAddProject.size());
                resultMap.put(name, totalProjectVo);
            }
        }

        if (resultMap.size() < 4) {
            if (!resultMap.containsKey(ProjectStudyTypeEnum.GCP.getName())) {
                TotalProjectVo totalProjectVo = new TotalProjectVo();
                totalProjectVo.setName(ProjectStudyTypeEnum.GCP.getName());
                totalProjectVo.setProject(0);
                totalProjectVo.setInbound(0);
                resultMap.put(ProjectStudyTypeEnum.GCP.getName(), totalProjectVo);
            }
            if (!resultMap.containsKey(ProjectStudyTypeEnum.INTERVENTION_IIT.getName())) {
                TotalProjectVo totalProjectVo = new TotalProjectVo();
                totalProjectVo.setName(ProjectStudyTypeEnum.INTERVENTION_IIT.getName());
                totalProjectVo.setProject(0);
                totalProjectVo.setInbound(0);
                resultMap.put(ProjectStudyTypeEnum.INTERVENTION_IIT.getName(), totalProjectVo);
            }
            if (!resultMap.containsKey(ProjectStudyTypeEnum.PROSPECTIVE_OBSERVATION.getName())) {
                TotalProjectVo totalProjectVo = new TotalProjectVo();
                totalProjectVo.setName(ProjectStudyTypeEnum.PROSPECTIVE_OBSERVATION.getName());
                totalProjectVo.setProject(0);
                totalProjectVo.setInbound(0);
                resultMap.put(ProjectStudyTypeEnum.PROSPECTIVE_OBSERVATION.getName(), totalProjectVo);
            }
            if (!resultMap.containsKey(ProjectStudyTypeEnum.SPECIALIZED_DISEASE_QUEUE.getName())) {
                TotalProjectVo totalProjectVo = new TotalProjectVo();
                totalProjectVo.setName(ProjectStudyTypeEnum.SPECIALIZED_DISEASE_QUEUE.getName());
                totalProjectVo.setProject(0);
                totalProjectVo.setInbound(0);
                resultMap.put(ProjectStudyTypeEnum.SPECIALIZED_DISEASE_QUEUE.getName(), totalProjectVo);
            }
        }

        return resultMap.values();
    }

    /**
     * 修改医联体可用性
     */
    @Override
    public boolean updateVisual(Long projectId) {
        ProjectEntity projectEntity = this.getById(projectId);
        if (!ObjectUtils.isEmpty(projectEntity)) {
            if (projectEntity.getEnableMedConst() == null
                    || projectEntity.getEnableMedConst() == 0) {
                projectEntity.setEnableMedConst(1);
            } else {
                projectEntity.setEnableMedConst(0);
            }
            return updateById(projectEntity);
        }
        return true;
    }
}




