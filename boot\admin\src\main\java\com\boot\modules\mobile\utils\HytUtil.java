package com.boot.modules.mobile.utils;

import cn.hutool.core.codec.Base64;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.mobile.enums.AESTypeEnum;
import com.boot.modules.mobile.model.HytParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

@Slf4j
public class HytUtil {

    private final static String CIPHER_MODE_PADDING = "AES/CBC/PKCS5Padding";
    private final static String HYT_TIMESTAMP = "timestamp";
    private final static String HYT_DOCTOR_CODE = "doctorCode";
    private final static String HYT_ORGAN_CODE = "organCode";


    public static HytParam getHytParam(String encryptedStr) {
        HytParam hytParam = new HytParam();
        if (StringUtils.isEmpty(encryptedStr)) {
            return null;
        }
        log.error("华医通digest: {}", encryptedStr);
        try {
            String code = AESTypeEnum.HYT.getCode();
            String digest = HytUtil.decrypt(encryptedStr, getSecretKey(code), getIvStr(code));
            String[] params = digest.split("&amp;").length != 3 ? digest.split("\\&") : digest.split("&amp;");
            if (params.length == 0) {
                return null;
            }
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length != 2) {
                    continue;
                }
                switch (keyValue[0]) {
                    case HYT_TIMESTAMP:
                        hytParam.setTimestamp(Long.parseLong(keyValue[1]));
                        break;
                    case HYT_DOCTOR_CODE:
                        hytParam.setDoctorCode(keyValue[1]);

                        break;
                    case HYT_ORGAN_CODE:
                        hytParam.setOrganCode(keyValue[1]);
                        break;
                }
            }
        } catch (Exception ex) {
            log.error("getHytParam-> ", ex);
        }
        return hytParam;
    }

    public static String getSecretKey(String code) {
        switch (code) {
            case "mnwk":
            case "hyt":
            case "rp":
            default:
                return StringUtils.isEmpty(BootAdminProperties.rpSecretKey) ? "0CoJUM6Qyw8W8jec" : BootAdminProperties.rpSecretKey;
        }
    }

    public static String getIvStr(String code) {
        switch (code) {
            case "mnwk":
            case "hyt":
            case "rp":
            default:
                return StringUtils.isEmpty(BootAdminProperties.rpIvStr) ? "DYgjCetcVrj2W9xf" : BootAdminProperties.rpIvStr;
        }
    }

    public static String encrypt(String content, String secretKey, String ivStr) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        if (StringUtils.isNotBlank(secretKey) && secretKey.length() == 16) {
            try {
                byte[] bytes = secretKey.getBytes(StandardCharsets.UTF_8);
                SecretKeySpec keySpec = new SecretKeySpec(bytes, "AES");
                Cipher cipher = Cipher.getInstance(CIPHER_MODE_PADDING);
                IvParameterSpec ivParameterSpec = new IvParameterSpec(ivStr.getBytes(StandardCharsets.UTF_8));
                cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec);
                byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
                return Base64Utils.encodeToString(encrypted);
            } catch (Exception ex) {
                log.error("encrypt-> ", ex);
            }
        }
        return null;
    }

    public static String decrypt(String encryptedStr, String secretKey, String ivStr) {
        try {
            //创建AES秘钥
            SecretKeySpec skeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "AES");
            // 创建密码器
            Cipher cipher = Cipher.getInstance(CIPHER_MODE_PADDING);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivStr.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, ivParameterSpec);
            // 解密
            byte[] bytes = Base64.decode(encryptedStr);
            byte[] original = cipher.doFinal(bytes);
            return new String(original);
        } catch (Exception ex) {
            log.error("decrypt-> ", ex);
        }
        return null;
    }


}

