package com.boot.commons.enums;

/**
 * 单点登录模式枚举类
 * @Author: wang<PERSON><PERSON><PERSON>
 * @Date: 2021/6/17 19:00
 */
public enum GrantTypeEnum {
    /**
     * 授权码模式
     */
    AUTHORIZATION_CODE("authorization_code", "授权码模式"),
    /**
     * 密码模式
     */
    PASSWORD("pssword", "密码模式");

    private String code;
    private String name;

    private GrantTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
