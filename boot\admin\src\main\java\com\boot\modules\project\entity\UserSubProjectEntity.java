package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc : 子项目用户关联表
 * @create 2021-03-18
 */
@Data
@TableName("rp_user_subproject")
public class UserSubProjectEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 子项目表id
     */
    private Long subProjectId;

    /**
     * 项目类型角色id
     */
    private Long projRoleId;

    /**
     * cx
     * 项目中心关联id 对应proj_project_dept表
     * 修改为：成员入组时，选择的机构ID
     */
    private Long projectDeptId;

    /**
     * 医院中心ID
     */
    private Long topDeptId;
//    /**
//     * 是否是项目管理员 1：是；0：否  默认是否
//     */
//    private Integer isProjectAdmin;

//    /**
//     * 部门id 用户所属机构
//     */
//    private Long deptId;


//    /**
//     * 用户状态
//     */
//    @ApiModelProperty(hidden = true)
//    @TableField(exist = false)
//    private Integer userStatus;
//
//    @ApiModelProperty(value = "用户名称", hidden = true)
//    @TableField(exist = false)
//    private String userName;
//
//    @ApiModelProperty(value = "用户昵称", hidden = true)
//    @TableField(exist = false)
//    private String nickName;
//
//    @ApiModelProperty(value = "角色名称", hidden = true)
//    @TableField(exist = false)
//    private String roleName;
//
//    @ApiModelProperty(value = "域", hidden = true)
//    @TableField(exist = false)
//    private Integer domain;
//
//    @ApiModelProperty(value = "业务id", hidden = true)
//    @TableField(exist = false)
//    private Long businessId;

//    @ApiModelProperty(value = "机构名称", hidden = true)
//    @TableField(exist = false)
//    private String deptName;
//
//    @ApiModelProperty(value = "是否是主中心，1：是主中心；0：否", hidden = true)
//    @TableField(exist = false)
//    private Integer isPrimaryCenter;


}
