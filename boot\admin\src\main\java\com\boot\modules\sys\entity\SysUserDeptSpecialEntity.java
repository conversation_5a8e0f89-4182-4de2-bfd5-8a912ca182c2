package com.boot.modules.sys.entity;//package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc : 特殊用户科室信息 -- 胰腺炎医疗中心门诊医生工作量归入胰腺炎医疗中心
 * @create 2023-09-08
 */
@Data
@TableName("rp_sys_user_dept_special")
public class SysUserDeptSpecialEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id ;

    @NotNull(message = "userID不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long userId;

    @NotNull(message = "机构不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long deptId;

    private String hisDeptCode;

}
