package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("rp_enroll_record")
public class EnrollRecordEntity {
    /**
     * 项目id
     */
    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 子项目ID
     */
    private Long subProjectId;
    /**
     * 纳排入组配置序号
     */
    private Long groupConfigId;
    /**
     * 执行结果(0-进行中；1-成功；2-失败)
     */
    private int status;
    /**
     * 就诊总条数
     */
    private int totalCount;
    /**
     * 成功条数
     */
    private int successCount;
    /**
     * 失败条数
     */
    private int failCount;
    /**
     * 执行策略（0-首次；1-增量）
     */
    private int type;
    /**
     * 任务开始时间
     */
    private String startTime;
    /**
     * 任务结束时间
     */
    private String endTime;
}
