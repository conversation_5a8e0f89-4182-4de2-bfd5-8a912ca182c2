package com.boot.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.project.entity.ProjectRoleSettingEntity;
import com.boot.modules.project.entity.SubProjectEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProjectRoleSettingService extends IService<ProjectRoleSettingEntity> {
    List<ProjectRoleSettingEntity> query(Long subProjectId, Long roleId);

    List<ProjectRoleSettingEntity> queryBySubProjectId(Long subProjectId, Integer projectCategory);

    /**
     * 获取指定用户在指定子项目中的权限
     *
     * @param userId
     * @param subProjectId
     * @return
     */
    ProjectRoleSettingEntity getByUserIdAndSubProjId(Long userId, Long subProjectId);


    boolean saveSetting(List<ProjectRoleSettingEntity> settings, Long subProjectId);

    /**
     * 子项目新增添加默认配置
     * @param subProjectEntity
     * @return
     */
    boolean addConfigInit(SubProjectEntity subProjectEntity);

    /**
     * 子项目新增添加默认配置
     * @param subProjectEntity
     * @return
     */
    boolean updateConfigInit(SubProjectEntity subProjectEntity, List<ProjectRoleSettingEntity> projectRoleSettingEntityList);
    }
