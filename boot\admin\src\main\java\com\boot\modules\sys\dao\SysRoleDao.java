package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.sys.entity.SysRoleEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色管理
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysRoleDao extends BaseMapper<SysRoleEntity> {

    /**
     * 根据条件查询角色信息
     * @param qw
     * @return
     */
    List<SysRoleEntity> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysRoleEntity> qw);
}
