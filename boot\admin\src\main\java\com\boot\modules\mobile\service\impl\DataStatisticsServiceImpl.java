package com.boot.modules.mobile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boot.commons.constants.Const;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.modules.mobile.dto.MedReportDto;
import com.boot.modules.mobile.dto.ProjectReportDto;
import com.boot.modules.mobile.model.HospitalRecommendModel;
import com.boot.modules.mobile.model.OverviewModel;
import com.boot.modules.mobile.model.PercentModel;
import com.boot.modules.mobile.model.RecommendCountModel;
import com.boot.modules.mobile.service.DataStatisticsService;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dao.InboundProcessDao;
import com.boot.modules.patient.dto.IcoReportDto;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.project.dao.ProjectDao;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.sys.dao.SysDeptDao;
import com.boot.modules.sys.entity.SysDeptEntity;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataStatisticsServiceImpl implements DataStatisticsService {
    @Resource
    private DoctorRecommendationDao recommendationDao;

    @Resource
    private InboundProcessDao inboundProcessDao;

    @Resource
    private SysDeptDao deptDao;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectDao projectDao;

    /**
     * 数据总览
     * roleType 1 临管部管理者 2 科室管理者 其他 PI
     * isYlt 是否为医联体用户 1是 0否
     *
     * @param param
     * @return
     */
    @Override
    public OverviewModel overview(Map<String, Object> param) {
        OverviewModel model = new OverviewModel();
        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        Integer isYlt = MapUtils.getValue(param, "isYlt", Integer.class);
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        List<Long> deptId = MapUtils.getValue(param, "deptId", ArrayList.class);
        List<Long> medId = MapUtils.getValue(param, "medId", ArrayList.class);
        Date startDate = MapUtils.getValue(param, "startDate", Date.class);
        Date endDate = MapUtils.getValue(param, "endDate", Date.class);
        // 将截止日期加1
        if (endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
        }
        QueryWrapper<DoctorRecommendationEntity> qwProjectRec = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwRec = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwInb = new QueryWrapper<>();
        switch (roleType) {
            case 1: //临管部管理者
                if (isYlt != null && isYlt == 1) {
                    qwProjectRec.in("b.top_recommend_dept_id", medId);
                    qwRec.in("a.top_recommend_dept_id", medId);
                    qwInb.in("c.top_recommend_dept_id", medId);
                }
                qwInb.isNotNull("a.signing_op_time");
                if (startDate != null && endDate != null) {
                    qwProjectRec.ge("b.recommend_time", startDate)
                            .le("b.recommend_time", endDate);
                    qwRec.ge("a.recommend_time", startDate)
                            .le("a.recommend_time", endDate);
                    qwInb.ge("a.signing_op_time", startDate)
                            .le("a.signing_op_time", endDate);
                }

                model.setProjectCount(recommendationDao.icoProjectCount(qwProjectRec));
                model.setRecommendCount(recommendationDao.icoTotalRecommend(qwRec));
                model.setInboundCount(inboundProcessDao.icoTotalInbound(qwInb));
                break;
            case 2: //科室管理者
                qwProjectRec.in("b.recommend_dept_id", deptId);
                qwRec.in("a.recommend_dept_id", deptId);
                qwInb.in("c.recommend_dept_id", deptId);

                qwInb.isNotNull("a.signing_op_time");
                if (startDate != null && endDate != null) {
                    qwProjectRec.ge("b.recommend_time", startDate)
                            .le("b.recommend_time", endDate);
                    qwRec.ge("a.recommend_time", startDate)
                            .le("a.recommend_time", endDate);
                    qwInb.ge("a.signing_op_time", startDate)
                            .le("a.signing_op_time", endDate);
                }
                model.setProjectCount(recommendationDao.icoProjectCount(qwProjectRec));
                model.setRecommendCount(recommendationDao.icoTotalRecommend(qwRec));
                model.setInboundCount(inboundProcessDao.icoTotalInbound(qwInb));
                break;
            default:
                // PI
                qwProjectRec.eq("a.project_admin_id", userId);
                qwRec.eq("b.project_admin_id", userId);
                qwInb.eq("b.project_admin_id", userId);

                if (startDate != null && endDate != null) {
                    qwProjectRec.ge("b.recommend_time", startDate)
                            .le("b.recommend_time", endDate);
                    qwRec.ge("a.recommend_time", startDate)
                            .le("a.recommend_time", endDate);
                    qwInb.ge("a.signing_op_time", startDate)
                            .le("a.signing_op_time", endDate);
                }
                // 参与项目数
                model.setProjectCount(recommendationDao.icoProjectCount(qwProjectRec));
                model.setRecommendCount(recommendationDao.icoTotalRecommend(qwRec));
                model.setInboundCount(inboundProcessDao.icoTotalInbound(qwInb));
                break;
        }
        return model;
    }

    /**
     * 推荐数占比
     *
     * @param param
     * @return
     */
    @Override
    public List<PercentModel> percent(Map<String, Object> param) {
        // 临管部管理者
        // 所有一级医疗机构，排除掉华西医院
        List<SysDeptEntity> deptEntityList = getOtherTopDept();
        List<PercentModel> models = recommendationDao.groupByTopDeptId(new QueryWrapper<DoctorRecommendationEntity>()
                .eq("a.type", 2));
        Integer totalCount = models.stream().filter(p -> p.getHospitalId() != null).mapToInt(PercentModel::getRecommendCount).sum();
        List<PercentModel> ret = new ArrayList<>();
        for (SysDeptEntity deptEntity : deptEntityList) {
            List<PercentModel> cur = models.stream().filter(p -> p.getHospitalId() != null && p.getHospitalId().equals(deptEntity.getId())).collect(Collectors.toList());
            PercentModel model = new PercentModel();
            if (CollectionUtils.isEmpty(cur)) {
                model.setHospitalId(deptEntity.getId());
                model.setHospitalName(deptEntity.getName());
                model.setRecommendCount(0);
                model.setPercent("0.00%");
                ret.add(model);
            } else {
                model = cur.get(0);
                model.setPercent(model.getRecommendCount() == 0 ? "0.00%" : (double) Math.round(model.getRecommendCount() * 10000 / totalCount) / 100 + "%");
                ret.add(model);
            }
        }
        return ret;
    }

    /**
     * 推荐入组数统计
     *
     * @param param
     * @return
     */
    @Override
    public RecommendCountModel count(Map<String, Object> param) throws ParseException {
        // 临管部管理者
        // 所有一级医疗机构，排除掉华西医院
        List<SysDeptEntity> deptEntityList = getOtherTopDept();

        // 计算最近一年的12个月
        RecommendCountModel model = new RecommendCountModel();
        String now = DateUtils.getNowTimeStr();
        Map<Long, List<Integer>> map = new LinkedHashMap<>();
        List<String> categories = new ArrayList<>();
        for (Integer i = 11; i >= 0; i--) {
            String dateStr = DateUtils.getPastMonth(now, i.toString());
            categories.add(dateStr.substring(0, 7));

            Date date = DateUtils.stringToDate(dateStr, DateUtils.DATE_PATTERN);
            String start = DateUtils.format(DateUtils.getFirstDayDateOfMonth(date)) + " 00:00:00";
            String end = DateUtils.format(DateUtils.getLastDayOfMonth(date)) + " 23:59:59";

            List<PercentModel> percentModels = recommendationDao.groupByTopDeptId(new QueryWrapper<DoctorRecommendationEntity>()
                    .eq("a.type", 2)
                    .ge("a.recommend_time", start)
                    .le("a.recommend_time", end));

            for (SysDeptEntity deptEntity : deptEntityList) {
                Integer count = 0;
                if (!CollectionUtils.isEmpty(percentModels)) {
                    List<PercentModel> cur = percentModels.stream().filter(p -> p.getHospitalId() != null && p.getHospitalId().equals(deptEntity.getId())).collect(Collectors.toList());
                    count = CollectionUtils.isEmpty(cur) ? 0 : cur.get(0).getRecommendCount();
                }

                if (map.containsKey(deptEntity.getId())) {
                    List<Integer> values = map.get(deptEntity.getId());
                    values.add(count);
                    map.put(deptEntity.getId(), values);
                } else {
                    List<Integer> values = new ArrayList<>();
                    values.add(count);
                    map.put(deptEntity.getId(), values);
                }
            }
        }
        List<HospitalRecommendModel> series = new ArrayList<>();
        for (Long deptId : map.keySet()) {
            HospitalRecommendModel recommendModel = new HospitalRecommendModel();
            List<SysDeptEntity> curDept = deptEntityList.stream().filter(p -> p.getId().equals(deptId)).collect(Collectors.toList());
            recommendModel.setName(CollectionUtils.isEmpty(curDept) ? "" : curDept.get(0).getName());
            recommendModel.setData(map.get(deptId));
            series.add(recommendModel);
        }
        model.setCategories(categories);
        model.setSeries(series);
        return model;
    }

    private List<SysDeptEntity> getOtherTopDept() {
        return deptDao.selectList(new QueryWrapper<SysDeptEntity>().lambda().eq(SysDeptEntity::getPid, 0)
                .ne(SysDeptEntity::getCode, "HID0101")
                .select(SysDeptEntity::getId, SysDeptEntity::getName));
    }

    @Override
    public PageUtils queryPage(Map<String, Object> param) {
        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        List<Long> deptId = MapUtils.getValue(param, "deptId", ArrayList.class);
        List<Long> medId = MapUtils.getValue(param, "medId", ArrayList.class);
        Date startDate = MapUtils.getValue(param, "startDate", Date.class);
        Date endDate = MapUtils.getValue(param, "endDate", Date.class);
        // 将截止日期加1
        if (endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
        }
        String orderField = MapUtils.getValue(param, Const.ORDER_FIELD, String.class);

        QueryWrapper<ProjectReportDto> qwRec = new QueryWrapper<>();
        QueryWrapper<ProjectReportDto> qwInb = new QueryWrapper<>();
        if (roleType != null && roleType.equals(1)) {
            // 临管部管理者
            qwRec.in("b.top_recommend_dept_id", medId);
            qwInb.in("b.top_recommend_dept_id", medId);
        } else {
            // 科室管理者
            qwRec.in("b.recommend_dept_id", deptId);
            qwInb.in("b.recommend_dept_id", deptId);
        }
        qwInb.isNotNull("c.signing_op_time");
        // 有开始截止时间
        if (startDate != null && endDate != null) {
            qwRec.ge("b.recommend_time", startDate);
            qwRec.le("b.recommend_time", endDate);
            qwInb.ge("c.signing_op_time", startDate);
            qwInb.le("c.signing_op_time", endDate);
        }

        IPage<ProjectReportDto> pageFilter = new Query<ProjectReportDto>().getPage(param);
        if (StringUtils.isNotBlank(orderField) && orderField.equals("recommendCount")) {
            // 按推荐数倒序
            IPage<ProjectReportDto> pageRec = recommendationDao.groupProject(pageFilter, qwRec);
            List<ProjectReportDto> recommendList = pageRec.getRecords();
            if (CollectionUtils.isEmpty(recommendList)) {
                return null;
            }

            List<Long> projectIds = recommendList.stream().map(p -> p.getProjectId()).collect(Collectors.toList());
            qwInb.in("a.id", projectIds);
            List<ProjectReportDto> inboundList = inboundProcessDao.groupInbProject(qwInb);
            if (!CollectionUtils.isEmpty(inboundList)) {
                // 查项目详情
                List<ProjectReportDto> prjectInfos = projectDao.getProjectInfo(new QueryWrapper<ProjectReportDto>().in("a.id", projectIds));
                for (ProjectReportDto recommend : recommendList) {
                    for (ProjectReportDto project : prjectInfos) {
                        if (recommend.getProjectId().equals(project.getProjectId())) {
                            recommend.setProjectName(project.getProjectName());
                            recommend.setAdminUserName(project.getAdminUserName());
                            recommend.setDeptName(project.getDeptName());
                        }
                    }
                    for (ProjectReportDto inbound : inboundList) {
                        if (recommend.getProjectId().equals(inbound.getProjectId())) {
                            recommend.setInboundCount(inbound.getInboundCount());
                            break;
                        }
                    }
                }
            }
            pageRec.setRecords(recommendList);
            return new PageUtils(pageRec);
        } else {
            // 按入组数倒序
            IPage<ProjectReportDto> pageInb = inboundProcessDao.groupInbProject(pageFilter, qwInb);
            List<ProjectReportDto> inboundList = pageInb.getRecords();
            if (CollectionUtils.isEmpty(inboundList)) {
                return null;
            }
            List<Long> projectIds = inboundList.stream().map(p -> p.getProjectId()).collect(Collectors.toList());
            qwRec.in("a.id", projectIds);
            List<ProjectReportDto> recommendList = recommendationDao.groupProject(qwRec);
            if (!CollectionUtils.isEmpty(recommendList)) {
                // 查项目详情
                List<ProjectReportDto> prjectInfos = projectDao.getProjectInfo(new QueryWrapper<ProjectReportDto>().in("a.id", projectIds));

                for (ProjectReportDto inbound : inboundList) {
                    for (ProjectReportDto project : prjectInfos) {
                        if (inbound.getProjectId().equals(project.getProjectId())) {
                            inbound.setProjectName(project.getProjectName());
                            inbound.setAdminUserName(project.getAdminUserName());
                            inbound.setDeptName(project.getDeptName());
                        }
                    }

                    for (ProjectReportDto recommend : recommendList) {
                        if (inbound.getProjectId().equals(recommend.getProjectId())) {
                            inbound.setRecommendCount(recommend.getRecommendCount());
                            break;
                        }
                    }
                }
            }
            pageInb.setRecords(inboundList);
            return new PageUtils(pageInb);
        }
    }

    @Override
    public List<ProjectReportDto> project(Map<String, Object> param) {
        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        Integer isYlt = MapUtils.getValue(param, "isYlt", Integer.class);
        List<Long> deptId = MapUtils.getValue(param, "deptId", ArrayList.class);
        List<Long> medId = MapUtils.getValue(param, "medId", ArrayList.class);
        Date startDate = MapUtils.getValue(param, "startDate", Date.class);
        Date endDate = MapUtils.getValue(param, "endDate", Date.class);
        // 将截止日期加1
        if (endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
        }
        String orderField = MapUtils.getValue(param, "orderField", String.class);
        Long userId = MapUtils.getValue(param, "userId", Long.class);

        QueryWrapper<ProjectReportDto> qwRec = new QueryWrapper<>();
        QueryWrapper<ProjectReportDto> qwInb = new QueryWrapper<>();

        switch (roleType) {
            case 1:
                // 临管部管理者
                if (isYlt.equals(1)) {
                    qwRec.in(!CollectionUtils.isEmpty(medId), "b.top_recommend_dept_id", medId);
                    qwInb.in(!CollectionUtils.isEmpty(medId), "b.top_recommend_dept_id", medId);
                }
                break;
            case 2:
                // 科室管理者
                qwRec.in(!CollectionUtils.isEmpty(deptId), "b.recommend_dept_id", deptId);
                qwInb.in(!CollectionUtils.isEmpty(deptId), "b.recommend_dept_id", deptId);
                break;
            default:
                // PI
                qwRec.eq("a.project_admin_id", userId);
                qwInb.eq("a.project_admin_id", userId);
                break;
        }

        qwInb.isNotNull("c.signing_op_time");
        // 有开始截止时间
        if (startDate != null && endDate != null) {
            qwRec.ge("b.recommend_time", startDate);
            qwRec.le("b.recommend_time", endDate);
            qwInb.ge("c.signing_op_time", startDate);
            qwInb.le("c.signing_op_time", endDate);
        }

        List<ProjectReportDto> recommendList = recommendationDao.groupProject(qwRec);
        List<ProjectReportDto> inboundList = inboundProcessDao.groupInbProject(qwInb);
        // 以推荐为主
        List<Long> projectRec = CollectionUtils.isEmpty(recommendList) ? new ArrayList<>() : recommendList.stream().map(p -> p.getProjectId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(projectRec)) {
            return null;
        }
        // 查项目详情
        List<ProjectReportDto> prjectInfos = projectDao.getProjectInfo(new QueryWrapper<ProjectReportDto>().in("a.id", projectRec));
        List<ProjectReportDto> result = new ArrayList<>();
        for (Long projectId : projectRec) {
            ProjectReportDto one = new ProjectReportDto();
            one.setProjectId(projectId);
            if (!CollectionUtils.isEmpty(prjectInfos)) {
                // 填充项目信息
                for (ProjectReportDto projectEntity : prjectInfos) {
                    if (projectId.equals(projectEntity.getProjectId())) {
                        BeanUtils.copyProperties(projectEntity, one);
                        break;
                    }
                }
            }

            if (!CollectionUtils.isEmpty(recommendList)) {
                // 填充推荐信息
                for (ProjectReportDto recommend : recommendList) {
                    if (projectId.equals(recommend.getProjectId())) {
                        one.setRecommendCount(recommend.getRecommendCount());
                        break;
                    }
                }
            }

            if (!CollectionUtils.isEmpty(inboundList)) {
                // 填充入组信息
                for (ProjectReportDto inbound : inboundList) {
                    if (projectId.equals(inbound.getProjectId())) {
                        one.setInboundCount(inbound.getInboundCount());
                        break;
                    }
                }
            }
            result.add(one);
        }
        // 排序
        if (orderField.equals("recommendCount")) {
            result.sort(Collections.reverseOrder(Comparator.comparing(ProjectReportDto::getRecommendCount)));
        } else {
            result.sort(Collections.reverseOrder(Comparator.comparing(ProjectReportDto::getInboundCount)));
        }
        return result;
    }

    /**
     * roleType 1临管部管理者；2科室管理者；
     * tab 1项目数；2推荐数；3入组数
     *
     * @param param
     * @return
     */
    @Override
    public List<MedReportDto> sort(Map<String, Object> param) {
        List<MedReportDto> result = new ArrayList<>();
        Integer tab = MapUtils.getValue(param, "tab", Integer.class);
        switch (tab) {
            case 1:
                result = sortProject(param);
                break;
            case 2:
                result = sortRecommend(param);
                break;
            case 3:
                result = sortInbound(param);
                break;
            default:
                result = sortProject(param);
                break;
        }
        return result;
    }

    private List<MedReportDto> sortProject(Map<String, Object> param) {
        Date startDate = MapUtils.getValue(param, "startDate", Date.class);
        Date endDate = MapUtils.getValue(param, "endDate", Date.class);
        // 将截止日期加1
        if (endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
        }
        List<Long> deptId = MapUtils.getValue(param, "deptId", ArrayList.class);
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        QueryWrapper<IcoReportDto> qw = new QueryWrapper<>();

        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        switch (roleType) {
            case 1:
                // 临管部
                break;
            case 2:
                // 科室管理者
                qw.in("b.recommend_dept_id", deptId);
                break;
            default:
                qw.eq("a.project_admin_id", userId);
                break;
        }

        if (startDate != null && endDate != null) {
            qw.ge("b.recommend_time", startDate);
            qw.le("b.recommend_time", endDate);
        }

        List<IcoReportDto> icoReportDtoList = recommendationDao.groupIcoProject(qw);
        if (CollectionUtils.isEmpty(icoReportDtoList)) {
            return new ArrayList<>();
        }
        List<MedReportDto> reportDtos = new ArrayList<>();
        for (IcoReportDto dto : icoReportDtoList) {
            MedReportDto medReportDto = new MedReportDto();
            medReportDto.setMedId(dto.getMedId());
            medReportDto.setMedName(dto.getMedName());
            medReportDto.setCount(dto.getTotalProject());
            reportDtos.add(medReportDto);
        }

        return reportDtos;
    }


    private List<MedReportDto> sortRecommend(Map<String, Object> param) {
        Date startDate = MapUtils.getValue(param, "startDate", Date.class);
        Date endDate = MapUtils.getValue(param, "endDate", Date.class);
        // 将截止日期加1
        if (endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
        }
        List<Long> deptId = MapUtils.getValue(param, "deptId", ArrayList.class);
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        QueryWrapper<IcoReportDto> qw = new QueryWrapper<>();

        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        switch (roleType) {
            case 1:
                // 临管部
                break;
            case 2:
                // 科室管理者
                qw.in("b.recommend_dept_id", deptId);
                break;
            default:
                qw.eq("a.project_admin_id", userId);
                break;
        }

        if (startDate != null && endDate != null) {
            qw.ge("b.recommend_time", startDate);
            qw.le("b.recommend_time", endDate);
        }
        List<IcoReportDto> icoReportDtoList = recommendationDao.groupIcoRecommend(qw);
        if (CollectionUtils.isEmpty(icoReportDtoList)) {
            return new ArrayList<>();
        }

        List<MedReportDto> result = new ArrayList<>();
        List<Long> medIds = icoReportDtoList.stream().map(p -> p.getMedId()).collect(Collectors.toList());
        List<SysDeptEntity> deptEntityList = deptDao.selectList(new QueryWrapper<SysDeptEntity>()
                .lambda().in(SysDeptEntity::getId, medIds)
                .select(SysDeptEntity::getId, SysDeptEntity::getName));
        for (IcoReportDto dto : icoReportDtoList) {
            MedReportDto one = new MedReportDto();
            one.setMedId(dto.getMedId());
            one.setCount(dto.getTotalRecommend());
            for (SysDeptEntity dept : deptEntityList) {
                if (dto.getMedId().equals(dept.getId())) {
                    one.setMedName(dept.getName());
                    break;
                }
            }
            result.add(one);
        }
        return result;
    }


    private List<MedReportDto> sortInbound(Map<String, Object> param) {
        Date startDate = MapUtils.getValue(param, "startDate", Date.class);
        Date endDate = MapUtils.getValue(param, "endDate", Date.class);
        // 将截止日期加1
        if (endDate != null) {
            endDate = DateUtils.addDateDays(endDate, 1);
        }
        List<Long> deptId = MapUtils.getValue(param, "deptId", ArrayList.class);
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        QueryWrapper<IcoReportDto> qw = new QueryWrapper<>();

        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        switch (roleType) {
            case 1:
                // 临管部
                break;
            case 2:
                // 科室管理者
                qw.in("b.recommend_dept_id", deptId);
                break;
            default:
                qw.eq("a.project_admin_id", userId);
                break;
        }
        qw.isNotNull("c.signing_op_time");
        if (startDate != null && endDate != null) {
            qw.ge("c.signing_op_time", startDate);
            qw.le("c.signing_op_time", endDate);
        }
        List<IcoReportDto> icoReportDtoList = inboundProcessDao.groupIcoInbound(qw);
        if (CollectionUtils.isEmpty(icoReportDtoList)) {
            return new ArrayList<>();
        }

        List<MedReportDto> result = new ArrayList<>();
        for (IcoReportDto dto : icoReportDtoList) {
            MedReportDto one = new MedReportDto();
            one.setMedId(dto.getMedId());
            one.setMedName(dto.getMedName());
            one.setCount(dto.getTotalInbound());
            result.add(one);
        }
        return result;
    }
}
