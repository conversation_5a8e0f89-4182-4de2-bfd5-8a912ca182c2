package com.boot.modules.patient.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.DateUtils;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dao.RecommendCountDao;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.RecommendCountEntity;
import com.boot.modules.patient.service.RecommendCountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class RecommendCountServiceImpl extends ServiceImpl<RecommendCountDao, RecommendCountEntity> implements RecommendCountService {
    @Resource
    private DoctorRecommendationDao doctorRecommendationDao;

    @Override
    public void updateNewDate() {
        // 删除今天的记录
        this.remove(new QueryWrapper<RecommendCountEntity>().lambda().eq(RecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr()));

        List<RecommendCountEntity> list = new ArrayList<>();
        // 所有就诊类型累计
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        Integer allRecommendCount = this.doctorRecommendationDao.totalRecommend(qw);
        RecommendCountEntity allType = new RecommendCountEntity();
        allType.setAdmType("ALL");
        allType.setRecommendCount(allRecommendCount);
        allType.setCreateTime(DateUtils.getNowTimeStr());
        list.add(allType);

        // 门诊累计
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        qwO.eq("a.adm_type", "O");
        Integer allORecommendCount = this.doctorRecommendationDao.totalRecommend(qwO);
        RecommendCountEntity oType = new RecommendCountEntity();
        oType.setAdmType("O");
        oType.setRecommendCount(allORecommendCount);
        oType.setCreateTime(DateUtils.getNowTimeStr());
        list.add(oType);

        // 住院累计
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        qwI.eq("a.adm_type", "I");
        Integer allIRecommendCount = this.doctorRecommendationDao.totalRecommend(qwI);
        RecommendCountEntity iType = new RecommendCountEntity();
        iType.setAdmType("I");
        iType.setRecommendCount(allIRecommendCount);
        iType.setCreateTime(DateUtils.getNowTimeStr());
        list.add(iType);

        // 急诊累计
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        qwE.eq("a.adm_type", "E");
        Integer allERecommendCount = this.doctorRecommendationDao.totalRecommend(qwE);
        RecommendCountEntity eType = new RecommendCountEntity();
        eType.setAdmType("E");
        eType.setRecommendCount(allERecommendCount);
        eType.setCreateTime(DateUtils.getNowTimeStr());
        list.add(eType);

        this.saveBatch(list);
    }
}
