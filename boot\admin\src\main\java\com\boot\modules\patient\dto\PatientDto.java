package com.boot.modules.patient.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PatientDto implements Serializable {
    @ExcelProperty(index = 0, value = "登记号")
    private String regno;

    @ExcelProperty(index = 1, value = "姓名")
    private String name;

    @ExcelProperty(index = 2, value = "身份证号")
    private String idCard;

    @ExcelProperty(index = 3, value = "入组时间")
    private String intoDate;

    @ExcelProperty(index = 4, value = "签约时间")
    private String signDate;

    @ExcelProperty(index = 5, value = "医疗机构名称")
    private String deptName;

    @ExcelProperty(index = 6, value = "导入失败信息")
    private String errorInfo;
}
