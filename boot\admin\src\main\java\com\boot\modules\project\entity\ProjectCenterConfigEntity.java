package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc : 项目中心配置
 * @create 2021-09-08
 */
@Data
@TableName("rp_project_center_config")
public class ProjectCenterConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private String name;

    private String code;

    /**
     * 是否展示，0：否，不展示  1：是、展示  默认为 1
     */
    private Integer isShow;

    /**
     * 中心id
     */
    private Long centerId;

    /**
     * 字段排序
     */
    private Integer sort;

    private Long subprojectId;


}
