package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目表实体类
 */
@Data
@TableName("rp_project")
public class ProjectEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @TableId
    private Long id;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不许为空", groups = {AddGroup.class, UpdateGroup.class})
    private String projectName;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 项目开始时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 项目结束时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 项目创建时间
     */
    private Date createTime;

    /**
     * 创建者id
     */
    private Long createUserId;

    /**
     * 项目状态 0:立项1：在研，2.结束, 3:入组完成锁定受试者不释放
     */
    private Integer status;

    /**
     * 项目类型 1：单病种；2：多病钟
     */
    private Integer type;

    /**
     * 项目类型 1：IIT；2：GCP
     */
    private Integer projectType;

    /**
     * 项目类型 1：单中心 ；2：多中心
     */
    private Integer projectCategory;

    /**
     * 子项目数量
     */
    private Integer subProjectCount;

    /**
     * 项目管理员ID
     */
    private Long projectAdminId;

    /**
     * 数据导出审核权限     0：否 1：是
     */
    private Integer dataExportAudit;

    /**
     * 启用入组员 0：否 1：是
     */
    private Integer enableGroupMember;

    /**
     * 启用审核 0：否 1：是
     */
    private Integer enableAudit;

    /**
     * 数据双审核0：否 1：是
     */
    private Integer dataDoubleExport;

    /**
     * 启用图像下载 0：否 1：是
     */
    private Integer enableDownloadImage;

    /**
     * 所属科室ID
     */
    private Long mainDeptId;

    /**
     * 启用华医通0：否 1：是
     */
    private Integer enableHyt;

    /**
     * 启用edc同步0:否，1：是
     */
    private Integer enableSync;
    /**
     * 知情同意签署时限
     */
    private Integer signDateline;
    /**
     * 所属病种id
     */
    private Integer diseaseId;

    /**
     * 预计入组数
     */
    private Integer enrollmentsEstimatedCount;

    /**
     * 立项年度
     */
    private String approvalYear;

    /**
     * 伦理批件号
     */
    private String approvalNumber;

    /**
     * 项目联系人
     */
    private String contacts;

    /**
     * 项目联系人电话
     */
    private String contactsPhone;

    /**
     * 多中心角色
     */
    private Integer multicenterType;

    /**
     * 多中心描述
     */
    private String multicenterDescription;

    /**
     * edc 子项目id
     */
    private Long edcProjectId;

    /**
     * 启用医联体 0否；1是
     */
    private Integer enableMedConst;

    /**
     * 所属机构ID
     */
    private Long mainMedId;
}
