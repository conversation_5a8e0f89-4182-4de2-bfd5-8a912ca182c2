package com.boot.commons.enums;

/**
 * 密码复杂度
 * <AUTHOR>
 * @create 2020-12-03 11:14
 */
public enum PasswordLevelEnum {

    WEAK(1, "弱"),

    MEDIUM(2, "中"),

    STRONG(3, "强"),

    UNQUALIFIED(4, "不符合要求");

    private Integer type;

    private String desc;

    PasswordLevelEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }}


