package com.boot.modules.openApi.utils;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class PooledHttpApiClient {

    private final static Logger log = LoggerFactory.getLogger(PooledHttpApiClient.class);

    private final CloseableHttpClient httpClient;

    private final int retryInterval;

    /**
     * 设置最大重试次数
     */
    private final int maxRetries;

    public PooledHttpApiClient() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(50); // 设置最大连接数
        connectionManager.setDefaultMaxPerRoute(10); // 设置每个路由的最大连接数

        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(5000)
                .setConnectTimeout(5000)
                .build();
        // 在重试之间添加一些延迟一定时间后执行
        this.retryInterval = 1000;
        this.maxRetries = 3;
        this.httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    public String doGet(String url) throws IOException {
        return doRequest(new HttpGet(url));
    }

    public String doPost(String url, String body) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(body, ContentType.APPLICATION_JSON));

        return doRequest(httpPost);
    }

    private String doRequest(HttpRequestBase request) {

        for (int retryCount = 0; retryCount <= maxRetries; retryCount++) {
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 200) {
                    HttpEntity entity = response.getEntity();
                    return EntityUtils.toString(entity);
                } else {
                    // 失败处理
                    log.error("接口请求失败，请求地址：" + request.getURI() + "，请求方法：" + request.getMethod() + "，错误码：" + response.getStatusLine().getStatusCode());
                    return response.getStatusLine().getReasonPhrase();
                }
            } catch (IOException e) {
                if (retryCount < maxRetries) {
                    // 在重试之间添加一些延迟，比如 Thread.sleep(1000);
                    if (retryInterval > 0) {
                        try {
                            Thread.sleep(retryInterval); // 添加等待时间
                        } catch (InterruptedException ex) {
                            Thread.currentThread().interrupt();
                        }
                    }

                    continue;
                }
                return e.getMessage(); // 如果重试次数用尽，抛出异常
            }
        }
        return "timeOut";
    }
}
