<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.modules.project.dao.ProjectRoleSettingDao">

    <select id="getByUserIdAndSubProjId" resultType="com.boot.modules.project.entity.ProjectRoleSettingEntity">

        SELECT
            a.*
        FROM
            rp_projrole_setting a
                RIGHT JOIN rp_user_subproject b ON a.role_id = b.proj_role_id
                AND a.sub_project_id = b.sub_project_id
        WHERE
            b.sub_project_id = #{subProjectId}
          AND b.user_id = #{userId}
    </select>

    <select id="getByAdminUserIdAndSubProjId" resultType="com.boot.modules.project.entity.ProjectRoleSettingEntity">
        SELECT
            a.*
        FROM
            rp_projrole_setting a
                RIGHT JOIN rp_sub_project b on b.id=a.sub_project_id
                RIGHT JOIN rp_project c on c.id=b.project_id
                JOIN rp_sys_role d on a.role_id=d.id
        WHERE
            a.sub_project_id = #{subProjectId}
          AND c.project_admin_id = #{userId}
          AND d.is_proj_admin=1
    </select>

</mapper>