package com.boot.modules.project.vo;

import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc : 项目用户根据项目中心返回的数据结构
 * @create 2021-05-20
 */
@Data
public class UserSubProjectCenterVo {

    /**
     * 项目机构
     */
    private Long projDeptId;

    private Long deptId;

    private Integer isPrimaryCenter;

    private String deptName;

    List<UserSubProjectVo> userSubProjectVoList;

}
