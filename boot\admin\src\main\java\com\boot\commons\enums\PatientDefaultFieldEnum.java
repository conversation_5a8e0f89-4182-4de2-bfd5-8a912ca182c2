package com.boot.commons.enums;

import com.boot.commons.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc : 病人信息的基础字段枚举
 * @create 2021-09-10
 */
public enum PatientDefaultFieldEnum {

    REGNO("regno", "登记号", 1, 0, 1),

    SYS_UNIQUE_ID("sysUniqueId", "系统唯一字段", 2, 0, 0),

    PROJ_UNIQUE_ID("projUniqueId", "项目唯一字段", 3, 0, 0),

    FILTER_NO("filterNo", "筛选号", 4, 1, 0),

    NAME("name", "姓名", 5, 1, 1),

    INITIALS("initials", "姓名缩写", 6, 0, 1),

    GENDER("gender", "性别", 7, 1, 1),

    ID_CARD("idCard", "身份证", 8, 0, 1),

    RECORD_ID("recordId", "病案号", 9, 0, 1),

    BIRTHDAY("birthday", "出生日期", 10, 1, 1),

    INTO_DATE("intoDate", "入组日期", 11, 1, 1),

    TEL("tel", "手机号", 12, 0, 0),

    UPDATE_TIME("updateTime", "更新时间", 13, 1, 1);

    private String key;

    private String name;

    private Integer sort;

    private Integer isBlank;

    private Integer isShow;

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getIsBlank() {
        return isBlank;
    }

    public void setIsBlank(Integer isBlank) {
        this.isBlank = isBlank;
    }

    PatientDefaultFieldEnum(String key, String name, Integer sort, Integer isBlank, Integer isShow) {
        this.key = key;
        this.name = name;
        this.sort = sort;
        this.isBlank = isBlank;
        this.isShow = isShow;
    }

    public static PatientDefaultFieldEnum getByKey(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (PatientDefaultFieldEnum defaultFieldEnum : PatientDefaultFieldEnum.values()) {
                if (defaultFieldEnum.getKey().equals(key)) {
                    return defaultFieldEnum;
                }
            }
        }
        return null;
    }

    public static Map<String, Object> PatientDefaultFieldMap = new HashMap<String, Object>();
    public static Map<String, PatientDefaultFieldEnum> PatientDefaultFieldEnumMap = new HashMap<String, PatientDefaultFieldEnum>();

    static {
        PatientDefaultFieldEnum[] types = PatientDefaultFieldEnum.values();
        for (PatientDefaultFieldEnum type : types) {
            Map<String, Object> map = new HashMap<>();
            map.put("key", type.key);
            map.put("name", type.name);
            map.put("sort", type.sort);
            map.put("isBlank", type.isBlank);
            PatientDefaultFieldMap.put(String.valueOf(type.key), map);
            PatientDefaultFieldEnumMap.put(String.valueOf(type.key), type);
        }
    }

}
