<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysDictItemDao">
	<cache-ref namespace="com.boot.modules.sys.dao.SysDictItemDao"/>

    <select id="getByQuery" resultType="com.boot.modules.sys.vo.SysDicItemVo">
        select a.*,b.cate_name AS cateName,b.cate_code AS cateCode
        from rp_sys_dict_item a
        left join rp_sys_dict_cate b on a.dict_cate_id =  b.id
            ${ew.customSqlSegment}
    </select>

</mapper>