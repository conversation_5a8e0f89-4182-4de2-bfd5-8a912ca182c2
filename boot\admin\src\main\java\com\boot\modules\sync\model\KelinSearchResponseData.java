package com.boot.modules.sync.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/2 15:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KelinSearchResponseData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回的数据列表总数
     */
    private Integer count;

    /**
     * 返回的数据列表
     */
    private List<Map<String, Object>> list;
}
