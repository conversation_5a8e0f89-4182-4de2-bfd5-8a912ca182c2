package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.ClientInfoUtil;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.service.SysRoleDeptService;
import com.boot.modules.sys.service.SysRoleMenuService;
import com.boot.modules.sys.service.SysRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 角色管理
 *
 * <AUTHOR>
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/sys/role")
public class SysRoleController extends AbstractController {
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysRoleMenuService sysRoleMenuService;
    @Resource
    private SysRoleDeptService sysRoleDeptService;

    private boolean distinguishInAndOutNet = BootAdminProperties.distinguishInAndOutNet;
    /**
     * 角色列表
     */
    @GetMapping("/list")
    @RequiresPermissions("sys:role:list")
    @ApiOperation("获取角色列表")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysRoleService.queryPage(params);

        return R.success(page);
    }

    /**
     * 角色列表
     */
    @GetMapping("/select")
    @RequiresPermissions("sys:role:select")
    @ApiOperation("角色列表")
    public Result select(@RequestParam(value = "type", required = false, defaultValue = "0") Integer type) {
        List<SysRoleEntity> list;
        if (type > 0) {
            list = sysRoleService.list(
                    new QueryWrapper<SysRoleEntity>().lambda().eq(SysRoleEntity::getRoleLevel, type)
            );
        } else {
            list = sysRoleService.list();
        }

        return R.success(list);
    }

    /**
     * 角色信息
     */
    @GetMapping("/{roleId}")
    @RequiresPermissions("sys:role:info")
    @ApiOperation("角色信息")
    public Result info(@PathVariable("roleId") Long roleId) {
        SysRoleEntity role = sysRoleService.getById(roleId);

        //查询角色对应的菜单
        if(distinguishInAndOutNet){
            // chenwei 2022/10/24 查询添加字段type(功能改造)
            Long type = ClientInfoUtil.netType();
            List<Long> menuIdList = sysRoleMenuService.queryMenuIdList(roleId, type);
            role.setMenuIdList(menuIdList);
        }
        else{
            List<Long> menuIdList = sysRoleMenuService.queryMenuIdList(roleId);
            role.setMenuIdList(menuIdList);
        }

        //查询角色对应的部门
        List<Long> deptIdList = sysRoleDeptService.queryDeptIdList(new Long[]{roleId});
        role.setDeptIdList(deptIdList);

        return R.success(role);
    }

    @GetMapping("/all/{scope}")
    @RequiresPermissions("sys:role:select")
    @ApiOperation("指定范围的角色列表")
    public Result scopeRole(@PathVariable("scope") Integer scope) {
        List<SysRoleEntity> list = sysRoleService.getByScope(scope);
        return R.success(list);
    }


    /**
     * 保存角色
     */
    @SysLog("保存角色")
    @PostMapping()
    @RequiresPermissions("sys:role:save")
    @ApiOperation("保存角色")
    public Result save(@RequestBody SysRoleEntity role) {
        // 保存角色和机构，同时关联对个角色

        ValidatorUtils.validateEntity(role, AddGroup.class);

        sysRoleService.saveRole(role);

        return R.success();
    }

    /**
     * 修改角色
     */
    @SysLog("修改角色")
    @PutMapping()
    @RequiresPermissions("sys:role:update")
    @ApiOperation("修改角色")
    public Result update(@RequestBody SysRoleEntity role) {
        ValidatorUtils.validateEntity(role, UpdateGroup.class);

        sysRoleService.update(role);

        return R.success();
    }

    /**
     * 删除角色
     */
    @SysLog("删除角色")
    @DeleteMapping()
    @RequiresPermissions("sys:role:delete")
    @ApiOperation("删除角色")
    public Result delete(@RequestBody Long[] roleIds) {
        sysRoleService.deleteBatch(roleIds);

        return R.success();
    }
}
