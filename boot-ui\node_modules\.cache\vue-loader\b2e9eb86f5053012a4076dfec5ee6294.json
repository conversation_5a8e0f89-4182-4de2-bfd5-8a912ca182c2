{"remainingRequest": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue", "dependencies": [{"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue", "mtime": 1755140556641}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./step-two.vue?vue&type=template&id=47bde6f4&scoped=true&\"\nimport script from \"./step-two.vue?vue&type=script&lang=js&\"\nexport * from \"./step-two.vue?vue&type=script&lang=js&\"\nimport style0 from \"./step-two.vue?vue&type=style&index=0&id=47bde6f4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"47bde6f4\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\1_Work\\\\CODE\\\\2025-donghua\\\\research_participant\\\\boot-ui\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('47bde6f4')) {\n      api.createRecord('47bde6f4', component.options)\n    } else {\n      api.reload('47bde6f4', component.options)\n    }\n    module.hot.accept(\"./step-two.vue?vue&type=template&id=47bde6f4&scoped=true&\", function () {\n      api.rerender('47bde6f4', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/modules/components/recommend-list/step-two.vue\"\nexport default component.exports"]}