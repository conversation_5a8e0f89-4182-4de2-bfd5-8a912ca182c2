<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.mobile.dao.PushHytDao">

    <select id="getPage"  resultType="com.boot.modules.mobile.dto.PushHytDto">
        SELECT a.*,b.name as patName,b.gender,b.nation
        FROM rp_push_hyt a
        LEFT JOIN  rp_patient b on b.empiid = a.empi
        ${ew.customSqlSegment}
    </select>


    <select id="getPatientById"  resultType="com.boot.modules.mobile.dto.PushHytDto">
        SELECT a.*,b.name as patName,b.gender,b.nation,c.project_name
        FROM rp_push_hyt a
        LEFT JOIN  rp_patient b on b.empiid = a.empi
        LEFT JOIN rp_project c on a.project_id = c.id
        WHERE a.id = #{id}
    </select>
</mapper>