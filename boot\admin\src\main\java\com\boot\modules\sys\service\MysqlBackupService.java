package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.BackupTaskEntity;

import java.util.Map;

/**
 * MySql命令行备份恢复服务
 * <AUTHOR>
 */
public interface MysqlBackupService extends IService<BackupTaskEntity> {

    /**
     * 备份数据库
     * @param backupFolderPath 备份的路径
     * @param fileName 备份的文件名
     * @param database 需要备份的数据库的名称
     * @param mysqlPath 需要备份的数据库的mysql安装路径
     * @throws Exception
     */
    void backup(String backupFolderPath, String fileName, String database, String mysqlPath);

    /**
     * 恢复数据库
     * @param restoreFilePath 数据库备份的脚本路径
     * @return
     */
    boolean restore(String restoreFilePath);

    /**
     * 分页查询备份任务记录
     * @param params
     * @return
     */
    PageUtils searchBackupTaskRecords(Map<String, Object> params);

    /**
     * 根据id删除备份记录
     * @param id backup_task主键
     * @return
     */
    boolean deleteBackupRecordById(Long id);

    /**
     * 根据yml配置的留存天数
     * 自动删除指定日期之外的数据
     */
    void autoDeleteBackupFile();

}
