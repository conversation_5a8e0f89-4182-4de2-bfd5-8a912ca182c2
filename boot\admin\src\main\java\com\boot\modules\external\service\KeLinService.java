package com.boot.modules.external.service;

import com.boot.modules.external.model.RdrResponseModel;
import com.boot.modules.sync.model.KelinSearchResponse;

import java.util.List;

public interface KeLinService {

    /**
     * his获取指定患者所有就诊信息
     * @param empi
     * @return
     */
    RdrResponseModel getAdmInfo(String empi);

    RdrResponseModel getPatientInfo(String regNo, String deptCode);

    KelinSearchResponse getPatientByRegNoAndMed(String[] regnos, String medorg);

    /**
     * 获取指定admIds的就诊信息
     *
     * @param admIds
     * @return
     */
    KelinSearchResponse getAdmByVisitId(String[] admIds);
    /**
     * 批量获取指定empiid的病例基本信息
     *
     * @param empiids
     * @return
     */
    KelinSearchResponse getPatientByEmpiids(String[] empiids, List<Object> medCodeList);

    /**
     * 获取今日就诊的患者唯一ID
     * @return
     */
    RdrResponseModel getTodayEmpiList(List<Object> medCodeList);

    /**
     * 通过身份证号和机构信息查询基本信息
     * @param idCards
     * @param medCodes
     * @return
     */
    KelinSearchResponse getPatientInfoByIdCard(String[] idCards, String[] medCodes);
}
