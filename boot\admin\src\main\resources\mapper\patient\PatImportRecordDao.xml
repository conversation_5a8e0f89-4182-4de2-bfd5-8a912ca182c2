<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.patient.dao.PatImportRecordDao">

    <select id="queryPage" resultType="com.boot.modules.patient.entity.PatImportRecordEntity">
        SELECT a.*,
               b.nickname as nickName,
               b.username as userName
        from rp_pat_import_record a
                 left join rp_sys_user b on a.user_id = b.id
            ${ew.customSqlSegment}
    </select>


</mapper>