package com.boot.modules.sync.service;

import org.springframework.scheduling.annotation.Async;

import java.text.ParseException;

public interface KelinSyncService {
    /**
     * 检索自动任务
     * @throws ParseException
     */
    @Async("bootExecutor")
    void autoIntoCase() throws ParseException;

    /**
     * 增量数据写入通知
     * @throws ParseException
     */
    @Async("bootExecutor")
    void autoNotice() throws ParseException;
}
