/*
 用于v1.0.0 数据库升级至v1.0.1
 research_participant 版本：V1.0.1

 Date: 15/05/2023 10:38:24
*/

--
-- add index for table `rp_group_config`
--
alter table `rp_group_config` add KEY `project_id` (`project_id`) USING BTREE;
alter table `rp_group_config` add KEY `sort` (`sort`) USING BTREE;

--
-- add index for table `rp_sys_disease`
--
alter table `rp_sys_disease` add KEY `dept_id` (`dept_id`) USING BTREE;

--
-- add index for table `rp_inbound_process`
--
alter table `rp_inbound_process` add KEY `project_id` (`project_id`) USING BTREE;
alter table `rp_inbound_process` add KEY `empiid` (`empiid`) USING BTREE;
alter table `rp_inbound_process` add KEY `status` (`status`) USING BTREE;

--
-- add index for table `rp_kelin_config`
--
alter table `rp_kelin_config` add KEY `request_id` (`request_id`) USING BTREE;
alter table `rp_kelin_config` add KEY `group_config_id` (`group_config_id`) USING BTREE;

--
-- add index for table `rp_enroll_record`
--
alter table `rp_enroll_record` add KEY `project_id` (`project_id`) USING BTREE;
alter table `rp_enroll_record` add KEY `group_config_id` (`group_config_id`) USING BTREE;

--
-- add index for table `rp_enroll_detail`
--
alter table `rp_enroll_detail` add KEY `project_id` (`project_id`) USING BTREE;
alter table `rp_enroll_detail` add KEY `group_config_id` (`group_config_id`) USING BTREE;

--
-- add index for table `rp_doctor_recommendation`
--
alter table `rp_doctor_recommendation` add KEY `project_id` (`project_id`) USING BTREE;
alter table `rp_doctor_recommendation` add KEY `empiid` (`empiid`) USING BTREE;
alter table `rp_doctor_recommendation` add KEY `status` (`status`) USING BTREE;
--
-- Table rp_project
-- 2023/05/22
--
alter table rp_project add COLUMN `enrollments_estimated_count` int(10) DEFAULT 0 COMMENT '预计入组数';

ALTER TABLE rp_inbound_process ADD signing_op_time datetime NULL;
update rp_inbound_process set signing_op_time = signing_time;
ALTER TABLE rp_inbound_process MODIFY COLUMN signing_time date NULL;
ALTER TABLE rp_sys_user MODIFY COLUMN dept_id varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'deptId列表';


ALTER TABLE rp_user_subproject ADD center_dept_id bigint(20) NULL COMMENT '医院中心ID';'' ||
ALTER TABLE rp_sys_user MODIFY COLUMN dept_id varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '部门ID';

CREATE TABLE `rp_pat_import_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '导出记录id',
  `status` int(2) NOT NULL COMMENT '导出状态。0导出进行中，1导出成功，2导出失败',
  `start_time` varchar(20) COLLATE utf8_bin NOT NULL COMMENT '导出开始时间',
  `end_time` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '导出结束时间',
  `sub_project_id` bigint(20) NOT NULL COMMENT '关联子项目id',
  `user_id` bigint(20) NOT NULL COMMENT '执行导出用户',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `error_info_url` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '错误excel下载地址',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `proj` (`sub_project_id`) USING BTREE,
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='病例导入记录表';


ALTER TABLE rp_user_subproject CHANGE center_dept_id top_dept_id bigint(20) NULL COMMENT '医院中心ID';
ALTER TABLE rp_project_dept ADD COLUMN top_dept_id bigint(20) NULL;
ALTER TABLE rp_project ADD COLUMN approval_year varchar(100) NULL COMMENT '立项年度';
ALTER TABLE rp_project ADD COLUMN approval_number varchar(100) NULL COMMENT '伦理批件号';
ALTER TABLE rp_project ADD COLUMN contacts varchar(100) NULL COMMENT '联系人';
ALTER TABLE rp_project ADD COLUMN contacts_phone varchar(100) NULL COMMENT '联系人电话';

ALTER TABLE rp_doctor_recommendation ADD COLUMN recommend_dept_id bigint(20) NULL;

ALTER TABLE rp_project MODIFY COLUMN project_name varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '项目名称';
ALTER TABLE rp_sub_project MODIFY COLUMN name varchar(500) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '项目名称';
ALTER TABLE rp_project ADD COLUMN multicenter_type varchar(100) NULL COMMENT '角色';
ALTER TABLE rp_project ADD COLUMN multicenter_description varchar(100) NULL COMMENT '描述';

ALTER TABLE rp_sys_user MODIFY COLUMN dept_id varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '部门ID';
ALTER TABLE rp_sys_disease DROP dept_id;
ALTER TABLE rp_sys_user ADD COLUMN admin_dept_id varchar(500) NULL COMMENT '科室管理员医疗单元，用户角色是科室管理者时需要填写';
