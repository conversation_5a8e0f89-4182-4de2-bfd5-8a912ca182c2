//package com.boot.modules.sys.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.boot.modules.patient.dto.DepartmentReportDto;
//import com.boot.modules.patient.dto.PersonalReportDto;
//import com.boot.modules.sys.entity.SysUserDeptEntity;
//import com.boot.modules.sys.vo.UserDeptsVo;
//import com.boot.modules.sys.vo.UserPermissionVo;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @desc :
// * @create 2022-05-19
// */
//public interface SysUserDeptService extends IService<SysUserDeptEntity> {
//
//    /**
//     * 获取用户的医联体数据权限
//     *
//     * @param userId
//     * @return
//     */
//    List<UserPermissionVo> getUserPermission(Long userId);
//
//    /**
//     * 获取机构与用户数量对照
//     *
//     * @return
//     */
//    List<DepartmentReportDto> getDeptUserCount(List<Long> DeptIdList);
//
//    /**
//     * 获取所有用户机构
//     * @return
//     */
//    List<PersonalReportDto> getAllUserDept();
//
//    /**
//     * 批量新增或者修改某个用户的机构权限
//     *
//     * @param vo
//     */
//    void saveOrUpdate(UserDeptsVo vo);
//
//
//}
