package com.boot.modules.sys.controller;

import com.boot.commons.annotation.SysLog;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.result.Result;
import com.boot.commons.utils.StringUtils;
import com.boot.commons.utils.file.FileHelper;
import com.boot.modules.sys.entity.SysDictCateEntity;
import com.boot.modules.sys.entity.SysLogEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysLogService;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.result.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 系统日志
 *
 * <AUTHOR>
 */
@Api(tags = "系统日志")
@RestController
@RequestMapping("/sys/log")
public class SysLogController {
    @Resource
    private SysLogService sysLogService;

    /**
     * 列表
     */
    @ResponseBody
    @GetMapping("/list")
    @RequiresPermissions("sys:log:list")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysLogService.queryPage(params);

        return R.success(page);
    }

    @ResponseBody
    @GetMapping("/export")
    @RequiresPermissions("sys:log:export")
    public Result export(HttpServletResponse response, @RequestParam Map<String, Object> params) {
        response.setContentType("application/json;charset=utf-8");

        List<SysLogEntity> sysLogEntityList = sysLogService.queryAll(params);
        if (!CollectionUtils.isEmpty(sysLogEntityList)) {
            String paramStr;
            String errorInfo;
            //这是因为Excel单元格的字符只能是32,767以内。可以每隔30000字符将字符串截断：
            for (SysLogEntity log : sysLogEntityList) {
                if (StringUtils.isNotBlank(log.getParams()) && log.getParams().length() > 30000) {
                    paramStr = log.getParams().substring(30000);
                    log.setParams(paramStr);
                }
                if (StringUtils.isNotBlank(log.getErrorInfo()) && log.getErrorInfo().length() > 30000) {
                    errorInfo = log.getErrorInfo().substring(30000);
                    log.setErrorInfo(errorInfo);
                }

            }
        }
        ExcelUtils.wirteExcel(response, "日志导出文件", SysLogEntity.class, sysLogEntityList);

        return R.success("下载成功", true);

    }

    @ApiOperation(value = "删除指定时间外的日志", notes = "删除指定时间外的日志")
    @SysLog("删除指定日期外的日志信息")
    @DeleteMapping("/delete")
    @RequiresPermissions("sys:log:delete")
    public Result deleteBeforeDays(@RequestParam Integer days) {

        boolean res = sysLogService.deleteBeforeDays(days);

        return res ? R.success() : R.fail("删除失败");

    }


}
