package com.boot.modules.patient.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 医生推荐量统计返回参数
 */
@Data
public class PersonalReportDto implements Serializable {

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 医生名
     */
    private String doctorName;

    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 科室名
     */
    private String department;

    /**
     * 系统推荐患者数
     */
    private Integer system;


    /**
     * 个人推荐患者数
     */
    private Integer personal;

    /**
     * 推荐患者总数
     */
    private Integer total;

    /**
     * 推荐总数
     */
    private Integer sum;

    /**
     * 入组量
     */
    private Integer intoGroup;
}
