package com.boot.modules.sync.utils;

import com.boot.commons.constant.HxConst;
import com.boot.commons.utils.ConfigProperties;
import com.h3c.core.api.sdk.util.JwtSign;

public class JwtForKelin {
    public static String getJwt() {
        String accessKey = ConfigProperties.getKey(HxConst.PT_ACCESS_KEY);
        String secretKey = ConfigProperties.getKey(HxConst.PT_SECRET_KEY);
        String sign = JwtSign.sign(accessKey, secretKey, 300).split(" ")[1];
        System.out.println("Authorization: " + sign);
        return sign;
    }

    public static String getHisJwt(){
        String accessKey = ConfigProperties.getKey(HxConst.HIS_ACCESS_KEY);
        String secretKey = ConfigProperties.getKey(HxConst.HIS_SECRET_KEY);
        String sign = JwtSign.sign(accessKey, secretKey, 300).split(" ")[1];
        System.out.println("Authorization: " + sign);
        return sign;
    }
}
