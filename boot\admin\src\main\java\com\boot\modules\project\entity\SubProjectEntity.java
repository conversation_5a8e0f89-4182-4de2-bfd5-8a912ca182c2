package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc : 研究组实体类----子项目
 * @create 2021-03-18
 */

@Data
@TableName("rp_sub_project")
public class SubProjectEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 研究组名称
     */
    @NotBlank(message = "研究组名称不许为空", groups = {AddGroup.class, UpdateGroup.class})
//    @Size(max = 500, message = "研究组名称长度不能超过500")
    private String name;

    /**
     * 研究组描述
     */
    private String description;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 是否启用，1.启用（审核通过），0非启用（待审核） 默认是启用的， 当多病种项目中超出创建上限时需要关闭， 只有当管理源审核时才启用  2:审核不通过
     */
    private Integer enabled;

    /**
     * 是否开启审核，0：不开启， 1：开启 默认是1
     */
    private Integer enableAudit;

    /**
     * 子项目的状态 和项目状态同步更新  0,1,2  0:未开始 1：启用，2.停用
     */
    private Integer status;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 是否私密(功能改造)
     * chenwei 2022/10/24
     * edc3调用此类型
     */
    private Boolean isPrivate;

    /**
     * 是否关注在院状态
     * 1关注，0不关注
     */
    private Integer concernInpatientStatus;


    /**
     * 是否开启API，0：不开启， 1：开启 默认是0
     */
    private Integer enableApi;

    /**
     * 开启API后的唯一ID
     */
    @TableField(exist = false)
    private String guid;
}
