package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.Login;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.DiseaseEntity;
import com.boot.modules.sys.service.DiseaseService;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.vo.DeptVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 */

@Api(tags = "病种管理")
@RestController
@RequestMapping("/disease")
public class DiseaseController extends AbstractController {

    @Resource
    private DiseaseService diseaseService;

    @Resource
    private SysDeptService sysDeptService;

    @ApiOperation(
            value = "获取全部病种列表",
            notes = "获取全部病种列表"
    )
    @Login
    @GetMapping("/all")
    public Result all(@RequestParam Map<String, Object> params) {
        return R.success(diseaseService.getAllByPage(params));
    }

    @ApiOperation(
            value = "分页获取指定病种列表",
            notes = "分页获取指定病种列表"
    )
    @Login
    @GetMapping("/page")
    public Result page(@RequestParam Map<String, Object> params) {

        // 2.根据用户id判断获取类型
        Long doctorId = MapUtils.getValue(params, "doctorId", Long.class);

        if (doctorId != null) {
            return R.success(diseaseService.pageByDoctor(params));
        }
        return R.success(diseaseService.pageByDept(params));
    }

    @ApiOperation(
            value = "获取指定id的病种信息",
            notes = "获取指定id的病种信息"
    )
    @Login
    @GetMapping("/info")
    public Result info(@RequestParam Long id) {
        return R.success(diseaseService.getById(id));
    }

    @ApiOperation(
            value = "添加病种",
            notes = "添加病种"
    )
    @Login
    @PostMapping("")
    public Result addDisease(@RequestBody DiseaseEntity diseaseEntity) {
        boolean res;
        //验证合法性
        ValidatorUtils.validateEntity(diseaseEntity, AddGroup.class);
        //验证唯一性
        int count = validaCount(diseaseEntity);
        if (count > 0) {
            return R.fail("该病种已存在");
        }
        //添加
        res = diseaseService.add(diseaseEntity);

        return res ? R.success("添加病种成功") : R.fail("添加病种失败");
    }

    @ApiOperation(
            value = "修改病种",
            notes = "修改病种"
    )
    @Login
    @PutMapping("")
    public Result updateDisease(@RequestBody DiseaseEntity diseaseEntity) {
        //验证合法性
        ValidatorUtils.validateEntity(diseaseEntity, UpdateGroup.class);
        //验证唯一性
        int count = validaCount(diseaseEntity);
        if (count > 0) {
            return R.fail("该病种已存在");
        }
        //更新
        boolean res = diseaseService.updateDisease(diseaseEntity);
        return res ? R.success("修改病种成功") : R.fail("修改病种失败");
    }

    @ApiOperation(
            value = "删除病种",
            notes = "删除病种"
    )
    @Login
    @DeleteMapping("")
    public Result deleteDisease(@RequestBody Long[] ids) {
        if (CollectionUtils.isEmpty(Arrays.asList(ids))) {
            return R.fail("参数不正确");
        }
        //删除
        boolean res = diseaseService.removeByIds(Arrays.asList(ids));
        return res ? R.success("删除成功", true) : R.fail("删除失败");
    }

    @ApiOperation(
            value = "excel导入病种",
            notes = "excel导入病种"
    )
    @Login
    @PostMapping("/excel/import")
    public Result importDisease(@RequestParam("file") MultipartFile file) {
        if (file == null) {
            throw new BusinessException("上传的文件为空");
        }
        boolean res = diseaseService.excelImport(file);
        return res ? R.success("导入成功") : R.fail("导入失败");
    }

    @ApiOperation(value = "导入病种模板下载", notes = "导入病种模板下载")
    @GetMapping("/excel/download")
    public Result templateDownload(HttpServletResponse response, @RequestParam Map<String, Object> params) throws NoSuchFieldException {
        String fileName = "diseaseTemplate";
        List<List<String>> heads = new ArrayList<>();

        // 设置sheet1一级表头信息
        heads.add(Collections.singletonList("病种名"));
//        heads.add(Collections.singletonList("关联科室ID"));

//        List<List<String>> heads1 = new ArrayList<>();

//        // 设置sheet2一级表头信息
//        heads1.add(Collections.singletonList("科室ID"));
//        heads1.add(Collections.singletonList("科室名称"));
//        //获取所有科室信息
//        List<SysDeptEntity> sysDeptEntityList = sysDeptService.list(new QueryWrapper<SysDeptEntity>()
//                .lambda().gt(SysDeptEntity::getPid, 0));
//        List<DeptVo> deptVoList = new ArrayList<>();
//        //构造返回的结构
//        for (SysDeptEntity sysDeptEntity : sysDeptEntityList) {
//            DeptVo deptVo = new DeptVo();
//            deptVo.setDeptId(sysDeptEntity.getId());
//            deptVo.setDeptName(sysDeptEntity.getName());
//            deptVoList.add(deptVo);
//        }
        // 下载用户批量导入excel模板
        Map<String, List<List<String>>> headList = new LinkedHashMap<>();
        headList.put("导入病种", heads);
//        headList.put("科室信息", heads1);
        Map<String, List<DeptVo>> dataList = new LinkedHashMap<>();
//        dataList.put("科室信息", deptVoList);
        ExcelUtils.wirteExcel(response, fileName, headList, dataList);
        return R.success("下载成功");
    }

    /**
     * 验证病种记录的唯一性
     * @param diseaseEntity
     * @return
     */
    private int validaCount(DiseaseEntity diseaseEntity) {
        QueryWrapper<DiseaseEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(DiseaseEntity::getDiseaseName,diseaseEntity.getDiseaseName());
//                .eq(DiseaseEntity::getDeptId,diseaseEntity.getDeptId());
        return diseaseService.count(qw);
    }
}

