{"remainingRequest": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue?vue&type=template&id=47bde6f4&scoped=true&", "dependencies": [{"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue", "mtime": 1755140556641}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"stept-two\">\n\t<div class=\"table-container\">\n\t\t<el-form :inline=\"true\" size=\"mini\" :model=\"dataForm\">\n\t\t\t<el-form-item label=\"状态\">\n\t\t\t\t<el-select v-model=\"dataForm.isRead\" clearable placeholder=\"状态\" style=\"width: 150px\">\n\t\t\t\t\t<el-option :label=\"$t('all')\" value=\"\" />\n\t\t\t\t\t<el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n\t\t\t\t</el-select>\n\t\t\t</el-form-item>\n\n\t\t\t<el-form-item label=\"推荐医院\">\n\t\t\t\t<el-select v-model=\"dataForm.medId\" clearable placeholder=\"推荐医院\" style=\"width: 200px\">\n\t\t\t\t\t<el-option :label=\"$t('all')\" value=\"\" />\n\t\t\t\t\t<el-option v-for=\"item in hospitalList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\n\t\t\t\t</el-select>\n\t\t\t</el-form-item>\n\n\t\t\t<el-form-item>\n\t\t\t\t<el-input v-model=\"dataForm.inputQuery\" clearable placeholder=\"登记号或者姓名\" style=\"width: 150px\" />\n\t\t\t</el-form-item>\n\n\t\t\t<el-form-item>\n\t\t\t\t<el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getDataList(true)\">{{ $t('query') }}</el-button>\n\t\t\t</el-form-item>\n\t\t</el-form>\n\t\t<el-table v-loading=\"dataListLoading\" size=\"mini\" :data=\"dataList\">\n\t\t\t<el-table-column prop=\"regno\" label=\"登记号\" header-align=\"center\" align=\"center\" />\n\t\t\t<el-table-column prop=\"name\" label=\"姓名\" header-align=\"center\" align=\"center\" />\n\t\t\t<el-table-column prop=\"tel\" label=\"联系方式\" header-align=\"center\" align=\"center\" />\n\t\t\t<el-table-column prop=\"gender\" label=\"性别\" header-align=\"center\" align=\"center\">\n\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t{{ getGender(scope.row.gender) }}\n\t\t\t\t</template>\n\t\t\t</el-table-column>\n\t\t\t<el-table-column prop=\"birthday\" label=\"年龄\" header-align=\"center\" align=\"center\">\n\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t{{ calculateAge(scope.row.birthday) }}\n\t\t\t\t</template>\n\t\t\t</el-table-column>\n\t\t\t<el-table-column prop=\"type\" label=\"推荐方式\" header-align=\"center\" align=\"center\">\n\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t<span v-if=\"scope.row.type == 0\">系统推荐</span>\n\t\t\t\t\t<span v-else-if=\"scope.row.type == 1\">手动推荐</span>\n\t\t\t\t\t<span v-else-if=\"scope.row.type == 2\">医联体推荐</span>\n\t\t\t\t\t<span v-else>--</span>\n\t\t\t\t</template>\n\t\t\t</el-table-column>\n\t\t\t<el-table-column prop=\"recommendHospitalName\" label=\"推荐医院\" header-align=\"center\" align=\"center\" />\n\t\t\t<el-table-column prop=\"recommendName\" label=\"推荐医生\" header-align=\"center\" align=\"center\" />\n\t\t\t<el-table-column prop=\"recommendTime\" label=\"推荐时间\" header-align=\"center\" align=\"center\" />\n\t\t\t<el-table-column prop=\"isRead\" label=\"状态\" header-align=\"center\" align=\"center\" width=\"120px\">\n\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t<el-select v-model=\"scope.row.isRead\" placeholder=\"请选择\" size=\"mini\" @change=\"isReadChange($event, scope.row)\">\n\t\t\t\t\t\t<el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\n\t\t\t\t\t</el-select>\n\t\t\t\t</template>\n\t\t\t</el-table-column>\n\t\t\t<el-table-column :label=\"$t('handle')\" header-align=\"center\" align=\"center\" min-width=\"200px\">\n\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t<el-button size=\"mini\" @click=\"toConditionDialog(scope.row)\">查看</el-button>\n\t\t\t\t\t<el-button size=\"mini\" type=\"primary\" @click=\"intoGroup(scope.row)\">入组</el-button>\n\t\t\t\t\t<el-button size=\"mini\" type=\"danger\" @click=\"returnBack(scope.row)\">退回</el-button>\n\t\t\t\t</template>\n\t\t\t</el-table-column>\n\t\t</el-table>\n\t</div>\n\n\t<el-dialog :visible.sync=\"visible\" title=\"退回原因\" :close-on-click-modal=\"false\" :close-on-press-escape=\"false\" width=\"40%\">\n\t\t<el-form ref=\"returnForm\" :model=\"returnForm\" :rules=\"returnRules\" label-width=\"80px\">\n\t\t\t<el-form-item label=\"退回原因\" prop=\"returnReason\">\n\t\t\t\t<el-radio-group v-model=\"returnForm.returnReason\">\n\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"患者拒签\">患者拒签</el-radio></div>\n\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"不符合纳排标准\">不符合纳排标准</el-radio></div>\n\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"其他原因\">其他原因</el-radio></div>\n\t\t\t\t</el-radio-group>\n\t\t\t</el-form-item>\n\t\t\t<el-form-item v-if=\"returnForm.returnReason === '其他原因'\" label=\"具体原因\" prop=\"otherReason\">\n\t\t\t\t<el-input\n\t\t\t\t\tv-model=\"returnForm.otherReason\"\n\t\t\t\t\ttype=\"textarea\"\n\t\t\t\t\t:rows=\"3\"\n\t\t\t\t\tplaceholder=\"请输入具体原因\"\n\t\t\t\t\tmaxlength=\"200\"\n\t\t\t\t\tshow-word-limit\n\t\t\t\t/>\n\t\t\t</el-form-item>\n\t\t</el-form>\n\n\t\t<template slot=\"footer\">\n\t\t\t<el-button size=\"mini\" @click=\"visible = false\">取消</el-button>\n\t\t\t<el-button size=\"mini\" type=\"primary\" @click=\"quitConfirm\">确定</el-button>\n\t\t</template>\n\t</el-dialog>\n\n\t<!-- 分页 -->\n\t<el-pagination\n\t\tslot=\"footer\"\n\t\t:current-page=\"page\"\n\t\t:page-sizes=\"[10, 20, 50, 100]\"\n\t\t:page-size=\"limit\"\n\t\t:total=\"total\"\n\t\tlayout=\"total, sizes, prev, pager, next, jumper,slot\"\n\t\t@size-change=\"pageSizeChangeHandle\"\n\t\t@current-change=\"pageCurrentChangeHandle\"\n\t>\n\t\t<i class=\"el-icon-refresh-right page-refresh-btn\" @click=\"refreshHandle\" />\n\t\t<span v-if=\"dataListSelections && dataListSelections.length > 0\" class=\"page-selected-total\">已选中 {{ dataListSelections.length }} 条数据</span>\n\t</el-pagination>\n</div>\n", null]}