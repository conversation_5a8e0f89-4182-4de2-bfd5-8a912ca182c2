package com.boot.modules.sys.service;

import com.boot.modules.sys.entity.SysRoleDeptEntity;
import com.boot.modules.sys.entity.SysRoleMenuEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ShiroService {
    /**
     * 获取用户权限列表, 如果为0 ， 获取全部
     */
    Set<String> getUserPermissions(long userId);

    List<SysRoleMenuEntity> getRoleMenuPermissions(Long roleId);

    /**
     *  2022/10/24 chenwei
     *  区分内外网(功能改造) 新增入参字段type
     */
    List<SysRoleMenuEntity> getRoleMenuPermissions(Long roleId, Long type);

    List<SysRoleDeptEntity> getRoleDeptPermissions(Long roleId);

    SysUserTokenEntity queryByToken(String token);

    /**
     * 查询指定ID的用户信息，包括用户系统角色，及其科室相关信息
     * @param userId 用户ID
     */
    SysUserEntity queryUser(Long userId);

    @Transactional(rollbackFor = Exception.class)
    void saveDataPermission(Long roleId, Map<String, List<Long>> map);
}