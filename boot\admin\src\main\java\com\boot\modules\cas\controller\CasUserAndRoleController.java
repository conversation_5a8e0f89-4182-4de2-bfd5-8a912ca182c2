package com.boot.modules.cas.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.boot.commons.annotation.Login;
import com.boot.commons.constant.HxConst;
import com.boot.modules.cas.service.CasUserAndRoleService;
import com.boot.modules.cas.service.CasUserPushService;
import com.boot.modules.cas.vo.CasResult;
import com.boot.modules.cas.vo.CasUserVo;
import com.boot.modules.cas.vo.UserAndRolesVo;
import com.boot.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * cas用户角色管理
 *
 * <AUTHOR>
 */
@Api(tags = "cas用户角色管理")
@RestController
@RequestMapping("/cas")
public class CasUserAndRoleController extends AbstractController {
    @Resource
    private CasUserAndRoleService casUserAndRoleService;

    @Resource
    private CasUserPushService casUserPushService;

    /**
     * 获取所有角色信息,包括系统角色、项目角色及项目、子项目信息
     */
    @ApiOperation(
            value = "获取所有角色信息",
            notes = "获取所有角色信息")
    @Login
    @GetMapping("/role/list")
    public CasResult getRoles() {
        CasResult casResult;
        try {
            List<CasUserVo.CASRole> casRoles = casUserAndRoleService.getRoles();
            Boolean success = casRoles != null;
            String code = success ? String.valueOf(HxConst.RESPONSE_SUCCESS) : "0";
            casResult = new CasResult("", code, casRoles, success);
        } catch (Exception e) {
            casResult = new CasResult(e.getMessage(), String.valueOf(HxConst.RESPONSE_FAIL), null, false);
        }

        return casResult;
    }

    /**
     * 获取指定用户名的角色信息
     */
    @ApiOperation(
            value = "获取指定用户名的角色信息",
            notes = "获取指定用户名的角色信息")
    @GetMapping("/user/roles")
    public CasResult getByUserName(@RequestBody Map<String, Object> params) {
        CasResult casResult;
        try {
            String loginName = MapUtil.get(params, "login_name", String.class);
            List<CasUserVo.CASRole> casRoles = casUserAndRoleService.getByUserName(loginName);
            Boolean success = casRoles != null;
            String code = success ? String.valueOf(HxConst.RESPONSE_SUCCESS) : "0";
            casResult = new CasResult("", code, casRoles, success);
        } catch (Exception e) {
            casResult = new CasResult(e.getMessage(), String.valueOf(HxConst.RESPONSE_FAIL), null, false);
        }

        return casResult;
    }

    /**
     * cas同步用户信息及用户角色信息及HIS用户信息
     */
    @ApiOperation(
            value = "cas同步用户信息",
            notes = "cas同步用户信息")
    @PostMapping("/users")
    public CasResult saveUserAndRole(@RequestBody UserAndRolesVo userAndRolesVo) {
        //目前写死为系统管理员推送用户，ID=1
        Object o = JSONObject.toJSON(userAndRolesVo);
        logger.error("门户推送接口用户信息: " + o);
        Long User = Long.parseLong("1");
        System.out.println(User);
        return casUserPushService.doPushUsers(userAndRolesVo, User);
    }

}
