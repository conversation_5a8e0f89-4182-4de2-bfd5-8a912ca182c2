package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 项目流程设置表
 * <AUTHOR>
 * @date 2022/5/25
 * @time 16:13
 * @desc 项目流程设置表
 **/

@Data
@TableName("rp_project_setting")
public class ProjectSettingEntity  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  主键
     **/
    @TableId
    private Long id;
    /**
     * 子项目id
     */
    @NotBlank(message = "子项目id不可为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long subProjectId;
    /**
     * 每个步骤的状态 0未完成 1已完成
     * {
     * intoGrp: 1,
     * deForm: 0,
     * crfForm: 0,
     * followUp:0,
     * userGrp:0
     * }
     */
    private String setting;
    /**
     * 状态 0:未完成  1:已完成
     */
    private Integer status;

}