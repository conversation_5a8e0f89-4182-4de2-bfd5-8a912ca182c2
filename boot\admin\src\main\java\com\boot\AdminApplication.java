package com.boot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.SessionCookieConfig;
import javax.servlet.SessionTrackingMode;
import java.util.Collections;


/**
 * 启动类
 *
 * <AUTHOR>
 */
//@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@SpringBootApplication(exclude = {
        SecurityAutoConfiguration.class
})
@EnableScheduling //开启定时任务
@EnableAsync
//@EnableCasClient //是否开启单点登录 开发环境需要注释掉
public class AdminApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }

    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        super.onStartup(servletContext);
        servletContext.setSessionTrackingModes(Collections.singleton(SessionTrackingMode.COOKIE));
        SessionCookieConfig sessionCookieConfig= servletContext.getSessionCookieConfig();
        sessionCookieConfig.setHttpOnly(true);
    }

    /**
     * @Description: 定时任务
     * @param: [application]
     * @return: org.springframework.boot.builder.SpringApplicationBuilder
     *
     */

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(AdminApplication.class);
    }

    //@Bean
    //public CookieSerializer httpSessionIdResolver() {
        //DefaultCookieSerializer cookieSerializer = new DefaultCookieSerializer();
        //cookieSerializer.setCookieName("JSESSIONID");
        //cookieSerializer.setUseHttpOnlyCookie(false);
        //cookieSerializer.setSameSite(null);
        //return cookieSerializer;
    //}
}
