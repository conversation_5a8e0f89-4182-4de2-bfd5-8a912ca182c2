package com.boot.modules.sys.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 数据字典
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_dict_item")
public class SysDictItemEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @ExcelProperty(index = 0, value = "id")
    private Long id;
    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @ExcelProperty(index = 1, value = "name")
    @Size(max = 20, message = "字典名称长度不能超过20", groups = {AddGroup.class, UpdateGroup.class})
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "名称不能包含特殊字符", groups = {AddGroup.class, UpdateGroup.class})
    private String name;

    /**
     * 字典码
     */
    @NotBlank(message = "字典码不能为空")
    @ExcelProperty(index = 2, value = "code")
    @Size(max = 20, message = "字典码长度不能超过20", groups = {AddGroup.class, UpdateGroup.class})
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "字典码只能由英文字母和数字构成", groups = {AddGroup.class, UpdateGroup.class})
    private String code;

    /**
     * 字典类型
     */
    @NotNull(message = "字典分类id不能为空")
    @ExcelProperty(index = 3, value = "dict_cate_id")
    private Long dictCateId;

    /**
     * 字典值
     */
    @NotBlank(message = "字典值不能为空")
    @ExcelProperty(index = 4, value = "value")
    @Size(max = 20, message = "字典值长度不能超过20", groups = {AddGroup.class, UpdateGroup.class})
    private String value;
    /**
     * 排序
     */
    @ExcelProperty(index = 5, value = "sort")
    private Integer sort;

    /**
     * 备注
     */
    @ExcelProperty(index = 6, value = "remark")
    private String remark;
    /**
     * 删除标记  -1：已删除  0：正常
     */
    @TableLogic
    @ExcelProperty(index = 7, value = "del_flag")
    private Integer delFlag;

    /**
     * 字典项名称拼音
     */
    @ExcelProperty(index = 8, value = "pinyin")
    private String pinyin;

    /**
     * 字典项首字母
     */
    @ExcelProperty(index = 9, value = "first_letter")
    private String firstLetter;

}
