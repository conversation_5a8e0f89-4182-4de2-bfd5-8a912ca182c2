<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.SubProjectInfoDao">
    <select id="queryPage" resultType="com.boot.modules.project.vo.SubProjectInfoVo">
        SELECT b.id AS subProjectId,
            CASE WHEN a.type=1 THEN a.project_name WHEN a.type=2 THEN concat(a.project_name,'-',b.`name`) END AS projectName,
            d.name AS deptName,
            a.type,
            a.project_category as projectType,
            b.create_time AS createDate
        FROM rp_project a
            LEFT JOIN rp_sub_project b ON a.id=b.project_id
            JOIN rp_project_dept c ON a.id=c.project_id AND c.is_primary=1
            JOIN rp_sys_dept d ON c.dept_id=d.id
            ${ew.customSqlSegment}
    </select>

    <select id="myProject" resultType="com.boot.modules.project.vo.SubProjectInfoVo">
        SELECT b.id AS subProjectId,
            CASE WHEN a.type=1 THEN a.project_name WHEN a.type=2 THEN concat(a.project_name,'-',b.`name`) END AS projectName
        FROM rp_project a
            LEFT JOIN rp_sub_project b ON a.id=b.project_id
            LEFT JOIN rp_user_subProject c on b.id=c.sub_project_id
            ${ew.customSqlSegment}
    </select>

    <select id="theProject" resultType="com.boot.modules.project.vo.SubProjectInfoVo">
        SELECT a.id AS projectId,
            b.id AS subProjectId,
            CASE WHEN a.type=1 THEN a.project_name WHEN a.type=2 THEN concat(a.project_name,'-',b.`name`) END AS projectName,
            CASE WHEN b.description IS NULL OR b.description='' THEN a.description ELSE b.description end as description,
            a.start_date AS startDate,
            a.end_date AS endDate,
            a.type,
            c.username AS projectAdmin
        FROM rp_project a
            JOIN rp_sys_user c ON a.project_admin_id=c.id
            LEFT JOIN rp_sub_project b ON a.id=b.project_id
            ${ew.customSqlSegment}
            GROUP BY a.id
    </select>

    <select id="projectCenter" resultType="com.boot.modules.project.vo.ProjDeptVo">
        SELECT
            a.*,d.name as deptName
        FROM
            rp_project_dept a
                JOIN rp_sys_dept d on a.dept_id = d.id
                LEFT JOIN rp_project b ON a.project_id = b.id
                LEFT JOIN rp_sub_project c ON b.id = c.project_id
            ${ew.customSqlSegment}

    </select>
</mapper>