package com.boot.commons.utils;

import org.apache.cxf.configuration.security.AuthorizationPolicy;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transport.http.HTTPConduitConfigurer;

public class MyHTTPConduitConfigurer implements HTTPConduitConfigurer {

    private final String username;

    private final String password;

    public MyHTTPConduitConfigurer(String username, String password) {
        this.username = username;
        this.password = password;
    }

    @Override
    public void configure(String name, String address, HTTPConduit httpConduit) {
        AuthorizationPolicy authorizationPolicy = new AuthorizationPolicy();
        authorizationPolicy.setUserName(username);
        authorizationPolicy.setPassword(password);
        httpConduit.setAuthorization(authorizationPolicy);
    }
}
