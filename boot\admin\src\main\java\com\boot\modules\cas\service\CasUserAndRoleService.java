package com.boot.modules.cas.service;

import com.boot.modules.cas.vo.CasUserVo;

import java.util.List;
import java.util.Map;

/**
 * cas用户角色业务接口
 * <AUTHOR>
 * @createTime 2021年11月23日 10:01:00
 */
public interface CasUserAndRoleService {

    /**
     * 获取所有角色信息,包括系统角色、项目角色及项目、子项目信息
     * @return
     */
    List<CasUserVo.CASRole> getRoles();

    /**
     * 获取指定用户名的角色信息,包括系统角色、项目角色及项目、子项目信息
     * @param loginName 用户名
     * @return
     */
    List<CasUserVo.CASRole> getByUserName(String loginName);

    /**
     * cas同步用户信息及用户角色信息
     * @param casUserVos 用户信息及角色信息
     * @param loginUserId
     * @return
     */
    List<Map<String, Object>> saveUserAndRole(List<CasUserVo> casUserVos, Long loginUserId, Long deptId, String deptCode);
}
