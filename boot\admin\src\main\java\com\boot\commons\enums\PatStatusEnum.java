package com.boot.commons.enums;

/**
 * 患者状态枚举
 */
public enum PatStatusEnum {
    UNLOCK(0, "未锁定", "未锁定"),
    LOCK(1, "锁定", "锁定");

    private Integer code;

    private String name;

    private String desc;

    PatStatusEnum(Integer code, String name, String desc){
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static PatStatusEnum getByCode(Integer code){
        if (code != null) {
            for (PatStatusEnum patTypeEnum : PatStatusEnum.values()) {
                if (patTypeEnum.getCode().equals(code) ) {
                    return patTypeEnum;
                }
            }
        }
        return null;
    }
}
