{"remainingRequest": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\src\\views\\modules\\components\\recommend-list\\step-two.vue", "mtime": 1755140556641}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1_Work\\CODE\\2025-donghua\\research_participant\\boot-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport mixinViewModule from '@/mixins/view-module';\nimport { hisApiService } from '@/views/d2admin/his/his-recommend-api.js';\nexport default {\n  mixins: [mixinViewModule],\n  props: {\n    // 推荐医院\n    hospitalList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  data: function data() {\n    return {\n      foreignId: '',\n      regNo: '',\n      doctorUserId: '',\n      // 当前用户id\n      patEmpi: '',\n      mixinViewModuleOptions: {\n        activatedIsNeed: true,\n        getDataListURL: '/pat/list',\n        getDataListIsPage: true\n      },\n      dataForm: {\n        status: '1',\n        projectId: '',\n        subProjectId: '',\n        isRead: '',\n        inputQuery: '',\n        order: 'desc',\n        // 排序，asc／desc\n        orderField: 'recommendTime',\n        // 排序，字段\n        medId: ''\n      },\n      options: [{\n        value: 1,\n        label: '已阅'\n      }, {\n        value: 0,\n        label: '未阅'\n      }],\n      visible: false,\n      currentRow: null,\n      // 当前操作的行数据\n      recommendInfo: {},\n      // 推荐信息\n      returnForm: {\n        returnReason: '',\n        otherReason: ''\n      },\n      returnRules: {\n        returnReason: [{\n          required: true,\n          message: '请选择退回原因',\n          trigger: 'change'\n        }],\n        otherReason: [{\n          required: true,\n          message: '请输入具体原因',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  watch: {},\n  created: function created() {\n    this.init();\n  },\n  methods: {\n    setTestData: function setTestData() {\n      var _this = this;\n\n      setTimeout(function () {\n        _this.dataList = [{\n          projectId: 64,\n          subProjectId: 3326,\n          empiid: 121647588,\n          visitId: 1,\n          userId: 1,\n          name: '张三',\n          gender: 1,\n          birthday: '1992-04-11',\n          idCard: '15555555555',\n          initials: 'zs',\n          tel: '15555555555',\n          recommendId: 1,\n          recommendName: '张三',\n          recommendReason: '',\n          recommendTime: '2023-04-11',\n          outReason: '',\n          inboundTime: '2023-04-11',\n          signingTime: '2023-04-11',\n          signingAuditId: 1,\n          signingAuditName: '张三',\n          inboundId: 1,\n          inboundName: '张三',\n          type: 2\n        }];\n      }, 1000);\n    },\n    init: function init() {\n      this.setTestData();\n    },\n    // 展示纳排条件满足弹窗\n    toConditionDialog: function toConditionDialog(row) {\n      if (row.type && row.type == 2) {\n        // 打开医联体推荐的受试者详情弹层\\\n        this.$emit('showYltDialog', {\n          projectId: row.projectId,\n          subProjectId: row.subProjectId,\n          empi: row.empiid,\n          srcEmpiid: row.srcEmpiid\n        });\n      } else {\n        this.$emit('showDialog', {\n          projectId: row.projectId,\n          subProjectId: row.subProjectId,\n          empi: row.empiid,\n          srcEmpiid: row.srcEmpiid\n        });\n      }\n    },\n    // 入组\n    intoGroup: function intoGroup(row) {\n      var _this2 = this;\n\n      this.$confirm(\"\\u786E\\u5B9A\\u5165\\u7EC4\\u3010\".concat(row.name, \"\\u3011\\u5230\\u5F53\\u524D\\u9879\\u76EE?\"), this.$t('prompt.title'), {\n        loading: true,\n        confirmButtonText: this.$t('confirm'),\n        cancelButtonText: this.$t('cancel'),\n        type: 'warning'\n      }).then(function () {\n        hisApiService.setPatIntoGroup({\n          id: row.doctorRecommendId,\n          empi: row.empiid,\n          projectId: row.projectId,\n          subProjectId: row.subProjectId\n        }).then(function (res) {\n          _this2.$message({\n            message: _this2.$t('prompt.success'),\n            type: 'success',\n            duration: 500,\n            onClose: function onClose() {\n              _this2.getDataList();\n            }\n          });\n        });\n      });\n    },\n    returnBack: function returnBack(row) {\n      var _this3 = this;\n\n      // 保存当前行数据和推荐信息\n      this.currentRow = row;\n      this.recommendInfo = {\n        subProjectId: row.subProjectId,\n        empiid: row.empiid\n      }; // 重置表单\n\n      this.returnForm = {\n        returnReason: '',\n        otherReason: ''\n      }; // 清除表单验证\n\n      this.$nextTick(function () {\n        if (_this3.$refs.returnForm) {\n          _this3.$refs.returnForm.clearValidate();\n        }\n      }); // 显示弹窗\n\n      this.visible = true;\n    },\n    // 确认退回\n    quitConfirm: function quitConfirm() {\n      var _this4 = this;\n\n      this.$refs.returnForm.validate(function (valid) {\n        if (valid) {\n          // 如果选择了\"其他原因\"但没有填写具体原因，需要验证\n          if (_this4.returnForm.returnReason === '其他原因' && !_this4.returnForm.otherReason.trim()) {\n            _this4.$message.error('请输入具体原因');\n\n            return;\n          } // 调用接口\n\n\n          hisApiService.setPatBackGroup({\n            subProjectId: _this4.recommendInfo.subProjectId,\n            empi: _this4.recommendInfo.empiid,\n            reason: _this4.returnForm.returnReason === '其他原因' ? _this4.returnForm.otherReason : _this4.returnForm.returnReason\n          }).then(function (res) {\n            _this4.$message({\n              message: _this4.$t('prompt.success'),\n              type: 'success',\n              duration: 500,\n              onClose: function onClose() {\n                _this4.visible = false;\n\n                _this4.getDataList();\n              }\n            });\n          }).catch(function (error) {\n            console.error('退回失败:', error);\n\n            _this4.$message.error('退回失败，请重试');\n          });\n        }\n      });\n    },\n    getGender: function getGender(gender) {\n      if (gender == 1) {\n        return '女';\n      } else if (gender == 0) {\n        return '男';\n      } else {\n        return '保密';\n      }\n    },\n    calculateAge: function calculateAge(birthday) {\n      // 根据日期算年龄\n      if (birthday) {\n        birthday = birthday.split('-'); // 新建日期对象\n\n        var date = new Date(); // 今天日期，数组，同 birthday\n\n        var today = [date.getFullYear(), date.getMonth() + 1, date.getDate()]; // 分别计算年月日差值\n\n        var age = today.map(function (val, index) {\n          return val - birthday[index];\n        }); // 当天数为负数时，月减 1，天数加上月总天数\n\n        if (age[2] < 0) {\n          // 简单获取上个月总天数的方法，不会错\n          var lastMonth = new Date(today[0], today[1], 0);\n          age[1]--;\n          age[2] += lastMonth.getDate();\n        } // 当月数为负数时，年减 1，月数加上 12\n\n\n        if (age[1] < 0) {\n          age[0]--;\n          age[1] += 12;\n        } // console.log(age[0] + '岁' + age[1] + '月' + age[2] + '天')\n\n\n        if (age[0] < 1) {\n          return 1;\n        } else {\n          return age[0];\n        }\n      }\n    },\n    // 改变患者状态\n    isReadChange: function isReadChange(val, data) {\n      var _this5 = this;\n\n      // console.log(val, data, '219')\n      this.$axios.post(\"pat/read/\".concat(data.doctorRecommendId, \"/\").concat(val)).then(function (res) {\n        _this5.getDataList();\n      });\n    }\n  }\n};", {"version": 3, "sources": ["step-two.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA,OAAA,eAAA,MAAA,sBAAA;AACA,SAAA,aAAA,QAAA,0CAAA;AACA,eAAA;AACA,EAAA,MAAA,EAAA,CAAA,eAAA,CADA;AAEA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,YAAA,EAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA,EAAA;AACA;AAJA;AAFA,GAFA;AAWA,EAAA,IAXA,kBAWA;AACA,WAAA;AACA,MAAA,SAAA,EAAA,EADA;AAEA,MAAA,KAAA,EAAA,EAFA;AAGA,MAAA,YAAA,EAAA,EAHA;AAGA;AACA,MAAA,OAAA,EAAA,EAJA;AAKA,MAAA,sBAAA,EAAA;AACA,QAAA,eAAA,EAAA,IADA;AAEA,QAAA,cAAA,EAAA,WAFA;AAGA,QAAA,iBAAA,EAAA;AAHA,OALA;AAUA,MAAA,QAAA,EAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,UAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA,MANA;AAMA;AACA,QAAA,UAAA,EAAA,eAPA;AAOA;AACA,QAAA,KAAA,EAAA;AARA,OAVA;AAoBA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CApBA;AA8BA,MAAA,OAAA,EAAA,KA9BA;AA+BA,MAAA,UAAA,EAAA,IA/BA;AA+BA;AACA,MAAA,aAAA,EAAA,EAhCA;AAgCA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,YAAA,EAAA,EADA;AAEA,QAAA,WAAA,EAAA;AAFA,OAjCA;AAqCA,MAAA,WAAA,EAAA;AACA,QAAA,YAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA;AArCA,KAAA;AA8CA,GA1DA;AA2DA,EAAA,KAAA,EAAA,EA3DA;AA4DA,EAAA,OA5DA,qBA4DA;AACA,SAAA,IAAA;AACA,GA9DA;AA+DA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,yBACA;AAAA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,QAAA,GAAA,CACA;AACA,UAAA,SAAA,EAAA,EADA;AAEA,UAAA,YAAA,EAAA,IAFA;AAGA,UAAA,MAAA,EAAA,SAHA;AAIA,UAAA,OAAA,EAAA,CAJA;AAKA,UAAA,MAAA,EAAA,CALA;AAMA,UAAA,IAAA,EAAA,IANA;AAOA,UAAA,MAAA,EAAA,CAPA;AAQA,UAAA,QAAA,EAAA,YARA;AASA,UAAA,MAAA,EAAA,aATA;AAUA,UAAA,QAAA,EAAA,IAVA;AAWA,UAAA,GAAA,EAAA,aAXA;AAYA,UAAA,WAAA,EAAA,CAZA;AAaA,UAAA,aAAA,EAAA,IAbA;AAcA,UAAA,eAAA,EAAA,EAdA;AAeA,UAAA,aAAA,EAAA,YAfA;AAgBA,UAAA,SAAA,EAAA,EAhBA;AAiBA,UAAA,WAAA,EAAA,YAjBA;AAkBA,UAAA,WAAA,EAAA,YAlBA;AAmBA,UAAA,cAAA,EAAA,CAnBA;AAoBA,UAAA,gBAAA,EAAA,IApBA;AAqBA,UAAA,SAAA,EAAA,CArBA;AAsBA,UAAA,WAAA,EAAA,IAtBA;AAuBA,UAAA,IAAA,EAAA;AAvBA,SADA,CAAA;AA2BA,OA5BA,EA4BA,IA5BA,CAAA;AA6BA,KA/BA;AAgCA,IAAA,IAhCA,kBAgCA;AACA,WAAA,WAAA;AACA,KAlCA;AAmCA;AACA,IAAA,iBApCA,6BAoCA,GApCA,EAoCA;AACA,UAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,KAAA,CAAA,eAAA,EAAA;AACA,UAAA,SAAA,EAAA,GAAA,CAAA,SADA;AAEA,UAAA,YAAA,EAAA,GAAA,CAAA,YAFA;AAGA,UAAA,IAAA,EAAA,GAAA,CAAA,MAHA;AAIA,UAAA,SAAA,EAAA,GAAA,CAAA;AAJA,SAAA;AAMA,OARA,MAQA;AACA,aAAA,KAAA,CAAA,YAAA,EAAA;AACA,UAAA,SAAA,EAAA,GAAA,CAAA,SADA;AAEA,UAAA,YAAA,EAAA,GAAA,CAAA,YAFA;AAGA,UAAA,IAAA,EAAA,GAAA,CAAA,MAHA;AAIA,UAAA,SAAA,EAAA,GAAA,CAAA;AAJA,SAAA;AAMA;AACA,KArDA;AAsDA;AACA,IAAA,SAvDA,qBAuDA,GAvDA,EAuDA;AAAA;;AACA,WAAA,QAAA,yCAAA,GAAA,CAAA,IAAA,4CAAA,KAAA,EAAA,CAAA,cAAA,CAAA,EAAA;AACA,QAAA,OAAA,EAAA,IADA;AAEA,QAAA,iBAAA,EAAA,KAAA,EAAA,CAAA,SAAA,CAFA;AAGA,QAAA,gBAAA,EAAA,KAAA,EAAA,CAAA,QAAA,CAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,QAAA,aAAA,CACA,eADA,CACA;AACA,UAAA,EAAA,EAAA,GAAA,CAAA,iBADA;AAEA,UAAA,IAAA,EAAA,GAAA,CAAA,MAFA;AAGA,UAAA,SAAA,EAAA,GAAA,CAAA,SAHA;AAIA,UAAA,YAAA,EAAA,GAAA,CAAA;AAJA,SADA,EAOA,IAPA,CAOA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,OAAA,EAAA,MAAA,CAAA,EAAA,CAAA,gBAAA,CADA;AAEA,YAAA,IAAA,EAAA,SAFA;AAGA,YAAA,QAAA,EAAA,GAHA;AAIA,YAAA,OAAA,EAAA,mBAAA;AACA,cAAA,MAAA,CAAA,WAAA;AACA;AANA,WAAA;AAQA,SAhBA;AAiBA,OAvBA;AAwBA,KAhFA;AAiFA,IAAA,UAjFA,sBAiFA,GAjFA,EAiFA;AAAA;;AACA;AACA,WAAA,UAAA,GAAA,GAAA;AACA,WAAA,aAAA,GAAA;AACA,QAAA,YAAA,EAAA,GAAA,CAAA,YADA;AAEA,QAAA,MAAA,EAAA,GAAA,CAAA;AAFA,OAAA,CAHA,CAQA;;AACA,WAAA,UAAA,GAAA;AACA,QAAA,YAAA,EAAA,EADA;AAEA,QAAA,WAAA,EAAA;AAFA,OAAA,CATA,CAcA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,aAAA;AACA;AACA,OAJA,EAfA,CAqBA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,KAxGA;AAyGA;AACA,IAAA,WA1GA,yBA0GA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,YAAA,KAAA,MAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA,WAAA,CAAA,IAAA,EAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA;;AACA;AACA,WALA,CAOA;;;AACA,UAAA,aAAA,CACA,eADA,CACA;AACA,YAAA,YAAA,EAAA,MAAA,CAAA,aAAA,CAAA,YADA;AAEA,YAAA,IAAA,EAAA,MAAA,CAAA,aAAA,CAAA,MAFA;AAGA,YAAA,MAAA,EAAA,MAAA,CAAA,UAAA,CAAA,YAAA,KAAA,MAAA,GAAA,MAAA,CAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,UAAA,CAAA;AAHA,WADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,OAAA,EAAA,MAAA,CAAA,EAAA,CAAA,gBAAA,CADA;AAEA,cAAA,IAAA,EAAA,SAFA;AAGA,cAAA,QAAA,EAAA,GAHA;AAIA,cAAA,OAAA,EAAA,mBAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,gBAAA,MAAA,CAAA,WAAA;AACA;AAPA,aAAA;AASA,WAhBA,EAiBA,KAjBA,CAiBA,UAAA,KAAA,EAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,OAAA,EAAA,KAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,WApBA;AAqBA;AACA,OA/BA;AAgCA,KA3IA;AA4IA,IAAA,SA5IA,qBA4IA,MA5IA,EA4IA;AACA,UAAA,MAAA,IAAA,CAAA,EAAA;AACA,eAAA,GAAA;AACA,OAFA,MAEA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,eAAA,GAAA;AACA,OAFA,MAEA;AACA,eAAA,IAAA;AACA;AACA,KApJA;AAqJA,IAAA,YArJA,wBAqJA,QArJA,EAqJA;AACA;AACA,UAAA,QAAA,EAAA;AACA,QAAA,QAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CADA,CAEA;;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,EAAA,CAHA,CAIA;;AACA,YAAA,KAAA,GAAA,CAAA,IAAA,CAAA,WAAA,EAAA,EAAA,IAAA,CAAA,QAAA,KAAA,CAAA,EAAA,IAAA,CAAA,OAAA,EAAA,CAAA,CALA,CAMA;;AACA,YAAA,GAAA,GAAA,KAAA,CAAA,GAAA,CAAA,UAAA,GAAA,EAAA,KAAA,EAAA;AACA,iBAAA,GAAA,GAAA,QAAA,CAAA,KAAA,CAAA;AACA,SAFA,CAAA,CAPA,CAUA;;AACA,YAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,SAAA,GAAA,IAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,UAAA,GAAA,CAAA,CAAA,CAAA;AACA,UAAA,GAAA,CAAA,CAAA,CAAA,IAAA,SAAA,CAAA,OAAA,EAAA;AACA,SAhBA,CAiBA;;;AACA,YAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,CAAA,CAAA;AACA,UAAA,GAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,SArBA,CAuBA;;;AACA,YAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA;AACA,iBAAA,CAAA;AACA,SAFA,MAEA;AACA,iBAAA,GAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA,KArLA;AAsLA;AACA,IAAA,YAvLA,wBAuLA,GAvLA,EAuLA,IAvLA,EAuLA;AAAA;;AACA;AACA,WAAA,MAAA,CAAA,IAAA,oBAAA,IAAA,CAAA,iBAAA,cAAA,GAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA;AACA,OAFA;AAGA;AA5LA;AA/DA,CAAA", "sourcesContent": ["<template>\r\n\t<div class=\"stept-two\">\r\n\t\t<div class=\"table-container\">\r\n\t\t\t<el-form :inline=\"true\" size=\"mini\" :model=\"dataForm\">\r\n\t\t\t\t<el-form-item label=\"状态\">\r\n\t\t\t\t\t<el-select v-model=\"dataForm.isRead\" clearable placeholder=\"状态\" style=\"width: 150px\">\r\n\t\t\t\t\t\t<el-option :label=\"$t('all')\" value=\"\" />\r\n\t\t\t\t\t\t<el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t<el-form-item label=\"推荐医院\">\r\n\t\t\t\t\t<el-select v-model=\"dataForm.medId\" clearable placeholder=\"推荐医院\" style=\"width: 200px\">\r\n\t\t\t\t\t\t<el-option :label=\"$t('all')\" value=\"\" />\r\n\t\t\t\t\t\t<el-option v-for=\"item in hospitalList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t<el-form-item>\r\n\t\t\t\t\t<el-input v-model=\"dataForm.inputQuery\" clearable placeholder=\"登记号或者姓名\" style=\"width: 150px\" />\r\n\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t<el-form-item>\r\n\t\t\t\t\t<el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getDataList(true)\">{{ $t('query') }}</el-button>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\t\t\t<el-table v-loading=\"dataListLoading\" size=\"mini\" :data=\"dataList\">\r\n\t\t\t\t<el-table-column prop=\"regno\" label=\"登记号\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"name\" label=\"姓名\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"tel\" label=\"联系方式\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"gender\" label=\"性别\" header-align=\"center\" align=\"center\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t{{ getGender(scope.row.gender) }}\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"birthday\" label=\"年龄\" header-align=\"center\" align=\"center\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t{{ calculateAge(scope.row.birthday) }}\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"type\" label=\"推荐方式\" header-align=\"center\" align=\"center\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<span v-if=\"scope.row.type == 0\">系统推荐</span>\r\n\t\t\t\t\t\t<span v-else-if=\"scope.row.type == 1\">手动推荐</span>\r\n\t\t\t\t\t\t<span v-else-if=\"scope.row.type == 2\">医联体推荐</span>\r\n\t\t\t\t\t\t<span v-else>--</span>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column prop=\"recommendHospitalName\" label=\"推荐医院\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"recommendName\" label=\"推荐医生\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"recommendTime\" label=\"推荐时间\" header-align=\"center\" align=\"center\" />\r\n\t\t\t\t<el-table-column prop=\"isRead\" label=\"状态\" header-align=\"center\" align=\"center\" width=\"120px\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-select v-model=\"scope.row.isRead\" placeholder=\"请选择\" size=\"mini\" @change=\"isReadChange($event, scope.row)\">\r\n\t\t\t\t\t\t\t<el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n\t\t\t\t\t\t</el-select>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t\t<el-table-column :label=\"$t('handle')\" header-align=\"center\" align=\"center\" min-width=\"200px\">\r\n\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t<el-button size=\"mini\" @click=\"toConditionDialog(scope.row)\">查看</el-button>\r\n\t\t\t\t\t\t<el-button size=\"mini\" type=\"primary\" @click=\"intoGroup(scope.row)\">入组</el-button>\r\n\t\t\t\t\t\t<el-button size=\"mini\" type=\"danger\" @click=\"returnBack(scope.row)\">退回</el-button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</el-table-column>\r\n\t\t\t</el-table>\r\n\t\t</div>\r\n\r\n\t\t<el-dialog :visible.sync=\"visible\" title=\"退回原因\" :close-on-click-modal=\"false\" :close-on-press-escape=\"false\" width=\"40%\">\r\n\t\t\t<el-form ref=\"returnForm\" :model=\"returnForm\" :rules=\"returnRules\" label-width=\"80px\">\r\n\t\t\t\t<el-form-item label=\"退回原因\" prop=\"returnReason\">\r\n\t\t\t\t\t<el-radio-group v-model=\"returnForm.returnReason\">\r\n\t\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"患者拒签\">患者拒签</el-radio></div>\r\n\t\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"不符合纳排标准\">不符合纳排标准</el-radio></div>\r\n\t\t\t\t\t\t<div style=\"margin-bottom: 10px\"><el-radio label=\"其他原因\">其他原因</el-radio></div>\r\n\t\t\t\t\t</el-radio-group>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item v-if=\"returnForm.returnReason === '其他原因'\" label=\"具体原因\" prop=\"otherReason\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t\tv-model=\"returnForm.otherReason\"\r\n\t\t\t\t\t\ttype=\"textarea\"\r\n\t\t\t\t\t\t:rows=\"3\"\r\n\t\t\t\t\t\tplaceholder=\"请输入具体原因\"\r\n\t\t\t\t\t\tmaxlength=\"200\"\r\n\t\t\t\t\t\tshow-word-limit\r\n\t\t\t\t\t/>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</el-form>\r\n\r\n\t\t\t<template slot=\"footer\">\r\n\t\t\t\t<el-button size=\"mini\" @click=\"visible = false\">取消</el-button>\r\n\t\t\t\t<el-button size=\"mini\" type=\"primary\" @click=\"quitConfirm\">确定</el-button>\r\n\t\t\t</template>\r\n\t\t</el-dialog>\r\n\r\n\t\t<!-- 分页 -->\r\n\t\t<el-pagination\r\n\t\t\tslot=\"footer\"\r\n\t\t\t:current-page=\"page\"\r\n\t\t\t:page-sizes=\"[10, 20, 50, 100]\"\r\n\t\t\t:page-size=\"limit\"\r\n\t\t\t:total=\"total\"\r\n\t\t\tlayout=\"total, sizes, prev, pager, next, jumper,slot\"\r\n\t\t\t@size-change=\"pageSizeChangeHandle\"\r\n\t\t\t@current-change=\"pageCurrentChangeHandle\"\r\n\t\t>\r\n\t\t\t<i class=\"el-icon-refresh-right page-refresh-btn\" @click=\"refreshHandle\" />\r\n\t\t\t<span v-if=\"dataListSelections && dataListSelections.length > 0\" class=\"page-selected-total\">已选中 {{ dataListSelections.length }} 条数据</span>\r\n\t\t</el-pagination>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport mixinViewModule from '@/mixins/view-module'\r\nimport { hisApiService } from '@/views/d2admin/his/his-recommend-api.js'\r\nexport default {\r\n\tmixins: [mixinViewModule],\r\n\tprops: {\r\n\t\t// 推荐医院\r\n\t\thospitalList: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn []\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tforeignId: '',\r\n\t\t\tregNo: '',\r\n\t\t\tdoctorUserId: '', // 当前用户id\r\n\t\t\tpatEmpi: '',\r\n\t\t\tmixinViewModuleOptions: {\r\n\t\t\t\tactivatedIsNeed: true,\r\n\t\t\t\tgetDataListURL: '/pat/list',\r\n\t\t\t\tgetDataListIsPage: true\r\n\t\t\t},\r\n\t\t\tdataForm: {\r\n\t\t\t\tstatus: '1',\r\n\t\t\t\tprojectId: '',\r\n\t\t\t\tsubProjectId: '',\r\n\t\t\t\tisRead: '',\r\n\t\t\t\tinputQuery: '',\r\n\t\t\t\torder: 'desc', // 排序，asc／desc\r\n\t\t\t\torderField: 'recommendTime', // 排序，字段\r\n\t\t\t\tmedId: ''\r\n\t\t\t},\r\n\t\t\toptions: [\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 1,\r\n\t\t\t\t\tlabel: '已阅'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tvalue: 0,\r\n\t\t\t\t\tlabel: '未阅'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tvisible: false,\r\n\t\t\tcurrentRow: null, // 当前操作的行数据\r\n\t\t\trecommendInfo: {}, // 推荐信息\r\n\t\t\treturnForm: {\r\n\t\t\t\treturnReason: '',\r\n\t\t\t\totherReason: ''\r\n\t\t\t},\r\n\t\t\treturnRules: {\r\n\t\t\t\treturnReason: [\r\n\t\t\t\t\t{ required: true, message: '请选择退回原因', trigger: 'change' }\r\n\t\t\t\t],\r\n\t\t\t\totherReason: [\r\n\t\t\t\t\t{ required: true, message: '请输入具体原因', trigger: 'blur' }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\twatch: {},\r\n\tcreated() {\r\n\t\tthis.init()\r\n\t},\r\n\tmethods: {\r\n\t\tsetTestData() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.dataList = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tprojectId: 64,\r\n\t\t\t\t\t\tsubProjectId: 3326,\r\n\t\t\t\t\t\tempiid: 121647588,\r\n\t\t\t\t\t\tvisitId: 1,\r\n\t\t\t\t\t\tuserId: 1,\r\n\t\t\t\t\t\tname: '张三',\r\n\t\t\t\t\t\tgender: 1,\r\n\t\t\t\t\t\tbirthday: '1992-04-11',\r\n\t\t\t\t\t\tidCard: '15555555555',\r\n\t\t\t\t\t\tinitials: 'zs',\r\n\t\t\t\t\t\ttel: '15555555555',\r\n\t\t\t\t\t\trecommendId: 1,\r\n\t\t\t\t\t\trecommendName: '张三',\r\n\t\t\t\t\t\trecommendReason: '',\r\n\t\t\t\t\t\trecommendTime: '2023-04-11',\r\n\t\t\t\t\t\toutReason: '',\r\n\t\t\t\t\t\tinboundTime: '2023-04-11',\r\n\t\t\t\t\t\tsigningTime: '2023-04-11',\r\n\t\t\t\t\t\tsigningAuditId: 1,\r\n\t\t\t\t\t\tsigningAuditName: '张三',\r\n\t\t\t\t\t\tinboundId: 1,\r\n\t\t\t\t\t\tinboundName: '张三',\r\n\t\t\t\t\t\ttype: 2\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}, 1000)\r\n\t\t},\r\n\t\tinit() {\r\n\t\t\tthis.setTestData()\r\n\t\t},\r\n\t\t// 展示纳排条件满足弹窗\r\n\t\ttoConditionDialog(row) {\r\n\t\t\tif (row.type && row.type == 2) {\r\n\t\t\t\t// 打开医联体推荐的受试者详情弹层\\\r\n\t\t\t\tthis.$emit('showYltDialog', {\r\n\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\tsrcEmpiid: row.srcEmpiid\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tthis.$emit('showDialog', {\r\n\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\tsrcEmpiid: row.srcEmpiid\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 入组\r\n\t\tintoGroup(row) {\r\n\t\t\tthis.$confirm(`确定入组【${row.name}】到当前项目?`, this.$t('prompt.title'), {\r\n\t\t\t\tloading: true,\r\n\t\t\t\tconfirmButtonText: this.$t('confirm'),\r\n\t\t\t\tcancelButtonText: this.$t('cancel'),\r\n\t\t\t\ttype: 'warning'\r\n\t\t\t}).then(() => {\r\n\t\t\t\thisApiService\r\n\t\t\t\t\t.setPatIntoGroup({\r\n\t\t\t\t\t\tid: row.doctorRecommendId,\r\n\t\t\t\t\t\tempi: row.empiid,\r\n\t\t\t\t\t\tprojectId: row.projectId,\r\n\t\t\t\t\t\tsubProjectId: row.subProjectId\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\tmessage: this.$t('prompt.success'),\r\n\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\tduration: 500,\r\n\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\tthis.getDataList()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\treturnBack(row) {\r\n\t\t\t// 保存当前行数据和推荐信息\r\n\t\t\tthis.currentRow = row\r\n\t\t\tthis.recommendInfo = {\r\n\t\t\t\tsubProjectId: row.subProjectId,\r\n\t\t\t\tempiid: row.empiid\r\n\t\t\t}\r\n\r\n\t\t\t// 重置表单\r\n\t\t\tthis.returnForm = {\r\n\t\t\t\treturnReason: '',\r\n\t\t\t\totherReason: ''\r\n\t\t\t}\r\n\r\n\t\t\t// 清除表单验证\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tif (this.$refs.returnForm) {\r\n\t\t\t\t\tthis.$refs.returnForm.clearValidate()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\r\n\t\t\t// 显示弹窗\r\n\t\t\tthis.visible = true\r\n\t\t},\r\n\t\t// 确认退回\r\n\t\tquitConfirm() {\r\n\t\t\tthis.$refs.returnForm.validate((valid) => {\r\n\t\t\t\tif (valid) {\r\n\t\t\t\t\t// 如果选择了\"其他原因\"但没有填写具体原因，需要验证\r\n\t\t\t\t\tif (this.returnForm.returnReason === '其他原因' && !this.returnForm.otherReason.trim()) {\r\n\t\t\t\t\t\tthis.$message.error('请输入具体原因')\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 调用接口\r\n\t\t\t\t\thisApiService\r\n\t\t\t\t\t\t.setPatBackGroup({\r\n\t\t\t\t\t\t\tsubProjectId: this.recommendInfo.subProjectId,\r\n\t\t\t\t\t\t\tempi: this.recommendInfo.empiid,\r\n\t\t\t\t\t\t\treason: this.returnForm.returnReason === '其他原因' ? this.returnForm.otherReason : this.returnForm.returnReason\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\tmessage: this.$t('prompt.success'),\r\n\t\t\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\t\t\tduration: 500,\r\n\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\tthis.visible = false\r\n\t\t\t\t\t\t\t\t\tthis.getDataList()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\t\tconsole.error('退回失败:', error)\r\n\t\t\t\t\t\t\tthis.$message.error('退回失败，请重试')\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetGender(gender) {\r\n\t\t\tif (gender == 1) {\r\n\t\t\t\treturn '女'\r\n\t\t\t} else if (gender == 0) {\r\n\t\t\t\treturn '男'\r\n\t\t\t} else {\r\n\t\t\t\treturn '保密'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcalculateAge(birthday) {\r\n\t\t\t// 根据日期算年龄\r\n\t\t\tif (birthday) {\r\n\t\t\t\tbirthday = birthday.split('-')\r\n\t\t\t\t// 新建日期对象\r\n\t\t\t\tconst date = new Date()\r\n\t\t\t\t// 今天日期，数组，同 birthday\r\n\t\t\t\tconst today = [date.getFullYear(), date.getMonth() + 1, date.getDate()]\r\n\t\t\t\t// 分别计算年月日差值\r\n\t\t\t\tconst age = today.map((val, index) => {\r\n\t\t\t\t\treturn val - birthday[index]\r\n\t\t\t\t})\r\n\t\t\t\t// 当天数为负数时，月减 1，天数加上月总天数\r\n\t\t\t\tif (age[2] < 0) {\r\n\t\t\t\t\t// 简单获取上个月总天数的方法，不会错\r\n\t\t\t\t\tconst lastMonth = new Date(today[0], today[1], 0)\r\n\t\t\t\t\tage[1]--\r\n\t\t\t\t\tage[2] += lastMonth.getDate()\r\n\t\t\t\t}\r\n\t\t\t\t// 当月数为负数时，年减 1，月数加上 12\r\n\t\t\t\tif (age[1] < 0) {\r\n\t\t\t\t\tage[0]--\r\n\t\t\t\t\tage[1] += 12\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// console.log(age[0] + '岁' + age[1] + '月' + age[2] + '天')\r\n\t\t\t\tif (age[0] < 1) {\r\n\t\t\t\t\treturn 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn age[0]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 改变患者状态\r\n\t\tisReadChange(val, data) {\r\n\t\t\t// console.log(val, data, '219')\r\n\t\t\tthis.$axios.post(`pat/read/${data.doctorRecommendId}/${val}`).then(res => {\r\n\t\t\t\tthis.getDataList()\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.stept-two {\r\n\theight: 100%;\r\n\r\n\t.table-container {\r\n\t\theight: calc(100% - 35px);\r\n\r\n\t\t.el-table {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: calc(100% - 50px);\r\n\t\t}\r\n\t}\r\n}\r\n</style>"], "sourceRoot": "src/views/modules/components/recommend-list"}]}