package com.boot.modules.project.vo;

import lombok.Data;

@Data
public class TotalProjectVo {
    /**
     * 项目类型名
     */
    private String name;
    /**
     * 入组数
     */
    private long inbound;
    /**
     * 项目类型
     */
    private int projectType;
    /**
     * 项目数
     */
    private int project;

    /**
     * 项目数
     */
    private int addProject;

    /**
     * 新增入组数
     */
    private long addInbound = 0;

    /**
     * 加权入组数
     */
    private long weightInbound = 0;

    /**
     * 新增加权入组数
     */
    private long addWeightInbound = 0;


    /**
     * 累计Iit项目实际入组人数
     */
    private Integer totalIitInbound;

    /**
     * 累计GCP项目实际入组人数
     */
    private Integer totalGcpInbound;

    /**
     * 累计前瞻项目实际入组人数
     */
    private Integer totalPreObsInbound;

    /**
     * 累计专病队列实际入组人数
     */
    private Integer totalSpecialInbound;
}
