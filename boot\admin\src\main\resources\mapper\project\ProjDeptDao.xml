<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.project.dao.ProjDeptDao">

    <select id="getByQuery" resultType="com.boot.modules.project.vo.ProjDeptVo">

        select a.*,
               b.name as deptName
        from rp_project_dept a
                 left join rp_sys_dept b on a.dept_id = b.id
            ${ew.customSqlSegment}

    </select>

    <select id="getBySubProjectId" resultType="com.boot.modules.project.entity.ProjDeptEntity">

        SELECT
            a.*
        FROM
            rp_project_dept a
                LEFT JOIN rp_project b ON a.project_id = b.id
                LEFT JOIN rp_sub_project c ON b.id = c.project_id
            ${ew.customSqlSegment}

    </select>

</mapper>