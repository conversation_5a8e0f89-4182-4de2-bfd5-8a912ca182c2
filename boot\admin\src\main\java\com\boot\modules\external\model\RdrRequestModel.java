package com.boot.modules.external.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 单表复杂筛选条件查询
 * <AUTHOR>
 */
@Data
public class RdrRequestModel {

    /**
     * 表名
     */
    private String tableName;

    /**
     * params
     */
    private List<String> params;

    /**
     * 每页显示多少条数据,默认页宽1000
     */
    private Long pageSize = 10000L;

    /**
     * 获取第几页数据
     */
    private Long current = 1L;

    /**
     * 数据集
     */
    private List<RDRFieldQuery> fields;

    /**
     * 排序字段
     */
    private Map<String, String> sortFields;
    /**
     * 单个字段查询条件
     */
    @Data
    public static class RDRFieldQuery {

        /**
         * 字段名称
         */
        private String fieldName;
        /**
         * 字段值，如果有多个值就要拼多个查询条件，暂时不支持多值查询
         */
        private Object fieldValue;
        /**
         * 查询关系词
         * = > < <= >=
         */
        private String fieldMath;
        /**
         * 布尔条件拼接，默认为or
         * 与下⼀个条件的关系 or and 不区分⼤⼩写
         */
        private String fieldOperator;
        /**
         * 括号 ⽤来加括号构造逻辑关系,有左括号，右括号，左右括号
         * left right all
         */
        private String open;
    }
}
