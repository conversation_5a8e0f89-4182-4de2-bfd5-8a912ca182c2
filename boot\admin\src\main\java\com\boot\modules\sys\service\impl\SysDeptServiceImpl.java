package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.project.dao.ProjDeptPermissionDao;
import com.boot.modules.project.entity.ProjDeptPermissionEntity;
import com.boot.modules.sys.dao.SysDeptDao;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.boot.commons.constant.HxConst.HX_DEPT_CODE;


@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptDao, SysDeptEntity> implements SysDeptService {

    @Resource
    private ProjDeptPermissionDao projDeptPermissionDao;

    /**
     * 获取所有机构信息
     *
     * @return
     */
    @Override
    public List<SysDeptEntity> getAll() {
        return list();
    }

    @Override
//    @DataFilter(subDept = true, user = false, tableAlias = "t1", deptId = "id")
    public List<SysDeptEntity> queryList(Map<String, Object> params) {
        List<SysDeptEntity> list = baseMapper.queryList(params);

        List<SysDeptEntity> rootList = new ArrayList<>();

        // 重组成tree
        for (SysDeptEntity entity : list) {
            if (entity.getPid() == null || entity.getPid().equals(0L)) {
                entity.setParentName("一级部门");
                entity.setPid(0L);
                rootList.add(entity);
            }
        }

        // 获取子部门
        getSubDeptTree(rootList, list);

        return rootList;
    }

    @Override
    public List<SysDeptEntity> queryList(Long topDeptId, List<SysUserEntity> sysUserEntityList, List<Long> deptIds, Integer isAll) {
        List<SysDeptEntity> list = baseMapper.queryList(null);
        List<SysDeptEntity> rootList = new ArrayList<>();
        // 重组成tree
        for (SysDeptEntity entity : list) {
            if ((entity.getPid() == null || entity.getPid().equals(0L))
                    && entity.getId().equals(topDeptId)) {
                entity.setParentName("一级部门");
                entity.setPid(0L);
                rootList.add(entity);
            }
        }
        // 获取子部门
        getSubDeptTree(rootList, list, sysUserEntityList, deptIds, isAll);

        return rootList;
    }

    @Override
    public List<Long> queryDeptIdList(Long parentId) {
        List<SysDeptEntity> list = list(new QueryWrapper<SysDeptEntity>()
                .lambda().eq(SysDeptEntity::getPid, parentId)
                .eq(SysDeptEntity::getDelFlag, 0)
        );

        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().map(SysDeptEntity::getId).collect(Collectors.toList());
        }

        return null;
    }

    @Override
    public List<Long> getSubDeptIdList(Long deptId) {
        //部门及子部门ID列表
        List<Long> deptIdList = new ArrayList<>();

        if (deptId == null) {
            return deptIdList;
        }

        //获取子部门ID
        List<SysDeptEntity> allList = list(new QueryWrapper<SysDeptEntity>()
                .lambda()
                .eq(SysDeptEntity::getDelFlag, 0)
        );
        deptIdList = getDeptIdList(deptId, allList);
        deptIdList.add(deptId);
        return deptIdList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> getSubDeptIdList(List<SysDeptEntity> allDeptList, Long deptId){
        //部门及子部门列表
        List<Long> deptIdList = new ArrayList<>();
        if (deptId == null) {
            return deptIdList;
        }
        deptIdList = getDeptIdList(deptId, allDeptList);
        deptIdList.add(deptId);
        return deptIdList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> getSubDeptIdList(List<Long> deptIds) {
        //部门及子部门ID列表
        List<Long> deptIdList = new ArrayList<>();

        //获取子部门ID
        List<SysDeptEntity> allList = list(new QueryWrapper<SysDeptEntity>()
                .lambda()
                .eq(SysDeptEntity::getDelFlag, 0)
        );

        deptIdList = getDeptIdList(deptIds, allList);
        deptIdList.addAll(deptIds);

        return deptIdList.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 只展示设为可以作为研究中心的机构
     *
     * @param
     * @return
     */
    @Override
    public List<SysDeptEntity> queryListShowResearchCenter() {

        List<SysDeptEntity> allResearchCenter = new ArrayList<>();
        QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(SysDeptEntity::getIsResearchCenter, 1)
                .orderByAsc(SysDeptEntity::getSort)
                .eq(SysDeptEntity::getDelFlag, 0);

        allResearchCenter = baseMapper.getByQuery(qw);

        return allResearchCenter;
    }

    /**
     * 获取用户所属部门
     *
     * @param userId
     * @return
     */
    @Override
    public SysDeptEntity getByUserId(Long userId) {

        QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
        qw.eq("b.id", userId);

        List<SysDeptEntity> list = baseMapper.selectByQuery(qw);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 获取所属机构
     * 机构的最上层机构
     *
     * @param code
     * @return
     */
    @Override
    public SysDeptEntity getTopDeptByCode(String code) {
        QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(SysDeptEntity::getCode, code)
                .eq(SysDeptEntity::getPid, 0);
        List<SysDeptEntity> list = baseMapper.selectByQuery(qw);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<SysDeptEntity> getParentDeptAndCodeNotNull() {
        List<SysDeptEntity> list = this.list(
                new QueryWrapper<SysDeptEntity>()
                        .lambda().eq(SysDeptEntity::getPid, 0L)
        );
        if (!CollectionUtils.isEmpty(list)) {
            List<SysDeptEntity> result = new ArrayList<>();
            for (SysDeptEntity deptEntity : list) {
                if (StringUtils.isNotBlank(deptEntity.getCode())) {
                    result.add(deptEntity);
                }
            }
            return result;
        }
        return list;
    }

    /**
     * 新增机构  如果排序为0  则设置为最大的
     *
     * @param dept
     * @return
     */
    @Override
    public boolean addDept(SysDeptEntity dept) {

        if (dept == null) {
            return true;
        }
        if (StringUtils.isBlank(dept.getCode())) {
            throw new BusinessException("机构CODE不许为空");
        }
//        int count = this.count(
//                new QueryWrapper<SysDeptEntity>()
//                        .lambda().eq(SysDeptEntity::getPid, dept.getPid())
//                        .eq(SysDeptEntity::getCode, dept.getCode())
//        );
//        if (count > 0) {
//            throw new BusinessException("当前CODE重复");
//        }

        if (dept.getSort().equals(0)) {
            //如果排序为0  则设置为最大的 + 1
            QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(SysDeptEntity::getPid, dept.getPid());
            Integer maxSort = baseMapper.queryMaxSort(qw);
            if (maxSort == null) {
                //当 当前父级部门第一次添加子部门的时候， maxsort = null
                maxSort = 0;
            }
            dept.setSort(maxSort + 1);
        }

        return this.save(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDept(SysDeptEntity dept) {
        boolean res = true;
        Integer isSyncChildDept = dept.getIsSyncChildDept();
        if (isSyncChildDept.equals(1)) {

            List<SysDeptEntity> allChild = getAllChildDept(Arrays.asList(dept), this.list(), new ArrayList<>());
            if (!CollectionUtils.isEmpty(allChild)) {

                for (SysDeptEntity deptEntity : allChild) {
                    deptEntity.setCode(dept.getCode());
                }
                allChild.add(dept);
                res = this.updateBatchById(allChild);
            }
        }
        SysDeptEntity old = this.getById(dept.getId());
        res = res && this.updateById(dept);
        if (dept.getPid().equals(0L) && !old.getCode().equals(dept.getCode())) {
            //如果第一层级的医疗机构修改了code，那就要对应修改关联表中的数据
            //edc_sys_user_dept   edc_pat_regno  edc_project_dept_permission
//            SysUserDeptEntity userDeptEntity = new SysUserDeptEntity();
//            userDeptEntity.setDeptCode(dept.getCode());
//            res = res && userDeptDao.update(
//                    userDeptEntity,
//                    new UpdateWrapper<SysUserDeptEntity>()
//                            .lambda().eq(SysUserDeptEntity::getDeptId, dept.getId())) >= 0;

            ProjDeptPermissionEntity projDeptPermissionEntity = new ProjDeptPermissionEntity();
            projDeptPermissionEntity.setDeptCode(dept.getCode());
            res = res && projDeptPermissionDao.update(
                    projDeptPermissionEntity,
                    new UpdateWrapper<ProjDeptPermissionEntity>()
                            .lambda().eq(ProjDeptPermissionEntity::getDeptId, dept.getId())) >= 0;
        }
        if (!res) {
            throw new BusinessException("修改失败");
        }

        return res;
    }

    /**
     * 递归获取某些机构的子机构
     *
     * @param rootList
     * @param list
     * @param result
     * @return
     */
    private List<SysDeptEntity> getAllChildDept(List<SysDeptEntity> rootList, List<SysDeptEntity> list, List<SysDeptEntity> result) {
        if (CollectionUtils.isEmpty(rootList) || CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (SysDeptEntity deptEntity : rootList) {
            List<SysDeptEntity> childList = new ArrayList<>();

            for (SysDeptEntity dept : list) {
                if (dept.getPid() != null && dept.getPid().equals(deptEntity.getId())) {
                    result.add(dept);
                    childList.add(dept);
                }
            }
            getAllChildDept(childList, list, result);
        }
        return result;
    }

    private List<Long> getDeptIdList(Long deptId, List<SysDeptEntity> list) {
        List<Long> subDeptIds = new ArrayList<>();
        for (SysDeptEntity entity : list) {
            if (entity.getPid() != null && entity.getPid().equals(deptId)) {
                subDeptIds.add(entity.getId());
                subDeptIds.addAll(getDeptIdList(entity.getId(), list));
            }
        }

        return subDeptIds;
    }

    private List<Long> getDeptIdList(List<Long> deptIds, List<SysDeptEntity> list) {
        List<Long> subDeptIds = new ArrayList<>();
        for (SysDeptEntity entity : list) {
            if ((entity.getPid() != null && entity.getPid() != 0)) {
                if (deptIds.contains(entity.getPid())) {
                    subDeptIds.add(entity.getId());
                    subDeptIds.addAll(getDeptIdList(Collections.singletonList(entity.getId()), list));
                }
            }
        }

        return subDeptIds;
    }

    private List<SysDeptEntity> getDeptList(List<Long> deptIds, List<SysDeptEntity> list) {
        List<SysDeptEntity> subDepts = new ArrayList<>();
        for (SysDeptEntity entity : list) {
            if ((entity.getPid() != null && entity.getPid() != 0)) {
                if (deptIds.contains(entity.getPid())) {
                    subDepts.add(entity);
                    subDepts.addAll(getDeptList(Collections.singletonList(entity.getId()), list));
                }
            }
        }

        return subDepts;
    }

    /**
     * 递归 获取子部门
     */
    private void getSubDeptTree(List<SysDeptEntity> rootList, List<SysDeptEntity> list) {
        if (CollectionUtils.isEmpty(rootList)) {
            return;
        }

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (SysDeptEntity entity : rootList) {
            List<SysDeptEntity> childList = new ArrayList<>();
            for (SysDeptEntity dept : list) {
                if (dept.getPid() != null && dept.getPid().equals(entity.getId())) {
                    dept.setParentName(entity.getName());
                    childList.add(dept);
                }
            }
            // 递归循环
            getSubDeptTree(childList, list);
            entity.setChildren(childList);
        }
    }

    /**
     * 递归 获取子部门，获取子节点用户
     */
    private void getSubDeptTree(List<SysDeptEntity> rootList, List<SysDeptEntity> list, List<SysUserEntity> userList, List<Long> deptIds, Integer isAll) {
        if (CollectionUtils.isEmpty(rootList)) {
            return;
        }

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (SysDeptEntity entity : rootList) {
            List<SysDeptEntity> childList = new ArrayList<>();
            for (SysDeptEntity dept : list) {
                if (dept.getPid() != null && dept.getPid().equals(entity.getId())) {
                    dept.setParentName(entity.getName());
                    if (isAll.equals(1)
                            || (!isAll.equals(1) && deptIds.contains(dept.getId()))) {
                        List<SysUserEntity> curUserList = userList.stream().filter(p -> Arrays.asList(p.getDeptId().split(",")).contains(dept.getId().toString())).collect(Collectors.toList());
                        dept.setUserList(curUserList);
                        childList.add(dept);
                    }
                }
            }
            // 递归循环
            getSubDeptTree(childList, list, userList, deptIds, isAll);
            entity.setChildren(childList);
        }
    }

    /**
     * 递归获取最上层科室信息
     *
     * @param deptEntities
     * @param deptId
     * @return
     */
    @Override
    public SysDeptEntity getTopDeptById(List<SysDeptEntity> deptEntities, Long deptId) {
        if (!CollectionUtils.isEmpty(deptEntities)) {
            for (SysDeptEntity deptEntity : deptEntities) {
                if (deptId.equals(deptEntity.getId())) {

                    if (deptEntity.getPid() == null || deptEntity.getPid() == 0) {
                        return deptEntity;
                    }
                    deptId = deptEntity.getPid();
                    // 递归调用
                    return getTopDeptById(deptEntities, deptId);
                }
            }
        }

        return null;
    }

    /**
     * 递归获取父级点
     *
     * @param deptEntities
     * @param deptId
     * @return
     */
    @Override
    public List<SysDeptEntity> getParentDept(List<SysDeptEntity> deptEntities, List<SysDeptEntity> parentDeptEntityList, Long deptId) {
        if (!CollectionUtils.isEmpty(deptEntities)) {
            for (SysDeptEntity deptEntity : deptEntities) {
                if (deptId.equals(deptEntity.getId())) {
                    parentDeptEntityList.add(deptEntity);
                    if (deptEntity.getPid() == null || deptEntity.getPid() == 0) {
                        return parentDeptEntityList;
                    }
                    deptId = deptEntity.getPid();
                    // 递归调用
                    return getParentDept(deptEntities, parentDeptEntityList, deptId);
                }
            }
        }

        return null;
    }

    @Override
    public List<Long> getAllHosId(List<Long> deptIdList) {
        List<SysDeptEntity> sysDeptEntities = this.listByIds(deptIdList);
        List<Long> hosIdList = new ArrayList<>();
        List<Long> pidList;

        for (;;) {
            pidList = new ArrayList<>();
            for (SysDeptEntity deptEntity : sysDeptEntities) {
                Long pid = deptEntity.getPid();
                if (pid != null && pid != 0) {
                    if (!pidList.contains(pid)) {
                        pidList.add(pid);
                    }
                }else if (! hosIdList.contains(hosIdList)){
                    hosIdList.add(deptEntity.getId());
                }
            }

            if (CollectionUtils.isEmpty(pidList)) {
                break;
            }

            sysDeptEntities = this.listByIds(pidList);
        }

        return hosIdList;
    }

    @Override
    public List<Long> getAllMedicalUnitId() {
        // 1. 获取所有一级医院id列表
        List<SysDeptEntity> list = this.list();

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<Long> deptIdList = new ArrayList<>();
        for(SysDeptEntity deptEntity : list) {
            if (deptEntity.getPid() != null && deptEntity.getPid() == 0) {
                deptIdList.add(deptEntity.getId());
            }
        }

        // 2.获取科室id
        List<SysDeptEntity> deptEntityList = getDeptList(deptIdList, list);

        if (CollectionUtils.isEmpty(deptEntityList)) {
            return new ArrayList<>();
        }

        deptIdList = deptEntityList.stream().map(SysDeptEntity::getId).collect(Collectors.toList());

        // 2.获取医疗单元id
        deptEntityList = getDeptList(deptIdList, list);

        if (CollectionUtils.isEmpty(deptEntityList)) {
            return new ArrayList<>();
        }

        return deptEntityList.stream().map(SysDeptEntity::getId).collect(Collectors.toList());

    }

    /**
     * 获取数据中心的医疗机构
     *
     * @return
     */
    @Override
    public List<SysDeptEntity> queryDataCenter() {
        QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(SysDeptEntity::getIsDataCenter, 1)
                .eq(SysDeptEntity::getPid, 0);
        return this.list(qw);
    }

    @Override
    public List<SysDeptEntity> getSubDeptList(List<Long> deptIds) {
        //部门及子部门ID列表
        List<SysDeptEntity> deptList = new ArrayList<>();

        //获取子部门ID
        List<SysDeptEntity> allList = list(new QueryWrapper<SysDeptEntity>()
                .lambda()
                .eq(SysDeptEntity::getDelFlag, 0)
        );

        deptList = getDeptList(deptIds, allList);

        return deptList;
    }

    @Override
    public List<ReportDto> listCount(List<Long> deptIdList, String deptName, Date startTime, Date endTime) {

        QueryWrapper<ReportDto> qwDis = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwProj = new QueryWrapper<>();

        // 1.设置查询机构名
        if (!StringUtils.isEmpty(deptName)) {
            qwDis.like("a.name", deptName);
            qwProj.like("a.name", deptName);
        }

        if (! CollectionUtils.isEmpty(deptIdList)) {
            qwDis.in("a.id", deptIdList);
            qwProj.in("a.id", deptIdList);
        }
//        else {
//            List<Long> allMedicalUnitId = getAllMedicalUnitId();
//            if (CollectionUtils.isEmpty(allMedicalUnitId)) {
//                return new ArrayList<>();
//            }
//            qwDis.in("a.id", allMedicalUnitId);
//            qwProj.in("a.id", allMedicalUnitId);
//        }

        // 临管部管理者只查看华西本院的科室
        List<Long> hxDeptIds = this.getSubDeptIdList(15L);
        qwDis.in("a.id", hxDeptIds);
        qwProj.in("a.id", hxDeptIds);

        // 2.设置总数相关统计
        List<ReportDto> total = this.baseMapper.listCount(qwDis);

        // 2.设置新增相关统计
        if (startTime != null) {
            qwDis.lt("b.create_time", endTime).ge("b.create_time", startTime);
            qwProj.lt("c.create_time", endTime).ge("c.create_time", startTime);
            List<ReportDto> addDis = this.baseMapper.listCount(qwDis);
            List<ReportDto> addProject = this.baseMapper.listCount(qwProj);
            Map<Long, Integer> addDisMap = new HashMap<>();
            Map<Long, Integer> addProjMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(addDis)) {
                addDisMap = addDis.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalDisease));
            }

            if (!CollectionUtils.isEmpty(addProject)) {
                addProjMap = addProject.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalProject));
            }

            for (ReportDto dto : total) {
                dto.setAddDisease(addDisMap.get(dto.getDeptId()) == null ? 0 : addDisMap.get(dto.getDeptId()));
                dto.setAddProject(addProjMap.get(dto.getDeptId()) == null ? 0 : addProjMap.get(dto.getDeptId()));
            }
        } else {
            for (ReportDto dto : total) {
                dto.setAddDisease(dto.getTotalDisease());
                dto.setAddProject(dto.getTotalProject());
            }
        }
        return total;
    }


    @Override
    public Long getIdByCode(String code) {
        return baseMapper.getIdByCode(code);
    }
}
