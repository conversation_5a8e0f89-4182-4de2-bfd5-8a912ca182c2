package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.modules.sys.dao.SysApplicationDao;
import com.boot.modules.sys.entity.SysApplicationEntity;
import com.boot.modules.sys.service.SysApplicationService;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;


/**
 * 应用管理
 *
 * <AUTHOR>
 */
@Service
public class SysApplicationServiceImpl extends ServiceImpl<SysApplicationDao, SysApplicationEntity> implements SysApplicationService {


    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String name = MapUtils.getValue(params, "appName", String.class);
        QueryWrapper<SysApplicationEntity> queryWrapper = MapUtils.getWrapperByParams(params, "sort", "rp_sys_application", SysApplicationEntity.class);

        queryWrapper.lambda()
                .like(StringUtils.isNotBlank(name), SysApplicationEntity::getAppName, name);

        IPage<SysApplicationEntity> page = this.page(
                new Query<SysApplicationEntity>().getPage(params), queryWrapper
        );

        return new PageUtils(page);
    }

}
