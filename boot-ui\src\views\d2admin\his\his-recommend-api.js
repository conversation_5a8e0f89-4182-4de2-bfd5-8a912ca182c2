import request from '@/plugin/axios'

/**
 * @description [ his ] his推荐相关接口
 */
export const hisApiService = {
  // 根据外部系统id获取用户信息
  getDoctorInfo(params) {
    return request({
      url: `his/user/${params.foreignId}/${params.medCode}`,
      method: 'get'
    })
  },
  // 根据登记号获取患者empi
  getPatientInfo(params) {
    return request({
      url: `his/pat/${params.regNo}/${params.medCode}`,
      method: 'get'
    })
  },

  // 获取用户参与的项目
  getProList(params) {
    return request({
      url: `his/user/subProject/list/${params.userId}/${params.empi ? params.empi : ''}`,
      method: 'get'
    })
  },

  // 获取患者就诊
  getPatientVisit(params) {
    return request({
      url: `his/pat/visit/list/${params.empi ? params.empi : ''}`,
      method: 'get'
    })
  },

  // 获取指定病人所有就诊的匹配纳排条件情况
  getVisitFillCount(params) {
    return request({
      url: `pat/match/detail/${params.empi}/${params.subProjectId}`,
      method: 'get'
    })
  },

  // 步骤二入组受试者
  setPatIntoGroup(params) {
    return request({
      url: `pat/input/${params.id}/${params.empi}/${params.projectId}/${params.subProjectId}`,
      method: 'post'
    })
  },

  // 退回受试者
  setPatBackGroup(data) {
    return request({
      url: `pat/recommend-refuse`,
      method: 'post',
      data
    })
  },

  // 步骤三签署知情同意书
  signAgreeBook(params, data) {
    return request({
      url: `pat/sign/${params.id}/${params.empi}/${params.subProjectId}`,
      method: 'post',
      data
    })
  },

  // 步骤三拟入组的退回
  quitBack(data) {
    return request({
      url: 'pat/revert',
      method: 'post',
      data
    })
  },

  // 步骤四的退出
  quitOut(data) {
    return request({
      url: 'pat/exit',
      method: 'post',
      data
    })
  }
}

/**
 * @description [ sys ] 菜单和路由相关
 */
export const sysMenuService = {
  /**
   * 获取菜单
   */
  getNav() {
    return request({
      url: `/sys/menu/nav`
    })
  },
  /**
   * 获取项目角色菜单
   */
  getProjRoleNav(params) {
    return request({
      url: `/sys/menu/proj/role/nav`,
      method: 'get',
      params
    })
  },
  /**
   * 获取权限
   */
  getPermissions(params) {
    return request({
      url: '/sys/menu/permissions',
      method: 'get',
      params
    })
  },
  /**
   * 获取指定应用指定角色的权限
   */
  async getProjRolePermissions(appId, roleId) {
    if (!appId || !roleId) { return [] }
    return await request({
      url: `/sys/permission/${appId}/${roleId}`,
      method: 'get'
    })
  }
}

/**
 * @description [ sys ] 用户相关
 */
export const sysUserService = {
  /**
   * 获取用户信息
   */
  getInfo() {
    return request({
      url: '/sys/user/info'
    })
  },
  updatePassword(data) {
    return request({
      url: '/sys/user/password',
      method: 'put',
      data
    })
  },
  getWatermarkInfo(data) {
    return request({
      url: 'sys/user/getWatermark',
      method: 'post',
      data
    })
  }
}