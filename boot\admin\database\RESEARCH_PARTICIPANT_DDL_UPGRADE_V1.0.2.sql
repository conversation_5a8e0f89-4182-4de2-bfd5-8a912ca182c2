
ALTER TABLE rp_doctor_recommendation ADD COLUMN is_read int(2) NOT NULL DEFAULT 0 COMMENT '0未读；1已读';
CREATE TABLE rp_sys_user_dept_special (
	id bigint(20) auto_increment NOT NULL COMMENT 'id',
	user_id bigint(20) NOT NULL COMMENT '用户id',
	dept_id bigint(20) NOT NULL COMMENT '机构id',
	his_dept_code varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '机构code，冗余个字段',
	CONSTRAINT `PRIMARY` PRIMARY KEY (id),
	CONSTRAINT user_id_2 UNIQUE KEY (user_id,dept_id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8
COLLATE=utf8_general_ci
COMMENT='特殊用户的机构权限';
CREATE INDEX user_id USING BTREE ON research_new.rp_sys_user_dept_special (user_id);

