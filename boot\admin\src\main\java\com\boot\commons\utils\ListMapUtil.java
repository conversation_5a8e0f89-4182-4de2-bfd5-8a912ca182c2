package com.boot.commons.utils;

import com.alibaba.fastjson.JSON;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListMapUtil {
    public static List<Map<String,Object>> convertListMap(List<Object> list){
        List<Map<String,Object>> maps=new ArrayList<Map<String,Object>>();
        for(Object obj:list){
            Class c = obj.getClass();
            Field[] f = c.getDeclaredFields();
            Map<String,Object> map=new HashMap<String, Object>();
            for(Field fie : f){
                try {
                    fie.setAccessible(true);//取消语言访问检查
                    map.put(fie.getName(), fie.get(obj));//获取私有变量值
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            //获取父类的私有属性
            for(Field fie : c.getSuperclass().getDeclaredFields()){
                try {
                    fie.setAccessible(true);//取消语言访问检查
                    map.put(fie.getName(), fie.get(obj));//获取私有变量值
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            maps.add(map);
        }
        return maps;
    }

    public static List<Map<String, Object>> toListMap(String json){
        List<Object> list = JSON.parseArray(json);

        List< Map<String,Object>> listw = new ArrayList<Map<String,Object>>();
        for (Object object : list){
            Map<String,Object> ageMap = new HashMap<String,Object>();
            Map <String,Object> ret = (Map<String, Object>) object;//取出list里面的值转为map
            listw.add(ret);
        }
        return listw;

    }
}
