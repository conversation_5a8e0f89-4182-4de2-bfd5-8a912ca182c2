package com.boot.interceptor;

import com.boot.commons.enums.ResponseStatusEnum;
import com.boot.commons.exception.AbstractException;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.exception.TokenException;
import com.boot.commons.exception.UsernameNotFoundException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import org.apache.shiro.authz.AuthorizationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ValidationException;
import java.util.Objects;

/**
 * 全局异常捕获
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 业务异常信息
     *
     * @param e LoginException
     * @return 响应信息
     */
    @ExceptionHandler(AbstractException.class)
    public Result loginException(AbstractException e) {
        writeLog(e);
        return R.fail(e.getStatus(), e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result validExceptionHandler(MethodArgumentNotValidException e) {
        writeLog(e);
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        return R.fail(ResponseStatusEnum.PARAMS_REQUIRED_IS_NULL, message);
    }

    /**
     * 用户不存在
     */
    @ExceptionHandler(UsernameNotFoundException.class)
    public Result handlerUsernameNotFoundException(UsernameNotFoundException e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.USER_NAME_NOT_FONT, "用户不存在,请确认账户是否正确");
    }

    /**
     * 路径不存在
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public Result handlerNoFoundException(Exception e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.URL_NOT_FOUND, "路径不存在，请检查路径是否正确");
    }

    @ExceptionHandler(TokenException.class)
    public Result handlerTokenException(Exception e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.TOKEN_V_FAIL, "token认证失败");
    }

    @ExceptionHandler(NullPointerException.class)
    public Result handlerNullPointerException(Exception e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.FAIL, "空指针");
    }

    @ExceptionHandler(ValidationException.class)
    public Result handlerNullPointerException(ValidationException e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.FAIL, e.getMessage());
    }


    @ExceptionHandler(DuplicateKeyException.class)
    public Result handleDuplicateKeyException(DuplicateKeyException e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.EXISTS, "数据库中已存在该记录");
    }

    @ExceptionHandler(AuthorizationException.class)
    public Result handleAuthorizationException(AuthorizationException e) {
        writeLog(e);
        return R.fail(ResponseStatusEnum.NO_PERMISSION, "没有权限，请联系管理员授权");
    }

    /**
     * 基础异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result handlerBaseException(BusinessException e) {
        writeLog(e);
        return R.fail(e.getMessage());
    }


    private void writeLog(Exception e) {
        LOGGER.error(e.getMessage(), e);
    }
}
