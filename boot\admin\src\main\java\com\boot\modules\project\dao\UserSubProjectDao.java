package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.project.entity.UserSubProjectEntity;
import com.boot.modules.project.vo.UserSubProjectVo;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.vo.UserSubProjectDeptRoleVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc : 用户子项目关联dao
 * @create 2021-03-19
 */
@Mapper
@CacheNamespace
public interface UserSubProjectDao extends BaseMapper<UserSubProjectEntity> {

    /**
     * 分页查询
     *
     * @param pageFilter
     * @param qw
     * @return
     */
    IPage<UserSubProjectVo> queryPage(IPage<UserSubProjectVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<UserSubProjectVo> qw);

    /**
     * 条件查询
     *
     * @param qw
     * @return
     */
    List<UserSubProjectVo> selectByQuery(@Param(Constants.WRAPPER) QueryWrapper<UserSubProjectVo> qw);

//    /**
//     * 获取项目人员信息
//     *
//     * @param subProjectId 当前子项目Id
//     * @return
//     */
//    List<UserSubProjectVo> getProjMember(Long projectId, Long subProjectId);

    /**
     * 查询某项目下的所以用户
     *
     * @param projectId
     * @return
     */
    List<UserSubProjectEntity> selectByProjectId(Long projectId);

    Long getUserCenter(@Param(Constants.WRAPPER) QueryWrapper<Long> qw);

//    Long getAdminUserCenter(@Param(Constants.WRAPPER) QueryWrapper<Long> qw);

    List<UserSubProjectVo> listByQuery(@Param(Constants.WRAPPER) QueryWrapper<UserSubProjectVo> qwUser);

    List<SysUserEntity> getByMobile(@Param(Constants.WRAPPER) QueryWrapper<SysUserEntity> qw);

    List<UserSubProjectDeptRoleVo> query(@Param(Constants.WRAPPER) QueryWrapper<UserSubProjectDeptRoleVo> qwUser);
}
