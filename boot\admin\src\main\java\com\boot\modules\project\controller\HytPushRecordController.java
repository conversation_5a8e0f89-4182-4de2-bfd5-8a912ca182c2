package com.boot.modules.project.controller;

import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.modules.mobile.service.PushHytService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "华易通推送患者记录管理")
@RestController
@RequestMapping("/hyt")
public class HytPushRecordController {
    @Resource
    private PushHytService pushHytService;

    @ApiOperation(
            value = "分页查询推送患者记录",
            notes = "分页查询推送患者记录"
    )
    @GetMapping("/record/page")
    public Result recordPage(@RequestParam Map<String, Object> param) {
        if (param == null) {
            return R.fail("参数错误");
        }
        return R.success(pushHytService.queryPage(param));
    }

    @ApiOperation(
            value = "重新推送接口",
            notes = "重新推送接口"
    )
    @PostMapping("/again/{id}")
    public Result recordPage(@PathVariable("id") Long id) {
        if (id == null) {
            return R.fail("参数错误");
        }
        pushHytService.again(id);

        return R.success();
    }
}
