package com.boot.modules.cas.vo;

import lombok.Data;

import java.util.List;

/**
 * cas同步用户信息vo对象
 *
 * <AUTHOR>
 * @createTime 2021年11月22日 17:54:00
 */
@Data
public class CasUserVo {
    // 登录账号
    private String login_name;
    // 中文名
    private String user_name;
    // his工号
    private String gx_number;
    /**
     * 用户id
     * */
    private String user_id;
    private String description;
    private String email;
    private String telephone;
    private String address;
    /**
     * 角色
     */
    private List<CASRole> partner_roles;

    /**
     * 多组织信息
     */
    private List<MultiGroup> multi_group;

    /**
     * 医院信息
     * */
    GroupInfo group_info;

    @Data
    public static class CASRole {
        // 角色id,id为项目id^子项目id^角色id拼接而成
        private String id;
        // 角色名称
        private String label;
    }

    @Data
    public static class GroupInfo {
        /**
         * 医院名称
         */
        private String hospital;
        /**
         * 医院编码
         */
        private String hospital_code;
        /**
         * 部门科室
         */
        private String department;
        /**
         * 部门编码
         */
        private String department_code;
        /**
         * 关联信息
         */
        private String group_chain_info;
        /**
         * 编码
         */
        private String gx_number;
        /**
         * 职位
         */
        private String position;

    }

}
