package com.boot.commons.model;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
@Configuration
public class BootAdminProperties {

    /**
     * 超级管理员账号
     */
    public static String adminAccount = "csm";

    /**
     * 超级管理员密码
     */
    public static String adminPassword = "351db3a93c3d32e0abed4e3a5927d0f4b0349830be5f507a6dba7f382a92e806";

    /**
     * 超级管理员密码 salt
     */
    public static String salt = "hsy124UNS1265439UIqs";

    /**
     * IP白名单验证
     */
    public static Boolean ipInterceptor = false;

    /**
     * 登录验证码
     */
    public static Boolean captcha = false;

    /**
     * 数据备份的数据库host
     */
    public static String backHost;
    /**
     * 备份数据库用户名
     */
    public static String backUserName;
    /**
     * 备份数据库用户密码
     */
    public static String backPassword;
    /**
     * 需要备份的数据库名称
     */
    public static String backDatabase;
    /**
     * mysql 配置文件地址
     */
    public static String backMysqlPath;

    /**
     * 备份文件保留天数
     */
    public static Integer days;

    /**
     * 外部数据webservice提供公司
     */
    public static String company;

    /**
     * 东华外部数据webservice
     */
    public static String dhWebService;

    /**
     * RDR外部数据webservice
     */
    public static String rdrWebService;

    /**
     * 东华不同的外部接口系统
     */
    public static String dhSystem;

    /**
     * 登录时密码错误限制开关
     */
    public static boolean loginPasswordCheckEnable;

    /**
     * 登录时密码错误限制---时间限制
     */
    public static Integer loginPasswordCheckTime;

    /**
     * 登录时密码错误限制---错误次数
     */
    public static Integer loginPasswordCheckCount;

    /**
     * 密码复杂度校验功能 开关
     */
    public static boolean isCheckPasswordComplexity;

    /**
     * 用户名复杂度校验功能 开关
     */
    public static boolean isCheckUserNameComplexity;

    public static boolean userLgoin;

    public static boolean isAdminLoginIpCheck;

    public static String adminLoginIpWhiteList;

    public static boolean enableWechat;

    public static boolean enableWatermark;

    //是否开启导出审核
    public static boolean enableExportAudit;

    //审核类型
    public static String exportAuditType;

    //是否开启样本库
    public static boolean enableSample;

    //订阅表单字段上限
    public static Integer deformFieldCount;

    //拉取柯林病例延时
    public static Integer kelinRelaySeconds;

    //区分内外网
    public static boolean distinguishInAndOutNet;

    //获取手机验证码网址
    public static String phoneVerifyCodeUrl;

    //自动入组最大数限制
    public static int maxCount;

    //获取oa文件云的地址
    public static String oaIp;

    //科室ID
    public static String deptIdList;

    public static boolean deformFillCompress;

    //获取用户信息-appkey
    public static String userAppKey;

    //获取用户信息-appsecret
    public static String userAppSecret;

    // hyt 加密密钥
    public static String rpSecretKey;

    // hyt 偏移量
    public static String rpIvStr;

    // 受试者appId
    public static String appId;

    // 特需门诊 code
    public static String vipClinicCode;

    @Value("${deform.compress.enable:false}")
    public void setDeformFillCompress(boolean param) {
        deformFillCompress = param;
    }

    @Value("${deform.total:100}")
    public void setDeformFieldCount(Integer total) {
        deformFieldCount = total;
    }

    @Value("${kelin.relay:120}")
    public void setKelinRelaySeconds(Integer relay) {
        kelinRelaySeconds = relay;
    }

    @Value("${wechat.enable:false}")
    public void setEnableWechat(boolean param) {
        enableWechat = param;
    }

    @Value("${export.audit.enable:true}")
    public void setEnableExportAudit(boolean param) {
        enableExportAudit = param;
    }

    @Value("${export.audit.hospital:dh}")
    public void setExportAuditType(String param) {
        exportAuditType = param;
    }

    @Value("${sample.enable:false}")
    public void setEnableSample(boolean param) {
        enableSample = param;
    }

    @Value("${boot.admin.user-login.enable:true}")
    public void setUserLgoin(boolean param) {
        userLgoin = param;
    }

    @Value("${boot.admin.account:csm}")
    public void setAdminAccount(String param) {
        adminAccount = param;
    }

    @Value("${boot.admin.password:351db3a93c3d32e0abed4e3a5927d0f4b0349830be5f507a6dba7f382a92e806}")
    public void setAdminPassword(String param) {
        adminPassword = param;
    }

    @Value("${boot.admin.salt:hsy124UNS1265439UIqs}")
    public void setSalt(String param) {
        salt = param;
    }

    @Value("${boot.ip-interceptor:false}")
    public void setIpInterceptor(boolean param) {
        ipInterceptor = param;
    }

    @Value("${boot.captcha:false}")
    public void setCaptcha(boolean param) {
        captcha = param;
    }

    @Value("${boot.backup.datasource.host:*************:8005}")
    public void setBackHost(String param) {
        backHost = param;
    }


    @Value("${boot.backup.datasource.username:root}")
    public void setBackUserName(String param) {
        backUserName = param;
    }

    @Value("${boot.backup.datasource.password:123456}")
    public void setBackPassword(String param) {
        backPassword = param;
    }

    @Value("${boot.backup.datasource.database:boot-admin}")
    public void setBackDatabase(String param) {
        backDatabase = param;
    }

    @Value("${boot.backup.days:30}")
    public void setBackDays(Integer param) {
        days = 0 - param;
    }

    @Value("${boot.backup.datasource.mysqlConfigFilePath:/usr/local/mysql/etc/my.cnf}")
    public void setBackMysqlPath(String param) {
        backMysqlPath = param;
    }

    @Value("${boot.external.dhSystem:rdr}")
    public void setDhSystem(String param) {
        dhSystem = param;
    }

    @Value("${boot.external.company:dh}")
    public void setCompany(String param) {
        company = param;
    }

    @Value("${boot.external.dh-web-service:http://localhost:57772/csp/dhc-csm/CSM.DataInterface.CLS?WSDL}")
    public void setDHWebService(String param) {
        dhWebService = param;
    }

    @Value("${boot.external.rdr-web-service:http://***************:8082/kelin/export/api/queryByTable}")
    public void setRdrWebService(String param) {
        rdrWebService = param;
    }

    @Value("${boot.login-error-check.enabled:false}")
    public void setLoginPasswordCheckEnable(boolean loginPasswordCheckEnable) {
        BootAdminProperties.loginPasswordCheckEnable = loginPasswordCheckEnable;
    }

    @Value("${boot.login-error-check.time:0}")
    public void setLoginPasswordCheckTime(Integer loginPasswordCheckTime) {
        BootAdminProperties.loginPasswordCheckTime = loginPasswordCheckTime;
    }

    @Value("${boot.login-error-check.count:0}")
    public void setLoginPasswordCheckCount(Integer loginPasswordCheckCount) {
        BootAdminProperties.loginPasswordCheckCount = loginPasswordCheckCount;
    }

    @Value("${boot.check-password-complexity.enable:false}")
    public void setIsCheckPasswordComplexity(boolean isCheckPasswordComplexity) {
        BootAdminProperties.isCheckPasswordComplexity = isCheckPasswordComplexity;
    }

    @Value("${boot.check-username-complexity.enable:false}")
    public void setIsCheckUserNameComplexity(boolean isCheckUserNameComplexity) {
        BootAdminProperties.isCheckUserNameComplexity = isCheckUserNameComplexity;
    }

    @Value("${boot.admin.admin-login-ip-check:false}")
    public void setIsAdminLoginIpCheck(boolean isAdminLoginIpCheck) {
        BootAdminProperties.isAdminLoginIpCheck = isAdminLoginIpCheck;
    }

    @Value("${boot.admin.admin-login-ip-white-list: *.*.*.*}")
    public void setAdminLoginIpWhiteList(String adminLoginIpWhiteList) {
        BootAdminProperties.adminLoginIpWhiteList = adminLoginIpWhiteList;
    }

    @Value("${boot.admin.distinguish-in-out-net.enable:true}")
    public void setDistinguishInAndOutNet(boolean param) {
        distinguishInAndOutNet = param;
    }

    @Value("${boot.admin.phone.verify.code.url:http://localhost:8080/boot-admin}")
    public void setPhoneVerifyCodeUrl(String url) {
        phoneVerifyCodeUrl = url;
    }

    @Value("${solrQure.max-count:100000}")
    public void setEnableWechat(int param) {
        maxCount = param;
    }

    @Value("${watermark.enable:false}")
    public void setEnableWatermark(boolean param) {
        enableWatermark = param;
    }

    @Value("${boot.external.oa-file-ip:http://file:hxdmc:cn}")
    public void setOaIp(String url) {
        oaIp = url;
    }

    @Value("${dept.id:1}")
    public void setDeptIdList(String param) {
        deptIdList = param;
    }


    @Value("${boot.external.user-app-key:111111}")
    public void setUserAppKey(String param) {
        userAppKey = param;
    }

    @Value("${boot.external.user-app-secret:1111}")
    public void setUserAppSecret(String param) {
        userAppSecret = param;
    }

    @Value("${boot.external.aes.secret-key:0CoJUM6Qyw8W8jec}")
    public void setRpSecretKey(String param) {
        rpSecretKey = param;
    }

    @Value("${boot.external.aes.iv-str:DYgjCetcVrj2W9xf}")
    public void setRpIvStr(String param) {
        rpIvStr = param;
    }

    @Value("${boot.external.aes.appId:APP_3BB723CC82C54252A6158E8FE667B10C}")
    public void setRpAppId(String param) {
        appId = param;
    }

    @Value("${boot.vip-clinic.code: 1208,1216}")
    public void setVipClinicCode(String param) {
        vipClinicCode = param;
    }
}
