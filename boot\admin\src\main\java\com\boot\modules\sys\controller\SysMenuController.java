package com.boot.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.constants.Const;
import com.boot.commons.enums.MenuStatusEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;

import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.sys.entity.SysApplicationEntity;
import com.boot.modules.sys.entity.SysMenuEntity;
import com.boot.modules.sys.service.ShiroService;
import com.boot.modules.sys.service.SysApplicationService;
import com.boot.modules.sys.service.SysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 系统菜单
 *
 * <AUTHOR>
 */
@Api(tags = "系统菜单")
@RestController
@RequestMapping("/sys/menu")
public class SysMenuController extends AbstractController {
    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private ShiroService shiroService;

    @Resource
    private SysApplicationService applicationService;

    /**
     * 导航菜单, 不包含按钮菜单
     */
    @ApiOperation(value = "获取全部的菜单", notes = "获取全部的菜单")
    @GetMapping("/nav")
    public Result nav() {
        //所有的系统菜单
        List<SysMenuEntity> menuList = sysMenuService.getUserMenuList(getUserId(), false);
        //所有的项目菜单
        Map<Long, List<SysMenuEntity>> appMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(menuList)) {
            // 只需要循环一级菜单即可
            for (SysMenuEntity menuEntity : menuList) {
                if (menuEntity.getEnabled() == MenuStatusEnum.OFF.getKey()) {
                    continue;
                }
                Long curAppId = menuEntity.getAppId();
                if (curAppId != null) {
                    List<SysMenuEntity> list = appMap.get(curAppId);
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    list.add(menuEntity);
                    appMap.put(curAppId, list);
                }
            }
        }
        //排序
        QueryWrapper<SysApplicationEntity> qwApp = new QueryWrapper<>();
        qwApp.lambda().orderByAsc(SysApplicationEntity::getSort);
        List<SysApplicationEntity> appList = applicationService.list(qwApp);

        List<SysApplicationEntity> appNewList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(appList)) {
            for (SysApplicationEntity app : appList) {
                List<SysMenuEntity> list = appMap.get(app.getId());
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                app.setMenus(list);

                appNewList.add(app);
            }
        }

        return R.success(appNewList);
    }

    /**
     * 所有菜单列表树
     */
    @ApiOperation(value = "指定应用菜单列表树", notes = "指定应用菜单列表树")
    @GetMapping("/list")
    @RequiresPermissions("sys:menu:list")
    public Result list(@RequestParam(value = "type", required = false, defaultValue = "1") String type,
                       @RequestParam(value = "appId", required = false) Long appId) {
        List<SysMenuEntity> menuList = null;
        if (StringUtils.isNotEmpty(type) && type.equals(String.valueOf(Const.MenuType.MENU.getValue()))) {
            menuList = sysMenuService.getMenuTreeList(appId, false);
        } else {
            menuList = sysMenuService.getMenuTreeList(appId, true);
        }

        return R.success(menuList);
    }


    @ApiOperation(value = "指定角色的菜单列表树", notes = "指定角色的菜单列表树")
    @GetMapping("/proj/role/nav")
    public Result roleNav(@RequestParam(value = "roleId") Long roleId) {
        List<SysMenuEntity> menuList = new ArrayList<>();
        menuList = sysMenuService.getRoleMenus(roleId);

        return R.success(menuList);
    }

    /**
     * 获取用户权限
     */
    @ApiOperation(value = "获取用户权限", notes = "获取用户权限")
    @GetMapping("/permissions")
    public Result getUserPermissions() {

        Set<String> per = shiroService.getUserPermissions(getUserId());

        return R.success(per);
    }

    /**
     * 菜单信息
     */
    @ApiOperation(value = "获取指定菜单信息", notes = "获取指定菜单信息")
    @GetMapping("/{menuId}")
    @RequiresPermissions("sys:menu:info")
    public Result info(@PathVariable("menuId") Long menuId) {
        SysMenuEntity menu = sysMenuService.getById(menuId);
        // 获取父菜单信息

        if (menu.getPid() != null) {
            SysMenuEntity parentMenu = sysMenuService.getById(menu.getPid());
            if (parentMenu == null) {
                menu.setParentName("一级菜单");
            } else {
                menu.setParentName(parentMenu.getName());
            }
        }

        return R.success(menu);
    }

    /**
     * 保存
     */
    @ApiOperation(value = "报存菜单", notes = "报存菜单")
    @SysLog("保存菜单")
    @PostMapping()
    @RequiresPermissions("sys:menu:save")
    public Result save(@RequestBody SysMenuEntity menu) {
        ValidatorUtils.validateEntity(menu, AddGroup.class);
        //数据校验
        verifyForm(menu);

        menu.setEnabled(1);
        sysMenuService.save(menu);

        return R.success();
    }

    /**
     * 修改
     */
    @ApiOperation(value = "修改菜单", notes = "修改菜单")
    @SysLog("修改菜单")
    @PutMapping()
    @RequiresPermissions("sys:menu:update")
    public Result update(@RequestBody SysMenuEntity menu) {
        ValidatorUtils.validateEntity(menu, UpdateGroup.class);

        //数据校验
        verifyForm(menu);

        boolean res = sysMenuService.updateById(menu);

        return res ? R.success("修改成功") : R.fail("修改失败");
    }

    @ApiOperation(value = "修改菜单应用", notes = "修改菜单应用")
    @SysLog("修改菜单应用")
    @PutMapping("/update/app")
    @RequiresPermissions("sys:menu:updateapp")
    public Result app(@RequestParam("menuId") Long menuId,
                      @RequestParam("appId") Long appId) {

        if (menuId == null || appId == null) {
            return R.fail();
        }

        boolean res = sysMenuService.updateMenuApp(appId, menuId);
        return res ? R.success("修改成功") : R.fail("修改失败");

    }

    /**
     * 删除
     */
    @ApiOperation(value = "删除菜单", notes = "删除菜单")
    @SysLog("删除菜单")
    @DeleteMapping("/{menuId}")
    @RequiresPermissions("sys:menu:delete")
    public Result delete(@PathVariable("menuId") long menuId) {
        //判断是否有子菜单或按钮
        List<SysMenuEntity> menuList = sysMenuService.queryListParentId(menuId);
        if (menuList.size() > 0) {
            return R.fail("请先删除子菜单或按钮");
        }

        sysMenuService.delete(menuId);

        return R.success();
    }

    /**
     * 验证参数是否正确
     */
    private void verifyForm(SysMenuEntity menu) {
        if (StringUtils.isBlank(menu.getName())) {
            throw new BusinessException("菜单名称不能为空");
        }

        if (menu.getPid() == null) {
            throw new BusinessException("上级菜单不能为空");
        }

        //菜单(可能有修改为目录菜单的情况)
//		if(menu.getType() == Constant.MenuType.MENU.getValue()){
//			if(StringUtils.isBlank(menu.getUrl())){
//				throw new BusinessException("菜单URL不能为空");
//			}
//		}

        //上级菜单类型
        int parentType = Const.MenuType.MENU.getValue();
        if (menu.getPid() != 0L) {
            SysMenuEntity parentMenu = sysMenuService.getById(menu.getPid());
            parentType = parentMenu.getType();

            // 如果上级菜单不是一级菜单则不允许（暂时默认菜单支持二级）
//            if (parentMenu.getPid() > 0) {
//                throw new BusinessException("菜单只能支持二级");
//            }
        }

        //目录、菜单
        if (menu.getType() == Const.MenuType.MENU.getValue()) {
            if (parentType != Const.MenuType.MENU.getValue()) {
                throw new BusinessException("上级菜单不能为按钮类型");
            }
            return;
        }

        //按钮
        if (menu.getType() == Const.MenuType.BUTTON.getValue()) {
            if (parentType != Const.MenuType.MENU.getValue()) {
                throw new BusinessException("上级菜单只能为菜单类型");
            }
            return;
        }
    }
}
