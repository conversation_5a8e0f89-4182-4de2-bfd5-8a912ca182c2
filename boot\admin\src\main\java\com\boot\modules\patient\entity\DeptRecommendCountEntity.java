package com.boot.modules.patient.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 科室推荐数中间表
 */
@Data
@TableName("rp_dept_recommend_count")
public class DeptRecommendCountEntity implements Serializable {

    @TableId
    private Long id;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 就诊类型 O 、I 、E 、ALL
     */
    private String admType;

    /**
     * 推荐病例数
     */
    private Integer recommendCount;

    /**
     * 创建实际
     */
    private String createTime;
}
