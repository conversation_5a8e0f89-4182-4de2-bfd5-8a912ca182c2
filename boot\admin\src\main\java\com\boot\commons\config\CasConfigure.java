package com.boot.commons.config;

import net.unicon.cas.client.configuration.CasClientConfigurerAdapter;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
import org.jasig.cas.client.validation.Cas30ProxyReceivingTicketValidationFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Configuration
@Component
public class CasConfigure  extends CasClientConfigurerAdapter {

    @Resource
    SSOConfigurationProperties ssoConfig;

    @Bean
    public FilterRegistrationBean authenticationFilterRegistrationBean() {
        FilterRegistrationBean authenticationFilter = new FilterRegistrationBean();
        authenticationFilter.setFilter(new CasFilter());
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("casServerLoginUrl", ssoConfig.getServerLoginUrl());
        initParameters.put("service", ssoConfig.getBackendRedirectUrl() + "?appcode=" + ssoConfig.getAppCode());
        initParameters.put("encodeServiceUrl", "false");
        initParameters.put("ignorePattern", "/research/sso/enable|/sso/home|/sys/logout|/sso/his|/sso/login|/cas/*|/sys/login|/sso/hyt|/his/*|disease/*|test/*|/keLin/search/*|/open/api/edc/log/callback|/open/api/ico/*");
        authenticationFilter.setInitParameters(initParameters);
        authenticationFilter.setOrder(1);
        List<String> urlPatterns = new ArrayList<>();
        //设置匹配的Url
        urlPatterns.add("/*");
        authenticationFilter.setUrlPatterns(urlPatterns);
        return authenticationFilter;
    }

    @Bean
    public FilterRegistrationBean validationFilterRegistrationBean() {
        FilterRegistrationBean authenticationFilter = new FilterRegistrationBean();
        authenticationFilter.setFilter(new Cas30ProxyReceivingTicketValidationFilter());
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("casServerUrlPrefix", ssoConfig.getServerLoginUrlPrefix());
        initParameters.put("service", ssoConfig.getBackendRedirectUrl() + "?appcode=" + ssoConfig.getAppCode());
        initParameters.put("useSession", "true");
        initParameters.put("encodeServiceUrl", "false");
        authenticationFilter.setInitParameters(initParameters);
        authenticationFilter.setOrder(2);
        authenticationFilter.addUrlPatterns("/*");
        return authenticationFilter;
    }

    @Bean
    public FilterRegistrationBean filterWrapperRegistraton() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(new HttpServletRequestWrapperFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(3);
        return registrationBean;
    }

    @Bean
    public ServletListenerRegistrationBean<EventListener> singleSignOutListenerRegistration() {
        ServletListenerRegistrationBean<EventListener> registrationBean = new ServletListenerRegistrationBean<>();
        registrationBean.setListener(new SingleSignOutHttpSessionListener());
        registrationBean.setOrder(1);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean filterSingleRegistration() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(new SingleSignOutFilter());
        registrationBean.addUrlPatterns("/*");
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("casServerUrlPrefix", ssoConfig.getServerLoginUrlPrefix());
        registrationBean.setInitParameters(initParameters);
        registrationBean.setOrder(0);
        return registrationBean;
    }







}
