package com.boot.commons.utils;

import com.boot.commons.enums.PasswordLevelEnum;
import com.boot.commons.exception.BusinessException;
import lombok.Data;
import org.aspectj.weaver.ast.Var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

/**
 * 检验密码长度 工具类
 *
 * <AUTHOR>
 * @create 2020-12-03 10:47
 */
@Data

public class PasswordComplexityUtils {

    @Autowired
    private static Environment environment;

    public static PasswordLevelEnum checkPassword(String passwordStr) {

        // 全是数字或全是字母     6-16个字符
        String regexWeak = "^[0-9]{6,16}$|^[a-zA-Z]{6,16}$";
        //数字和字母组成      6-16个字符
        String regexMedium = "^[A-Za-z0-9]{6,16}$";
        //字母+特殊字符，特殊字符+数字 或者数字+字母+特殊字符
        String regexStrong = "^(?![\\d]+$)(?![a-zA-Z]+$)(?![^\\da-zA-Z]+$).{6,20}$";

        //弱
        if (passwordStr.matches(regexWeak)) {
            System.out.println("弱");
            return PasswordLevelEnum.WEAK;
        }
        //中
        if (passwordStr.matches(regexMedium)) {
            System.out.println("中");
            return PasswordLevelEnum.MEDIUM;
        }
        //强
        if (passwordStr.matches(regexStrong)) {
            System.out.println("强");
            return PasswordLevelEnum.STRONG;
        }
        //不符合要求的
        return PasswordLevelEnum.UNQUALIFIED;

    }

    public static String checkPswComplexity(String password, boolean isCheckPsw) {

        if (isCheckPsw) {
            PasswordLevelEnum passwordLevelEnum = PasswordComplexityUtils.checkPassword(password);
            if (passwordLevelEnum == PasswordLevelEnum.WEAK) {
                throw new BusinessException("密码强度太弱,密码应包含数字、字母、特殊字符其中任两项或者三项");
            }
            if (passwordLevelEnum == PasswordLevelEnum.UNQUALIFIED) {
                throw new BusinessException("密码应为6-16位之间,且至少包含数字、字母、特殊字符其中任两项或者三项");
            }
        }

        return password;
    }

    /**
     * 校验密码
     *
     * @param password
     */
    public static void validPassword(String password) {

        String regular = "^(?![0-9]+$)(?![a-zA-Z]+$)(?!([^(0-9a-zA-Z)]|[\\\\(\\\\)])+$)([^(0-9a-zA-Z)]|[\\\\(\\\\)]|[a-zA-Z]|[0-9]){6,16}$";

        if (StringUtils.isNotBlank(password)) {
            boolean b = password.matches(regular);
            if (!b) {
                throw new BusinessException("密码必须包含字母，数字，字符中的任意两种，长度在6到16位");
            }
        } else {
            throw new BusinessException("密码不许为空");
        }
    }

    public static void main(String[] args) {

        String password = "32233f";
        String s = checkPswComplexity(password, true);
        System.out.println(s);
    }
}


