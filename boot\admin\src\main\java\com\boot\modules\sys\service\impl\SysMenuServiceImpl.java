package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.constants.Const;
import com.boot.commons.enums.MenuStatusEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.ClientInfoUtil;
import com.boot.modules.sys.dao.SysMenuDao;
import com.boot.modules.sys.entity.SysMenuEntity;
import com.boot.modules.sys.entity.SysRoleMenuEntity;
import com.boot.modules.sys.service.SysMenuService;
import com.boot.modules.sys.service.SysRoleMenuService;
import com.boot.modules.sys.service.SysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuDao, SysMenuEntity> implements SysMenuService {
    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysRoleMenuService sysRoleMenuService;

    private boolean distinguishInAndOutNet = BootAdminProperties.distinguishInAndOutNet;
    /**
     * 根据父菜单，查询子菜单
     *
     * @param parentId   父菜单ID
     * @param menuIdList 用户菜单ID
     * @return List<SysMenuEntity>
     */
    @Override
    public List<SysMenuEntity> queryListParentId(Long parentId, List<Long> menuIdList) {
        return queryListParentId(parentId, menuIdList, true);
    }

    @Override
    public List<SysMenuEntity> queryListParentId(Long parentId, List<Long> menuIdList, boolean isIncludeButtonMenu) {
        List<SysMenuEntity> menuList = queryListParentId(parentId, isIncludeButtonMenu);
        if (CollectionUtils.isEmpty(menuIdList)) {
            return menuList;
        }

        List<SysMenuEntity> userMenuList = new ArrayList<>();
        for (SysMenuEntity menu : menuList) {
            if (menuIdList.contains(menu.getId())) {
                userMenuList.add(menu);
            }
        }
        return userMenuList;
    }


    /**
     * 根据父菜单，查询子菜单
     *
     * @param parentId 父菜单ID
     */
    @Override
    public List<SysMenuEntity> queryListParentId(Long parentId) {
        // 默认包含按钮类型菜单
        return queryListParentId(parentId, true);
    }

    @Override
    public List<SysMenuEntity> queryListParentId(Long parentId, boolean isIncludeButtonMenu) {
        QueryWrapper<SysMenuEntity> qw = new QueryWrapper<SysMenuEntity>();
        qw.lambda().eq(SysMenuEntity::getPid, parentId);
        if (!isIncludeButtonMenu) {
            qw.lambda().ne(SysMenuEntity::getType, Const.MenuType.BUTTON.getValue());
        }
        qw.lambda().orderByAsc(SysMenuEntity::getSort);

        return baseMapper.selectList(qw);
    }


    @Override
    public List<SysMenuEntity> getUserMenuList(Long userId) {
        return getUserMenuList(userId, true);
    }

    @Override
    public List<SysMenuEntity> getUserMenuList(Long userId, boolean isIncludeButtonMenu) {
        List<Long> userMenuIdList = new ArrayList<>();
        //系统管理员，拥有最高权限
        if (userId == Const.SUPER_ADMIN) {
            return getAllMenuList(null, isIncludeButtonMenu);
        }
        //用户菜单列表
        if (distinguishInAndOutNet) {
            Long type = ClientInfoUtil.netType();
            userMenuIdList = sysUserService.queryAllMenuId(userId, type);
        } else {
            userMenuIdList = sysUserService.queryAllMenuId(userId);
        }

        return getAllMenuList(userMenuIdList, isIncludeButtonMenu);
    }

    /**
     * 获取当前用户在所有应用中的菜单
     *
     * @param userId              用户id
     * @param isIncludeButtonMenu 是否包含按钮菜单
     * @return
     */
    @Override
    public List<SysMenuEntity> getUserMenuListByAppId(Long userId, Long appId, boolean isIncludeButtonMenu) {

        if (userId == Const.SUPER_ADMIN) {
            return getAllMenuListByAppId(null, appId, isIncludeButtonMenu);
        }
        //用户菜单列表
        List<Long> userMenuIdList = new ArrayList<>();
        if (distinguishInAndOutNet) {
            Long type = ClientInfoUtil.netType();
            userMenuIdList = sysUserService.queryAllMenuId(userId, type);
        } else {
            userMenuIdList = sysUserService.queryAllMenuId(userId);
        }

        return getAllMenuListByAppId(userMenuIdList, appId, isIncludeButtonMenu);
    }

    /**
     * 获取所有的菜单列表， 当isIncludeButtonMenu 为false 时 就只获取菜单栏，不获取按钮类型的菜单
     *
     * @param isIncludeButtonMenu
     * @return
     */
    @Override
    public List<SysMenuEntity> getMenuList(boolean isIncludeButtonMenu) {
        QueryWrapper<SysMenuEntity> qw = new QueryWrapper<SysMenuEntity>();
        if (!isIncludeButtonMenu) {
            qw.lambda().ne(SysMenuEntity::getType, Const.MenuType.BUTTON.getValue())
                    .ne(SysMenuEntity::getEnabled, MenuStatusEnum.OFF);
        }
        qw.lambda().orderByAsc(SysMenuEntity::getSort);

        return baseMapper.selectList(qw);
    }

    @Override
    public List<SysMenuEntity> getMenuTreeList(Long appId, boolean isIncludeButtonMenu) {
        if (appId == null) {
            //如果appId 传的是空值，则返回所有
            return getAllMenuList(null, isIncludeButtonMenu);
        }
        return getAllMenuListByAppId(null, appId, isIncludeButtonMenu);
    }

    /**
     * 获取指定角色的菜单树
     *
     * @param roleId
     * @return
     */
    @Override
    public List<SysMenuEntity> getRoleMenus(Long roleId) {

        if (roleId == null) {
            //超级管理员
            return null;
        }
        QueryWrapper<SysMenuEntity> qw = new QueryWrapper<>();
        qw.eq("b.role_id", roleId);
        List<SysMenuEntity> list = baseMapper.getByQuery(qw);
        List<Long> menuIds = list.stream().map(SysMenuEntity::getId).collect(Collectors.toList());
        List<SysMenuEntity> menuListTree = getAllMenuList(menuIds, false);

        return menuListTree;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long menuId) {
        //删除菜单
        this.removeById(menuId);
        //删除菜单与角色关联
        sysRoleMenuService.remove(new QueryWrapper<SysRoleMenuEntity>().lambda().eq(SysRoleMenuEntity::getMenuId, menuId));
    }

    /**
     * 修改菜单的所属应用
     *
     * @param appId
     * @param menuId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateMenuApp(Long appId, Long menuId) {

        //1.只允许修改第一级菜单
        SysMenuEntity curMenu = this.getById(menuId);
        if (curMenu.getPid() != 0L) {
            throw new BusinessException("只允许修改第一级的菜单");
        }

        List<SysMenuEntity> allNeedChangeAppMenus = new ArrayList<>();

        //2.找到需要修改的菜单 及其 子菜单
        List<SysMenuEntity> allMenuList = getAllMenuList(null, true);
        SysMenuEntity needParentMenu = new SysMenuEntity();
        for (SysMenuEntity menuEntity : allMenuList) {
            if (menuEntity.getId().equals(menuId)) {
                needParentMenu = menuEntity;
                break;
            }
        }
        getAllChildMenuList(needParentMenu, allNeedChangeAppMenus);
        allNeedChangeAppMenus.add(curMenu);

        for (SysMenuEntity menu : allNeedChangeAppMenus) {
            menu.setAppId(appId);
        }
        boolean res = this.updateBatchById(allNeedChangeAppMenus);
        if (!res) {
            throw new BusinessException("修改失败");
        }
        return true;
    }

    /**
     * 获取指定菜单下的所有子菜单
     *
     * @param
     * @return
     */
    private void getAllChildMenuList(SysMenuEntity parentMenu, List<SysMenuEntity> allNeedChangeAppMenus) {
        if (parentMenu.getChildren() == null || parentMenu.getChildren().size() <= 0) {
            return;
        }

        for (SysMenuEntity menu : parentMenu.getChildren()) {
            allNeedChangeAppMenus.add(menu);

            getAllChildMenuList(menu, allNeedChangeAppMenus);
        }

    }

    /**
     * 获取所有菜单列表
     *
     * @param userMenuIdList      用户有权限的菜单列表， 如果null，获取全部， 如果size为0， 则返回空
     * @param isIncludeButtonMenu 是否包含按钮类型菜单
     */
    private List<SysMenuEntity> getAllMenuList(List<Long> userMenuIdList, boolean isIncludeButtonMenu) {
        // 01 获取全部菜单， 内存中组合成树结构
        List<SysMenuEntity> allMenuList = getMenuList(isIncludeButtonMenu);

        if (userMenuIdList == null) {
            userMenuIdList = allMenuList.stream().map(SysMenuEntity::getId).collect(Collectors.toList());
        }

        if (userMenuIdList.size() == 0) {
            return null;
        }

        //过滤有权限的菜单列表一级根菜单列表,
        List<SysMenuEntity> userMenuList = new ArrayList<>();
        List<SysMenuEntity> rootMenuList = new ArrayList<>();

        for (SysMenuEntity entity : allMenuList) {

            if (userMenuIdList.contains(entity.getId())) {
                if (entity.getPid().equals(0L)) {
                    rootMenuList.add(entity);
                }
                userMenuList.add(entity);
            }
        }

        //递归从userMenuList获取子菜单
        getMenuTreeList(rootMenuList, userMenuList);

        return rootMenuList;
    }

    /**
     * 获取所有菜单列表
     *
     * @param userMenuIdList      用户有权限的菜单列表， 如果null，获取全部， 如果size为0， 则返回空
     * @param isIncludeButtonMenu 是否包含按钮类型菜单
     */
    private List<SysMenuEntity> getAllMenuList(List<Long> userMenuIdList, boolean isIncludeButtonMenu, Long type) {
        // 01 获取全部菜单， 内存中组合成树结构
        List<SysMenuEntity> allMenuList = getMenuList(isIncludeButtonMenu);

        if (userMenuIdList == null) {
            userMenuIdList = allMenuList.stream().map(SysMenuEntity::getId).collect(Collectors.toList());
        }

        if (userMenuIdList.size() == 0) {
            return null;
        }

        //过滤有权限的菜单列表一级根菜单列表,
        List<SysMenuEntity> userMenuList = new ArrayList<>();
        List<SysMenuEntity> rootMenuList = new ArrayList<>();

        for (SysMenuEntity entity : allMenuList) {

            if (userMenuIdList.contains(entity.getId())) {
                if (entity.getPid().equals(0L)) {
                    rootMenuList.add(entity);
                }
                userMenuList.add(entity);
            }
        }

        //递归从userMenuList获取子菜单
        getMenuTreeList(rootMenuList, userMenuList);

        return rootMenuList;
    }

    /**
     * 获取指定
     *
     * @param userMenuIdList      用户有权限的菜单列表， 如果null，获取全部， 如果size为0， 则返回空
     * @param appId               应用ID
     * @param isIncludeButtonMenu 是否包含按钮类型菜单
     * @return
     */
    private List<SysMenuEntity> getAllMenuListByAppId(List<Long> userMenuIdList, Long appId, boolean isIncludeButtonMenu) {
        // 01 获取全部菜单， 内存中组合成树结构
        List<SysMenuEntity> allMenuList = getMenuList(isIncludeButtonMenu);
        List<SysMenuEntity> allMenuByAppId = new ArrayList<>();
        for (SysMenuEntity sysMenuEntity : allMenuList) {
            if (sysMenuEntity.getAppId() != null && sysMenuEntity.getAppId().equals(appId)) {
                allMenuByAppId.add(sysMenuEntity);
            }
        }
        if (userMenuIdList == null) {
            userMenuIdList = allMenuByAppId.stream().map(SysMenuEntity::getId).collect(Collectors.toList());
        }

        if (userMenuIdList.size() == 0) {
            return null;
        }

        //过滤有权限的菜单列表一级根菜单列表,
        List<SysMenuEntity> userMenuList = new ArrayList<>();
        List<SysMenuEntity> rootMenuList = new ArrayList<>();

        for (SysMenuEntity entity : allMenuByAppId) {

            if (userMenuIdList.contains(entity.getId())) {
                if (entity.getPid().equals(0L)) {
                    rootMenuList.add(entity);
                }
                userMenuList.add(entity);
            }
        }

        //递归从userMenuList获取子菜单
        getMenuTreeList(rootMenuList, userMenuList);

        return rootMenuList;
    }

//    /**
//     * @param userMenuIdList      用户有权限的菜单列表， 如果null，获取全部， 如果size为0， 则返回空
//     * @param appList             应用列表
//     * @param isIncludeButtonMenu 是否包含按钮菜单
//     */
//    private List<SysApplicationEntity> getAllMenuListByAppList(List<Long> userMenuIdList, List<SysApplicationEntity> appList, boolean isIncludeButtonMenu) {
//
//        List<SysApplicationEntity> result = new ArrayList<>();
//        if (appList == null || appList.size() <= 0) {
//            return null;
//        }
//        // 01 获取全部菜单， 内存中组合成树结构
//        List<SysMenuEntity> allMenuList = getMenuList(isIncludeButtonMenu);
//        if (allMenuList != null) {
//            if (userMenuIdList == null) {
//                userMenuIdList = allMenuList.stream().map(SysMenuEntity::getId).collect(Collectors.toList());
//            }
//            if (userMenuIdList.size() == 0) {
//                return null;
//            }
//            //2.按应用id分组  将每组弄成树形结构
//            Map<Long, List<SysMenuEntity>> appMenuMap = allMenuList.stream().collect(Collectors.groupingBy(SysMenuEntity::getAppId));
//            for (Map.Entry<Long, List<SysMenuEntity>> entry : appMenuMap.entrySet()) {
//                //过滤有权限的菜单列表一级根菜单列表,
//                List<SysMenuEntity> userMenuList = new ArrayList<>();
//                List<SysMenuEntity> rootMenuList = new ArrayList<>();
//
//                for (SysMenuEntity entity : entry.getValue()) {
//                    if (userMenuIdList.contains(entity.getId())) {
//                        if (entity.getPid().equals(0L)) {
//                            rootMenuList.add(entity);
//                        }
//                        userMenuList.add(entity);
//                    }
//                }
//                //递归从userMenuList获取子菜单
//                getMenuTreeList(rootMenuList, userMenuList);
//                //排除掉为启用的菜单
//                if (rootMenuList != null && rootMenuList.size() > 0) {
//                    rootMenuList = rootMenuList.stream().filter(s -> s.getEnabled() != ResourcesStatusEnum.OFF.getKey()).collect(Collectors.toList());
//                }
//                for (SysApplicationEntity app : appList) {
//                    if (app.getId().equals(entry.getKey())) {
//                        if (entry.getValue() != null && entry.getValue().size() > 0) {
//                            //如果 应用菜单为空  则不返回应用
//                            app.setMenus(rootMenuList);
//                            result.add(app);
//                        }
//                    }
//                }
//            }
//        }
//        return result;
//    }


    private List<SysMenuEntity> getMenuTreeList(List<SysMenuEntity> parentMenuList, List<SysMenuEntity> userMenuList) {
        List<SysMenuEntity> subMenuList = new ArrayList<SysMenuEntity>();

        if (CollectionUtils.isEmpty(parentMenuList)) {
            return subMenuList;
        }

        for (SysMenuEntity entity : parentMenuList) {
            if (entity.getType() == Const.MenuType.MENU.getValue()) {
                List<SysMenuEntity> curChildMenuList = new ArrayList<>();
                for (SysMenuEntity userMenu : userMenuList) {
                    if (userMenu.getPid().equals(entity.getId())) {
                        userMenu.setParentName(entity.getName());
                        curChildMenuList.add(userMenu);
                    }
                }

                //递归获取子节点
                getMenuTreeList(curChildMenuList, userMenuList);
                entity.setChildren(curChildMenuList);
            }

            subMenuList.add(entity);
        }

        return subMenuList;
    }

    private List<SysMenuEntity> getMenuTreeList(List<SysMenuEntity> menuList, List<Long> menuIdList, boolean isIncludeButtonMenu) {
        List<SysMenuEntity> subMenuList = new ArrayList<SysMenuEntity>();

        if (CollectionUtils.isEmpty(menuList)) {
            return subMenuList;
        }

        for (SysMenuEntity entity : menuList) {
            if (!isIncludeButtonMenu) {
                if (entity.getType() == Const.MenuType.BUTTON.getValue()) {
                    continue;
                }
            }

            //目录
            if (entity.getType() == Const.MenuType.MENU.getValue()) {
                List<SysMenuEntity> curMenuList = queryListParentId(entity.getId(), menuIdList, isIncludeButtonMenu);

                // 递归获取
                List<SysMenuEntity> curChildMenuList = getMenuTreeList(curMenuList, menuIdList, isIncludeButtonMenu);
                entity.setChildren(curChildMenuList);
            }

            subMenuList.add(entity);
        }

        return subMenuList;
    }

    /**
     * 判断制定角色是否有入组权限
     *
     * @param roleId
     * @return
     */
    @Override
    public Boolean hasInputCasePermission(Long roleId) {
        if (roleId == null) {
            //超级管理员
            return true;
        }
        QueryWrapper<SysMenuEntity> qw = new QueryWrapper<>();
        qw.eq("b.role_id", roleId)
                .eq("a.permissions", "pat:patient:save");
        List<SysMenuEntity> list = baseMapper.getByQuery(qw);
        return list == null || list.size() == 0 ? false : true;
    }
}
