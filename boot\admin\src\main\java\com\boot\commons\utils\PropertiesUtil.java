package com.boot.commons.utils;


import com.boot.commons.utils.file.SourceFileUtils;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Properties;


@Data
public class PropertiesUtil extends Properties {
    private static final Logger log = LogManager.getLogger(PropertiesUtil.class);
    private static Properties instance;
    public final static String CUSTOM_CONFIG_FILE = "custom.properties";

    public static Properties getProperties() {
        return getInstance();
    }

    public static String getKey(String key) {
        Properties props = getInstance();

        return props.getProperty(key, "");
    }

    public static void refresh() {
        instance = null;

        instance = getInstance();
    }

    public static Properties readProperties(String path) throws IOException {
        Properties props = new Properties();
        File file = new File(path);
        String configPropertiesPath = SourceFileUtils.getConfigFilePath(CUSTOM_CONFIG_FILE);
        log.info("properties:" + configPropertiesPath);
        if (!file.exists() && configPropertiesPath.equals(path)) {
            // 如果conf目录找不到再去资源文件查找
            configPropertiesPath = new File(ResourceUtils.getURL("classpath:").getPath())
                    + File.separator + CUSTOM_CONFIG_FILE;
            log.info("properties1:" + configPropertiesPath);
            file = new File(configPropertiesPath);
            if (!file.exists()){
                // 如果不存在就创建，然后写入默认值
                boolean b = file.createNewFile();
            }
        }
        FileInputStream fis = new FileInputStream(file);
        props.load(new InputStreamReader(fis, StandardCharsets.UTF_8));
        return props;
    }

    private static Properties getInstance() {
        if (instance == null) {
            synchronized (PropertiesUtil.class) {
                if (instance == null) {
                    try {
                        instance = readProperties(
                                SourceFileUtils.getConfigFilePath(CUSTOM_CONFIG_FILE)
                        );
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    return instance;
                }
            }
        }

        return instance;
    }

    public static void main(String[] args) {
        Properties properties = PropertiesUtil.getProperties();
        String wjxt = properties.getProperty("wjxt");
//        String url = properties.("wjxt");

        System.out.println(wjxt);
    }

}
