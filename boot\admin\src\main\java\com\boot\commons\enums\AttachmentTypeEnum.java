package com.boot.commons.enums;

/**
 * <AUTHOR>
 * @desc : 文件类型枚举类
 * @create 2021-03-24
 */
public enum AttachmentTypeEnum {


    /**
     * 文件夹
     */
    DIR(0, "dir", "文件夹"),
    /**
     * 图片
     */
    IMAGE(1, "image", "图片"),
    /**
     * 审核不通过
     */
    WORD(2, "word", "文档"),

    VIDEO(3, "video", "视频"),

    AUDIO(4,
            "audio",
            "音频"),

    OTHER(5, "others", "其它");
    private int type;
    private String name;
    private String desc;

    AttachmentTypeEnum(int type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static AttachmentTypeEnum getByType(Integer type) {
        if (type != null) {
            for (AttachmentTypeEnum attachmentTypeEnum : AttachmentTypeEnum.values()) {
                if (attachmentTypeEnum.getType() == type) {
                    return attachmentTypeEnum;
                }
            }
        }
        return null;
    }


}
