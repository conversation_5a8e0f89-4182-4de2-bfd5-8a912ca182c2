package com.boot.modules.sys.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boot.modules.sys.entity.SysLogEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统日志
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysLogDao extends BaseMapper<SysLogEntity> {

    /**
     * 删除指定日期外的日志数据
     *
     * @param deleteTimeStr
     * @return
     */
    boolean deleteBeforeDays( String deleteTimeStr);
}
