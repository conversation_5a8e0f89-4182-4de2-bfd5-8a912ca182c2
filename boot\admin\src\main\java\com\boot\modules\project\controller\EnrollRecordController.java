package com.boot.modules.project.controller;

import com.boot.commons.annotation.Login;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.modules.project.service.EnrollDetailService;
import com.boot.modules.project.service.EnrollRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "执行记录管理")
@RestController
@RequestMapping("/enroll")
public class EnrollRecordController {
    @Resource
    private EnrollRecordService enrollRecordService;

    @Resource
    private EnrollDetailService enrollDetailService;

    @ApiOperation(
            value = "分页查询任务执行记录",
            notes = "分页查询任务执行记录"
    )
    @GetMapping("/record/page")
    public Result recordPage(@RequestParam Map<String, Object> params) {
        return R.success(enrollRecordService.getAllByPage(params));
    }
    @ApiOperation(
            value = "分页查询任务执行详情",
            notes = "分页查询任务执行详情"
    )
    @GetMapping("/detail/page")
    public Result detailPage(@RequestParam Map<String, Object> params) {
        return R.success(enrollDetailService.getAllByPage(params));
    }
}
