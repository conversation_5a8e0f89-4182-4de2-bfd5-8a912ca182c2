<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysRoleDeptDao">
	<cache-ref namespace="com.boot.modules.sys.dao.SysRoleDeptDao"/>
	<select id="queryDeptIdList" resultType="long">
		select dept_id from rp_sys_role_dept where role_id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</select>

	<delete id="deleteBatch">
		delete from rp_sys_role_dept where role_id in
		<foreach item="roleId" collection="array" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</delete>

</mapper>