package com.boot.modules.project.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc : 字典分类表实体类
 * @create 2021-02-25
 */
@Data
@TableName("rp_group_config")
public class GroupConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private Long projectId;

    private Long subProjectId;

    /**
     * 表达式
     */
    private String expression;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序 ，默认为1
     */
    private int sort;

    /**
     * 必要与否(1-是；0-否)
     */
    private int necessary;

    /**
     * 条件类型（0-纳入；1-排除）
     */
    private int type;

    /**
     * 是否是医联体的条件(1-是；0-否)
     */
    private Integer isYlt = 0;
}
