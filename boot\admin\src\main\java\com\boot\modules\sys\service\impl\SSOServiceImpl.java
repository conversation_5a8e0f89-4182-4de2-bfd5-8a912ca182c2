package com.boot.modules.sys.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.constants.ConfigConst;
import com.boot.commons.constants.Const;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.SSOUtil;
import com.boot.commons.utils.SpringContextUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import com.boot.modules.sys.service.SSOService;
import com.boot.modules.sys.service.SysUserService;
import com.boot.modules.sys.service.SysUserTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

/**
 * 单点登录业务接口实现类
 * <AUTHOR>
 * @createTime 2022年2月09日 18:21:00
 */
@Slf4j
@Service
public class SSOServiceImpl implements SSOService {
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserTokenService sysUserTokenService;

    @Resource
    private SSOConfigurationProperties ssoConfigurationProperties;

    private boolean loginType = BootAdminProperties.userLgoin;

    @Override
    public SysUserEntity ssoLogin(HttpServletRequest request, HttpServletResponse response, String code) throws Exception {
        // 如果code不为空
        log.info("跳转科研系统");
//        String indexUrl = ConfigProperties.getKey(ConfigConst.sso_redirect_uri);
        StringBuffer requestURL = request.getRequestURL();
        log.info("requestURL: " + requestURL);

        SysUserService userService = SpringContextUtils.getBean(SysUserService.class);
        // 1、获取授权码code
        // code不存在
        if (StringUtils.isEmpty(code)){
            //授权码为空，走正常登录逻辑进行登录
            log.error("授权码为空");
            return null;
        }

        // 2、获取token
        String accessTokenResponse = SSOUtil.getAccessTokenByCode(code);
        if (StringUtils.isEmpty(accessTokenResponse)){
            log.error("获取不到单点登录access_token信息");
            throw new BusinessException("获取access_token失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(accessTokenResponse);
        String accessToken = jsonObject.getString(ConfigConst.sso_access_token);
        String refreshToken = jsonObject.getString(ConfigConst.sso_refresh_token);
//        session.setAttribute(ConfigConst.sso_access_token, accessToken);
//        session.setAttribute(ConfigConst.sso_refresh_token, refreshToken);

        // 3、获取用户信息
        String userResponse = SSOUtil.getUserInfoByAccessToken(accessToken);
        if (StringUtils.isEmpty(userResponse)){
            log.error("获取不到单点登录用户信息");
            throw new BusinessException("获取用户信息失败");
        }
        JSONObject jsonObject2 = JSONObject.parseObject(userResponse);
        String userCode = jsonObject2.getString(ConfigConst.sso_user_code);
        log.info("userCode: " + userCode);
        //获取自定his工号用户信息
        SysUserEntity userInfo;
        userInfo = userService.queryByUserCode(userCode);
        if (userInfo == null){
            log.error("单点登录用户不存在");
            throw new BusinessException("用户不存在");
        }
        log.info("localUserInfo: " + userInfo);
        //单点登录成功，则手动生成token
//        String token = createTokenByUserName("csm", request, response);
//        request.getSession().setAttribute("token", token);
//        request.getSession().setAttribute("loginName", userInfo.getUsername());

        return userInfo;
    }


    @Override
    public boolean ssoLogout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return false;
    }

    @Override
    public String createTokenByUserName(String userName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        SysUserEntity user;

//        response.setContentType("application/json; charset=UTF-8");
//        PrintWriter out = response.getWriter();

        //当前登录ip地址和时间
        String loginIp = request.getRemoteAddr();
        //定义token相关信息
        if (BootAdminProperties.adminAccount.equals(userName)) {
            // 超级管理员

            //检验超级管理员的IP登录限制
            SSOUtil.adminLoginIpCheck(request);

            user = new SysUserEntity();
            user.setId(Const.SUPER_ADMIN);
            user.setUsername(userName);
            user.setNickname("超级管理员");
            user.setIsSuperAdmin(1);
            user.setIsSysAdmin(0);
        } else {
            user = sysUserService.queryByUserName(userName);
            if (user == null || user.getId() == null) {
                log.error("用户不存在");
                throw new BusinessException("用户不存在");
            } else {

                if (CollectionUtils.isEmpty(user.getRoleNameList())) {
                    log.error("账号未分配系统角色，无法登录");
                    throw new BusinessException("账号未分配系统角色，无法登录");
                }
            }
        }

        return loginCheck(user, loginIp);
    }

    /**
     * 判断用户是否已登录方法提取
     */
    private String loginCheck(SysUserEntity user, String loginIp) {
        //获取当前时间，定义判断已登录用户code为001
        SysUserTokenEntity userToken = sysUserTokenService.getById(user.getId());

        if (userToken == null) {
            // 第一次登录
            Result result = sysUserTokenService.createToken(user.getId(), loginIp);
            if (result == null) {
                log.error("生成token失败");
                throw new BusinessException("生成token失败");
            }

            return result.getToken();
        }

        String userIp = userToken.getLoginIp();
        Date expireTime = userToken.getExpireTime();
        Date nowTime = new Date();

        if (loginType) {
            if (!nowTime.before(expireTime)) {
                Result result = sysUserTokenService.createToken(user.getId(), loginIp);
                if (result == null) {
                    log.error("生成token失败");
                    throw new BusinessException("生成token失败");
                }
                return result.getToken();
            }
            return allowLogin(userToken, user, loginIp);
        } else {
            if (!loginIp.equals(userIp)) {
                //生成token，并保存到数据库
                Result result = sysUserTokenService.createToken(user.getId(), loginIp);
                if (result == null) {
                    log.error("生成token失败");
                    throw new BusinessException("生成token失败");
                }
                return result.getToken();
            }
        }

        return allowLogin(userToken, user, loginIp);

    }

    /**
     * 允许多人同时在线登录
     *
     * @param userToken
     * @param user
     * @param loginIp
     * @return
     */
    private String allowLogin(SysUserTokenEntity userToken, SysUserEntity user, String loginIp) {
        Date nowTime = new Date();
        userToken.setExpireTime(new Date(nowTime.getTime() + (3600 * 12 * 1000)));
        userToken.setLoginIp(loginIp);
        boolean flag = sysUserTokenService.updateToken(userToken);
        if (user.getLoginErrorCount() != null && user.getLoginErrorCount() != 0) {
            user.setLoginErrorCount(0);
            flag = flag && sysUserService.updateById(user);
        }
        if (flag) {
            user.setToken(userToken.getToken());
            Result result = R.success();
            result.setData(user);
            result.setToken(userToken.getToken());
            result.setExpire(3600 * 12);

            return userToken.getToken();
        }

        return null;
    }

}
