package com.boot.modules.patient.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.utils.DateUtils;
import com.boot.modules.patient.dao.DeptRecommendCountDao;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.DeptRecommendCountEntity;
import com.boot.modules.patient.service.DeptRecommendCountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeptRecommendCountServiceImpl extends ServiceImpl<DeptRecommendCountDao, DeptRecommendCountEntity> implements DeptRecommendCountService {
    @Resource
    private DoctorRecommendationDao doctorRecommendationDao;

    @Override
    public void updateNewDate() {
        // 删除今天的记录
        this.remove(new QueryWrapper<DeptRecommendCountEntity>().lambda().eq(DeptRecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr()));

        // 所有就诊类型累计
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        List<ReportDto> allRecommendCount = this.doctorRecommendationDao.groupDept(qw);
        Map<Long, Integer> map = CollectionUtils.isEmpty(allRecommendCount) ? new LinkedHashMap<>() : allRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));
        List<DeptRecommendCountEntity> allType = new ArrayList<>();
        for (Long deptId : map.keySet()) {
            DeptRecommendCountEntity deptRecommendCountEntity = new DeptRecommendCountEntity();
            deptRecommendCountEntity.setDeptId(deptId);
            deptRecommendCountEntity.setAdmType("ALL");
            deptRecommendCountEntity.setRecommendCount(map.get(deptId));
            deptRecommendCountEntity.setCreateTime(DateUtils.getNowTimeStr());
            allType.add(deptRecommendCountEntity);
        }
        this.saveBatch(allType);

        // 门诊累计
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        qwO.eq("a.adm_type", "O");
        List<ReportDto> allORecommendCount = this.doctorRecommendationDao.groupDept(qwO);
        Map<Long, Integer> mapO = CollectionUtils.isEmpty(allORecommendCount) ? new LinkedHashMap<>() : allORecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));
        List<DeptRecommendCountEntity> oType = new ArrayList<>();
        for (Long deptId : mapO.keySet()) {
            DeptRecommendCountEntity deptRecommendCountEntity = new DeptRecommendCountEntity();
            deptRecommendCountEntity.setDeptId(deptId);
            deptRecommendCountEntity.setAdmType("O");
            deptRecommendCountEntity.setRecommendCount(mapO.get(deptId));
            deptRecommendCountEntity.setCreateTime(DateUtils.getNowTimeStr());
            oType.add(deptRecommendCountEntity);
        }
        this.saveBatch(oType);

        // 住院累计
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        qwI.eq("a.adm_type", "I");
        List<ReportDto> allIRecommendCount = this.doctorRecommendationDao.groupDept(qwI);
        Map<Long, Integer> mapI = CollectionUtils.isEmpty(allIRecommendCount) ? new LinkedHashMap<>() : allIRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));
        List<DeptRecommendCountEntity> iType = new ArrayList<>();
        for (Long deptId : mapI.keySet()) {
            DeptRecommendCountEntity deptRecommendCountEntity = new DeptRecommendCountEntity();
            deptRecommendCountEntity.setDeptId(deptId);
            deptRecommendCountEntity.setAdmType("I");
            deptRecommendCountEntity.setRecommendCount(mapI.get(deptId));
            deptRecommendCountEntity.setCreateTime(DateUtils.getNowTimeStr());
            iType.add(deptRecommendCountEntity);
        }
        this.saveBatch(iType);

        // 急诊累计
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        qwE.eq("a.adm_type", "E");
        List<ReportDto> allERecommendCount = this.doctorRecommendationDao.groupDept(qwE);
        Map<Long, Integer> mapE = CollectionUtils.isEmpty(allERecommendCount) ? new LinkedHashMap<>() : allERecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));
        List<DeptRecommendCountEntity> eType = new ArrayList<>();
        for (Long deptId : mapE.keySet()) {
            DeptRecommendCountEntity deptRecommendCountEntity = new DeptRecommendCountEntity();
            deptRecommendCountEntity.setDeptId(deptId);
            deptRecommendCountEntity.setAdmType("E");
            deptRecommendCountEntity.setRecommendCount(mapE.get(deptId));
            deptRecommendCountEntity.setCreateTime(DateUtils.getNowTimeStr());
            eType.add(deptRecommendCountEntity);
        }
        this.saveBatch(eType);
    }
}
