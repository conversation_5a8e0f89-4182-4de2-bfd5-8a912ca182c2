package com.boot.modules.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.AesUtil;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.service.ProjectRoleSettingService;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc : 子项目管理
 * @create 2021-03-18
 */
@Api(tags = "子项目管理")
@RestController
@RequestMapping("/proj/subProject")
public class SubProjectController extends AbstractController {

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectRoleSettingService projectRoleSettingService;

    @Resource
    SSOConfigurationProperties ssoConfig;

    @ApiOperation(value = "根据项目id获取当前登录用户的子项目")
    @GetMapping("/list")
    @RequiresPermissions("proj:subproject:list")
    public Result list(@RequestParam("projectId") Long projectId) {

        if (projectId == null) {
            return R.fail();
        }
        Long userId = getUserId();

        List<SubProjectVo> list = subProjectService.getByProjectId(projectId, userId);

        return R.success(list);
    }

    @ApiOperation(value = "根据项目id获取子项目")
    @GetMapping("/list/{projectId}")
    public Result listByProjectId(@PathVariable("projectId") Long projectId) {

        if (projectId == null) {
            return R.fail();
        }

        List<SubProjectEntity> list = subProjectService.list(
                new QueryWrapper<SubProjectEntity>().lambda().eq(SubProjectEntity::getProjectId, projectId)
        );

        return R.success(list);
    }

    @ApiOperation(value = "根据指定项目id获取子项目")
    @GetMapping("/list/getProjectById")
    public Result getListByProjectId(@RequestParam(value = "projectId") Long projectId) {

        if (projectId == null) {
            return R.fail();
        }

        List<SubProjectEntity> list = subProjectService.list(
                new QueryWrapper<SubProjectEntity>().lambda().eq(SubProjectEntity::getProjectId, projectId)
        );

        return R.success(list);
    }


    @ApiOperation(
            value = "获取指定子项目的信息",
            notes = "获取指定子项目的信息"
    )
    @GetMapping("/{id}")
    @RequiresPermissions("proj:subproject:info")
    public Result info(@PathVariable("id") Long id) {
        SubProjectEntity subProjectEntity = subProjectService.getById(id);
        if (subProjectEntity.getEnableApi().equals(1)) {
            String subProjectId = AesUtil.encrypt(id.toString(), ssoConfig.getAppCode());
            subProjectEntity.setGuid(subProjectId);
        }

        return R.success(subProjectEntity);
    }

    @ApiOperation(
            value = "新增子项目",
            notes = "新增子项目"
    )
    @SysLog("新增子项目")
    @PostMapping()
    @RequiresPermissions("proj:subproject:save")
    public Result save(@RequestBody SubProjectEntity subProjectEntity) {
        ValidatorUtils.validateEntity(subProjectEntity, AddGroup.class);

        //唯一性验证---研究组名称唯一性验证
        QueryWrapper<SubProjectEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(SubProjectEntity::getName, subProjectEntity.getName())
                .eq(SubProjectEntity::getProjectId, subProjectEntity.getProjectId());
        int count = subProjectService.count(qw);
        if (count > 0) {
            return R.fail("该子项目名称已存在");
        }

        subProjectEntity.setCreateTime(new Date());
        subProjectEntity.setCreateUserId(getUserId());
        boolean res = subProjectService.add(subProjectEntity, null);

        //新增数据权限默认配置
        res = res && projectRoleSettingService.addConfigInit(subProjectEntity);

        return res ? R.success("新增成功") : R.fail("新增失败");
    }

    @ApiOperation(
            value = "修改子项目",
            notes = "修改子项目"
    )
    @SysLog("修改子项目")
    @PutMapping()
    @RequiresPermissions("proj:subproject:update")
    public Result update(@RequestBody SubProjectEntity researchGroup) {

        //唯一性验证---研究组名称唯一性验证
        SubProjectEntity old = subProjectService.getById(researchGroup.getId());
        if (!old.getName().equals(researchGroup.getName())) {
            QueryWrapper<SubProjectEntity> qw = new QueryWrapper<>();
            qw.lambda()
                    .eq(SubProjectEntity::getName, researchGroup.getName())
                    .eq(SubProjectEntity::getProjectId, researchGroup.getProjectId());
            int count = subProjectService.count(qw);
            if (count > 0) {
                return R.fail("该子项目名称已存在");
            }
        }
        researchGroup.setUpdateTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));

        boolean res = subProjectService.updateById(researchGroup);
        return res ? R.success("修改成功") : R.fail("修改失败");
    }

    @ApiOperation(
            value = "删除子项目",
            notes = "删除子项目"
    )
    @SysLog("删除子项目")
    @DeleteMapping()
    @RequiresPermissions("proj:subproject:delete")
    public Result delete(@RequestBody Long[] ids) {
        subProjectService.deleteBatch(Arrays.asList(ids));

        return R.success("删除成功", true);
    }

    /**
     * 子项目切换私密状态(功能改造)
     * chenwei 2022/10/24
     * @param subProjectId
     * @return
     */
    @ApiOperation(
            value = "子项目切换私密状态",
            notes = "子项目切换私密状态"
    )
    @GetMapping("/visual")
    public Result visual (@RequestParam Long subProjectId) {
        boolean ret = subProjectService.updateVisual(subProjectId);
        return ret ?  R.success("切换成功") : R.fail("切换失败");
    }
}

