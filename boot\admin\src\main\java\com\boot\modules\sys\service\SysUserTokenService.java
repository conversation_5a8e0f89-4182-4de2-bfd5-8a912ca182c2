package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.result.Result;
import com.boot.modules.sys.entity.SysUserTokenEntity;

import java.util.List;

/**
 * 用户Token
 *
 * <AUTHOR>
 */
public interface SysUserTokenService extends IService<SysUserTokenEntity> {

    /**
     * 生成token
     *
     * @param userId 用户ID
     */
    Result createToken(long userId, String loginIp);

    Result createToken(long userId, String loginIp, Long subPatientId);

    Result createToken(Long userId, String loginIp, String idCard);


    /**
     * 退出，修改token值
     *
     * @param userId 用户ID
     */
    void logout(long userId);

    /**
     * 修改token过期时间
     */
    boolean updateToken(SysUserTokenEntity sysUserTokenEntity);

	// modify by cx  根据userId获取token信息
    List<SysUserTokenEntity> getByUserId(String userId, Long subPatientId);
}
