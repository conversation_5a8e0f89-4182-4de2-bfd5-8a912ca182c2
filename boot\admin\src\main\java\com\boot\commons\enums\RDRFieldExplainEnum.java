package com.boot.commons.enums;

import java.util.HashMap;
import java.util.Map;

public enum RDRFieldExplainEnum {
    //字段说明：患者唯一ID、就诊ID、时间戳、就诊时间、医疗机构编码、其他（1/2/3/4/5/0）
    /**
     * 患者唯一ID
     */
    EMPID(1, "患者唯一ID"),
    /**
     * 就诊ID
     */
    ADM_ID(2, "就诊ID"),
    /**
     * 时间戳
     */
    TIME(3, "时间戳"),
    /**
     * 就诊时间
     */
    ADM_TIME(4, "就诊时间"),
    /**
     * 医疗机构编码
     */
    MED_ORG(5, "医疗机构编码"),
    /**
     * 医嘱ID
     */
    ORD_ID(6, "医嘱ID"),

    /**
     * 报告号
     */
    REPORT_NO(7, "报告ID"),
    /**
     * 其他
     */
    OTHER(0, "其他"),
    ;
    private int type;
    private String name;

    RDRFieldExplainEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static Map<String, Object> typeMap = new HashMap<>();
    public static Map<String, RDRFieldExplainEnum> typeEnumMap = new HashMap<>();

    static {
        RDRFieldExplainEnum[] types = RDRFieldExplainEnum.values();
        for (RDRFieldExplainEnum type : types) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", type.type);
            map.put("name", type.name);
            typeMap.put(String.valueOf(type.type), map);
            typeEnumMap.put(String.valueOf(type.type), type);
        }
    }
}
