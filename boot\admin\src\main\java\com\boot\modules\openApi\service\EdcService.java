package com.boot.modules.openApi.service;

import com.boot.modules.project.vo.ProjectVo;

/**
 * 调用 edc 外部接口服务接口
 */
public interface EdcService {

    /**
     * 入组患者
     * @param empi
     * @param projectId
     * @param userId
     */
    void intoPatient(String empi, Long projectId, Long edcProjectId, Long userId);

    /**
     * 入组患者
     * @param empi
     * @param regno
     * @param projectId
     * @param userId
     */
    void intoPatient(String empi, String regno,Long projectId, Long edcProjectId, Long userId);

    /**
     * 新增项目
     * @param projectVo
     */
    void addProject(ProjectVo projectVo);
}
