package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.constants.Const;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.ClientInfoUtil;
import com.boot.modules.sys.dao.*;
import com.boot.modules.sys.entity.*;
import com.boot.modules.sys.service.ShiroService;
import com.boot.modules.sys.service.SysRoleDeptService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ShiroServiceImpl implements ShiroService {
    @Resource
    private SysMenuDao sysMenuDao;

    @Resource
    private SysUserDao sysUserDao;

    @Resource
    private SysRoleMenuDao sysRoleMenuDao;

    @Resource
    private SysRoleDeptDao sysRoleDeptDao;

    @Resource
    private SysUserTokenDao sysUserTokenDao;

    @Resource
    private SysRoleDeptService roleDeptService;

    @Resource
    private SysRoleDao roleDao;

    @Resource
    private SysUserRoleDao userRoleDao;

    @Resource
    private SysDeptDao deptDao;

    @Resource
    private SysMenuDao menuDao;

    private boolean distinguishInAndOutNet = BootAdminProperties.distinguishInAndOutNet;

    @Override
    public Set<String> getUserPermissions(long userId) {
        List<String> permsList;

        //系统管理员，拥有最高权限
        if (userId == Const.SUPER_ADMIN) {
            List<SysMenuEntity> menuList = sysMenuDao.selectList(null);
            permsList = new ArrayList<>(menuList.size());
            for (SysMenuEntity menu : menuList) {
                permsList.add(menu.getPermissions());
            }
        } else {
            if (distinguishInAndOutNet) {
                long type = ClientInfoUtil.netType();
                permsList = menuDao.queryAllPermsByType(userId, type);
            } else {
                permsList = menuDao.queryAllPerms(userId);
            }
        }

        //用户权限列表
        Set<String> permsSet = new HashSet<>();
        for (String perms : permsList) {
            if (StringUtils.isBlank(perms)) {
                continue;
            }
            permsSet.addAll(Arrays.asList(perms.trim().split(",")));
        }
        return permsSet;
    }

    @Override
    public List<SysRoleMenuEntity> getRoleMenuPermissions(Long roleId) {
        if (roleId == null) {
            roleId = 0L;
        }
        //区分内外网(功能改造) 新增入参字段type 1-内网 2-外网
        //chenwei 2022/10/24
        List<SysRoleMenuEntity> roleMenuEntities = sysRoleMenuDao.selectList(
                new QueryWrapper<SysRoleMenuEntity>()
                        .lambda()
                        .eq(SysRoleMenuEntity::getRoleId, roleId)
        );

        return roleMenuEntities;
    }

    @Override
    public List<SysRoleMenuEntity> getRoleMenuPermissions(Long roleId, Long type) {
        if (roleId == null) {
            roleId = 0L;
        }
        //区分内外网(功能改造) 新增入参字段type 1-内网 2-外网
        //chenwei 2022/10/24
        List<SysRoleMenuEntity> roleMenuEntities = sysRoleMenuDao.selectList(
                new QueryWrapper<SysRoleMenuEntity>()
                        .lambda()
                        .eq(SysRoleMenuEntity::getRoleId, roleId)
                        .eq(SysRoleMenuEntity::getType , type)
        );

        return roleMenuEntities;
    }

    @Override
    public List<SysRoleDeptEntity> getRoleDeptPermissions(Long roleId) {
        List<SysRoleDeptEntity> roleDeptEntities = sysRoleDeptDao.selectList(
                new QueryWrapper<SysRoleDeptEntity>()
                        .lambda()
                        .eq(SysRoleDeptEntity::getRoleId, roleId)
        );

        return roleDeptEntities;
    }

    @Override
    public SysUserTokenEntity queryByToken(String token) {
        List<SysUserTokenEntity> entities = sysUserTokenDao.selectList(
                new QueryWrapper<SysUserTokenEntity>()
                        .lambda()
                        .eq(SysUserTokenEntity::getToken, token)
        );
        return !CollectionUtils.isEmpty(entities) ? entities.get(0) : null;
    }

    @Override
    public SysUserEntity queryUser(Long userId) {
        SysUserEntity sysUserEntity = null;
        // TODO 临时处理方案， 这里其他地方不应该随访调用的， 需要处理
        // 如果是超级管理员
        if (userId != null && userId.equals(Const.SUPER_ADMIN)) {
            sysUserEntity = new SysUserEntity();
            sysUserEntity.setId(0L);
            sysUserEntity.setUsername(BootAdminProperties.adminAccount);
            sysUserEntity.setNickname("超级管理员");
            sysUserEntity.setIsSuperAdmin(1);
            sysUserEntity.setIsSysAdmin(0);
        } else {
            sysUserEntity = sysUserDao.selectById(userId);
            List<SysDeptEntity> deptList = deptDao.selectList(null);
            // 查询用户系统角色
            List<SysUserRoleEntity> userRoleList = userRoleDao.selectList(null);
            List<SysRoleEntity> roleList = roleDao.selectList(null);
            List<Long> roleIds = userRoleList.stream()
                    .filter(s -> s.getUserId().equals(userId))
                    .map(SysUserRoleEntity::getRoleId)
                    .collect(Collectors.toList());

            List<String> roleNames = roleList.stream()
                    .filter(s -> roleIds.contains(s.getId()))
                    .map(SysRoleEntity::getRoleName).collect(Collectors.toList());

            List<SysRoleEntity> roles = roleList.stream()
                    .filter(s -> roleIds.contains(s.getId())).collect(Collectors.toList());

            sysUserEntity.setRoleIdList(roleIds);
            sysUserEntity.setRoleNameList(roleNames);
            sysUserEntity.setIsSysAdmin(roles.get(0).getIsSysAdmin());
            sysUserEntity.setIsSuperAdmin(0);

            List<String> deptIdList = Arrays.asList(sysUserEntity.getDeptId().split(","));

            if (! CollectionUtils.isEmpty(deptList)) {
                Map<Long, String> deptIdNameMap = deptList.stream().collect(Collectors.toMap(SysDeptEntity::getId, SysDeptEntity::getName));
                String deptName = "";
                for (String deptId : deptIdList) {
                    if (deptIdNameMap.containsKey(Long.parseLong(deptId))) {
                        if (com.boot.commons.utils.StringUtils.isEmpty(deptName)) {
                            deptName = deptIdNameMap.get(deptId);
                        }else {
                            deptName += "," + deptIdNameMap.get(deptId);
                        }
                        break;
                    }
                }

                sysUserEntity.setDeptName(deptName);
            }


        }

        return sysUserEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDataPermission(Long roleId, Map<String, List<Long>> map) {
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        List<Long> deptIds = map.get("dept");

        if (!CollectionUtils.isEmpty(deptIds)) {
            roleDeptService.saveOrUpdate(roleId, deptIds);
        }

        // todo 其他数据权限保存， 比如脱敏数据， 字段过滤数据
    }
}
