package com.boot.modules.openApi.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.modules.openApi.dao.EdcReqLogDao;
import com.boot.modules.openApi.entity.EdcReqLogEntity;
import com.boot.modules.openApi.service.EdcReqLogService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

@Service
public class EdcReqLogServiceImpl extends ServiceImpl<EdcReqLogDao, EdcReqLogEntity> implements EdcReqLogService {

    @Override
    public Map<String, EdcReqLogEntity> getByEmpiList(Long projectId, List<String> empiList) {

        Map<String, EdcReqLogEntity> map = new HashMap<>();
        
        List<EdcReqLogEntity> logList = this.baseMapper.selectList(new QueryWrapper<EdcReqLogEntity>().lambda()
                .eq(EdcReqLogEntity::getProjectId, projectId)
                .eq(EdcReqLogEntity::getType, 2)
                .in(!CollectionUtils.isEmpty(empiList), EdcReqLogEntity::getEmpiid, empiList)
                .select(EdcReqLogEntity::getStatus, EdcReqLogEntity::getEmpiid, EdcReqLogEntity::getErrMessage));

        if (! CollectionUtils.isEmpty(logList)) {
            logList.stream().forEach(log -> {
                String empiid = log.getEmpiid();
                if (map.containsKey(empiid)) {
                    map.replace(empiid, log);
                }else {
                    map.put(empiid, log);
                }
            });
        }

        return map;
    }
}
