package com.boot.modules.mobile.controller;

import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.modules.mobile.service.DataStatisticsService;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.service.SysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 移动端驾驶舱接口
 */
@Api(tags = "移动端驾驶舱接口")
@RestController
@RequestMapping("/h5/statistics")
@Slf4j
public class H5StatisticsController extends AbstractController {
    @Resource
    private DataStatisticsService dataStatisticsService;

    @Resource
    private SysDeptService deptService;

    @Resource
    private ProjectService projectService;

    /**
     * roleType 1 临管部管理者 2 科室管理者 其他 PI
     * isYlt 是否为医联体用户 1是 0否
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "数据总览", notes = "数据总览")
    @GetMapping({"/overview"})
    public Result overview(@RequestParam Map<String, Object> param) {
        param.put("userId", getUser());
        List<Long> deptIds = getDeptId(); // 用户所属科室
        List<Long> medIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deptIds)) {
            List<SysDeptEntity> deptEntities = deptService.getAll();
            for (Long deptId : deptIds) {
                SysDeptEntity dept = deptService.getTopDeptById(deptEntities, deptId);
                medIds.add(dept.getId());
            }
        }
        param.put("medId", medIds); // 用户所属科室

        Integer roleType = MapUtils.getValue(param, "roleType", Integer.class);
        if (roleType.equals(2)) {
            param.put("deptId", getAdminDeptId());//作为科室管理者的科室
        }
        param.put("userId", getUserId());
        return R.success(dataStatisticsService.overview(param));
    }

//    @ApiOperation(value = "按医院统计推荐数占比", notes = "按医院统计推荐数占比")
//    @GetMapping({"/percent"})
//    public Result percent(@RequestParam Map<String, Object> param) {
//        return R.success(dataStatisticsService.percent(param));
//    }
//
//    @ApiOperation(value = "按医院统计近一年的推荐数", notes = "按医院统计斤一年的推荐数")
//    @GetMapping({"/count"})
//    public Result count(@RequestParam Map<String, Object> param) throws ParseException {
//        return R.success(dataStatisticsService.count(param));
//    }

    /**
     * roleType 1 临管部管理者 2 科室管理者 其他 PI
     * isYlt 是否为医联体用户 1是 0否
     *
     * @param params
     * @return
     */
    @ApiOperation(
            value = "医联体用户获取推荐项目",
            notes = "医联体用户获取推荐项目"
    )
    @GetMapping("/project/list")
    public Result list(@RequestParam Map<String, Object> params) {
        Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
        Integer isYlt = MapUtils.getValue(params, "isYlt", Integer.class);
        // 医联体+临管部管理者时，查用户所在机构
        if (roleType.equals(1) && isYlt.equals(1)) {
            List<Long> deptIds = getDeptId(); // 用户所属科室
            List<Long> medIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(deptIds)) {
                List<SysDeptEntity> deptEntities = deptService.getAll();
                for (Long deptId : deptIds) {
                    SysDeptEntity dept = deptService.getTopDeptById(deptEntities, deptId);
                    medIds.add(dept.getId());
                }
            }
            params.put("medId", medIds); // 用户所属科室
        } else if (isYlt.equals(0)) {
            // 华西时，说明是项目数界面跳转的，需要传medId
            Long medId = MapUtils.getValue(params, "medId", Long.class);
            // 转为数组
            params.put("medId", Arrays.asList(medId));
        }
        if (roleType.equals(2)) {
            params.put("deptId", getAdminDeptId());//作为科室管理者的科室
        }
        // 拼接项目信息
        params.put("userId", getUserId());
        return R.success(dataStatisticsService.project(params));
    }

    /**
     * roleType 1 临管部管理者 2 科室管理者 其他 PI
     * isYlt 是否为医联体用户 1是 0否
     * tab 1项目数 2推荐数 3 入组数
     *
     * @param params
     * @return
     */
    @ApiOperation(
            value = "华西用户推荐统计",
            notes = "华西用户推荐统计"
    )
    @GetMapping("/hx/sort")
    public Result sort(@RequestParam Map<String, Object> params) {
        Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
        if (roleType.equals(2)) {
            params.put("deptId", getAdminDeptId());//作为科室管理者的科室
        }
        params.put("userId", getUserId()); // 登录用户
        // 拼接项目信息
        return R.success(dataStatisticsService.sort(params));
    }
}
