package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.ClientInfoUtil;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.sys.dao.SysUserTokenDao;
import com.boot.modules.sys.entity.SysUserTokenEntity;
import com.boot.modules.sys.service.SysUserTokenService;
import com.boot.modules.sys.shiro.TokenGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
public class SysUserTokenServiceImpl extends ServiceImpl<SysUserTokenDao, SysUserTokenEntity> implements SysUserTokenService {
    /**
     * 12小时后过期
     */
    private static final int EXPIRE = 3600 * 12;


    @Override
    public Result createToken(long userId, String loginIp) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        //当前时间
        Date now = new Date();
        //过期时间
        Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

        //判断是否生成过token
//        SysUserTokenEntity tokenEntity = this.getById(userId);
        // modify by cx 判断是否为PAD端登录
        String userIdStr = ClientInfoUtil.mobileType() + userId;
        List<SysUserTokenEntity> userTokenList = this.getByUserId(userIdStr, null);
        SysUserTokenEntity tokenEntity = CollectionUtils.isEmpty(userTokenList) ? null : userTokenList.get(0);
        if (tokenEntity == null) {
            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userIdStr);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);
            tokenEntity.setLoginIp(loginIp);

            //保存token
            this.save(tokenEntity);
        } else {
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);
            tokenEntity.setLoginIp(loginIp);

            //更新token
            this.updateById(tokenEntity);
        }

        Result r = R.success();
        r.setExpire(EXPIRE);
        r.setToken(token);

        return r;
    }

    @Override
    public Result createToken(long userId, String loginIp, Long subPatientId) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        //当前时间
        Date now = new Date();
        //过期时间
        Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

        // modify by cx 判断是否为PAD端登录
        String userIdStr = ClientInfoUtil.mobileType() + userId;
        List<SysUserTokenEntity> userTokenList = this.getByUserId(userIdStr, subPatientId);
        SysUserTokenEntity tokenEntity = CollectionUtils.isEmpty(userTokenList) ? null : userTokenList.get(0);
        if (tokenEntity == null) {
            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userIdStr);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);
            tokenEntity.setLoginIp(loginIp);
            tokenEntity.setSubProjPatientId(subPatientId);
            //保存token
            this.save(tokenEntity);
        } else {
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);
            tokenEntity.setLoginIp(loginIp);

            //更新token
            this.updateById(tokenEntity);
        }

        Result r = R.success();
        r.setExpire(EXPIRE);
        r.setToken(token);

        return r;
    }

    @Override
    public Result createToken(Long userId, String loginIp, String idCard) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        //当前时间
        Date now = new Date();
        //过期时间
        Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

        //判断是否生成过token
//        SysUserTokenEntity tokenEntity = this.getById(userId);
        // modify by cx 判断是否为PAD端登录
        String userIdStr = ClientInfoUtil.mobileType() + userId;
        List<SysUserTokenEntity> userTokenList = this.getByUserId(userIdStr, null);
        SysUserTokenEntity tokenEntity = CollectionUtils.isEmpty(userTokenList) ? null : userTokenList.get(0);
        if (tokenEntity == null) {
            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userIdStr);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);
            tokenEntity.setLoginIp(loginIp);

            if (userId < 0 && StringUtils.isNotBlank(idCard)) {
                tokenEntity.setIdCard(idCard);
            }

            //保存token
            this.save(tokenEntity);
        } else {
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);
            tokenEntity.setLoginIp(loginIp);

            //更新token
            this.updateById(tokenEntity);
        }

        Result r = R.success();
        r.setExpire(EXPIRE);
        r.setToken(token);

        return r;
    }

    @Override
    public void logout(long userId) {
        //生成一个token
        String token = TokenGenerator.generateValue();
        //修改token
//        SysUserTokenEntity tokenEntity = new SysUserTokenEntity();
//        tokenEntity.setUserId(userIdStr);
//        tokenEntity.setToken(token);
//        this.updateById(tokenEntity);
		// modify by cx 判断是否为PAD端登录
        String userIdStr = ClientInfoUtil.mobileType() + userId;
        UpdateWrapper<SysUserTokenEntity> w = new UpdateWrapper<>();
        w.eq("user_id", userIdStr);
        w.set("token", token);
        this.update(w);
    }

    @Override
    public boolean updateToken(SysUserTokenEntity sysUserTokenEntity) {
        boolean res = this.saveOrUpdate(sysUserTokenEntity);
        if (!res) {
            throw new BusinessException("修改失败");
        }
        return res;
    }

    // modify by cx 根据userId、patientId获取token信息
    @Override
    public List<SysUserTokenEntity> getByUserId(String userId, Long subPatientId) {
        if (subPatientId == null || subPatientId == 0) {
            return this.list(new QueryWrapper<SysUserTokenEntity>().lambda().eq(SysUserTokenEntity::getUserId, userId));
        }
        return this.list(new QueryWrapper<SysUserTokenEntity>().lambda().eq(SysUserTokenEntity::getUserId, userId)
                .eq(SysUserTokenEntity::getSubProjPatientId, subPatientId));
    }
}
