package com.boot.modules.patient.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 统计报表对象
 */
@Data
public class ReportDto implements Serializable {

    /**
     * 科室Id
     */
    private Long deptId;

    /**
     * 科室名
     */
    private String deptName;

    /**
     * 医生Id
     */
    private Long doctorId;

    /**
     * 医生名
     */
    private String doctorName;

    /**
     * 出院人数
     */
    private Integer dischargesCount = 0;

    /**
     * 总病种数
     */
    private Integer totalDisease = 0;

    /**
     * 新增病种数
     */
    private Integer addDisease = 0;

    /**
     * 总项目数
     */
    private Integer totalProject = 0;

    /**
     * 新增项目数
     */
    private Integer addProject = 0;

    /**
     * 累计推荐入组人数
     */
    private Integer totalRecommend = 0;

    /**
     * 新增推荐入组人数
     */
    private Integer addRecommend = 0;

    /**
     * 累计门诊推荐入组人数
     */
    private Integer totalORecommend = 0;

    /**
     * 新增门诊推荐入组人数
     */
    private Integer addORecommend = 0;

    /**
     * 累计住院推荐入组人数
     */
    private Integer totalIRecommend = 0;

    /**
     * 新增住院推荐入组人数
     */
    private Integer addIRecommend = 0;

    /**
     * 累计急诊推荐入组人数
     */
    private Integer totalERecommend = 0;

    /**
     * 新增急诊荐入组人数
     */
    private Integer addERecommend = 0;

    /**
     * 推荐入组率
     */
    private String recommendRatio;

    /**
     * 累计实际入组人数
     */
    private Integer totalInbound = 0;

    /**
     * 新增实际入组人数
     */
    private Integer addInbound = 0;

    /**
     * 累计门诊实际入组人数
     */
    private Integer totalOInbound = 0;

    /**
     * 新增门诊实际入组人数
     */
    private Integer addOInbound = 0;

    /**
     * 累计住院实际入组人数
     */
    private Integer totalIInbound = 0;

    /**
     * 新增住院实际入组人数
     */
    private Integer addIInbound = 0;

    /**
     * 累计急诊实际入组人数
     */
    private Integer totalEInbound = 0;

    /**
     * 新增急诊实际入组人数
     */
    private Integer addEInbound = 0;

    /**
     * 实际入组率
     */
    private String inboundRatio;

    /**
     * 目标推荐人数
     */
    private Long targetRecommend;

    /**
     * 目标推荐完成率
     */
    private String targetRecommendCompeteRatio;

    /**
     * 目标入组人数
     */
    private Long targetInbound;

    /**
     * 目标入组完成率
     */
    private String targetInboundCompeteRatio;


    /**
     * 累计Iit项目实际入组人数
     */
    private Integer totalIitInbound = 0;

    /**
     * 累计GCP项目实际入组人数
     */
    private Integer totalGcpInbound = 0;

    /**
     * 累计前瞻项目实际入组人数
     */
    private Integer totalPreObsInbound = 0;

    /**
     * 累计专病队列实际入组人数
     */
    private Integer totalSpecialInbound = 0;

    /**
     * 累计实际加权入组人数
     */
    private Integer totalWeightInbound = 0;

    /**
     * 新增实际加权入组人数
     */
    private Integer addWeightInbound = 0;

    /**
     * 实际加权入组率
     */
    private String weightInboundRatio = "0.0%";
}
