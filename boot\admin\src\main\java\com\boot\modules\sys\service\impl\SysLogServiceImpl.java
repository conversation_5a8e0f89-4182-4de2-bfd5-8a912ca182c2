package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.MapUtils;
import com.boot.modules.sys.service.SysLogService;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.modules.sys.dao.SysLogDao;
import com.boot.modules.sys.entity.SysLogEntity;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.boot.commons.utils.DateUtils.DATE_TIME_PATTERN;


@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogDao, SysLogEntity> implements SysLogService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        Integer type = MapUtils.getValue(params, "type", Integer.class);
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        String username = MapUtils.getValue(params, "username", String.class);
        Date startTime = MapUtils.getValue(params, "startTime", Date.class);
        Date endTime = MapUtils.getValue(params, "endTime", Date.class);

        QueryWrapper<SysLogEntity> qw = MapUtils.getWrapperByParams(params, "create_date", "rp_sys_log", SysLogEntity.class);
        if (type != null) {
            //此处不需要根据type来分类 当type为1时，只有status 不为空
            qw.lambda().eq(SysLogEntity::getOperaType, type)
                    .eq(status != null, SysLogEntity::getStatus, status)
                    .like(StringUtils.isNotBlank(username), SysLogEntity::getUsername, username)
                    .ge(startTime != null, SysLogEntity::getCreateDate, startTime)
                    .le(endTime != null, SysLogEntity::getCreateDate, endTime);
        }

        IPage<SysLogEntity> page = this.page(
                new Query<SysLogEntity>().getPage(params), qw
        );

        return new PageUtils(page);
    }

    /**
     * 查询全部满足条件的日志信息
     * @param params 过滤条件
     * @return 日志集合
     */
    @Override
    public List<SysLogEntity> queryAll(Map<String, Object> params) {

        Integer type = MapUtils.getValue(params, "type", Integer.class);
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        String username = MapUtils.getValue(params, "username", String.class);
        Date startTime = MapUtils.getValue(params, "startTime", Date.class);
        Date endTime = MapUtils.getValue(params, "endTime", Date.class);

        QueryWrapper<SysLogEntity> qw = MapUtils.getWrapperByParams(params, "create_date", "rp_sys_log", SysLogEntity.class);
        if (type != null) {
            //此处不需要根据type来分类 当type为1时，只有status 不为空
            qw.lambda().eq(SysLogEntity::getOperaType, type)
                    .eq(status != null, SysLogEntity::getStatus, status)
                    .like(StringUtils.isNotBlank(username), SysLogEntity::getUsername, username)
                    .ge(startTime != null, SysLogEntity::getCreateDate, startTime)
                    .le(endTime != null, SysLogEntity::getCreateDate, endTime);
        }


        return list(qw);
    }

    /**
     * 删除指定时间之外的日志数据
     *
     * @param days
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteBeforeDays(Integer days) {
        Date now = new Date();
        Date deleteTime = DateUtils.addDateDays(now, -days);
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_TIME_PATTERN);
        String deleteTimeStr = sdf.format(deleteTime);

        return baseMapper.deleteBeforeDays(deleteTimeStr);
    }
}
