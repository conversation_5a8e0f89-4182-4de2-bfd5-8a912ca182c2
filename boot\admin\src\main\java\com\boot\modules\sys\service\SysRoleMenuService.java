package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.sys.entity.SysRoleMenuEntity;

import java.util.List;


/**
 * 角色与菜单对应关系
 *
 * <AUTHOR>
 */
public interface SysRoleMenuService extends IService<SysRoleMenuEntity> {

    void saveOrUpdate(Long roleId, Long appId, List<Long> menuIdList);

    List<Long> queryMenuIdList(Long roleId);

    /**
     * 2022/10/24 chenwei
     * 区分内外网(功能改造) 新增入参字段type
     */
    void saveOrUpdate(Long roleId, Long appId, List<Long> menuIdList, Long type);

    /**
     * 根据角色ID，获取菜单ID列表
     * 2022/10/24
     * 内外网区分(功能改造)
     */
    List<Long> queryMenuIdList(Long roleId, Long type);

    /**
     * 根据角色ID数组，批量删除
     */
    int deleteBatch(Long[] roleIds);

    /**
     * 获取角色在指定应用的权限
     * @param appId
     * @param roleId
     * @return
     */
    List<String> getRolePermission(Long appId, Long roleId);
}
