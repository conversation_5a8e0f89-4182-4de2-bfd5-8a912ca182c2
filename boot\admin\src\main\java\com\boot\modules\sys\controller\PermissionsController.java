package com.boot.modules.sys.controller;


import com.boot.commons.annotation.SysLog;
import com.boot.commons.constants.Const;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.modules.sys.entity.SysMenuEntity;
import com.boot.modules.sys.entity.SysRoleDeptEntity;
import com.boot.modules.sys.entity.SysRoleMenuEntity;
import com.boot.modules.sys.service.ShiroService;
import com.boot.modules.sys.service.SysMenuService;
import com.boot.modules.sys.service.SysRoleMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @desc: 系统权限
 */

@Api(tags = "系统权限")
@RestController
@RequestMapping("/sys/permission")
public class PermissionsController extends AbstractController {
    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private ShiroService shiroService;

    @Resource
    private SysRoleMenuService roleMenuService;

    private boolean distinguishInAndOutNet = BootAdminProperties.distinguishInAndOutNet;
    /**
     * 获取操作权限
     *
     * @param roleId
     * @return
     */
    @ApiOperation(
            value = "获取角色操作权限",
            notes = "获取角色操作权限")
    @GetMapping(value = "/role/operation/{appId}/{roleId}/{type}")
    public Result getOperaPermission(@PathVariable(value = "appId") Long appId,
                                     @PathVariable(value = "roleId") Long roleId,
                                     @PathVariable(value = "type") Long type) {
        List<SysMenuEntity> menuTreeList = sysMenuService.getMenuTreeList(appId, true);
        List<SysRoleMenuEntity> roleMenuEntities = new ArrayList<>();
        if(distinguishInAndOutNet){
            // 2022/10/24 chenwei
            // 区分内外网(功能改造) 新增入参字段type
            roleMenuEntities = shiroService.getRoleMenuPermissions(roleId, type);
        }else {
            roleMenuEntities = shiroService.getRoleMenuPermissions(roleId);

        }
        List<Long> permissionMenuIds = roleMenuEntities.stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toList());
        // 重新拼接
        // 从子节点获取
        setChildPermission(permissionMenuIds, menuTreeList);

        return R.success(menuTreeList);
    }

    @ApiOperation(
            value = "获取角色数据权限",
            notes = "获取角色数据权限")
    @GetMapping("/role/data/{roleId}")
    public Result getDataPermission(@PathVariable(value = "roleId", required = false) Long roleId) {
        List<SysRoleDeptEntity> roleMenuEntities = shiroService.getRoleDeptPermissions(roleId);
        List<Long> permissionDeptIds = roleMenuEntities.stream().map(SysRoleDeptEntity::getDeptId).collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("dept", permissionDeptIds);

        return R.success(map);
    }

    @ApiOperation(
            value = "获取角色在指定应用的权限",
            notes = "获取角色在指定应用的权限")
    @GetMapping(value = "/{appId}/{roleId}")
    public Result getPermission(@PathVariable(value = "appId") Long appId,
                                @PathVariable(value = "roleId") Long roleId) {

        List<String> permissions = roleMenuService.getRolePermission(appId, roleId);
        return R.success(permissions);
    }

    @ApiOperation(
            value = "获取全部权限注解",
            notes = "获取全部权限注解"
    )
    @GetMapping("/annotation/all")
    public Result getPermissionAnnotation(HttpServletRequest request) {
        List<String> result = new ArrayList<>();

        ServletContext servletContext = request.getSession().getServletContext();
        if (servletContext == null) {
            return R.success();
        }
        WebApplicationContext appContext = WebApplicationContextUtils.getWebApplicationContext(servletContext);

        // 获取所有的RequestMapping
        Map<String, HandlerMapping> allRequestMappings = BeanFactoryUtils.beansOfTypeIncludingAncestors(appContext,
                HandlerMapping.class, true, false);

        for (HandlerMapping handlerMapping : allRequestMappings.values()) {
            // 只需要RequestMappingHandlerMapping中的URL映射
            if (handlerMapping instanceof RequestMappingHandlerMapping) {
                RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping) handlerMapping;
                Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
                for (Map.Entry<RequestMappingInfo, HandlerMethod> requestMappingInfoHandlerMethodEntry : handlerMethods.entrySet()) {
                    HandlerMethod mappingInfoValue = requestMappingInfoHandlerMethodEntry.getValue();
                    //选择有RequiresPermissions注解的
                    if (mappingInfoValue.getMethod().isAnnotationPresent(RequiresPermissions.class)) {
                        RequiresPermissions requiresPermissions = mappingInfoValue.getMethod().getAnnotation(RequiresPermissions.class);

                        if (requiresPermissions != null) {
                            //获取注解中的值
                            String[] values = requiresPermissions.value();
                            result.addAll(Arrays.asList(values));
                        }
                    }
                }
            }
        }
        result = result.stream().distinct().collect(Collectors.toList());
        return R.success(result);
    }

    @ApiOperation(
            value = "保存角色菜单权限",
            notes = "保存角色菜单权限")
    @SysLog("修改权限")
    @PostMapping("/role/operation")
    @RequiresPermissions("sys:perm:operation")
    public Result saveOperaPermission(@RequestParam(value = "roleId", required = false, defaultValue = "0") Long roleId,
                                      @RequestParam(value = "appId") Long appId,
                                      @RequestParam(value = "type") Long type,
                                      @RequestBody Long[] menuIds) {
        if(distinguishInAndOutNet){
            // 2022/10/24 chenwei
            // 区分内外网(功能改造) 新增入参字段type
            roleMenuService.saveOrUpdate(roleId, appId, Arrays.asList(menuIds), type);
        }else {
            roleMenuService.saveOrUpdate(roleId, appId, Arrays.asList(menuIds));
        }
        //todo 强制刷新menu的缓存
        for (Long menuId : menuIds) {
            SysMenuEntity menu = sysMenuService.getById(menuId);
            sysMenuService.updateById(menu);
            break;
        }
        return R.success("保存成功", true);
    }

    @ApiOperation(
            value = "保存角色数据权限",
            notes = "保存角色数据权限")
    @SysLog("修改数据权限")
    @PostMapping("/role/data")
    @RequiresPermissions("sys:perm:data")
    public Result saveDataPermission(@RequestParam(value = "roleId", required = false, defaultValue = "0") Long roleId,
                                     @RequestBody Map<String, List<Long>> map) {
        shiroService.saveDataPermission(roleId, map);

        return R.success("保存成功", true);
    }

    @ApiOperation(
            value = "获取是否区分内外网配置项",
            notes = "获取是否区分内外网配置项")
    @SysLog("获取是否区分内外网配置项")
    @GetMapping("/distinguish")
    @RequiresPermissions("sys:perm:data")
    public Result distinguishInAndOutNet() {
        return R.success(distinguishInAndOutNet);
    }

    private void setChildPermission(List<Long> permissionMenuIds, List<SysMenuEntity> child) {
        if (CollectionUtils.isEmpty(child)) {
            return;
        }

        for (SysMenuEntity menuEntity : child) {
            if (permissionMenuIds.contains(menuEntity.getId())) {
                menuEntity.setChecked(true);
            }
            if (menuEntity.getType().equals(Const.MenuType.MENU.getValue())) {
                // 从子节点获取
                setChildPermission(permissionMenuIds, menuEntity.getChildren());
            }
        }
    }
}
