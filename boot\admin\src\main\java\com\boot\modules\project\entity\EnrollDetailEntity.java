package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("rp_enroll_detail")
public class EnrollDetailEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 子项目ID
     */
    private Long subProjectId;

    /**
     * 条件ID
     */
    private Long groupConfigId;

    /**
     * 执行记录ID
     */
    private Long recordId;
    /**
     * 访视ID
     */
    private Long visitId;
    /**
     * 访视时间
     */
    private String visitDate;
    /**
     * 主治医生
     */
    private String doctorName;
    /**
     * 患者唯一ID
     */
    private String empi;

    /**
     * 获取就诊对应的患者唯一ID状态(1-成功；2-失败)
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String message;



}
