package com.boot.commons.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;

/**
 * Swagger配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableSwagger2
@EnableKnife4j
public class SwaggerConfig {
    @Bean
    public Docket appApi() {
        ParameterBuilder ticketPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<Parameter>();
        ticketPar
                .name("appId")
                .modelRef(new ModelRef("string"))
                .parameterType("header")
                .required(false)
                .build(); //header中的ticket参数非必填，传空也可以

        //根据每个方法名也知道当前方法在设置什么参数
        pars.add(ticketPar.build());
        return new Docket(DocumentationType.SWAGGER_2).groupName("app").genericModelSubstitutes(ResponseEntity.class)
                .apiInfo(appApiInfo())
                .select()
                .apis(RequestHandlerSelectors.any())
//                .apis(RequestHandlerSelectors.basePackage("com.gemdale.iot.business.app.controller"))//扫描的包
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
//                .paths(PathSelectors.any())
//                .paths(or(regex("/user/.*")))//过滤
                .build()
                .securitySchemes(security())
                .globalOperationParameters(pars);
    }

    private ApiInfo appApiInfo() {
        return new ApiInfoBuilder()
                .title("BOOT-ADMIN接口文档")
                .description("该文档主要提供APP端的接口 \r\n\n"
                        + "请求服务:http//127.0.0.1:8080/boot-admin/  （测试服务器不支持https）\r\n\n"
                        + "返回：  \r\n\n"
                        + "ReturnBody<T> {\"code\":\"标识码\",\"message\":\"描述\",data{ json字符串（对象）  } \r\n\n"
                        + "")
                .contact(new springfox.documentation.service.Contact("xxx业务后台开发组", null, null))
                .version("1.0.0")
                .build();
    }

    private List<ApiKey> security() {
        return newArrayList(
                new ApiKey("token", "token", "header")
        );
    }
}
