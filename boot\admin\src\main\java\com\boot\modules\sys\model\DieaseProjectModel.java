package com.boot.modules.sys.model;

import com.boot.modules.project.vo.SubProjectVo;
import lombok.Data;

import java.util.List;

@Data
public class DieaseProjectModel {
    /**
     * 病种id
     */
    private Long diseaseId;

    /**
     * 病种名称
     */
    private String diseaseName;

    /**
     * 关联科室Id
     */
    private Long deptId;

    /**
     * 是否符合系统推荐
     */
    private Integer isMatched = 0;

    /**
     * 是否有系统推荐标识
     */
    private Integer isRecommend = 0;

    /**
     * 项目信息
     */
    private List<SubProjectVo> projectList;
}
