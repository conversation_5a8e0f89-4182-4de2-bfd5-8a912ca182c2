package com.boot.modules.sys.service.impl;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.constant.HxConst;
import com.boot.commons.constants.Const;
import com.boot.commons.enums.RoleLevelEnum;
import com.boot.commons.enums.UserStatusEnum;
import com.boot.commons.excel.model.ReadExcelListener;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.*;
import com.boot.commons.utils.file.FileHelper;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.project.dao.UserSubProjectDao;
import com.boot.modules.sys.dao.SysUserDao;
import com.boot.modules.sys.dao.SysUserTokenDao;
import com.boot.modules.sys.dto.ExportUserDto;
import com.boot.modules.sys.dto.PassWordChangeDto;
import com.boot.modules.sys.entity.*;
import com.boot.modules.sys.model.KelinUserModel;
import com.boot.modules.sys.service.*;
import com.boot.modules.sys.vo.UserAccessTokenVo;
import com.boot.modules.sys.vo.UserHisForeignVo;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 系统用户
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {

    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysDeptService sysDeptService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserTokenDao userTokenDao;
    @Resource
    private SysUserHisService userHisService;

    @Resource
    private UserSubProjectDao userSubProjectDao;

    @Resource
    private UserHttpApiService userHttpApiService;

    /**
     * 单次获取用户数量
     */
    public static final Integer BATCH_SIZE = 20000;

    /**
     * 获取accessToken
     *
     * @return
     */
    private static UserAccessTokenVo accessTokenVo;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();
        String username = MapUtils.getValue(params, "username", String.class);
        //用户管理处  加上 角色过滤、机构过滤、状态过滤
        Long roleId = MapUtils.getValue(params, "roleId", Long.class);
        String deptName = MapUtils.getValue(params, "deptName", String.class);
        Integer status = MapUtils.getValue(params, "status", Integer.class);
        String orderField = MapUtils.getValue(params, "orderField", String.class);

        qw.like(StringUtils.isNotBlank(deptName), "c.name", deptName)
                .eq(roleId != null, "b.role_id", roleId)
                .lambda()
                .eq(status != null, SysUserEntity::getStatus, status);
        if (!StringUtils.isEmpty(username)) {
            qw.lambda().and(w -> w.like(SysUserEntity::getUsername, username)
                    .or()
                    .like(SysUserEntity::getNickname, username));
        }
        qw.apply(params.get(Const.SQL_FILTER) != null, (String) params.get(Const.SQL_FILTER));

//                .groupBy(SysUserEntity::getId);

        if (StringUtils.isBlank(orderField)) {
            qw.orderByDesc("a.create_time", "a.id");
        }
        IPage<SysUserEntity> pageFilter = new Query<SysUserEntity>().getPage(params);
        IPage<SysUserEntity> page = baseMapper.queryPage(pageFilter, qw);

        List<SysDeptEntity> deptList = sysDeptService.list();
        // 查询用户系统角色
        List<SysUserRoleEntity> userRoleList = sysUserRoleService.list();
        List<SysRoleEntity> roleList = sysRoleService.list();

        List<SysUserEntity> records = page.getRecords();
        for (SysUserEntity sysUserEntity : records) {

            List<Long> roleIds = userRoleList.stream()
                    .filter(s -> s.getUserId().equals(sysUserEntity.getId()))
                    .map(SysUserRoleEntity::getRoleId)
                    .collect(Collectors.toList());
            List<String> roleNames = roleList.stream()
                    .filter(s -> roleIds.contains(s.getId()))
                    .map(SysRoleEntity::getRoleName).collect(Collectors.toList());
            sysUserEntity.setRoleIdList(roleIds);
            sysUserEntity.setRoleNameList(roleNames);
            String deptId = sysUserEntity.getDeptId();

            String deptNameString = "";
            for (SysDeptEntity dept : deptList) {

                if (deptId.equals(dept.getId().toString())) {
                    sysUserEntity.setDeptName(dept.getName());
                    break;
                } else if (deptId.contains(",")) {
                    String[] split = deptId.split(",");

                    for (int i = 0; i < split.length; i++) {
                        if (split[i].equals(dept.getId().toString())) {
                            if (com.boot.commons.utils.StringUtils.isEmpty(deptNameString)) {
                                deptNameString = dept.getName();
                            } else {
                                deptNameString += "," + dept.getName();
                            }
                        }
                    }
                }
            }

            if (com.boot.commons.utils.StringUtils.isEmpty(deptNameString)) {
                sysUserEntity.setDeptName(deptNameString);
            }
        }

        return new PageUtils(page);
    }

    /**
     * @param allCenterUserQw
     * @return
     */
    @Override
    public List<SysUserEntity> getByQuery(QueryWrapper<SysUserEntity> allCenterUserQw) {

        allCenterUserQw = allCenterUserQw == null
                ? new QueryWrapper<SysUserEntity>() : allCenterUserQw;

        // todo 暂未修改机构结构修改逻辑
        List<SysUserEntity> list = this.list(allCenterUserQw);

        // 查询用户系统角色
        List<SysUserRoleEntity> userRoleList = sysUserRoleService.list();
        List<SysRoleEntity> roleList = sysRoleService.list();
        if (!CollectionUtils.isEmpty(list)) {
            for (SysUserEntity sysUserEntity : list) {

                List<Long> roleIds = userRoleList.stream()
                        .filter(s -> s.getUserId().equals(sysUserEntity.getId()))
                        .map(SysUserRoleEntity::getRoleId)
                        .collect(Collectors.toList());
                List<SysRoleEntity> roles = roleList.stream()
                        .filter(s -> roleIds.contains(s.getId())).collect(Collectors.toList());

                sysUserEntity.setRoleIdList(roleIds);
                if (!CollectionUtils.isEmpty(roles)) {
                    sysUserEntity.setIsSysAdmin(roles.get(0).getIsSysAdmin());
                }
                sysUserEntity.setIsSuperAdmin(0);
            }
        }


        return list;
    }

    @Override
    public List<String> queryAllPerms(Long userId) {
        List<String> res = baseMapper.queryAllPerms(userId);
        if (!CollectionUtils.isEmpty(res)) {
            // 过滤非空的项
            res = res.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        }

        return res;
    }

    @Override
    public List<Long> queryAllMenuId(Long userId) {

        List<Long> res = baseMapper.queryAllMenuId(userId);
        if (!CollectionUtils.isEmpty(res)) {
            // 过滤非空的项
            res = res.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        return res;
    }

    @Override
    public List<Long> queryAllMenuId(Long userId, Long type) {

        List<Long> res = baseMapper.queryAllMenuIdByType(userId, type);
        if (!CollectionUtils.isEmpty(res)) {
            // 过滤非空的项
            res = res.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        return res;
    }

    /**
     * 分页获取指定机构中的用户
     *
     * @param deptIds 机构ids
     * @param params
     * @return
     */
    @Override
    public PageUtils queryByDeptIds(List<Long> deptIds, Map<String, Object> params) {

        if (deptIds != null && deptIds.size() > 0) {
            IPage<SysUserEntity> pageFilter = new Query<SysUserEntity>().getPage(params);
            QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();

            qw.in("deptId", deptIds);

            IPage<SysUserEntity> page = this.baseMapper.pageByDeptId(pageFilter, qw);
            return new PageUtils(page);
        }

        return null;
    }

    @Override
    public PageUtils queryByDeptIds(List<Long> deptIds, Map<String, Object> params,
                                    boolean isReturnNoExpiredUser) {
        if (deptIds != null && deptIds.size() > 0) {
            String username = MapUtils.getValue(params, "username", String.class);
            IPage<SysUserEntity> pageFilter = new Query<SysUserEntity>().getPage(params);
            QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();

            qw.in("deptId", deptIds);
            if (isReturnNoExpiredUser) {
                qw.lambda().eq(SysUserEntity::getStatus, UserStatusEnum.UN_LOCK.getType());
            }
            if (StringUtils.isNotBlank(username)) {
                qw.lambda()
                        .and(
                                qw1 -> {
                                    qw1.like(StringUtils.isNotBlank(username), SysUserEntity::getUsername, username)
                                            .or().like(StringUtils.isNotBlank(username), SysUserEntity::getNickname, username);
                                }
                        );
            }

            IPage<SysUserEntity> page = this.baseMapper.pageByDeptId(pageFilter, qw);
            return new PageUtils(page);
        }

        return null;

    }

    @Override
    public SysUserEntity queryByUserName(String username) {
        SysUserEntity user = new SysUserEntity();
        List<SysUserEntity> list = baseMapper.selectList(new QueryWrapper<SysUserEntity>().
                lambda().eq(SysUserEntity::getUsername, username));

        if (!CollectionUtils.isEmpty(list)) {
            user = list.get(0);
            //查询用户的系统角色
            QueryWrapper<SysRoleEntity> qwRole = new QueryWrapper<>();
            qwRole.eq("a.id", user.getId())
                    .eq("c.role_level", RoleLevelEnum.SYSTEM.getLevel());
            List<SysRoleEntity> roleEntityList = sysRoleService.getByQuery(qwRole);
            if (!CollectionUtils.isEmpty(roleEntityList)) {
                // 一个用户只能有一中系统角色
                SysRoleEntity curSysRole = roleEntityList.get(0);

                user.setRoleNameList(roleEntityList.stream().map(SysRoleEntity::getRoleName).collect(Collectors.toList()));
                user.setRoleIndexUrl(curSysRole.getIndexUrl());
                user.setIsSysAdmin(curSysRole.getIsSysAdmin());
                user.setIsSuperAdmin(0);
            }

        }

        return user;
    }

    @Override
    public SysUserEntity getByForignAndDeptCode(String foreignId, String deptCode) {
        QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();
        qw.eq("a.foreign_id", foreignId)
                .eq("b.code", deptCode);
        List<SysUserEntity> sysUserEntities = baseMapper.getUserAndDept(qw);
        if (!CollectionUtils.isEmpty(sysUserEntities)) {
            return sysUserEntities.get(0);
        }

        return null;
    }


    @Override
    public SysUserEntity queryByUserCode(String userCode) {
        SysUserEntity user = new SysUserEntity();
        List<SysUserEntity> list = baseMapper.selectList(new QueryWrapper<SysUserEntity>().
                lambda().eq(SysUserEntity::getForeignId, userCode));

        if (!CollectionUtils.isEmpty(list)) {
            user = list.get(0);
            //查询用户的系统角色
            QueryWrapper<SysRoleEntity> qwRole = new QueryWrapper<>();
            qwRole.eq("a.id", user.getId())
                    .eq("c.role_level", RoleLevelEnum.SYSTEM.getLevel());
            List<SysRoleEntity> roleEntityList = sysRoleService.getByQuery(qwRole);
            if (!CollectionUtils.isEmpty(roleEntityList)) {
                // 一个用户只能有一中系统角色
                SysRoleEntity curSysRole = roleEntityList.get(0);

                user.setRoleNameList(roleEntityList.stream().map(SysRoleEntity::getRoleName).collect(Collectors.toList()));
                user.setRoleIndexUrl(curSysRole.getIndexUrl());
                user.setIsSysAdmin(curSysRole.getIsSysAdmin());
                user.setIsSuperAdmin(0);
            }
        }

        return user;
    }


    /**
     * 根据用户手机号 和项目id查用户
     *
     * @param phone
     * @param subProjectId
     * @return
     */
    @Override
    public SysUserEntity queryByMobile(String phone, Long subProjectId) {
        if (StringUtils.isBlank(phone) || subProjectId == null) {
            return null;
        }
        QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();
        qw.eq("a.sub_project_id", subProjectId)
                .eq("b.mobile", phone);
        List<SysUserEntity> sysUserEntityList = userSubProjectDao.getByMobile(qw);
        if (!CollectionUtils.isEmpty(sysUserEntityList)) {
            return sysUserEntityList.get(0);
        }

        return null;
    }

    /**
     * 根据用户手机号 和项目id查用户
     *
     * @param phone
     * @return
     */
    @Override
    public List<SysUserEntity> queryByMobile(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();
        qw.eq("a.mobile", phone);

        List<SysUserEntity> sysUserEntityList = this.baseMapper.getByQuery(qw);

        return sysUserEntityList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUser(SysUserEntity user) {

        //校验密码复杂度
        PasswordComplexityUtils.checkPswComplexity(user.getPassword(), BootAdminProperties.isCheckPasswordComplexity);

        user.setCreateTime(new Date());
        //sha256加密
        String salt = RandomStringUtils.randomAlphanumeric(20);
        user.setPassword(new Sha256Hash(user.getPassword(), salt).toHex());
        user.setSalt(salt);
        this.save(user);

        if (user.getUserHisList().length > 0) {
            List<SysUserHisEntity> userHisEntityList = new ArrayList<>();
            for (UserHisForeignVo sysUserHisEntity : user.getUserHisList()) {
                SysUserHisEntity userHis = new SysUserHisEntity();
                userHis.setUserId(user.getId());
                userHis.setDeptId(sysUserHisEntity.getDeptId());
                userHis.setHisForeignId(sysUserHisEntity.getHisForeignId());
                userHis.setDeptCode(sysUserHisEntity.getDeptCode());
                userHisEntityList.add(userHis);
            }
            userHisService.saveBatch(userHisEntityList);
        }

        //检查角色是否越权
        checkRole(user);

        //保存用户与角色关系
        sysUserRoleService.saveOrUpdate(user.getId(), user.getRoleIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserEntity user) {
        if (StringUtils.isBlank(user.getPassword())) {
            user.setPassword(null);
        } else {
            user.setPassword(new Sha256Hash(user.getPassword(), user.getSalt()).toHex());
        }
        if (user.getLoginErrorCount() != null) {
            user.setLoginErrorCount(0);
        }
        this.updateById(user);

        userHisService.remove(new QueryWrapper<SysUserHisEntity>()
                .lambda().eq(SysUserHisEntity::getUserId, user.getId())
        );

        if (user.getUserHisList() != null && user.getUserHisList().length > 0) {
            List<SysUserHisEntity> userHisEntityList = new ArrayList<>();
            for (UserHisForeignVo vo : user.getUserHisList()) {
                SysUserHisEntity sysUserHisEntity = new SysUserHisEntity();
                sysUserHisEntity.setUserId(user.getId());
                sysUserHisEntity.setHisForeignId(vo.getHisForeignId());
                sysUserHisEntity.setDeptId(vo.getDeptId());
                sysUserHisEntity.setDeptCode(vo.getDeptCode());
                userHisEntityList.add(sysUserHisEntity);
            }
            userHisService.saveOrUpdateBatch(userHisEntityList);
        }

        //修改用户之后，删除用户的token存储，试当前用户需要重新登录
        userTokenDao.delete(
                new QueryWrapper<SysUserTokenEntity>().lambda().eq(SysUserTokenEntity::getUserId, user.getId().toString())
                        .or().eq(SysUserTokenEntity::getUserId, HxConst.APP_CODE + user.getId().toString())
        );

        //检查角色是否越权
        checkRole(user);

        //保存用户与角色关系
        sysUserRoleService.saveOrUpdate(user.getId(), user.getRoleIdList());
    }

    /**
     * 修改密码 分为两种 一种是自己修改自己的密码，需要输入原始密码  一种是管理员修养密码，不需要输入原始密码
     *
     * @param userId      用户ID
     * @param password    原密码
     * @param newPassword 新密码
     * @return
     */
    @Override
    public boolean updatePassword(Long userId, String password, String newPassword) {
        SysUserEntity oldUser = this.getById(userId);
        if (oldUser == null) {
            throw new BusinessException("数据库中没有此用户");
        }
        if (!"".equals(password)) {
            String oldPwd = new Sha256Hash(password, oldUser.getSalt()).toHex();
            String newPwd = new Sha256Hash(newPassword, oldUser.getSalt()).toHex();
            if (!oldPwd.equals(oldUser.getPassword())) {
                throw new BusinessException("原密码不正确");
            }
            if (newPwd.equals(oldUser.getPassword())) {
                throw new BusinessException("新密码与旧密码相同");
            }
        }
        PasswordComplexityUtils.validPassword(newPassword);
        //sha256加密
        newPassword = new Sha256Hash(newPassword, oldUser.getSalt()).toHex();

        oldUser.setPassword(newPassword);
        oldUser.setLoginErrorCount(0);
        return this.updateById(oldUser);
    }


    @Override
    public String createUserMessage(SysUserEntity user) {
        // todo 获取对应的消息
        return null;
    }

    /**
     * 测试异步
     *
     * @param user
     * @return
     * @throws InterruptedException
     */
    @Async("taskExecutor")
    @Override
    public CompletableFuture<String> findUser(String user) throws InterruptedException {

        String results = this.list().toString();
        // Artificial delay of 3s for demonstration purposes
        Thread.sleep(3000L);
        return CompletableFuture.completedFuture(results);
    }

    /**
     * 用户excel批量导入
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean excelImport(MultipartFile file, Long userId) {
        ArrayList<SysUserEntity> data = new ArrayList<>();

        try {
            ReadExcelListener readExcelListener = new ReadExcelListener(ExportUserDto.class);

            List<ExportUserDto> list = ExcelUtils.readExcel(file, ExportUserDto.class, readExcelListener);

            if (CollectionUtils.isEmpty(list)) {
                throw new BusinessException("Excel没有用户数据");
            }
            //用户名去重
            List<SysUserEntity> allOldUsers = this.list();
            List<ExportUserDto> afterDeWeight = new ArrayList<>();

            afterDeWeight = ListUtils.getAddListThanList(list, afterDeWeight);
            if (!CollectionUtils.isEmpty(allOldUsers)) {

                List<String> userNames = allOldUsers.stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
                for (ExportUserDto userDto : list) {
                    if (userNames.contains(userDto.getUserName())) {
                        afterDeWeight.remove(userDto);
                    }
                }
            }
            afterDeWeight = afterDeWeight.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExportUserDto::getUserName))),
                    ArrayList::new
            ));
            if (CollectionUtils.isEmpty(afterDeWeight)) {
                return true;
            }
            // 获取所有医疗机构ID
            List<String> deptIdStrList = afterDeWeight.stream().map(p -> p.getDeptId()).collect(Collectors.toList());
            List<Long> deptIdList = new ArrayList<>();
            for (String deptId : deptIdStrList) {
                String[] deptIdArr = deptId.split(",");
                for (String dept : deptIdArr) {
                    deptIdList.add(Long.parseLong(dept));
                }
            }
            List<SysDeptEntity> allDeptList = sysDeptService.getAll();

            List<ExportUserDto> errorList = new ArrayList<>();
            //将excel数据赋给user 填写了的数据赋予给user  没有填写的数据就默认
            for (ExportUserDto u : afterDeWeight) {
                try {
                    SysUserEntity user = new SysUserEntity();
                    validUser(u);
                    //sha256加密
                    String salt = RandomStringUtils.randomAlphanumeric(20);
                    if (u.getPassword() != null && u.getPassword().trim().length() > 0) {
                        user.setPassword(new Sha256Hash(u.getPassword(), salt).toHex());
                    }
                    if (u.getNickName() != null && u.getNickName().trim().length() > 0) {
                        user.setNickname(u.getNickName());
                    } else {
                        user.setNickname(u.getUserName());
                    }
                    user.setUsername(u.getUserName());
                    user.setMobile(u.getMobile());
                    user.setCreateTime(new Date());
                    if (!StringUtils.isEmpty(u.getExpiredDate())) {
                        //验证日期合法性
                        String endDate;
                        try {
                            Date date = DateUtils.deserialize(u.getExpiredDate());
                            endDate = DateUtils.getStr(date);
                        } catch (Exception ex) {
                            log.error("截止日期格式错误" + u.getExpiredDate());
                            u.setErrorInfo(ex.getMessage());
                            errorList.add(u);
                            continue;
                        }
                        user.setExpiredDate(DateUtils.stringToDate(endDate, DateUtils.DATE_TIME_PATTERN));
                    }

                    user.setDeptId(u.getDeptId());
                    user.setSalt(salt);
                    user.setCreateUserId(userId);
                    user.setStatus(1);
                    user.setForeignId(StringUtils.isEmpty(u.getHisNo()) ? "" : u.getHisNo());
                    this.save(user);

                    if (u.getRoleId() != null) {
                        SysUserRoleEntity userRoleEntity = new SysUserRoleEntity();
                        userRoleEntity.setUserId(user.getId());
                        userRoleEntity.setRoleId(u.getRoleId());
                        sysUserRoleService.save(userRoleEntity);
                    }
                    if (u.getHisNo() != null) {
                        SysUserHisEntity sysUserHisEntity = new SysUserHisEntity();
                        sysUserHisEntity.setUserId(user.getId());
                        sysUserHisEntity.setHisForeignId(u.getHisNo());
                        // 查顶层父级点
                        String[] deptIdArr = u.getDeptId().split(",");
                        SysDeptEntity topDept = sysDeptService.getTopDeptById(allDeptList, Long.parseLong(deptIdArr[0]));
                        if (ObjectUtils.isEmpty(topDept)) {
                            continue;
                        }
                        sysUserHisEntity.setDeptId(topDept.getId());
                        sysUserHisEntity.setDeptCode(topDept.getCode());
                        userHisService.save(sysUserHisEntity);
                    }
                } catch (Exception ex) {
                    u.setErrorInfo(ex.getMessage());
                    errorList.add(u);
                    continue;
                }
            }
            if (!CollectionUtils.isEmpty(errorList)) {
                generateErrorExcel(errorList);
            }
            return true;
        } catch (Exception e) {
            throw new BusinessException("导入失败：" + e);
        }
    }

    /**
     * 将错误信息放入到excel中去
     *
     * @param errInfoList
     * @return
     */
    private String generateErrorExcel(List<ExportUserDto> errInfoList) throws IOException {
        if (CollectionUtils.isEmpty(errInfoList)) {
            return null;
        }
        try {
            List<List<String>> heads = ExcelUtils.getHeads(ExportUserDto.class);
            Field[] fields = ExportUserDto.class.getDeclaredFields();
            Map<String, String> fieldMap = new HashMap<>();
            for (Field field : fields) {
                fieldMap.put(field.getName(), field.getAnnotation(ExcelProperty.class).value()[0]);
            }
            List<Map<String, String>> dataList = JSONObject.parseObject(JSON.toJSONString(errInfoList), ArrayList.class);
            List<Map<String, String>> dataListNew = new ArrayList<>();
            for (Map<String, String> data : dataList) {
                Map<String, String> dataNew = new HashMap<>();
                for (String key : data.keySet()) {
                    dataNew.put(fieldMap.get(key), data.get(key));
                }
                dataListNew.add(dataNew);
            }
            String fileName = IdUtils.getEmpi() + ".xls";
            ExcelUtils.createExcel(FileHelper.getImportUserErrorInfoPath(), fileName, heads, JSONArray.parseArray(JSON.toJSONString(dataListNew)));
            return fileName;
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
            throw new BusinessException("创建excel失败");
        }
    }

    @Override
    public boolean changePwd(PassWordChangeDto wordChangeDto) {
        String username = wordChangeDto.getUsername();
        String password = wordChangeDto.getPassword();
        String confirmPassword = wordChangeDto.getConfirmPassword();

        SysUserEntity user = this.lambdaQuery().eq(SysUserEntity::getUsername, username).one();
        String salt = user.getSalt();

        if (!password.equals(confirmPassword)) {
            throw new BusinessException("两次输入的密码不一致！");
        }

        String pwd = new Sha256Hash(password, salt).toHex();
        user.setPassword(pwd);
        user.setLoginErrorCount(0);

        return this.updateById(user);
    }


    /**
     * 自动修改用户状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoUpdateUserStatus() {
        try {

            List<SysUserEntity> allUsers = this.list();

            List<SysUserEntity> expireUserList = new ArrayList<>();

            for (SysUserEntity user : allUsers) {

                if (user != null && user.getStatus() != null && user.getStatus().equals(1)) {
                    if (DateUtils.compareStrDate(DateUtils.getNowTimeStr(DateUtils.DATE_PATTERN), DateUtils.format(user.getExpiredDate(), DateUtils.DATE_PATTERN))) {
                        user.setStatus(0);
                        expireUserList.add(user);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(expireUserList)) {
                this.updateBatchById(expireUserList);
            }
        } catch (Exception e) {
            log.error("自动更新用户状态失败");
            throw new BusinessException("自动更新用户状态失败");
        }
    }

    @Override
    public Result getWatermark(SysUserEntity user) {
        //获取登陆名和医院名
        String loginName = user.getUsername();
        String hospital = user.getDeptName();
        String key = loginName + loginName + "_Watermark";
        String watermarkStr = null;
        // 从缓冲中获取
        Object cacheWatermark = EhCacheUtil.getInstance().get("crf_external", key);
        if (cacheWatermark == null) {
            //请求统一水印接口
            String url = ConfigProperties.getKey(HxConst.PT_URL) + ConfigProperties.getKey(HxConst.WATER_MARK_URL);
            Map<String, String> param = new HashMap<>();
            param.put("loginname", loginName);
            param.put("hospital", hospital);
            //为对接前端暂时屏蔽测试后期需放开
//            String result = HttpClientJwtUtil.doGet(url, param);
//            if (StringUtils.isEmpty(result)) {
//                throw new BusinessException("返回水印结果为空");
//            }
//            JSONObject jsonObject = JSONObject.parseObject(result);

            //TestData  测试start
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", "200");
            jsonObject.put("msg", "17851_刘然");
            //TestData  测试end


            if (jsonObject == null) {
                throw new BusinessException("返回水印结果为空");
            }

            //设置返回结果
            if ("200".equals(String.valueOf(jsonObject.get("code")))) {
                watermarkStr = jsonObject.getString("msg");
                EhCacheUtil.getInstance().put("crf_external", key, watermarkStr);
            } else {
                return R.fail(jsonObject.getString("msg"));
            }
        } else {
            watermarkStr = cacheWatermark.toString();
        }
        return R.success(watermarkStr);
    }


    /**
     * 检查角色是否越权,除超级管理员和系统管理员外，其他人如果可以创建用户只能选择自己创建的角色
     */
    private void checkRole(SysUserEntity user) {
        if (user.getRoleIdList() == null || user.getRoleIdList().size() == 0) {
            return;
        }
        //如果不是超级管理员，则需要判断用户的角色是否自己创建
        if (user.getUsername().equals(BootAdminProperties.adminAccount)) {
            return;
        }

        //查询用户创建的角色列表  TODO
//        List<Long> roleIdList = sysRoleService.queryRoleIdList(user.getCreateUserId());

        //判断是否越权
//        if(!roleIdList.containsAll(user.getRoleIdList())){
//            throw new BusinessException("新增用户所选角色，不是本人创建");
//        }
    }


    /**
     * 校验导入的用户数据
     *
     * @param u
     */
    private void validUser(ExportUserDto u) {

        if (StringUtils.isEmpty(u.getUserName())) {
            throw new BusinessException("用户名不准为空");
        }
        boolean usnBool = Pattern.matches("[A-Za-z0-9_\\-]+", u.getUserName());
        if (BootAdminProperties.isCheckUserNameComplexity && !usnBool) {
            throw new BusinessException("用户名为：" + u.getUserName() + "的用户名不能包含中文和特殊字符");
        }
        if (StringUtils.isBlank(u.getPassword())) {
            throw new BusinessException("密码不许为空");
        }
        boolean pwdBool = Pattern.matches("^(?![0-9]+$)(?![a-zA-Z]+$)(?!([^(0-9a-zA-Z)]|[\\(\\)])+$)([^(0-9a-zA-Z)]|[\\(\\)]|[a-zA-Z]|[0-9]){6,16}$", u.getPassword());
        if (!pwdBool) {
            throw new BusinessException("用户名为：" + u.getUserName() + "的密码必须包含字母，数字，字符中的任意两种，长度在6到16位");
        }
        if (StringUtils.isNotBlank(u.getMobile())) {
            boolean mobileBool = Pattern.matches("^1[0-9]{10}$", u.getMobile());
            if (!mobileBool) {
                throw new BusinessException("用户名为：" + u.getUserName() + "的手机号格式不对");
            }
        }
        if (u.getDeptId() == null) {
            throw new BusinessException("所属机构不许为空");
        }
    }

    /**
     * 同步用户科室
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void asynUser() {


        int count = BATCH_SIZE;
        int page = 1;

        // 1.获取或新增华西医院相关科室信息
        List<SysDeptEntity> list = sysDeptService.list(new QueryWrapper<SysDeptEntity>().lambda()
                .eq(SysDeptEntity::getCode, HxConst.HX_DEPT_CODE)
                .eq(SysDeptEntity::getPid, 0));

        Long hxId;

        if (CollectionUtils.isEmpty(list)) {
            SysDeptEntity sysDeptEntity = new SysDeptEntity();
            sysDeptEntity.setCode(HxConst.HX_DEPT_CODE);
            sysDeptEntity.setPid(0L);
            sysDeptEntity.setName("华西医院");
            sysDeptEntity.setSort(1);
            sysDeptEntity.setDelFlag(0);
            sysDeptEntity.setIsResearchCenter(1);
            sysDeptEntity.setIsDataCenter(0);
            sysDeptService.save(sysDeptEntity);
            hxId = sysDeptEntity.getId();
        } else {
            hxId = list.get(0).getId();
        }

        int sort = 0;

//        while (count >= BATCH_SIZE) {
        // 获取token
        UserAccessTokenVo accessToken = getAccessToken();
        if (accessToken.getAccessToken() == null || accessToken.getTokenExpiryTime() == null) {
            throw new BusinessException("获取第三方用户接口的token失败");
        }
        List<UserHisForeignVo> userList = getUserList(count, page, accessToken.getAccessToken());

        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        // 2.新增未加入系统的科室
        List<SysDeptEntity> deptList = sysDeptService.list(new QueryWrapper<SysDeptEntity>().lambda()
                .eq(SysDeptEntity::getPid, hxId)
                .orderByDesc(SysDeptEntity::getSort));
        List<SysDeptEntity> addList = new ArrayList<>();

        if (CollectionUtils.isEmpty(deptList)) {
            for (UserHisForeignVo user : userList) {
                if (user.getDeptCode() != null) {
                    SysDeptEntity sysDeptEntity = new SysDeptEntity();
                    sysDeptEntity.setCode(user.getDeptCode());
                    sysDeptEntity.setPid(hxId);
                    sysDeptEntity.setName(user.getDeptName());
                    sysDeptEntity.setSort(sort++);
                    sysDeptEntity.setDelFlag(0);
                    sysDeptEntity.setIsResearchCenter(1);
                    sysDeptEntity.setIsDataCenter(0);
                    addList.add(sysDeptEntity);
                }
            }
        } else {
            sort = deptList.get(0).getSort();
            List<String> codeList = deptList.stream().map(SysDeptEntity::getCode).collect(Collectors.toList());

            for (UserHisForeignVo user : userList) {
                if (user.getDeptCode() != null && !codeList.contains(user.getDeptCode())) {
                    SysDeptEntity sysDeptEntity = new SysDeptEntity();
                    sysDeptEntity.setCode(user.getDeptCode());
                    sysDeptEntity.setPid(hxId);
                    sysDeptEntity.setName(user.getDeptName());
                    sysDeptEntity.setSort(sort++);
                    sysDeptEntity.setDelFlag(0);
                    sysDeptEntity.setIsResearchCenter(1);
                    sysDeptEntity.setIsDataCenter(0);
                    addList.add(sysDeptEntity);
                }
            }
        }

        sysDeptService.saveBatch(addList);

        // 3.新增患者表数据
        deptList = sysDeptService.list(new QueryWrapper<SysDeptEntity>().lambda()
                .eq(SysDeptEntity::getPid, hxId)
                .orderByDesc(SysDeptEntity::getSort));
        Map<String, Long> map = deptList.stream().collect(Collectors.toMap(SysDeptEntity::getCode, SysDeptEntity::getId));
        List<SysUserEntity> sysUserList = new ArrayList<>();
        Date now = new Date();
        List<String> hisList = userList.stream().map(UserHisForeignVo::getHisForeignId).collect(Collectors.toList());
        List<SysUserEntity> sysUserEntities = this.baseMapper.selectList(new QueryWrapper<SysUserEntity>().lambda()
                .in(SysUserEntity::getUsername, hisList));
        Map<String, List<SysUserEntity>> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(sysUserEntities)) {
            userMap = sysUserEntities.stream().collect(Collectors.groupingBy(SysUserEntity::getUsername));
        }

        for (UserHisForeignVo user : userList) {
            SysUserEntity sysUserEntity = new SysUserEntity();
            String hisForeignId = user.getHisForeignId();

            if (userMap.containsKey(hisForeignId)) {
                sysUserEntity = userMap.get(hisForeignId).get(0);
            }

            sysUserEntity.setUsername(hisForeignId);
            sysUserEntity.setPassword(BootAdminProperties.adminPassword);
            sysUserEntity.setNickname(user.getUserNickname());
            sysUserEntity.setStatus(1);
            sysUserEntity.setDeptId(user.getDeptCode() == null ? hxId.toString() : map.get(user.getDeptCode()).toString());
            sysUserEntity.setCreateTime(now);
            sysUserEntity.setCreateUserId(0L);
            sysUserEntity.setForeignId(hisForeignId);
            sysUserList.add(sysUserEntity);

        }

        this.saveOrUpdateBatch(sysUserList);
        List<SysUserRoleEntity> sysUserRoleEntities = new ArrayList<>();
        for (SysUserEntity user : sysUserList) {
            SysUserRoleEntity userRoleEntity = new SysUserRoleEntity();
            userRoleEntity.setUserId(user.getId());
            userRoleEntity.setRoleId(15L);
            sysUserRoleEntities.add(userRoleEntity);
        }
        //添加角色信息
        sysUserRoleService.saveOrUpdateBatch(sysUserRoleEntities);

        // 4.添加userHis表数据
        List<SysUserHisEntity> sysUserHisList = new ArrayList<>();

        List<SysUserHisEntity> hisUserEntities = userHisService.list(new QueryWrapper<SysUserHisEntity>().lambda()
                .in(SysUserHisEntity::getHisForeignId, hisList));
        Map<String, List<SysUserHisEntity>> hisUserMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(hisUserEntities)) {
            hisUserMap = hisUserEntities.stream().collect(Collectors.groupingBy(SysUserHisEntity::getHisForeignId));
        }

        for (SysUserEntity user : sysUserList) {
            SysUserHisEntity sysUserHisEntity = new SysUserHisEntity();
            String foreignId = user.getForeignId();

            if (hisUserMap.containsKey(foreignId)) {
                sysUserHisEntity = hisUserMap.get(foreignId).get(0);
            }

            sysUserHisEntity.setUserId(user.getId());
            sysUserHisEntity.setHisForeignId(foreignId);
            sysUserHisEntity.setDeptId(hxId);
            sysUserHisEntity.setDeptCode(HxConst.HX_DEPT_CODE);
            sysUserHisList.add(sysUserHisEntity);
        }

        userHisService.saveOrUpdateBatch(sysUserHisList);

    }

    @Override
    public UserAccessTokenVo getAccessToken() {
        // 判断accessToken是否存在或是否过期
        if ((accessTokenVo == null)
                || (accessTokenVo != null && ((System.currentTimeMillis() - accessTokenVo.getCreateTime().getTime()) / 1000 > 7200))) {
            String appKey = BootAdminProperties.userAppKey;
            String appSecret = BootAdminProperties.userAppSecret;
            String result = userHttpApiService.getAccessToken(appKey, appSecret);

            if (!StringUtils.isEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                String status = jsonObject.getString("status");
                if (status.equals("success")) {
                    accessTokenVo = JSON.parseObject(jsonObject.getString(Const.TOKEN), UserAccessTokenVo.class);
                    if (accessTokenVo.getAccessToken() == null || accessTokenVo.getTokenExpiryTime() == null) {
                        return null;
                    }
                    // 计算令牌生成时间
                    Double seconds = Double.valueOf(accessTokenVo.getTokenExpiryTime());
                    Date createTime = DateUtils.addDateSeconds(new Date(), (int) (seconds - 7200));
                    accessTokenVo.setCreateTime(createTime);
                }
            }
        }

        return accessTokenVo;
    }

    @Override
    public List<Long> getUserIdByDeptId(List<Long> deptIdList) {
        // 获取所属医院对应所有子机构
        List<Long> allHosId = sysDeptService.getAllHosId(deptIdList);
        List<Long> subDeptIdList = sysDeptService.getSubDeptList(allHosId).stream().map(SysDeptEntity::getId).collect(Collectors.toList());

        List<SysUserEntity> userList = this.baseMapper.listByDeptId(new QueryWrapper<SysUserEntity>()
                .eq("deptId", subDeptIdList));

        if (CollectionUtils.isEmpty(userList)) {
            return null;
        }

        return userList.stream().map(SysUserEntity::getId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<ReportDto> listCount(String doctorName, List<Long> deptId, Date startTime, Date endTime) {
        QueryWrapper<ReportDto> qwProj = new QueryWrapper<>();
        QueryWrapper<ReportDto> qwDis = new QueryWrapper<>();

        if (!CollectionUtils.isEmpty(deptId)) {
            qwProj.in("a.dept_id", deptId);
            qwDis.in("a.dept_id", deptId);
        }

        // 1.获取全部项目与病种全量统计
        if (!StringUtils.isEmpty(doctorName)) {
            qwProj.like("a.nickname", doctorName);
            qwDis.like("a.nickname", doctorName);
        }

        // 临管部管理者只查看华西本院的科室
        List<Long> hxDeptIds = sysDeptService.getSubDeptIdList(15L);
        qwProj.in("a.dept_id", hxDeptIds);
        qwDis.in("a.dept_id", hxDeptIds);

        List<ReportDto> allCount = this.baseMapper.listCountProject(qwProj);

        if (!StringUtils.isEmpty(doctorName) && !CollectionUtils.isEmpty(allCount)) {
            // 按医生名称过滤
            allCount = allCount.stream().filter(p -> p.getDoctorName().contains(doctorName)).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(deptId) && !CollectionUtils.isEmpty(allCount)) {
            // 按科室过滤
            allCount = allCount.stream().filter(p -> deptId.contains(p.getDeptId())).collect(Collectors.toList());
        }
        // 2.获取全部项目与病种新建统计
        // 2.设置新增相关统计
        if (startTime != null) {
            qwProj.lt("d.create_time", endTime).ge("d.create_time", startTime);
            List<ReportDto> addProject = this.baseMapper.listCountProject(qwProj);
            qwDis.lt("d.create_time", endTime).ge("d.create_time", startTime);
            List<ReportDto> addDisease = this.baseMapper.listCountProject(qwDis);
            Map<Long, List<ReportDto>> addDisMap = new HashMap<>();
            Map<Long, List<ReportDto>> addProjMap = new HashMap<>();

            if (!CollectionUtils.isEmpty(addDisease)) {
                addDisMap = addDisease.stream().collect(Collectors.groupingBy(ReportDto::getDoctorId));
            }

            if (!CollectionUtils.isEmpty(addProject)) {
                addProjMap = addProject.stream().collect(Collectors.groupingBy(ReportDto::getDoctorId));
            }

            for (ReportDto dto : allCount) {
                Integer totalProject = dto.getTotalProject();
                if (totalProject == null || totalProject == 0) {
                    dto.setTotalDisease(0);
                    dto.setTotalProject(0);
                }
                long doctorId = dto.getDoctorId();
                dto.setAddDisease(0);
                dto.setAddProject(0);

                if (addDisMap.containsKey(doctorId)) {
                    List<ReportDto> list = addDisMap.get(doctorId);

                    for (ReportDto reportDto : list) {
                        if (reportDto.getDeptId() == dto.getDeptId()) {
                            dto.setAddDisease(reportDto.getTotalDisease() == null ? 0 : reportDto.getTotalDisease());
                            break;
                        }
                    }

                }

                if (addProjMap.containsKey(doctorId)) {
                    List<ReportDto> list = addProjMap.get(doctorId);

                    for (ReportDto reportDto : list) {
                        if (reportDto.getDeptId() == dto.getDeptId()) {
                            dto.setAddProject(reportDto.getTotalProject() == null ? 0 : reportDto.getTotalProject());
                            break;
                        }
                    }

                }
            }
        } else {
            for (ReportDto dto : allCount) {
                Integer totalProject = dto.getTotalProject();
                if (totalProject == null || totalProject == 0) {
                    dto.setTotalDisease(0);
                    dto.setTotalProject(0);
                }
                dto.setAddDisease(dto.getTotalDisease() == null ? 0 : dto.getTotalDisease());
                dto.setAddProject(dto.getTotalProject() == null ? 0 : dto.getTotalProject());
            }
        }

        return allCount;
    }

    /**
     * 从科林获取单页用户接口
     *
     * @param size
     * @param page
     * @param token
     * @return
     */
    private List<UserHisForeignVo> getUserList(int size, int page, String token) {
        // 1.获取数据
        String result = userHttpApiService.getList("Bearer " + token, size, page);
        List<UserHisForeignVo> list = new ArrayList<>();

        if (result != null) {
            if (!result.isEmpty()) {// 2.解析数据
                JSONObject jsonObject = JSON.parseObject(result);
                JSONArray rows = jsonObject.getJSONArray("rows");
                if (!CollectionUtils.isEmpty(rows)) {
                    List<KelinUserModel> kelinUserModels = rows.toJavaList(KelinUserModel.class);
                    kelinUserModels.forEach(kelinUserModel -> {
                        UserHisForeignVo userHisForeignVo = new UserHisForeignVo();
                        userHisForeignVo.setUserNickname(kelinUserModel.getXM());
                        userHisForeignVo.setHisForeignId(kelinUserModel.getGH());
                        userHisForeignVo.setDeptCode(kelinUserModel.getBMDM());
                        userHisForeignVo.setDeptName(kelinUserModel.getBMMC());
                        list.add(userHisForeignVo);
                    });
                }
            }
        } else {
            throw new BusinessException("科林获取用户列表有误");
        }

        return list;
    }

    private List<ReportDto> list(String doctorName, List<Long> deptId) {
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();

        if (!CollectionUtils.isEmpty(deptId)) {
            qw.in("a.dept_id", deptId);
        }

        // 1.获取全部项目与病种全量统计
        if (!StringUtils.isEmpty(doctorName)) {
            qw.like("a.nickname", doctorName);
        }

        return this.baseMapper.listAll(qw);
    }
}
