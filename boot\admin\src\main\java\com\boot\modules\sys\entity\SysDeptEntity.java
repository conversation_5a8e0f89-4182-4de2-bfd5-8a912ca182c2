package com.boot.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


/**
 * 机构部门管理
 *
 * <AUTHOR>
 */
@Data
@TableName("rp_sys_dept")
public class SysDeptEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @TableId
    private Long id;
    /**
     * 上级部门ID，一级部门为0
     */
    private Long pid;
    /**
     * 部门名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @Size(max = 20, message = "查询项目名称长度不能超过20", groups = {AddGroup.class, UpdateGroup.class})
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "名称不能包含特殊字符", groups = {AddGroup.class, UpdateGroup.class})
    private String name;

    /**
     * 科室编码
     */
    @NotBlank(message = "科室编码不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String code;

    /**
     * 部门级别
     */
//	private String level;

    /**
     * 排序
     */
    private Integer sort;

    @TableLogic
    private Integer delFlag;

    /**
     * 是否作为研究中心 默认为否0  1：可以作为研究中心
     */
    private Integer isResearchCenter;

    /**
     * 是否属于数据中心
     * 0:否   1；是
     */
    private Integer isDataCenter;

    /**
     * 是否同步子机构的编码code
     * 0:否   1；是
     */
    @TableField(exist = false)
    private Integer isSyncChildDept;

    /**
     * 上级部门名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * tree属性
     */
    @TableField(exist = false)
    private Boolean open;

    @TableField(exist = false)
    private Integer level;


    @TableField(exist = false)
    private List<?> children;

    @TableField(exist = false)
    private List<?> userList;
}
