package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boot.modules.project.entity.ProjectRoleSettingEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 项目角色高级设置
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface ProjectRoleSettingDao extends BaseMapper<ProjectRoleSettingEntity> {

    /**
     * 获取指定用户在指定子项目中的权限
     *
     * @param
     * @return
     */
    List<ProjectRoleSettingEntity> getByUserIdAndSubProjId(@Param("subProjectId") Long subProjectId, @Param("userId") Long userId);

    /**
     * 获取指定用户在指定子项目中的权限
     *
     * @param
     * @return
     */
    List<ProjectRoleSettingEntity> getByAdminUserIdAndSubProjId(@Param("subProjectId") Long subProjectId, @Param("userId") Long userId);
}
