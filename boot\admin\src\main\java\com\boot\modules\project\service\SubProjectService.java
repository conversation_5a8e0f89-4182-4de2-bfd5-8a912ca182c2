package com.boot.modules.project.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.entity.SysUserEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :子项目服务
 * @create 2021-03-18
 */
public interface SubProjectService extends IService<SubProjectEntity> {


    /**
     * 根据项目id 查询当前用户的所有研究组
     *
     * @param projectId
     * @param userId
     * @return
     */
    List<SubProjectVo> getByProjectId(Long projectId, Long userId);

    List<SubProjectVo> getByProjectId(Long projectId, Long userId, QueryWrapper<SubProjectVo> qw);

    /**
     * 获取登录
     * @param projectId
     * @param userInfo
     * @return
     */
//    List<SubProjectVo> getByProjectId(Long projectId, SysUserEntity userInfo);

    /**
     * 查询当前用户的所有子项目
     *
     * @param userId
     * @return
     */
    List<SubProjectVo> getByQuery(Long projectId, Long userId);

    /**
     * 获取当前登录用户的所有enabled为1的子项目信息,也获取该用户角色为项目管理员的子项目信息
     * @param userId
     * @return
     */
    List<SubProjectVo> getByUserId(Long userId);

    boolean add(SubProjectEntity researchGroup, ProjectEntity projectEntity);

    /**
     * 批量删除指定id子项目
     *
     * @param ids
     * @return
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 对查询数据脱敏
     *
     * @param paramMap
     * @param patientData
     * @return
     */
    List<Map<String, Object>> tuominForSearch(Map<String, Object> paramMap, List<Map<String, Object>> patientData);
    /**
     * 查指定子项目的私密设置(功能改造)
     * @param subProjectId
     * @return
     * chenwei 2022/10/24
     */
    boolean updateVisual (Long subProjectId);

    List<SysUserEntity> getUserBySubProjectId(Long projectId,Long subProjectId);

    /**
     * 根据条件查询子项目
     * @param param
     * @param projectType
     * @return
     */
    PageUtils getSubjectListByQuery(Map<String, Object> param, Integer projectType);


    /**
     * 查询历史推荐的子项目
     * @param param
     * @param projectType
     * @return
     */
    PageUtils getHisRecProject(Map<String, Object> param, Integer projectType);
}
