package com.boot.modules.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.annotation.Login;
import com.boot.commons.annotation.SysLog;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.constants.Const;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.*;
import com.boot.commons.validator.ValidatorUtils;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.vo.ImportVo;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.DiseaseEntity;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.DiseaseService;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc : 项目相关接口  controller
 * @create 2021-02-23
 */

@Api(tags = "项目管理")
@RestController
@RequestMapping("/proj/project")
public class ProjectController extends AbstractController {

    @Resource
    private ProjectService projectService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    SSOConfigurationProperties ssoConfig;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private DiseaseService diseaseService;

    @Resource
    private SysDeptService deptService;

    @ApiOperation(
            value = "获取全部项目列表",
            notes = "获取全部项目列表"
    )
    @Login
    @GetMapping("/all")
    @RequiresPermissions("proj:project:all")
    public Result all(@RequestParam Map<String, Object> params) {
        SysUserEntity user = getUser();

        if (params.get(Const.LIMIT) == null) {
            // 获取全部
            List<ProjectVo> list = projectService.queryList(params, user, true, false);

            return R.success(list);
        } else {
            PageUtils page = projectService.queryPage(params, user, true, false);

            return R.success(page);
        }
    }

    @ApiOperation(
            value = "分页获取相关项目",
            notes = "分页获取相关项目"
    )
    @GetMapping("/page")
    @RequiresPermissions("proj:project:all")
    public Result page(@RequestParam Map<String, Object> params) {
        Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
        if (roleType != null && roleType.equals(2)) {
            params.put("mainDeptId", getAdminDeptId());
        }
        return R.success(projectService.page(params));
    }

    @ApiOperation(
            value = "项目总数统计",
            notes = "项目总数统计"
    )
    @GetMapping("/total")
    @RequiresPermissions("proj:project:all")
    public Result total(@RequestParam Map<String, Object> params) {
        Integer roleType = MapUtils.getValue(params, "roleType", Integer.class);
        if (roleType != null && roleType.equals(2)) {
            params.put("mainDeptId", getAdminDeptId());
        }
        return R.success(projectService.total(params));
    }

    @ApiOperation(
            value = "项目导出",
            notes = "项目导出"
    )
    @GetMapping("/export")
    @RequiresPermissions("proj:project:all")
    public void export(@RequestParam Map<String, Object> params,
                       HttpServletResponse response) {
        projectService.export(response, params);
    }

    @ApiOperation(
            value = "项目列表",
            notes = "项目列表"
    )
    @Login
    @GetMapping("/list")
    @RequiresPermissions("proj:project:list")
    public Result list(@RequestParam Map<String, Object> params) {
        SysUserEntity user = getUser();
        if (params.get(Const.LIMIT) == null) {
            // 获取全部
            List<ProjectVo> list = projectService.queryList(params, user, false, true);
            projectService.getRoleInfo(list, getUserId());
            List<ProjectVo> listRet = CollectionUtils.isEmpty(list) ? null : list.stream().filter(p -> !CollectionUtils.isEmpty(p.getSubProjList())
                    && !ObjectUtils.isEmpty(p.getSubProjList().get(0).getRoleInfo())
                    && !StringUtils.isEmpty(p.getSubProjList().get(0).getRoleInfo().getRoleName())
                    && !p.getSubProjList().get(0).getRoleInfo().getRoleName().equals("普通成员")).collect(Collectors.toList());
            // 过滤掉角色为普通用户的项目
            return R.success(listRet);
        } else {
            PageUtils page = projectService.queryPage(params, user, false, true);
            return R.success(page);
        }
    }


    @ApiOperation(value = "获取当前用户为项目管理员的所有项目列表", notes = "获取当前用户为项目管理员的所有项目列表")
    @Login
    @GetMapping("/list/admin")
    @RequiresPermissions("proj:project:list")
    public Result listAdmin() {

        SysUserEntity user = getUser();
        List<ProjectVo> list = projectService.queryAdminProjectByUser(user);

        return R.success(list);


    }


    @ApiOperation(
            value = "获取指定项目的信息",
            notes = "获取指定项目的信息"
    )
    @GetMapping("/{projectId}")
    @RequiresPermissions("proj:project:info")
    public Result info(@PathVariable("projectId") Long projectId) {
        ProjectVo projectVo = projectService.getInfoById(projectId);
        // 获取子项目信息
        QueryWrapper<SubProjectEntity> qwSubPrj = new QueryWrapper<>();
        qwSubPrj.lambda().eq(SubProjectEntity::getProjectId, projectId);
        List<SubProjectEntity> subProjectEntityList = subProjectService.list(qwSubPrj);
        if (!CollectionUtils.isEmpty(subProjectEntityList)) {
            String subProjectId = AesUtil.encrypt(subProjectEntityList.get(0).getId().toString(), ssoConfig.getAppCode());
            projectVo.setGuid(subProjectId);
            projectVo.setEnableApi(subProjectEntityList.get(0).getEnableApi());
            projectVo.setEnableAudit(subProjectEntityList.get(0).getEnableApi());
        }
        return R.success(projectVo);
    }

    @ApiOperation(
            value = "新增项目",
            notes = "新增项目"
    )
    @SysLog("新增项目")
    @PostMapping()
    @RequiresPermissions("proj:project:save")
    public Result save(@RequestBody ProjectVo project) {
        ValidatorUtils.validateEntity(project, AddGroup.class);
        //唯一性验证---项目名称唯一性验证
        QueryWrapper<ProjectEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjectEntity::getProjectName, project.getProjectName());
        int count = projectService.count(qw);
        if (count > 0) {
            return R.fail("该项目名称已存在");
        }

        project.setCreateTime(new Date());
        project.setCreateUserId(getUserId());

        // 设置所属机构ID
        Long topRecommendDeptId = null;
        Long mainDeptId = project.getMainDeptId();
        if (mainDeptId != null) {
            //根据科室ID获取机构
            List<SysDeptEntity> deptEntities = deptService.getAll();
            SysDeptEntity dept = deptService.getTopDeptById(deptEntities, mainDeptId);
            if (dept != null) {
                topRecommendDeptId = dept.getId();
            }
        }
        project.setMainMedId(topRecommendDeptId);
        boolean res = projectService.add(project);

        return res ? R.success(project) : R.fail("新增失败");
    }

    @ApiOperation(
            value = "修改项目",
            notes = "修改项目"
    )
    @SysLog("修改项目")
    @PutMapping()
    @RequiresPermissions("proj:project:update")
    public Result update(@RequestBody ProjectVo projectVo) {
        ValidatorUtils.validateEntity(projectVo, UpdateGroup.class);

        // 设置所属机构ID
        Long topRecommendDeptId = null;
        Long mainDeptId = projectVo.getMainDeptId();
        if (mainDeptId != null) {
            //根据科室ID获取机构
            List<SysDeptEntity> deptEntities = deptService.getAll();
            SysDeptEntity dept = deptService.getTopDeptById(deptEntities, mainDeptId);
            if (dept != null) {
                topRecommendDeptId = dept.getId();
            }
        }
        projectVo.setMainMedId(topRecommendDeptId);
        boolean res = projectService.updateProject(projectVo);
        return res ? R.success("修改成功") : R.fail("修改失败");
    }


    @ApiOperation(
            value = "删除项目",
            notes = "删除项目"
    )
    @SysLog("删除项目")
    @DeleteMapping()
    @RequiresPermissions("proj:project:delete")
    public Result delete(@RequestBody Long[] projectIds) {
        boolean res = projectService.removeProjects(Arrays.asList(projectIds));

        return res ? R.success("删除成功", true) : R.fail("删除失败");
    }

    @ApiOperation(
            value = "excel导入项目",
            notes = "excel导入项目"
    )
    @Login
    @PostMapping("/excel/import")
    public Result excelImport(@RequestParam("file") MultipartFile file) {
        if (file == null) {
            throw new BusinessException("上传的文件为空");
        }
        projectService.excelImport(file, getUserId());
        return R.success("导入成功");
    }

    @ApiOperation(value = "导入项目模板下载", notes = "导入项目模板下载")
    @GetMapping("/excel/download")
    public Result templateDownload(HttpServletResponse response, @RequestParam Map<String, Object> params) throws NoSuchFieldException {
        String fileName = "projectTemplate";

        // 设置sheet1一级表头信息
        List<List<String>> heads = new ArrayList<>();
        heads.add(Collections.singletonList("项目名称(必填)"));
        heads.add(Collections.singletonList("研究中心类型(必填; 1表示单中心,2表示多中心)"));
        heads.add(Collections.singletonList("项目类型(1表示干预性IIT,2表示GCP,3表示前瞻观察性,4表示专病队列)"));
        heads.add(Collections.singletonList("关联病种ID(必填)"));
        heads.add(Collections.singletonList("科室ID(必填)"));
        heads.add(Collections.singletonList("分中心ID(多中心项目)"));
        heads.add(Collections.singletonList("PI(必填)"));
        heads.add(Collections.singletonList("开始时间"));
        heads.add(Collections.singletonList("结束时间"));
        heads.add(Collections.singletonList("项目状态(必填; 0表示立项,1表示在研,2表示结束)"));
        heads.add(Collections.singletonList("签署知情同意有效期(必填; 单位天)"));
        heads.add(Collections.singletonList("立项年度"));
        heads.add(Collections.singletonList("伦理批件号"));
        heads.add(Collections.singletonList("项目联系人"));
        heads.add(Collections.singletonList("项目联系人电话"));
        heads.add(Collections.singletonList("多中心角色（1表示牵头,0表示参与）"));
        heads.add(Collections.singletonList("多中心描述"));
        heads.add(Collections.singletonList("预计入组量"));
        heads.add(Collections.singletonList("导入失败信息"));
        heads.add(Collections.singletonList("同步到EDC（1表示是,0表示否）"));
        heads.add(Collections.singletonList("启用审核（1表示启用,0表示不启用）"));
        heads.add(Collections.singletonList("启用图像下载（1表示启用,0表示不启用）"));
        heads.add(Collections.singletonList("启用华医通（1表示启用,0表示不启用）"));

        // 设置sheet2一级表头信息
        List<List<String>> heads1 = new ArrayList<>();
        heads1.add(Collections.singletonList("病种ID"));
        heads1.add(Collections.singletonList("病种名称"));
        //获取所有病种信息
        List<DiseaseEntity> diseaseEntityList = diseaseService.list();
        List<ImportVo> diseaseVoList = new ArrayList<>();
        //构造返回的结构
        for (DiseaseEntity diseaseEntity : diseaseEntityList) {
            ImportVo deptVo = new ImportVo();
            deptVo.setFirst(diseaseEntity.getId());
            deptVo.setSecond(diseaseEntity.getDiseaseName());
            diseaseVoList.add(deptVo);
        }

        // 设置sheet3一级表头信息
        List<List<String>> heads2 = new ArrayList<>();
        heads2.add(Collections.singletonList("中心ID"));
        heads2.add(Collections.singletonList("中心名称"));
        //获取所有科室信息
        List<SysDeptEntity> sysDeptEntityList = sysDeptService.queryListShowResearchCenter();
        List<ImportVo> deptVoList = new ArrayList<>();
        //构造返回的结构
        for (SysDeptEntity sysDeptEntity : sysDeptEntityList) {
            ImportVo deptVo = new ImportVo();
            deptVo.setFirst(sysDeptEntity.getId());
            deptVo.setSecond(sysDeptEntity.getName());
            deptVoList.add(deptVo);
        }

        // 设置sheet3一级表头信息
        List<List<String>> heads3 = new ArrayList<>();
        heads3.add(Collections.singletonList("用户ID"));
        heads3.add(Collections.singletonList("用户登录名"));
        heads3.add(Collections.singletonList("用户昵称"));
        //获取所有用户信息
        List<SysUserEntity> sysUserEntityList = sysUserService.list();
        //构造返回的结构
        List<ImportVo> userVoList = new ArrayList<>();
        for (SysUserEntity sysUserEntity : sysUserEntityList) {
            ImportVo userVo = new ImportVo();
            userVo.setFirst(sysUserEntity.getId());
            userVo.setSecond(sysUserEntity.getUsername());
            userVo.setThird(sysUserEntity.getNickname());
            userVoList.add(userVo);
        }

        // 下载用户批量导入excel模板
        Map<String, List<List<String>>> headList = new LinkedHashMap<>();
        headList.put("导入项目", heads);
        headList.put("病种信息", heads1);
        headList.put("中心信息", heads2);
        headList.put("用户信息", heads3);
        Map<String, List<ImportVo>> dataList = new LinkedHashMap<>();
        dataList.put("病种信息", diseaseVoList);
        dataList.put("中心信息", deptVoList);
        dataList.put("用户信息", userVoList);
        ExcelUtils.wirteExcel(response, fileName, headList, dataList);
        return R.success("下载成功");
    }

    @ApiOperation(
            value = "修改项目医联体可见性",
            notes = "修改项目医联体可见性"
    )
    @SysLog("修改项目医联体可见性")
    @PostMapping("/visual")
    @RequiresPermissions("center:update:private")
    public Result visual(@RequestBody Map<String, Object> params) {
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        boolean ret = projectService.updateVisual(projectId);
        return ret ? R.success("切换成功") : R.fail("切换失败");
    }
}
