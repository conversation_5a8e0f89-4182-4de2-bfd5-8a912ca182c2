package com.boot.modules.sync.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.commons.constant.HxConst;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.ObjectUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.external.service.KeLinService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.project.dao.EnrollDetailDao;
import com.boot.modules.project.dao.GroupConfigDao;
import com.boot.modules.project.entity.EnrollDetailEntity;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.project.redis.EnrollDetailRedis;
import com.boot.modules.project.dao.EnrollRecordDao;
import com.boot.modules.sync.dao.KelinConfigDao;
import com.boot.modules.project.entity.EnrollRecordEntity;
import com.boot.modules.sync.entity.KelinConfigEntity;
import com.boot.modules.sync.model.KelinSearchExportResponse;
import com.boot.modules.sync.model.KelinSearchExportResponseData;
import com.boot.modules.sync.model.KelinSearchResponse;
import com.boot.modules.sync.service.KeLinExportHttpApiService;
import com.boot.modules.sync.service.KelinConfigService;
import com.boot.modules.sync.utils.JwtForKelin;
import com.boot.modules.sync.vo.PageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KelinConfigServiceImpl extends ServiceImpl<KelinConfigDao, KelinConfigEntity> implements KelinConfigService {
    @Resource
    private KelinConfigDao kelinConfigDao;

    @Resource
    private KeLinExportHttpApiService keLinExportHttpApiService;

    @Resource
    private EnrollRecordDao enrollRecordDao;

    @Resource
    private KeLinService keLinService;

    @Resource
    private EnrollDetailRedis enrollDetailRedis;

    @Resource
    private EnrollDetailDao enrollDetailDao;

    @Resource
    private GroupConfigDao groupConfigDao;

    @Resource
    private PatientService patientService;

    @Resource
    SSOConfigurationProperties ssoConfig;

    @Override
    public Boolean update(Long diseaseId, Integer status, String message, Integer notice) {
        UpdateWrapper<KelinConfigEntity> w = new UpdateWrapper<>();
        w.lambda().eq(KelinConfigEntity::getDiseaseId, diseaseId);
        w.lambda().set(KelinConfigEntity::getStatus, status);
        w.lambda().set(KelinConfigEntity::getNoticeStatus, notice);
        if (!StringUtils.isEmpty(message)) {
            w.lambda().set(KelinConfigEntity::getMessage, message);
        }
        return this.update(w);
    }

    @Override
    public Boolean firstSearch(Long diseaseId, Long expressionId, Long exportId) throws ParseException {
        //1.查指定diseaseId的纳排任务表
        QueryWrapper<KelinConfigEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(KelinConfigEntity::getDiseaseId, diseaseId)
                .eq(KelinConfigEntity::getExpressionId, expressionId)
                .eq(KelinConfigEntity::getExportId, exportId);
        List<KelinConfigEntity> configList = kelinConfigDao.selectList(qw);
        if (CollectionUtils.isEmpty(configList)) {
            log.error("未找到指定的纳排任务:策略ID{}", diseaseId);
            return false;
        }
        if (configList.size() > 1) {
            log.error("检索策略有多条记录:策略ID{}", diseaseId);
            return false;
        }
        //2.查检索导出数据,入组，修改执行时间，保存执行记录、错误记录
        KelinConfigEntity kelinConfigEntity = configList.get(0);
        //3.删除旧有rp_enroll_detail表单数据
        enrollDetailDao.delete(new QueryWrapper<EnrollDetailEntity>().lambda()
                .eq(EnrollDetailEntity::getSubProjectId, kelinConfigEntity.getSubProjectId()));
        //4.同步数据
        this.asyncPatient(kelinConfigEntity, false);
        return true;
    }

    /**
     * 同步病例
     *
     * @param kelinConfigEntity 实体类
     * @param isDecreased       是否增量
     * @throws ParseException
     */
    @Async("bootExecutor")
    @Override
    public void asyncPatient(KelinConfigEntity kelinConfigEntity, Boolean isDecreased) throws ParseException {
        //2.当前任务的执行记录
        EnrollRecordEntity enrollRecordEntity = new EnrollRecordEntity();
        enrollRecordEntity.setSubProjectId(kelinConfigEntity.getSubProjectId());
        enrollRecordEntity.setProjectId(kelinConfigEntity.getProjectId());
        enrollRecordEntity.setGroupConfigId(kelinConfigEntity.getGroupConfigId());
        //默认初值失败
        enrollRecordEntity.setStatus(2);
        enrollRecordEntity.setType(!isDecreased ? 0 : 1);
        String startTime = kelinConfigEntity.getKlStartTime();
        String endTime = kelinConfigEntity.getKlEndTime();
        if (!isDecreased) {
            endTime = "";
        }
        //无论成功与否都应设置初值0
        //保证下次执行能通知科林写数据
        kelinConfigEntity.setNoticeStatus(0);
        enrollRecordEntity.setStartTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        if (isDecreased) {
            //增量
            QueryWrapper<GroupConfigEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(GroupConfigEntity::getId, kelinConfigEntity.getGroupConfigId());
            List<GroupConfigEntity> groupConfigEntity = groupConfigDao.selectList(qw);
            if (CollectionUtils.isEmpty(groupConfigEntity)) {
                log.error("无id为：{}的纳排配置", kelinConfigEntity.getGroupConfigId());
                kelinConfigEntity.setMessage("无纳排配置");
                this.updateById(kelinConfigEntity);
                enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                enrollRecordDao.insert(enrollRecordEntity);
                return;
            }
            GroupConfigEntity group = groupConfigEntity.get(0);
            // 1.查导出病例状态
            PageVo pageVo = new PageVo();
            JSONObject jsonObject = new JSONObject(new LinkedHashMap<>());
            BigInteger exportId = new BigInteger(kelinConfigEntity.getExportId().toString());
            int[] saveType = new int[2];
            saveType[0] = 5;
            saveType[1] = 6;
            jsonObject.put("exportId", exportId);
            jsonObject.put("expression", group.getExpression());
            jsonObject.put("page", pageVo);
            jsonObject.put("saveType", saveType);
            jsonObject.put("status", "");
            jsonObject.put("platformCode", ssoConfig.getAppCode());

            //1.请求判断总数是否大于页宽
            KelinSearchExportResponse exportStatusRes = keLinExportHttpApiService.appliedRecords(jsonObject, JwtForKelin.getJwt());

            if (ObjectUtils.isEmpty(exportStatusRes) || exportStatusRes.getCode() == null || exportStatusRes.getCode() != HxConst.RESPONSE_SUCCESS) {
                log.error("查询柯林接口异常（导出病例状态）--HTTP状态码:{}, 异常原因:{}", exportStatusRes.getCode(), exportStatusRes.getData());
                log.error("查询柯林接口异常（导出病例状态）");
                kelinConfigEntity.setMessage("查询柯林接口异常（导出病例状态）");
                this.updateById(kelinConfigEntity);
                enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                enrollRecordDao.insert(enrollRecordEntity);
                return;
            }

            List<Map<String, Object>> exportStatusList = exportStatusRes.getData().getData();
            if (CollectionUtils.isEmpty(exportStatusList)
                    || exportStatusList.size() == 0
                    || exportStatusList.get(0) == null
                    || exportStatusList.get(0).get("status") == null) {
                log.error("获取导出状态异常");
                kelinConfigEntity.setMessage("获取导出状态异常");
                this.updateById(kelinConfigEntity);
                enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                enrollRecordDao.insert(enrollRecordEntity);
                return;
            }

            String exportStatus = exportStatusList.get(0).get("status").toString();
            if (!"2".equals(exportStatus)) {
                //导出未完成，退出
                log.error("导出未完成");
                kelinConfigEntity.setMessage("导出未完成");
                this.updateById(kelinConfigEntity);
                enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                enrollRecordDao.insert(enrollRecordEntity);
                return;
            }
            Integer count = exportStatusRes.getData().getTotal();

            if (count.equals(0)) {
                //更新任务状态
                log.error("无数据");
                kelinConfigEntity.setMessage("无数据");
                kelinConfigEntity.setNoticeStatus(0);
                kelinConfigEntity.setKlStartTime(startTime);
                this.updateById(kelinConfigEntity);
                enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
                enrollRecordDao.insert(enrollRecordEntity);
                return;
            }
        }
        //首次
        //3.查检索导出数据
        PageVo page = new PageVo();
        Map<String, Object> request = new LinkedHashMap<>();
        request.put("exportId", kelinConfigEntity.getExportId().toString());
        request.put("diseaseExpressionId", Collections.singletonList(kelinConfigEntity.getExpressionId().toString()));
        request.put("platformCode", ssoConfig.getAppCode());
        request.put("page", page);
        request.put("startDate", startTime);
        request.put("endDate", endTime);
        JSONObject jsonObject = new JSONObject(request);
        //设置首次的开始时间

        KelinSearchExportResponse exportDataRes = keLinExportHttpApiService.expressionDimension(jsonObject, JwtForKelin.getJwt());
        if (exportDataRes.getData().getTotal() > page.getPageSize()) {
            KelinSearchExportResponseData kelinSearchExportResponseData = exportDataRes.getData();
            List<Map<String, Object>> resultData = new ArrayList<>(exportDataRes.getData().getData());
            //2.循环请求，更改当前页码
            for (int i = 2; i < exportDataRes.getData().getTotal() / page.getPageSize() + 2; i++) {
                page.setCurrentPage(i);
                request.put("page", page);
                jsonObject = new JSONObject(request);
                KelinSearchExportResponse result = keLinExportHttpApiService.expressionDimension(jsonObject, JwtForKelin.getJwt());
                List<Map<String, Object>> data = result.getData().getData();
                resultData.addAll(data);
            }
            //得到循环后的结果
            kelinSearchExportResponseData.setData(resultData);
            exportDataRes.setData(kelinSearchExportResponseData);
        }
        //TestData
//        exportDataRes = new KelinSearchExportResponse();
//        exportDataRes.setCode(HxConst.RESPONSE_SUCCESS);
//        KelinSearchExportResponseData data = new KelinSearchExportResponseData();
//        data.setCurrentPage(1);
//        data.setPageSize(10);
//        data.setTotal(10);
//        List<Map<String, Object>> datalist = new ArrayList<>();
//        Map<String, Object> dataOne = new HashMap<>();
//        dataOne.put("value", 1260053);
//        datalist.add(dataOne);
//        Map<String, Object> dataTwo = new HashMap<>();
//        dataTwo.put("value",2539513);
//        datalist.add(dataTwo);
//        data.setData(datalist);
//        exportDataRes.setData(data);

        if (ObjectUtils.isEmpty(exportDataRes) || exportDataRes.getCode() == null || exportDataRes.getCode() != HxConst.RESPONSE_SUCCESS) {
            log.error("查询柯林接口异常（导出病例数据）--HTTP状态码:{}, 异常原因:{}", exportDataRes.getCode(), exportDataRes.getData());
            kelinConfigEntity.setMessage("查询柯林接口异常（导出病例数据）");
            this.updateById(kelinConfigEntity);
            enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            enrollRecordDao.insert(enrollRecordEntity);
            return;
        }
        List<Map<String, Object>> exportDataList = exportDataRes.getData().getData();
        if (CollectionUtils.isEmpty(exportDataList) || exportDataList.size() == 0) {
            kelinConfigEntity.setMessage("获取导出数据异常");
            this.updateById(kelinConfigEntity);
            enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            enrollRecordDao.insert(enrollRecordEntity);
            return;
        }
        // 科林导出数据
        List<String> dataList = exportDataList.stream().map(p -> String.valueOf(p.get("value"))).collect(Collectors.toList());
        List<String> empiList;
        List<String> admIdList;
        List<Map<String, Object>> exportAdmDataList;
        int totalCnt = dataList.size();
        int fCnt = 0;
        enrollRecordEntity.setTotalCount(totalCnt);
        KelinSearchResponse exportAdmDataRes = keLinService.getAdmByVisitId(dataList.toArray(new String[dataList.size()]));
        if (exportAdmDataRes == null) {
            kelinConfigEntity.setMessage("查询柯林接口异常（导出病例就诊数据)");
            this.updateById(kelinConfigEntity);
            enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            enrollRecordDao.insert(enrollRecordEntity);
            log.error("查询柯林接口异常（导出病例就诊数据)返回为空");
            return;
        }
        if (exportAdmDataRes.getCode() == null || exportAdmDataRes.getCode() != HxConst.RESPONSE_SUCCESS) {
            kelinConfigEntity.setMessage("查询柯林接口异常（导出病例就诊数据)");
            this.updateById(kelinConfigEntity);
            enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            enrollRecordDao.insert(enrollRecordEntity);
            log.error("查询柯林接口异常（导出病例就诊数据）--HTTP状态码:{}, 异常原因:{}", exportAdmDataRes.getCode(), exportAdmDataRes.getData());
            return;
        }
        exportAdmDataList = exportAdmDataRes.getData().getList();
        if (CollectionUtils.isEmpty(exportAdmDataList) || exportAdmDataList.size() == 0) {
            kelinConfigEntity.setMessage("获取导出病例就诊数据异常");
            this.updateById(kelinConfigEntity);
            enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            enrollRecordDao.insert(enrollRecordEntity);
            return;
        }
        //就诊-empi的map
        Map<String, String> nMap = new LinkedHashMap<>();
        //就诊-就诊时间的map
        Map<String, String> mMap = new LinkedHashMap<>();
        //就诊-就诊医生的map
        Map<String, String> dMap = new LinkedHashMap<>();
        //empi-登记号的map
        Map<String, String> rMap = new LinkedHashMap<>();
        for (Map<String, Object> map : exportAdmDataList) {
            if (ObjectUtils.isEmpty(map.get(HxConst.KELIN_EMPI_ID_FIELD)) ||
                    ObjectUtils.isEmpty(map.get(HxConst.KELIN_VISIT_ID_FIELD))) {
                log.error("柯林参数错误失败：empi:{},visitid:{}", map.get(HxConst.KELIN_EMPI_ID_FIELD), map.get(HxConst.KELIN_VISIT_ID_FIELD));
                continue;
            }
            //结果集存放map里，key-就诊，value-empi
            nMap.put(map.get(HxConst.KELIN_VISIT_ID_FIELD).toString(), map.get(HxConst.KELIN_EMPI_ID_FIELD).toString());
            mMap.put(map.get(HxConst.KELIN_VISIT_ID_FIELD).toString(), map.get(HxConst.KELIN_VISIT_TIME_FIELD) == null
                    ? "" : map.get(HxConst.KELIN_VISIT_TIME_FIELD).toString());
            dMap.put(map.get(HxConst.KELIN_VISIT_ID_FIELD).toString(), map.get(HxConst.KELIN_ATTEND_DOCTOR_FIELD) == null
                    ? "" : map.get(HxConst.KELIN_ATTEND_DOCTOR_FIELD).toString());
            //记录患者第一次符合条件的就诊的登记号
            if (!rMap.containsKey(map.get(HxConst.KELIN_EMPI_ID_FIELD).toString())) {
                rMap.put(map.get(HxConst.KELIN_EMPI_ID_FIELD).toString(), map.get(HxConst.KELIN_VISIT_PERSNO_FIELD) == null
                        ? "" : map.get(HxConst.KELIN_VISIT_PERSNO_FIELD).toString());
            }
        }
        //判空
        if (CollectionUtils.isEmpty(nMap)) {
            kelinConfigEntity.setMessage("获取导出病例就诊数据列表为空");
            this.updateById(kelinConfigEntity);
            enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
            enrollRecordDao.insert(enrollRecordEntity);
            return;
        }
        // 过滤掉非华西医院的
        exportAdmDataList = exportAdmDataList.stream().filter(p -> p.get(HxConst.KELIN_VISIT_MED_CODE_FIELD) != null && p.get(HxConst.KELIN_VISIT_MED_CODE_FIELD).toString().equals(HxConst.HX_DEPT_CODE)).collect(Collectors.toList());
        empiList = exportAdmDataList.stream().map(p -> p.get(HxConst.KELIN_EMPI_ID_FIELD).toString()).distinct().collect(Collectors.toList());
        admIdList = exportAdmDataList.stream().map(p -> p.get(HxConst.KELIN_VISIT_ID_FIELD).toString()).distinct().collect(Collectors.toList());
        log.info("策略id:{}, 查询的患者数量:{}", kelinConfigEntity.getDiseaseId(), empiList.size());
        //就诊对照
        for (String admId : dataList) {
            EnrollDetailEntity enrollDetailEntity = new EnrollDetailEntity();
            enrollDetailEntity.setGroupConfigId(kelinConfigEntity.getGroupConfigId());
            enrollDetailEntity.setRecordId(kelinConfigEntity.getId());
            enrollDetailEntity.setSubProjectId(kelinConfigEntity.getSubProjectId());
            enrollDetailEntity.setProjectId(kelinConfigEntity.getProjectId());
            try {
                if (admIdList.contains(admId)) {
                    enrollDetailEntity.setEmpi(nMap.get(admId));
                    enrollDetailEntity.setVisitId(Long.parseLong(admId));
                    enrollDetailEntity.setStatus(1);
                    enrollDetailEntity.setVisitDate(mMap.get(admId));
                    enrollDetailEntity.setDoctorName(dMap.get(admId));
                    // 首次时，记录今日就诊患者
                    if (!isDecreased && mMap.get(admId).contains(DateUtils.getNowTimeStr())) {
                        enrollDetailRedis.saveOnedayEmpi(nMap.get(admId));
                    }
                } else {
                    if (nMap.get(admId) != null) {
                        //非华西的就诊
                        enrollDetailEntity.setEmpi(nMap.get(admId));
                        enrollDetailEntity.setVisitId(Long.parseLong(admId));
                        enrollDetailEntity.setStatus(2);
                        enrollDetailEntity.setVisitDate(mMap.get(admId));
                        enrollDetailEntity.setDoctorName(dMap.get(admId));
                        enrollDetailEntity.setMessage("非华西本院的就诊");
                    } else {
                        enrollDetailEntity.setEmpi("");
                        enrollDetailEntity.setVisitId(Long.parseLong(admId));
                        enrollDetailEntity.setStatus(2);
                        enrollDetailEntity.setVisitDate("");
                        enrollDetailEntity.setMessage("未在柯林查询到就诊对应的empi");
                    }
                }
                //循环写入执行详情表
                enrollDetailDao.insert(enrollDetailEntity);
                //异常就诊，不写入redis
                if (!enrollDetailEntity.getStatus().equals(2)) {
                    enrollDetailRedis.saveOrUpdateEmpi(enrollDetailEntity);
                    enrollDetailRedis.saveOrUpdateConfig(enrollDetailEntity);
                    enrollDetailRedis.saveOrUpdatePatient(enrollDetailEntity);
                    enrollDetailRedis.saveDoctorEmpi(enrollDetailEntity);
                }
            } catch (Exception ex) {
                fCnt++;
                log.error("写入失败：" + JSONObject.toJSONString(enrollDetailEntity));
                ex.printStackTrace();
                continue;
            }
        }
        enrollRecordEntity.setFailCount(fCnt);
        enrollRecordEntity.setSuccessCount(totalCnt - fCnt);
        enrollRecordEntity.setStatus(1);
        enrollRecordEntity.setEndTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        enrollRecordDao.insert(enrollRecordEntity);
        //更新通知状态为未通知
        kelinConfigEntity.setMessage("");
        kelinConfigEntity.setNoticeStatus(0);
        //只有成功才会更新下次的开始时间
        //开始时间-上次的结束时间 结束时间-下次通知时间
        kelinConfigEntity.setKlStartTime(kelinConfigEntity.getKlEndTime());
        this.updateById(kelinConfigEntity);
        //患者录入到推荐池里
        patientService.intoGroup(empiList, rMap, null);
    }
}
