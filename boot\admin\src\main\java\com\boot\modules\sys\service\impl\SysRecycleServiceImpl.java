package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.SpringContextHolder;
import com.boot.modules.sys.dao.SysRecycleDao;
import com.boot.modules.sys.entity.SysRecycleEntity;
import com.boot.modules.sys.service.SysRecycleService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@PropertySource(value = "classpath:application-dev.yml", encoding = "utf-8")
@Service
public class SysRecycleServiceImpl extends ServiceImpl<SysRecycleDao, SysRecycleEntity> implements SysRecycleService {


    @Value("${spring.datasource.druid.driver-class-name}")
    private String driveClassName;
    @Value("${spring.datasource.druid.url}")
    private String url;
    @Value("${spring.datasource.druid.username}")
    private String dbname;
    @Value("${spring.datasource.druid.password}")
    private String password;


    /**
     * 查询分页数据
     */
    @Override
    public PageUtils list(Map<String, Object> params) {

        Long id = MapUtils.getValue(params, "id", Long.class);
        String entityname = MapUtils.getValue(params, "entityname", String.class);
        String implname = MapUtils.getValue(params, "implname", String.class);
        String username = MapUtils.getValue(params, "username", String.class);
        Date date = MapUtils.getValue(params, "date", Date.class);

        QueryWrapper<SysRecycleEntity> qwList = MapUtils.getWrapperByParams(params, "sort", "edc_recycle", SysRecycleEntity.class);
        qwList.lambda().eq(id != null, SysRecycleEntity::getId, id)
                .like(entityname != null, SysRecycleEntity::getEntityname, entityname)
                .eq(implname != null, SysRecycleEntity::getImplname, implname)
                .like(username != null, SysRecycleEntity::getUsername, username)
                .eq(date != null, SysRecycleEntity::getDate, date);
        IPage<SysRecycleEntity> page = this.page(
                new Query<SysRecycleEntity>().getPage(params), qwList
        );

        return new PageUtils(page);
    }

    /**
     * 写入数据
     */
    @Override
    public boolean insert(SysRecycleEntity sysRecycleEntity) {
        boolean res = this.save(sysRecycleEntity);
        if (!res) {
            throw new BusinessException("修改失败");
        }
        return res;
    }

    /**
     * 通过传递sql语句恢复数据
     */
    @Override
    public boolean recover(Map<String, Object> params) {
        Long id = MapUtils.getValue(params, "id", Long.class);
        QueryWrapper<SysRecycleEntity> qw = new QueryWrapper<>();
        qw.lambda().in(SysRecycleEntity::getId, id);
        List<SysRecycleEntity> list = baseMapper.selectList(qw);
        List<String> datas = list.stream().map(SysRecycleEntity::getData).collect(Collectors.toList());
        List<String> entityNames = list.stream().map(SysRecycleEntity::getEntityname).collect(Collectors.toList());
        Iterator<String> iterData = datas.iterator();
        Iterator<String> iterName = entityNames.iterator();
        int result = 1;
        while (iterData.hasNext() && iterName.hasNext()) {
            Class aClass = null;
            try {
                aClass = Class.forName(String.valueOf(iterName.next()));
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
            Annotation[] annotations = aClass.getAnnotations();
            String tableName = null;
            for (Annotation a : annotations
            ) {
                if (String.valueOf(a).contains("TableName")) {
                    tableName = String.valueOf(a).split(",")[4].split("=")[1];
                }
            }
            String preData = iterData.next();
            String[] data = preData.substring(2, preData.length() - 2).split("\\},\\{");
            for (int j = 0; j < data.length; j++) {
                String[] split = data[j].split(",");
                String value1 = null;
                for (int k = 0; k < split.length; k++) {
                    String[] param = split[k].split(":");
                    String key = param[0].substring(1, param[0].length() - 1);
                    String value = param[1];
                    if (value.contains("\"")) {
                        value = value.substring(1, value.length() - 1);
                    }
                    value1 = value1 + "'" + value + "'" + ",";
                }
                String finalValue = "value(" + value1.substring(4, value1.length() - 1) + ")";
                try {
                    Class.forName(driveClassName);
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
                //2.获取数据库连接( alt+shift+L ), java.sql.Connection
                Connection conn = null;
                try {
                    conn = DriverManager.getConnection(url, dbname, password);
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
                //3.获取传输器 java.sql.Statement
                Statement stat = null;
                try {
                    stat = conn.createStatement();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
                //4.发送SQL到服务器执行
                String sql = "insert into" + " " + tableName + " " + finalValue;
                try {
                    int res = stat.executeUpdate(sql);
                    if (res == 0) {
                        result = 0;
                    }
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
                try {
                    stat.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
                try {
                    conn.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
        }
        if (result == 1) {
            int i = baseMapper.deleteById(id);
            if (i == 0) {
                throw new BusinessException("删除失败");
            }
        }
        return true;
    }

    /**
     * 通过反射恢复数据
     */
    @Override
    public boolean recoverByReflect(Map<String, Object> params) {
        Long id = MapUtils.getValue(params, "id", Long.class);
        QueryWrapper<SysRecycleEntity> qw = new QueryWrapper<>();
        qw.lambda().in(SysRecycleEntity::getId, id);
        List<SysRecycleEntity> list = baseMapper.selectList(qw);
        Iterator<String> iterData = list.stream()
                .map(SysRecycleEntity::getData).collect(Collectors.toList()).iterator();
        Iterator<String> iterName = list.stream()
                .map(SysRecycleEntity::getEntityname).collect(Collectors.toList()).iterator();
        Iterator<String> iterImpl = list.stream()
                .map(SysRecycleEntity::getImplname).collect(Collectors.toList()).iterator();
        int result = 1;
        while (iterData.hasNext() && iterName.hasNext() && iterImpl.hasNext()) {
            Class aImplClass = null;
            try {
                aImplClass = Class.forName(String.valueOf(iterImpl.next()));
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
            String[] aImplArr = String.valueOf(aImplClass).split("\\.");
            String aImpl = toLowerCaseFirstOne(aImplArr[aImplArr.length - 1]);
            Class aClass = null;
            try {
                aClass = Class.forName(String.valueOf(iterName.next()));
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
            Object aEntity = null;
            try {
                aEntity = aClass.newInstance();
            } catch (InstantiationException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            String preData = iterData.next();
            String[] data = preData.substring(2, preData.length() - 2).split("\\},\\{");
            for (int j = 0; j < data.length; j++) {
                String preSplit = data[j];
                String[] split = preSplit.substring(0, preSplit.length()).split(",\"");
                String key = null;
                String value = null;
                for (int k = 0; k < split.length; k++) {
                    if (k == 0) {
                        key = split[0].split("\":")[0].substring(1, split[k].split(":")[0].length() - 1);
                        value = split[0].split("\":")[1];
                    } else {
                        key = split[k].split("\":")[0].substring(0, split[k].split(":")[0].length() - 1);
                        value = split[k].split("\":")[1];
                    }
                    if (value.contains("\"")) {
                        value = value.substring(1, value.length() - 1);
                    }
                    Field field = null;
                    try {
                        field = aClass.getDeclaredField(key);
                    } catch (NoSuchFieldException e) {
                        e.printStackTrace();
                    }
                    String[] pretype = field.getGenericType().toString().split("\\.");
                    String type = pretype[pretype.length - 1];
                    field.setAccessible(true);
                    if ("Integer".equals(type)) {
                        try {
                            field.set(aEntity, Integer.parseInt(value));
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    } else if ("Long".equals(type)) {
                        try {
                            field.set(aEntity, Long.parseLong(value));
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    } else if ("Date".equals(type)) {
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        SimpleDateFormat sdf = new SimpleDateFormat("MMM d, yyyy K:m:s a", Locale.ENGLISH);
                        Date date = null;
                        try {
                            date = sdf.parse(value);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        try {
                            field.set(aEntity, formatter.parse(formatter.format(date)));
                        } catch (IllegalAccessException | ParseException e) {
                            e.printStackTrace();
                        }
                    } else {
                        try {
                            field.set(aEntity, value);
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    }
                }
                Method save = null;
                try {
                    save = aImplClass.getMethod("save", Object.class);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                try {
                    Object invoke = save.invoke(SpringContextHolder.getBean(aImpl), aEntity);
                    if (invoke == null) {
                        result = 0;
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }

            }
        }
        if (result == 1) {
            int i = baseMapper.deleteById(id);
            if (i == 0) {
                throw new BusinessException("删除失败");
            }
        }
        return true;
    }

    /**
     * 删除数据
     */
    @Override
    public boolean delete(Long[] ids) {
        QueryWrapper<SysRecycleEntity> qw = new QueryWrapper<>();
        qw.lambda().in(SysRecycleEntity::getId, ids);
        boolean res = true;
        res = this.remove(qw);
        if (!res) {
            throw new BusinessException("删除失败");
        }
        return res;
    }

    /**
     * 将首字母转换成小写方法
     */
    public static String toLowerCaseFirstOne(String s) {
        if (Character.isLowerCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }
}