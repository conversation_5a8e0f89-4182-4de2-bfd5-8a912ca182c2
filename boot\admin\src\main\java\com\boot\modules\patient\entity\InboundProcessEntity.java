package com.boot.modules.patient.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 受试者信息表
 */
@Data
@TableName("rp_inbound_process")
public class InboundProcessEntity implements Serializable {

    @TableId
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 子项目id
     */
    private Long subProjectId;

    /**
     * 患者唯一id
     */
    private String empiid;

    /**
     * 退出理由
     */
    private String outReason;

    /**
     * 退出时间
     */
    private String outTime;

    /**
     * 入组时间
     */
    private String inboundTime;

    /**
     * 签署知情同意书时间
     */
    private String signingTime;

    /**
     * 操作已签时间
     */
    private String signingOpTime;

    /**
     * 签署知情同意书审核用户id
     */
    private Long signingAuditId;

    /**
     * 入职医生id
     */
    private Long inboundId;

    /**
     * 受试者状态 2-拟入组 3-已入组 4-异常退出 5-正常退出 6-退回
     */
    private Integer status;

    /**
     * 医生推荐表id
     */
    private Long doctorRecommendId;

    /**
     * 过期剩余时间
     */
    private Integer signExpireDays;

    /**
     * edc 同步病人id
     */
    private Long edcPatId;

    /**
     * 就诊类型
     */
    private String admType;
}
