package com.boot.commons.enums;

/**
 * <AUTHOR>
 * @desc 数据库备份任务状态枚举
 * @date 2021/3/23 15:30
 */
public enum BackupTaskEnums {

    /**
     * 备份成功
     */
    BACKUP_SUCCESS(0, "备份成功"),

    /**
     * 备份失败
     */
    BACKUP_ERROR(1, "备份失败"),

    /**
     * 压缩失败
     */
    BACKUP_ZIP_ERROR(2, "压缩失败"),

    /**
     * 其他错误
     */
    BACKUP_OTHER_ERROR(3, "其他错误");


    /**
     * 任务状态码
     */
    private Integer code;
    /**
     * 任务信息
     */
    private String message;

    BackupTaskEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }


    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
