package com.boot.modules.sys.mapper;


import com.boot.commons.config.SSOConfigurationProperties;
import com.boot.modules.sys.service.SSOService;
import com.boot.modules.sys.service.impl.SSOServiceImpl;
import com.boot.modules.sys.service.impl.SsoHxyyLoginServiceImpl;
import org.springframework.context.ApplicationContext;

/**
 * 不同单点登录模式bean映射获取类
 * <AUTHOR>
 * @createTime 2021年12月20日 17:34:00
 */
public class SSOServiceMapper {

    private static SSOService mapper = null;

    public static SSOService getMapper(ApplicationContext applicationContext) {
        if (mapper == null) {
            synchronized (SSOServiceMapper.class) {
                SSOConfigurationProperties ssoConfig = applicationContext.getBean(SSOConfigurationProperties.class);
                if (mapper == null) {
                    // 默认单点登录模式
                    if ("default".equals(ssoConfig.getHospital())) {
                        mapper = applicationContext.getBean(SSOServiceImpl.class);
                    } else if ("hxyy".equals(ssoConfig.getHospital())) {
                        mapper = applicationContext.getBean(SsoHxyyLoginServiceImpl.class);
                    }
                }
            }
        }

        return mapper;
    }

}
