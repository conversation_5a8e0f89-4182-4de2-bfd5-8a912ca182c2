package com.boot.commons.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2024年12月10日 10:31:00
 * 受试者与医联体性别映射
 */
public class SexMapUtil {

    private static final String defaultSexStr = "不详";

    public static Integer getIcoSex(Integer rpSex) {
        Map<Integer, Integer> map = new HashMap<>();
        //受试者 0- 男 1-女 2-其他
        //医联体 1-男 2-女 3-保密
        map.put(0, 1);
        map.put(1, 2);
        map.put(2, 3);
        return rpSex == null || map.get(rpSex) == null ? 3 : map.get(rpSex);
    }

    public static Integer getRpSex(Integer icoSex) {
        Map<Integer, Integer> map = new HashMap<>();
        //受试者 0- 男 1-女 2-其他
        //医联体 1-男 2-女 3-保密
        map.put(1, 0);
        map.put(2, 1);
        map.put(3, 2);
        return icoSex == null || map.get(icoSex) == null ? 2 : map.get(icoSex);
    }


    public static String getRpSexStr(String sex) {
        if (StringUtils.isEmpty(sex)) {
            return defaultSexStr;
        }
        Map<String, String> map = new HashMap<>();
        //受试者 0- 男 1-女 2-其他
        map.put("0", "男");
        map.put("1", "女");
        return map.get(sex) == null ? defaultSexStr : map.get(sex);
    }
}
