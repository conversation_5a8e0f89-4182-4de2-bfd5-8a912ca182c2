package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysRecycleEntity;

import java.util.Map;

/**
 * 回收站
 *
 * <AUTHOR>
 */
public interface SysRecycleService extends IService<SysRecycleEntity> {

    /**
     * 查询分页数据
     */
    PageUtils list(Map<String, Object> params);


    /**
     * 新增数据
     */
    boolean insert(SysRecycleEntity sysRecycleEntity);

    /**
     * 通过传递sql语句恢复数据
     */
    boolean recover(Map<String, Object> params);

    /**
     * 通过反射恢复数据
     */
    boolean recoverByReflect(Map<String, Object> params);

    /**
     * 删除数据
     */
    boolean delete(Long[] ids);

}
