package com.boot.modules.patient.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * PI报表对象
 */
@Data
public class PIReportDto implements Serializable {

    /**
     * 项目Id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 牵头项目数
     */
    private Integer totalProject = 0;

    /**
     * 新增牵头项目数
     */
    private Integer addProject = 0;

    /**
     * 累计推荐入组人数
     */
    private Integer totalRecommend = 0;

    /**
     * 新增推荐入组人数
     */
    private Integer addRecommend = 0;

    /**
     * 累计实际入组人数
     */
    private Integer totalInbound = 0;

    /**
     * 新增实际入组人数
     */
    private Integer addInbound = 0;
}
