package com.boot.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.entity.SysDictCateEntity;
import com.boot.modules.sys.entity.SysDictItemEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :
 * @create 2021-02-25
 */
public interface SysDictCateService extends IService<SysDictCateEntity> {

    /**
     * 分页查询字典分类
     *
     * @param params
     * @return
     */
    PageUtils queryPage(Map<String, Object> params);

    /**
     * 根据分类id，查分类信息，并返回此分类对应的所有字典信息
     *
     * @param id
     * @return
     */
    SysDictCateEntity selectById(Long id);

    /**
     * 批量删除字典分类，并关联删除此分类下的字典项
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<Long> ids);

    /**
     * 字典分类
     * @param file
     * @return
     */
    boolean excelImport(MultipartFile file);
}
