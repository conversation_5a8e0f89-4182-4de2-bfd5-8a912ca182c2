package com.boot.modules.cas.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.constant.HxConst;
import com.boot.modules.cas.service.CasUserAndRoleService;
import com.boot.modules.cas.service.CasUserPushService;
import com.boot.modules.cas.vo.CasResult;
import com.boot.modules.cas.vo.CasUserVo;
import com.boot.modules.cas.vo.UserAndRolesVo;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserHisEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserHisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 推送用户信息接口实现类
 * 操作：
 * <p/>
 * 1. 更新用户信息
 * <p/>
 * 2. 更新用户角色信息
 * <p/>
 * 3. 更新 his 表信息
 * <p/>
 * <AUTHOR>
 */
@Slf4j
@Service
public class CasUserPushServiceImpl implements CasUserPushService {
    @Resource
    private CasUserAndRoleService casUserAndRoleService;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysUserHisService sysUserHisService;
    /**
     * 对推送来的用户信息数据进行业务处理
     *
     * @param userAndRolesVo 接口指定到数据结构
     * @return boolean 操作结构是否成功
     */
    @Override
    public CasResult doPushUsers(UserAndRolesVo userAndRolesVo, Long loginUserId) {
        CasResult casResult = null;
        int set = 0;
        List<Map<String, Object>> results = new ArrayList<>();

        //遍历获取 user 数据
        for (CasUserVo casUserVo : userAndRolesVo.getUsers()) {
            if (casUserVo == null) {
                return new CasResult("casUserVos为空，请检查。", "-1", null, false);
            }
            //获取对象
            try {
               //根据医疗机构编码获取deptId
                String deptCode = casUserVo.getGroup_info().getHospital_code();
                SysDeptEntity dept = sysDeptService.getTopDeptByCode(deptCode);
                long deptId = dept == null ? 0 : dept.getId();

                // =======      更新  <用户信息> 和 <用户角色信息>  <his账号信息>    ======
                List<CasUserVo> casUserVos = new ArrayList<>();
                casUserVos.add(casUserVo);
                List<Map<String, Object>> result = casUserAndRoleService.saveUserAndRole(casUserVos, loginUserId, deptId, deptCode);

                // 每个更新对象情况存入数组 results
                results.add(set, result.get(0));
                set++;
            } catch (Exception e) {
                e.printStackTrace();
                casResult = new CasResult(e.getMessage(), "0", results, false);
            }
        }
        return new CasResult("", "0", results, true);
    }


    /**
     * @param judge            用于判断目前是否可以进入下一步
     * @param sysUserHisEntity his操作实体对象
     * @param casUserVo        请求入参数据
     * @param hisCount         是否已有his记录
     * @param deptId           子项目id
     * @param results          之前更新《用户信息》和《用户角色信息》结果信息
     */
    private CasResult jageUpdateHis(String judge,
                                    SysUserHisEntity sysUserHisEntity,
                                    CasUserVo casUserVo,
                                    int hisCount,
                                    Long deptId,
                                    List<Map<String, Object>> results
    ) throws Exception {
        boolean success;
        String code;
        String msg;
        switch (judge) {
            case "true":
                if (hisCount == 0) {
                    // ============  新增 his表记录
                    sysUserHisEntity.setHisForeignId(casUserVo.getGx_number());
                    boolean r = sysUserHisService.save(sysUserHisEntity);
                    if (!r) {
                        throw new Exception(" -------------- 保存或更新 his表 时发生异常 --------------- ");
                    }
                } else {
                    // ============= 更新 his 记录

                    SysUserHisEntity whereEntity = new SysUserHisEntity();
                    whereEntity.setUserId(Long.parseLong(casUserVo.getUser_id()));
                    whereEntity.setDeptId(deptId);
                    // 根据条件 whereWrapper 指定需要更新的记录
                    Wrapper<SysUserHisEntity> whereWrapper = new QueryWrapper<>(whereEntity);
                    sysUserHisEntity.setHisForeignId(casUserVo.getGx_number());
                    boolean ru = sysUserHisService.update(sysUserHisEntity, whereWrapper);
                    log.info(" 更新his表结果 " + ru);
                }
                success = true;
                msg = "数据同步更新成功， 更新了" + results.size() + "条数据";
                break;
            case "false":
                success = false;
                msg = "数据同步更新失败";
                break;
            default:
                success = false;
                msg = "同步更新时发生异常";
        }

        code = success ? String.valueOf(HxConst.RESPONSE_SUCCESS) : "0";

        return new CasResult(msg, code, results, success);
    }
}
