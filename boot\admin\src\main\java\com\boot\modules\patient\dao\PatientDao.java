package com.boot.modules.patient.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.patient.dto.InboundProcessDto;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.project.vo.ProjectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PatientDao extends BaseMapper<PatientEntity> {

    IPage<InboundProcessDto> listDoctorRecommendationBySubProjectId(IPage<InboundProcessDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<InboundProcessDto> qw);

    IPage<InboundProcessDto> listInboundProcessBySubProjectId(IPage<InboundProcessDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<InboundProcessDto> qw);

    IPage<InboundProcessDto> listDoctorRecommendation(IPage<InboundProcessDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<InboundProcessDto> qw);


    IPage<InboundProcessDto> listDoctorRecommendationMobile(IPage<InboundProcessDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<InboundProcessDto> qw);

    IPage<InboundProcessDto> listInboundProcessMobile(IPage<InboundProcessDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<InboundProcessDto> qw);
}
