package com.boot.modules.patient.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.patient.entity.PatImportRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface PatImportRecordDao extends BaseMapper<PatImportRecordEntity> {
    IPage<PatImportRecordEntity> queryPage(IPage<PatImportRecordEntity> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<PatImportRecordEntity> qw);
}
