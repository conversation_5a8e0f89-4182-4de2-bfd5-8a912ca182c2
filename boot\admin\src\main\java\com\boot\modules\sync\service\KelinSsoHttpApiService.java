package com.boot.modules.sync.service;

import com.github.lianjiatech.retrofit.spring.boot.annotation.OkHttpClientBuilder;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import okhttp3.OkHttpClient;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/22 11:48
 */
@RetrofitClient(
        baseUrl = "${boot.external.kelin-sso-api}",
        poolName = "kelinSsoApi"
)
public interface KelinSsoHttpApiService {

    @OkHttpClientBuilder
    static OkHttpClient.Builder okhttpClientBuilder() {
        return new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.MINUTES)
                .readTimeout(5, TimeUnit.MINUTES)
                .writeTimeout(5, TimeUnit.MINUTES);
    }

    /*
     * 柯林的获取token接口
     */
    @POST("sso/Home/GetUserToken")
    String getHsspToken(@Query("userCode") String code,@Query("jwt") String jwt);
}