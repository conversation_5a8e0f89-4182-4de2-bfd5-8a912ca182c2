package com.boot.modules.project.enums;

import com.boot.commons.enums.PatTypeEnum;

/**
 * 项目研究类型枚举
 *
 * <AUTHOR>
 */
public enum ProjectStudyTypeEnum {
    INTERVENTION_IIT(1, "干预性IIT", "干预性IIT"),
    GCP(2, "<PERSON><PERSON>", "GCP"),
    PROSPECTIVE_OBSERVATION(3, "前瞻观察性", "前瞻观察性"),
    SPECIALIZED_DISEASE_QUEUE(4, "专病队列 ", "专病队列");

    private Integer code;

    private String name;

    private String desc;

    ProjectStudyTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static ProjectStudyTypeEnum getByCode(Integer code) {
        if (code != null) {
            for (ProjectStudyTypeEnum projectStudyTypeEnum : ProjectStudyTypeEnum.values()) {
                if (projectStudyTypeEnum.getCode().equals(code)) {
                    return projectStudyTypeEnum;
                }
            }
        }
        return null;
    }

}
