package com.boot.modules.project.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.enums.UserStatusEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.project.dao.UserSubProjectDao;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.UserSubProjectEntity;
import com.boot.modules.project.enums.IsAdminEnum;
import com.boot.modules.project.service.ProjDeptService;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.service.UserSubProjectService;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.project.vo.UserSubProjectCenterVo;
import com.boot.modules.project.vo.UserSubProjectVo;
import com.boot.modules.sys.dao.SysDeptDao;
import com.boot.modules.sys.dao.SysUserDao;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserService;
import com.boot.modules.sys.vo.UserSubProjectDeptRoleVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc : 项目和研究团队相关联 关连上，就相当于研究团队中的用户在项目中去了，然后给项目设置用户或者管理员 就是往此表中插入数据
 * @create 2021-02-23
 */
@Service
public class UserSubProjectServiceImpl extends ServiceImpl<UserSubProjectDao, UserSubProjectEntity> implements UserSubProjectService {

    @Resource
    private SysUserService userService;

    @Resource
    private SysDeptService deptService;

    @Resource
    private ProjectService projectService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private ProjDeptService projDeptService;

    @Resource
    private SysDeptDao sysDeptDao;

    /**
     * 分页查询该项目的用户和角色信息
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        String userName = MapUtils.getValue(params, "username", String.class);

        Long deptId = MapUtils.getValue(params, "deptId", Long.class);
        Long roleId = MapUtils.getValue(params, "roleId", Long.class);
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);

        IPage<UserSubProjectVo> pageFilter = new Query<UserSubProjectVo>().getPage(params);

        QueryWrapper<UserSubProjectVo> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(userName), "b.userName", userName)
                .eq(subProjectId != null, "a.sub_project_id", subProjectId)
                .eq(projectId != null, "e.project_id", projectId)
                .eq(roleId != null, "a.proj_role_id", roleId)
                .eq(deptId != null, "d.id", deptId)
                .eq("b.status", UserStatusEnum.UN_LOCK.getType());

        List<Long> allDeptIds = new ArrayList<>();

        ProjectEntity project = projectService.getById(projectId);
        if (project == null || project.getId() == null) {
            return null;
        }

        List<ProjDeptEntity> projDeptList = projDeptService.getByProjectId(projectId);
        Long primaryCenter = null;
        if (!CollectionUtils.isEmpty(projDeptList)) {
            for (ProjDeptEntity projDeptEntity : projDeptList) {
                if (projDeptEntity.getIsPrimary().equals(IsAdminEnum.ADMIN.getType())) {
                    primaryCenter = projDeptEntity.getDeptId();
                }
                allDeptIds.add(projDeptEntity.getDeptId());
            }
        }

        // 若过滤的中心ID不在主中心或者分中心， 直接返回空
        if (deptId != null && !allDeptIds.contains(deptId)) {
            return null;
        }

        // 获取deptId及全部子deptId
        List<Long> allDeptList = deptService.getSubDeptIdList(allDeptIds);

        qw.in(!CollectionUtils.isEmpty(allDeptList), "d.id", allDeptList);

        IPage<UserSubProjectVo> page = baseMapper.queryPage(pageFilter, qw);

        List<UserSubProjectVo> records = page.getRecords().stream().distinct().collect(Collectors.toList());

        for (UserSubProjectVo record : records) {
            if (record.getDeptId().equals(primaryCenter.toString())){
                //是主中心：1
                record.setIsPrimaryCenter(IsAdminEnum.ADMIN.getType());
            } else {
                //是分中心：0
                record.setIsPrimaryCenter(IsAdminEnum.NO_ADMIN.getType());
            }
        }

        page.setRecords(records);
        return new PageUtils(page);
    }

    /**
     * 获取当前子项目的所有项目用户 按中心分开
     * 如果是项目管理员 则返回所有中心的所有用户
     * 如果是项目中心管理员，则只返回此中心的用户
     *
     * @param params
     * @return
     */
    @Override
    public List<UserSubProjectCenterVo> getAllSubProjUser(Map<String, Object> params) {
        List<UserSubProjectCenterVo> result = new ArrayList<>();

        String userName = MapUtils.getValue(params, "userName", String.class);
        Long roleId = MapUtils.getValue(params, "roleId", Long.class);
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        SysUserEntity user = MapUtils.getValue(params, "user", SysUserEntity.class);
        Long userId = user.getId();
        List<SysDeptEntity> sysDeptEntityList = deptService.getAll();
        // 查当前登陆用户在项目中的角色
        List<UserSubProjectDeptRoleVo> userRole = baseMapper.query(
                new QueryWrapper<UserSubProjectDeptRoleVo>()
                        .eq("a.user_id", userId)
                        .eq("a.sub_project_id", subProjectId)
        );
        QueryWrapper<UserSubProjectVo> qw = new QueryWrapper<>();
        qw
                .eq(subProjectId != null, "a.sub_project_id", subProjectId)
                .eq(roleId != null, "a.proj_role_id", roleId);
        if (StringUtils.isNotBlank(userName)) {
            qw
                    .and(
                            qw1 -> {
                                qw1.like("d.userName", userName)
                                        .or().like("d.nickname", userName);
                            }
                    );
        }
        ProjectEntity project = projectService.getById(projectId);
        if (project == null) {
            return result;
        }
        Long projectAdminUserId = project.getProjectAdminId();
        // 系统管理员、PI、SubI查项目所有中心下用户，分中心管理员查用户所属中心下的用户
        List<ProjDeptEntity> projDeptList = new ArrayList<>();
        // 分中心PI角色值判断，根据使用权限，分中心
        if (!CollectionUtils.isEmpty(userRole) && userRole.get(0).getRoleName().equals("分中心PI")) {
            qw.in("c.id", deptService.getSubDeptIdList(userRole.get(0).getTopDeptId()));
            QueryWrapper<ProjDeptEntity> qwProjDept = new QueryWrapper<>();
            qwProjDept.lambda().eq(ProjDeptEntity::getProjectId, projectId)
                    .in(user.getDeptId() != null, ProjDeptEntity::getDeptId,
                            deptService.getSubDeptIdList(userRole.get(0).getTopDeptId()));
            projDeptList = projDeptService.list(qwProjDept);
        } else {
            // 项目科室信息
            projDeptList = projDeptService.list(new QueryWrapper<ProjDeptEntity>().lambda().eq(ProjDeptEntity::getProjectId, projectId));
            List<Long> centerDeptIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(projDeptList)) {
                for (ProjDeptEntity projDeptEntity : projDeptList) {
                    List<Long> centerChildDept = deptService.getSubDeptIdList(projDeptEntity.getTopDeptId());
                    centerDeptIds.addAll(centerChildDept);
                }
            }
            qw.in(! CollectionUtils.isEmpty(centerDeptIds), "c.id", centerDeptIds);
        }

        List<UserSubProjectVo> list = baseMapper.selectByQuery(qw);
        if (CollectionUtils.isEmpty(projDeptList)) {
            return null;
        }
        List<SysDeptEntity> allDeptList = deptService.list();

        List<Long> existCenterDeptIds = new ArrayList<>();
        for (ProjDeptEntity projDeptEntity : projDeptList) {
            UserSubProjectCenterVo vo = new UserSubProjectCenterVo();
            vo.setProjDeptId(projDeptEntity.getDeptId());
            Long topDeptId = projDeptEntity.getTopDeptId();
            if (existCenterDeptIds.contains(topDeptId)) {
                continue;
            }
            existCenterDeptIds.add(topDeptId);
            vo.setDeptId(topDeptId);
            vo.setIsPrimaryCenter(projDeptEntity.getIsPrimary());
            for (SysDeptEntity dept : allDeptList) {
                if (dept.getId().equals(topDeptId)) {
                    vo.setDeptName(dept.getName());
                    break;
                }
            }
            result.add(vo);
        }

        if (list != null && list.size() > 0) {
            //根据中心分组
            for (UserSubProjectCenterVo centerVo : result) {
                List<UserSubProjectVo> userSubProjectVoList = new ArrayList<>();
                for (UserSubProjectVo userSubProjVo : list) {
                    if (!userSubProjVo.getUserId().equals(projectAdminUserId) && centerVo.getDeptId().equals(userSubProjVo.getTopDeptId())) {
                        userSubProjectVoList.add(userSubProjVo);
                    }
                }
                centerVo.setUserSubProjectVoList(userSubProjectVoList);
            }
        }

        //对结果排序，防止修改了项目主中心后 造成的主中心不在第一位的问题----这个问题只针对展示全部中心的
        boolean tag = false;
        if (!CollectionUtils.isEmpty(result)) {
            if (userId != null && !userId.equals(projectAdminUserId) && userId != 0L) {
                if (user.getIsSysAdmin() == null || user.getIsSysAdmin().equals(0)) {
                    tag = true;
                }
            }

            if (!tag) {
                if (!result.get(0).getIsPrimaryCenter().equals(1)) {
                    //如果第一个不是主中心
                    UserSubProjectCenterVo primaryCenterVo = new UserSubProjectCenterVo();
                    int index = 0;
                    for (int i = 0; i < result.size(); i++) {
                        if (result.get(i).getIsPrimaryCenter().equals(1)) {
                            primaryCenterVo = result.get(i);
                            index = i;
                        }
                    }
                    result.remove(index);
                    result.add(0, primaryCenterVo);
                }
            }
        }


        return result;
    }
@Resource
    private SysUserDao sysUserDao;
    /**
     * 项目设置用户角色时 需展示还没有被设置角色的所有成员
     *
     * @return
     */
    @Override
    public List<SysDeptEntity> getNotAddUser(Map<String, Object> params) {
        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
        Long deptId = MapUtils.getValue(params, "deptId", Long.class);
        Long projDeptId = MapUtils.getValue(params, "projectDeptId", Long.class);
        Integer isAll = MapUtils.getValue(params, "isAll", Integer.class);

        //获取所有部门
        List<SysDeptEntity> allList = deptService.list(new QueryWrapper<SysDeptEntity>()
                .lambda()
                .eq(SysDeptEntity::getDelFlag, 0)
        );
        // 找到projDeptId的上级deptId
        List<SysDeptEntity> parentDept = new ArrayList<>();
        deptService.getParentDept(allList, parentDept, projDeptId);

        // 获取医疗机构下的所有子节点
        List<Long> deptIds = new ArrayList<>();
        if (isAll != null && isAll.equals(1)) {
            // 全部页签，查医院下所有科室
            deptIds = deptService.getSubDeptIdList(allList, deptId);
        } else {
            // 本医疗单元页签，查医疗单元下所有
            deptIds = deptService.getSubDeptIdList(allList, projDeptId);
        }
        // 查指定医疗机构的用户
        List<SysUserEntity> userEntityList = sysUserDao.getByDeptId(
                new QueryWrapper<SysUserEntity>()
                        .in("c.deptIdStr", deptIds)
                        .eq("c.status", 1)).stream().distinct().collect(Collectors.toList());
        // 查已入组的用户
        List<UserSubProjectEntity> userSubProjectEntityList = this.list(new QueryWrapper<UserSubProjectEntity>().lambda().eq(UserSubProjectEntity::getSubProjectId, subProjectId));
        List<Long> existUserId = userSubProjectEntityList.stream().map(p -> p.getUserId()).collect(Collectors.toList());
        // 未入组的用户
        List<SysUserEntity> lastUserList = userEntityList.stream().filter(p -> !existUserId.contains(p.getId())).collect(Collectors.toList());

        List<Long> parentDeptIds = parentDept.stream().map(p -> p.getId()).collect(Collectors.toList());
        return deptService.queryList(deptId, lastUserList, parentDeptIds, isAll);
    }

//    /**
//     * 获取当前子项目的所有项目用户 按中心分开
//     * 如果是项目管理员 则返回所有中心的所有用户
//     * 如果是项目中心管理员，则只返回此中心的用户
//     *
//     * @param params
//     * @return
//     */
//    @Override
//    public List<UserSubProjectCenterVo> getAllSubProjUser(Map<String, Object> params) {
//        List<UserSubProjectCenterVo> result = new ArrayList<>();
//
//        String userName = MapUtils.getValue(params, "userName", String.class);
//        Long roleId = MapUtils.getValue(params, "roleId", Long.class);
//        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
//        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
//        SysUserEntity user = MapUtils.getValue(params, "user", SysUserEntity.class);
//
//        Long userId = user.getId();
//        QueryWrapper<UserSubProjectVo> qw = new QueryWrapper<>();
//
//        qw
//                .eq(subProjectId != null, "a.sub_project_id", subProjectId)
//                .eq(roleId != null, "a.proj_role_id", roleId);
//
//        if (StringUtils.isNotBlank(userName)) {
//            qw
//                    .and(
//                            qw1 -> {
//                                qw1.like("d.userName", userName)
//                                        .or().like("d.nickname", userName);
//                            }
//                    );
//        }
//        ProjectEntity project = projectService.getById(projectId);
//        if (project == null) {
//            return result;
//        }
//        Long projectAdminUserId = project.getProjectAdminId();
//        //如果当前用户不是项目管理员 且不是csm 且不是系统管理员
//        if (userId != null && !userId.equals(projectAdminUserId) && userId != 0L) {
//            if (user.getIsSysAdmin() == null || user.getIsSysAdmin().equals(0)) {
//                //当前用户所属医院id
//                Long curDeptId = deptService.getTopDeptByUserId(userId).getId();
//                qw.in("c.id", deptService.getSubDeptIdList(curDeptId));
//            }
//        }
//        List<UserSubProjectVo> list = baseMapper.selectByQuery(qw);
//        //获取所的项目中心  如果是项目管理员....则返回所有的项目中心  如果不是 则返回当前用户中心的
//        QueryWrapper<ProjDeptEntity> qwProjDept = new QueryWrapper<>();
//        qwProjDept.lambda().eq(ProjDeptEntity::getProjectId, projectId);
//
//        if (userId != null && !userId.equals(projectAdminUserId) && userId != 0L) {
//            if (user.getIsSysAdmin() == null || user.getIsSysAdmin().equals(0)) {
//                qwProjDept
//                        .lambda()
//                        .in(user.getDeptId() != null, ProjDeptEntity::getDeptId,
//                                deptService.getSubDeptIdList(user.getDeptId()));
//            }
//        }
//
//        List<ProjDeptEntity> projDeptList = projDeptService.list(qwProjDept);
//        if (CollectionUtils.isEmpty(projDeptList)) {
//            return null;
//        }
//        List<SysDeptEntity> allDeptList = deptService.list();
//
//        for (ProjDeptEntity projDeptEntity : projDeptList) {
//
//            UserSubProjectCenterVo vo = new UserSubProjectCenterVo();
//            vo.setProjDeptId(projDeptEntity.getId());
//            vo.setDeptId(projDeptEntity.getDeptId());
//            vo.setIsPrimaryCenter(projDeptEntity.getIsPrimary());
//
//            for (SysDeptEntity dept : allDeptList) {
//                if (dept.getId().equals(projDeptEntity.getDeptId())) {
//                    vo.setDeptName(dept.getName());
//                    break;
//                }
//            }
//            result.add(vo);
//        }
//
//        if (list != null && list.size() > 0) {
//            //根据中心分组
//            for (UserSubProjectCenterVo centerVo : result) {
//                List<UserSubProjectVo> userSubProjectVoList = new ArrayList<>();
//                for (UserSubProjectVo userSubProjVo : list) {
//                    if (! userSubProjVo.getUserId().equals(projectAdminUserId) && centerVo.getDeptId().equals(userSubProjVo.getDeptId())) {
//                        userSubProjectVoList.add(userSubProjVo);
//                    }
//                }
//                centerVo.setUserSubProjectVoList(userSubProjectVoList);
//            }
//        }
//
//        //对结果排序，防止修改了项目主中心后 造成的主中心不在第一位的问题----这个问题只针对展示全部中心的
//        boolean tag = false;
//        if (!CollectionUtils.isEmpty(result)) {
//            if (userId != null && !userId.equals(projectAdminUserId) && userId != 0L) {
//                if (user.getIsSysAdmin() == null || user.getIsSysAdmin().equals(0)) {
//                    tag = true;
//                }
//            }
//
//            if (!tag) {
//                if (!result.get(0).getIsPrimaryCenter().equals(1)) {
//                    //如果第一个不是主中心
//                    UserSubProjectCenterVo primaryCenterVo = new UserSubProjectCenterVo();
//                    int index = 0;
//                    for (int i = 0; i < result.size(); i++) {
//                        if (result.get(i).getIsPrimaryCenter().equals(1)) {
//                            primaryCenterVo = result.get(i);
//                            index = i;
//                        }
//                    }
//                    result.remove(index);
//                    result.add(0, primaryCenterVo);
//                }
//            }
//        }
//
//
//        return result;
//    }
//
//    /**
//     * 项目设置用户角色时 需展示还没有被设置角色的所有成员
//     *
//     * @return
//     */
//    @Override
//    public PageUtils getNotAddUser(Map<String, Object> params) {
//        // 注意从子项目
//        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
//        Long subProjectId = MapUtils.getValue(params, "subProjectId", Long.class);
//        // 当前登录用户Id
//        String userName = MapUtils.getValue(params, "username", String.class);
//        Long deptId = MapUtils.getValue(params, "deptId", Long.class);
//
//        SubProjectEntity subProjectEntity = subProjectService.getById(subProjectId);
//        //先获取项目信息
//        ProjectEntity project = projectService.getById(subProjectEntity.getProjectId());
//        if (project == null || project.getId() == null) {
//            return null;
//        }
//
//        List<Long> allDeptIds = new ArrayList<>();
//
//        List<ProjDeptEntity> projDeptList = projDeptService.getByProjectId(projectId);
//        //主中心
//        Long primaryCenter = null;
//        if (!CollectionUtils.isEmpty(projDeptList)) {
//            for (ProjDeptEntity projDeptEntity : projDeptList) {
//                if (projDeptEntity.getIsPrimary().equals(IsAdminEnum.ADMIN.getType())) {
//                    primaryCenter = projDeptEntity.getDeptId();
//                }
//                allDeptIds.add(projDeptEntity.getDeptId());
//            }
//        }
//        // 若过滤的中心ID不在主中心或者分中心， 直接返回空
//        if (deptId != null && !allDeptIds.contains(deptId)) {
//            return null;
//        }
//        // 获取deptId及全部子deptId
//        List<Long> allDeptList = deptService.getSubDeptIdList(allDeptIds);
//
//        //获取相关机构的所有用户
//        QueryWrapper<SysUserEntity> allCenterUserQw = new QueryWrapper<>();
//
//        allCenterUserQw.lambda()
//                .eq(SysUserEntity::getStatus, UserStatusEnum.UN_LOCK.getType());
//
//        if (deptId != null) {
//            allCenterUserQw.lambda().in(SysUserEntity::getDeptId, deptService.getSubDeptIdList(deptId));
//        } else {
//            allCenterUserQw.lambda().in(SysUserEntity::getDeptId, allDeptList);
//        }
//
//        if (StringUtils.isNotBlank(userName)) {
//            allCenterUserQw.lambda()
//                    .and(
//                            qw1 -> {
//                                qw1.like(SysUserEntity::getUsername, userName)
//                                        .or().like(SysUserEntity::getNickname, userName);
//                            }
//                    );
//        }
//
//        List<UserSubProjectVo> allCenterUsers = new ArrayList<>();
////        List<SysUserEntity> userList = userService.list(allCenterUserQw);
//        List<SysUserEntity> userList = userService.getByQuery(allCenterUserQw);
//        for (SysUserEntity userEntity : userList) {
//            //过滤掉系统管理员
//            if (userEntity.getIsSysAdmin() != null && userEntity.getIsSysAdmin().equals(1)) {
//                continue;
//            }
//
//            UserSubProjectVo userSubProjectVo = null;
//            userSubProjectVo = user2UserSubProjectVo(userEntity);
//            allCenterUsers.add(userSubProjectVo);
//        }
//
//        if (!CollectionUtils.isEmpty(allCenterUsers)) {
//            // 排除掉已经加入子项目的成员
//            QueryWrapper<UserSubProjectEntity> qwProjUser = new QueryWrapper<>();
//            qwProjUser.lambda().eq(UserSubProjectEntity::getSubProjectId, subProjectId);
//            List<UserSubProjectEntity> allProjUsers = this.list(qwProjUser);
//
//            List<UserSubProjectVo> users = new ArrayList<>();
//            for (UserSubProjectVo user : allCenterUsers) {
//                boolean isExit = false;
//
//                // 排除掉项目管理员和子项目管理员
//                if (user.getId().equals(project.getProjectAdminId())) {
//                    isExit = true;
//                } else {
//                    for (UserSubProjectEntity alUser : allProjUsers) {
//                        if (alUser.getUserId().equals(user.getId())) {
//                            isExit = true;
//                            continue;
//                        }
//                    }
//                }
//
//                if (!isExit) {
//                    if (user.getDeptId().equals(primaryCenter)) {
//                        //是主中心：1
//                        user.setIsPrimaryCenter(IsAdminEnum.ADMIN.getType());
//                    } else {
//                        //是分中心：0
//                        user.setIsPrimaryCenter(IsAdminEnum.NO_ADMIN.getType());
//                    }
//                    users.add(user);
//                }
//            }
//
//            IPage<SysUserEntity> page = new Query<SysUserEntity>().getPage(params);
//
//            int pageNum = (int) page.getCurrent();
//            int pageSize = (int) page.getSize();
//            int startIndex = (pageNum - 1) * pageSize;
//            //结束下标 subList()方法不包含结束下标的元素
//            int endIndex = pageNum * pageSize;
//            //endIndex 最后一页的时候 会发生下标月结
//            List<UserSubProjectVo> currUsers = new ArrayList<>();
//            try {
//                currUsers = users.subList(startIndex, endIndex);
//            } catch (IndexOutOfBoundsException e) {
//                //如果下标越界，说明是最后一页了
//                currUsers = users.subList(startIndex, users.size());
//            }
//            return new PageUtils(currUsers, users.size(), pageSize, pageNum);
//        }
//
//        return null;
//    }


    /**
     * 获取某项目下的所以用户
     *
     * @param projectId
     * @return
     */
    @Override
    public List<UserSubProjectEntity> getByProjectId(Long projectId) {

        return baseMapper.selectByProjectId(projectId);
    }

    /**
     * 获取当前用户在当前项目中的所属中心
     *
     * @param subProjectId
     * @param projectId
     * @param userId
     * @return
     */
    @Override
    public Long getUserCenter(Long subProjectId, Long projectId, Long userId) {
//        // 项目管理员
//        QueryWrapper<Long> qwAdmin = new QueryWrapper<>();
//        qwAdmin.eq("a.project_admin_id", userId)
//                .eq("a.id", projectId);
//        Long userAdminCenter = baseMapper.getAdminUserCenter(qwAdmin);
//        if (userAdminCenter != null && userAdminCenter != 0) {
//            return userAdminCenter;
//        }
        // 项目成员
        QueryWrapper<Long> qw = new QueryWrapper<>();
        qw.eq("a.sub_project_id", subProjectId)
//                .eq("b.project_id", projectId)
                .eq("a.user_id", userId);
        Long userCenter = baseMapper.getUserCenter(qw);
        return userCenter;
//        QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
//        qw.eq("b.id", userId);
//        List<SysDeptEntity> list = sysDeptDao.selectByQuery(qw);
//        if (!CollectionUtils.isEmpty(list)) {
//            return list.get(0).getId();
//        }
//        return null;
    }

    @Override
    public SysDeptEntity getCurUserCenter(Long projectId, Long subProjectId, Long userId) {

        ProjectVo projectVo = projectService.getInfoById(projectId);
        Long centerId = null;
        Long projectAdminId = projectVo.getProjectAdminId();
        if (userId.equals(projectAdminId)) {
            centerId = projectVo.getPrimaryCenter();
        } else {
            centerId = getUserCenter(subProjectId, projectId, userId);
        }
        if (centerId != null) {
            return deptService.getById(centerId);
        } else {
            return deptService.getByUserId(userId);
        }
    }

    /**
     * 新增项目用户
     *
     * @param projUser
     * @return
     */
    @Override
    public boolean add(UserSubProjectEntity projUser) {

        boolean res = true;
        if (projUser == null) {
            return res;
        }
        //唯一性验证
        QueryWrapper<UserSubProjectEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(UserSubProjectEntity::getSubProjectId, projUser.getSubProjectId())
                .eq(UserSubProjectEntity::getUserId, projUser.getUserId());
        List<UserSubProjectEntity> list = this.list(qw);

        if (!CollectionUtils.isEmpty(list)) {
            throw new BusinessException("此用户已存在");
        }

        res = this.save(projUser);

        return res;
    }

    /**
     * 批量新增
     *
     * @param projUsers 项目用户数组
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addBatch(List<UserSubProjectEntity> projUsers) {
        boolean res = true;
        List<UserSubProjectEntity> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(projUsers)) {
            Long subProjectId = projUsers.get(0).getSubProjectId();

            QueryWrapper<UserSubProjectEntity> qw = new QueryWrapper<>();
            qw.lambda().eq(UserSubProjectEntity::getSubProjectId, subProjectId);
            List<UserSubProjectEntity> old = this.list(qw);
            if (!CollectionUtils.isEmpty(old)) {
                List<Long> oldUserIds = old.stream().map(UserSubProjectEntity::getUserId).collect(Collectors.toList());

                for (UserSubProjectEntity projUser : projUsers) {
                    if (!oldUserIds.contains(projUser.getUserId())) {
                        result.add(projUser);
                    }
                }

            } else {
                result = projUsers;
            }
            res = this.saveBatch(result);
        }

        return res;
    }

    /**
     * 强转失败 手动转换
     * 父类强转为字类 会报cast error
     *
     * @param userEntity
     * @return
     */
    private UserSubProjectVo user2UserSubProjectVo(SysUserEntity userEntity) {

        String ob = JSONObject.toJSONString(userEntity);
        UserSubProjectVo userSubProjectVo = (UserSubProjectVo) JSONObject.parseObject(ob, UserSubProjectVo.class);

        return userSubProjectVo;
    }

    @Override
    public List<UserSubProjectVo> getByQuery(QueryWrapper<UserSubProjectVo> qwUser) {
        if (qwUser == null) {
            qwUser = new QueryWrapper<>();
        }
        List<UserSubProjectVo> list = baseMapper.listByQuery(qwUser);
        return list;
    }
}
