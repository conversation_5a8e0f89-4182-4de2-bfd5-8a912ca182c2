package com.boot.modules.patient.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.*;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.dto.PersonalReportDto;
import com.boot.modules.patient.dto.RecommendDto;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.DeptRecommendCountEntity;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.patient.entity.RecommendCountEntity;
import com.boot.modules.patient.service.*;
import com.boot.modules.patient.vo.DoctorRecommendProjectDiseaseVo;
import com.boot.modules.patient.vo.InboundProjectDieaseVo;
import com.boot.modules.patient.vo.TempRecommendationVO;
import com.boot.modules.project.entity.PatCustomRecordEntity;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.project.service.PatCustomRecordService;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.service.SysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.boot.commons.enums.PatStatusEnum.LOCK;
import static com.boot.commons.enums.PatTypeEnum.*;

//import com.boot.modules.sys.service.SysUserDeptService;

@Service
@Slf4j
public class DoctorRecommendationServiceImpl
        extends ServiceImpl<DoctorRecommendationDao, DoctorRecommendationEntity>
        implements DoctorRecommendationService {

    @Resource
    @Lazy
    private PatientService patientService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private SysDeptService deptService;
//
//    @Resource
//    private SysUserDeptService userDeptService;

    @Resource
    private PreRecommendService preRecommendService;

    @Resource
    private RecommendCountService recommendCountService;

    @Resource
    private DeptRecommendCountService deptRecommendCountService;

    /**
     * status为0，允许推荐，不进行弹框提示；status为1，允许推荐，但是有弹框提示；status为2，不允许推荐
     *
     * @param empi
     * @param recommendDto
     * @return
     */
    @Override
    public Map<String, Object> verify(String empi, RecommendDto recommendDto) {
        Map<String, Object> map = new HashMap<>();
        map.put("status", 0);
        // 1.判断患者是否已经入组锁定
        List<InboundProjectDieaseVo> inboundList = inboundProcessService.getInboundProject(empi, false);
        inboundList.forEach(p -> p.setProjectTypeName(ProjectStudyTypeEnum.getByCode(p.getProjectType()).getName()));
        if (!CollectionUtils.isEmpty(inboundList)
                && inboundList.stream().filter(p -> p.getPatientStatus().equals(LOCK.getCode())).count() > 0) {
            // 已锁定，不允许继续推荐
            map.put("status", 2);
            map.put("data", inboundList);
            map.put("message", "患者已加入GCP项目或干预性IIT项目，不能重复推荐。");
            return map;
        }

        // 2.判断是否已推荐
        List<RecommendDto.SubProjectInfo> subProjectList = recommendDto.getSubProjectList();
        Integer count = 0;
        for (RecommendDto.SubProjectInfo subProject : subProjectList) {
            Long subProjectId = subProject.getSubProjectId();
            // 4.判断项目是否已被推荐至该项目
            Boolean hasRecommend = this.hasRecommend(empi, subProjectId);
            if (hasRecommend) {
                continue;
            }
            count++;
        }
        if (count == 0) {
            // 该患者已推荐
            map.put("status", 2);
            map.put("message", "患者已推荐至所选择项目，不能重复推荐。");
            List<DoctorRecommendProjectDiseaseVo> data = getRecommendProject(empi);
            map.put("data", data);
            return map;
        }

        if (!CollectionUtils.isEmpty(inboundList)
                && inboundList.stream().filter(p -> p.getPatientStatus().equals(LOCK.getCode())).count() == 0) {
            // 已入组未锁定，允许继续推荐
            map.put("status", 1);
            map.put("data", inboundList);
            map.put("message", "患者已经加入专病队列或前瞻观察性研究项目，确认要推荐至其他项目？");
            return map;
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recommendation(String empi, String regno, RecommendDto recommendDto,
                               Long userId, Integer type, String nation) {

        TempRecommendationVO vo = getDocRecommendationList(empi, recommendDto, userId, type);

        List<DoctorRecommendationEntity> list = vo.getList();
        List<String> empiList = vo.getEmpiList();

        Map<String, String> empiRegMap = new HashMap<>();
        empiRegMap.put(empi, regno);

        Map<String, String> nationMap = new HashMap<>();
        if (!StringUtils.isBlank(nation)) {
            nationMap.put(empi, nation);
        }

        patientService.intoGroup(empiList, empiRegMap, nationMap);

        // 5.若没有一个项目可以被推荐 返回报错
        if (list.isEmpty()) {
            throw new BusinessException("患者已推荐至所选择项目，无法重复推荐");
        }

        // 6.批量插入推荐信息
        this.saveBatch(list);
    }

    private TempRecommendationVO getDocRecommendationList(String empi, RecommendDto recommendDto,
                                                          Long userId, Integer type) {
        TempRecommendationVO vo = new TempRecommendationVO();
        List<String> empiList = new ArrayList<>();
        // 1.判断患者是否已经入组
        List<InboundProjectDieaseVo> lockList = inboundProcessService.getInboundProject(empi, true);
        if (!CollectionUtils.isEmpty(lockList)) {
            throw new BusinessException("患者已加入GCP项目或干预性IIT项目，不能重复推荐。");
        }

        // 2.获取子项目 项目id对照map
        List<RecommendDto.SubProjectInfo> subProjectList = recommendDto.getSubProjectList();

        //推荐顶部医疗机构ID
        Long topRecommendDeptId = getTopDeptId(recommendDto);

        // 3.遍历构造推荐受试者
        List<DoctorRecommendationEntity> list = new ArrayList<>();
        String now = DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
        for (RecommendDto.SubProjectInfo subProject : subProjectList) {
            Long subProjectId = subProject.getSubProjectId();
            // 4.判断项目是否已被推荐至该项目
            Boolean hasRecommend = this.hasRecommend(empi, subProjectId);
            if (hasRecommend) {
                continue;
            }

            DoctorRecommendationEntity doctorRecommendationEntity = new DoctorRecommendationEntity();
            doctorRecommendationEntity.setRecommendTime(now);
            doctorRecommendationEntity.setRecommendReason(recommendDto.getRecommendReason());
            doctorRecommendationEntity.setRecommendDeptId(recommendDto.getRecommendDeptId());
            doctorRecommendationEntity.setRecommendHisDeptCode(StringUtils.isEmpty(recommendDto.getHisDeptCode()) ? "" : recommendDto.getHisDeptCode());
            doctorRecommendationEntity.setSource(StringUtils.isEmpty(recommendDto.getHisDeptCode()) ? 2 : 1);
            doctorRecommendationEntity.setEmpiid(empi);
            doctorRecommendationEntity.setProjectId(subProject.getProjectId());
            doctorRecommendationEntity.setSubProjectId(subProjectId);
            doctorRecommendationEntity.setHisAdmId(recommendDto.getHisAdmId());
            doctorRecommendationEntity.setAdmType(recommendDto.getAdmType());
            doctorRecommendationEntity.setContent(recommendDto.getContent());

            //设置顶级医疗机构ID
            if (topRecommendDeptId != null) {
                doctorRecommendationEntity.setTopRecommendDeptId(topRecommendDeptId);
            }

            if (type == null) {
                type = subProject.getType();
                // 受试者推荐时，需要判断患者是否为系统推荐
                if (type.equals(2)) {
                    type = preRecommendService.isMatch(subProject.getProjectId(), subProjectId, empi) ? 0 : 1;
                }
            }
            doctorRecommendationEntity.setType(type);
            doctorRecommendationEntity.setRecommendId(userId);

            list.add(doctorRecommendationEntity);

            // 5.手动推荐病例需同步患者信息
            if (type == 1) {
                empiList.add(empi);
            }
        }
        vo.setList(list);
        vo.setEmpiList(empiList);
        return vo;
    }

    private Long getTopDeptId(RecommendDto recommendDto) {
        Long topRecommendDeptId = null;
        Long recommendDeptId = recommendDto.getRecommendDeptId();
        if (recommendDeptId != null) {
            //根据科室ID获取机构
            List<SysDeptEntity> deptEntities = deptService.getAll();
            SysDeptEntity dept = deptService.getTopDeptById(deptEntities, recommendDeptId);
            if (dept != null) {
                topRecommendDeptId = dept.getId();
            }
        }
        return topRecommendDeptId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String recommendationByMobile(MobilePatientDTO mobilePatientDTO, Long userId, Integer type) {
        PatCustomRecordService patCustomRecordService = SpringContextHolder.getBean(PatCustomRecordService.class);

        //临时生成一个empiid
        String empiid = "H5" + IdUtils.getEmpi();
        mobilePatientDTO.setEmpi(empiid);

        //先查询患者表是否已存在
        //查询是否有重复
        QueryWrapper<PatientEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(PatientEntity::getIdCard, mobilePatientDTO.getIdCard());
        List<PatientEntity> patientList = patientService.list(qw);
        if (!CollectionUtils.isEmpty(patientList)) {
            empiid = patientList.get(0).getEmpiid();
        }


        RecommendDto recommendDto = mobilePatientDTO.getRecommendDto();

        TempRecommendationVO vo = getDocRecommendationList(empiid, recommendDto, userId, type);

        List<DoctorRecommendationEntity> list = vo.getList();

        if (CollectionUtils.isEmpty(patientList)) {
            //只有empi是新生成的才进行保存
            patientService.intoGroupByMobile(mobilePatientDTO, empiid);
        }

        // 5.若没有一个项目可以被推荐 返回报错
        if (list.isEmpty()) {
            throw new BusinessException("患者已推荐至所选择项目，无法重复推荐");
        }

        // 6.批量插入推荐信息
        this.saveBatch(list);

        //添加自定义勾选纳排的记录
        List<PatCustomRecordEntity> addList = new ArrayList<>();
        for (Long group : recommendDto.getGroupConfigIds()) {
            PatCustomRecordEntity record = new PatCustomRecordEntity();
            record.setEmpiid(empiid);
            record.setGroupConfigId(group);
            addList.add(record);
        }
        if (!CollectionUtils.isEmpty(addList)) {
            patCustomRecordService.saveBatch(addList);
        }

        return empiid;
    }

    @Override
    public List<String> getEmpiBySubProjectId(Long subProjectId) {
        return baseMapper.getEmpiBySubProjectId(subProjectId);
    }

    @Override
    public List<PersonalReportDto> personalRecommendData(String date) {
        QueryWrapper<DoctorRecommendationEntity> qw = new QueryWrapper<>();

        if (StringUtils.isNotBlank(date)) {
            // 2.拼接完整日期获取当月起始与结束日期
            Date startDate = DateUtils.getByDateStr(date + "-01");
            Date endDate = DateUtils.addDateMonths(startDate, 1);
            qw.le("recommend_time", endDate).ge("recommend_time", startDate);
        }


        // 3.统计总推荐数
        List<PersonalReportDto> totalPersonalReportDtoList = this.baseMapper.groupPat(qw);

        // 4.统计系统推荐数
        List<PersonalReportDto> systemPersonalReportDtoList = this.baseMapper.groupPat(qw.eq("type", 0));

        if (CollectionUtils.isEmpty(totalPersonalReportDtoList)) {
            return new ArrayList<>();
        }

        // 5.整合计算统计数
        for (PersonalReportDto personalReportDto : totalPersonalReportDtoList) {
            Integer total = personalReportDto.getSum();

            for (PersonalReportDto systemPersonalReportDto : systemPersonalReportDtoList) {
                if (personalReportDto.getDoctorId() == systemPersonalReportDto.getDoctorId() && personalReportDto.getProjectId().equals(personalReportDto.getProjectId())) {
                    Integer system = systemPersonalReportDto.getSum();
                    personalReportDto.setSystem(system);
                    personalReportDto.setPersonal(total - system);
                }
            }

            if (personalReportDto.getSystem() == null) {
                personalReportDto.setSystem(0);
                personalReportDto.setPersonal(total);
            }
        }

        return totalPersonalReportDtoList;
    }

//    @Override
//    public List<DepartmentReportDto> departmentRecommendData(String date) {
//        QueryWrapper<DoctorRecommendationEntity> qw = new QueryWrapper<>();
//
//        if (StringUtils.isNotBlank(date)) {
//            // 1.拼接完整日期获取当月起始与结束日期
//            Date startDate = DateUtils.getByDateStr(date + "-01");
//            Date endDate = DateUtils.addDateMonths(startDate, 1);
//            qw.le("a.recommend_time", endDate)
//                    .ge("a.recommend_time", startDate);
//        }
//
//        // 2.获取科室统计情况
//        List<DepartmentReportDto> departmentReportDtoList = this.baseMapper.groupByDept(qw);
//
//        if (CollectionUtils.isEmpty(departmentReportDtoList)) {
//            return new ArrayList<>();
//        }
//
//        // 3.设置机构总医生数添加为空的科室信息
//        setProperties(departmentReportDtoList);
//
//        return departmentReportDtoList;
//
//    }


    @Override
    public void setRecommendCount(List<ReportDto> result, Date startTime, Date endTime) {
        // 所有就诊类型累计
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        List<Long> deptIdList = result.stream().map(ReportDto::getDeptId).collect(Collectors.toList());
        qw.in("a.recommend_dept_id", deptIdList);
//        List<ReportDto> allRecommendCount = this.baseMapper.groupDept(qw);
//        Map<Long, Integer> map = CollectionUtils.isEmpty(allRecommendCount) ? new LinkedHashMap<>() : allRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

        // 门诊累计
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        qwO.eq("a.adm_type", "O");
        qwO.in("a.recommend_dept_id", deptIdList);
//        List<ReportDto> allORecommendCount = this.baseMapper.groupDept(qwO);
//        Map<Long, Integer> mapO = CollectionUtils.isEmpty(allORecommendCount) ? new LinkedHashMap<>() : allORecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

        // 住院累计
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        qwI.eq("a.adm_type", "I");
        qwI.in("a.recommend_dept_id", deptIdList);
//        List<ReportDto> allIRecommendCount = this.baseMapper.groupDept(qwI);
//        Map<Long, Integer> mapI = CollectionUtils.isEmpty(allIRecommendCount) ? new LinkedHashMap<>() : allIRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

        // 急诊累计
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        qwE.eq("a.adm_type", "E");
        qwE.in("a.recommend_dept_id", deptIdList);
//        List<ReportDto> allERecommendCount = this.baseMapper.groupDept(qwE);
//        Map<Long, Integer> mapE = CollectionUtils.isEmpty(allERecommendCount) ? new LinkedHashMap<>() : allERecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));


        // 所有就诊类型累计
        QueryWrapper<DeptRecommendCountEntity> qwMid = new QueryWrapper<>();
        qwMid.lambda().eq(DeptRecommendCountEntity::getAdmType, "ALL");
        qwMid.lambda().in(DeptRecommendCountEntity::getDeptId, deptIdList);
        qwMid.lambda().eq(DeptRecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<DeptRecommendCountEntity> allRecommendCount = deptRecommendCountService.list(qwMid);
        Map<Long, Integer> map = CollectionUtils.isEmpty(allRecommendCount) ? new LinkedHashMap<>() : allRecommendCount.stream().collect(Collectors.toMap(DeptRecommendCountEntity::getDeptId, DeptRecommendCountEntity::getRecommendCount));

        // 门诊累计
        QueryWrapper<DeptRecommendCountEntity> qwOMid = new QueryWrapper<>();
        qwOMid.lambda().eq(DeptRecommendCountEntity::getAdmType, "O");
        qwOMid.lambda().in(DeptRecommendCountEntity::getDeptId, deptIdList);
        qwOMid.lambda().eq(DeptRecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<DeptRecommendCountEntity> allORecommendCount = deptRecommendCountService.list(qwOMid);
        Map<Long, Integer> mapO = CollectionUtils.isEmpty(allORecommendCount) ? new LinkedHashMap<>() : allORecommendCount.stream().collect(Collectors.toMap(DeptRecommendCountEntity::getDeptId, DeptRecommendCountEntity::getRecommendCount));

        // 住院累计
        QueryWrapper<DeptRecommendCountEntity> qwIMid = new QueryWrapper<>();
        qwIMid.lambda().eq(DeptRecommendCountEntity::getAdmType, "I");
        qwIMid.lambda().in(DeptRecommendCountEntity::getDeptId, deptIdList);
        qwIMid.lambda().eq(DeptRecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<DeptRecommendCountEntity> allIRecommendCount = deptRecommendCountService.list(qwIMid);
        Map<Long, Integer> mapI = CollectionUtils.isEmpty(allIRecommendCount) ? new LinkedHashMap<>() : allIRecommendCount.stream().collect(Collectors.toMap(DeptRecommendCountEntity::getDeptId, DeptRecommendCountEntity::getRecommendCount));

        // 急诊累计
        QueryWrapper<DeptRecommendCountEntity> qwEMid = new QueryWrapper<>();
        qwEMid.lambda().eq(DeptRecommendCountEntity::getAdmType, "E");
        qwEMid.lambda().in(DeptRecommendCountEntity::getDeptId, deptIdList);
        qwEMid.lambda().eq(DeptRecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<DeptRecommendCountEntity> allERecommendCount = deptRecommendCountService.list(qwEMid);
        Map<Long, Integer> mapE = CollectionUtils.isEmpty(allERecommendCount) ? new LinkedHashMap<>() : allERecommendCount.stream().collect(Collectors.toMap(DeptRecommendCountEntity::getDeptId, DeptRecommendCountEntity::getRecommendCount));

        // 累计值填充
        result.forEach(reportDto -> {
            Long deptId = reportDto.getDeptId();
            int totalRecommend = map.containsKey(deptId) ? map.get(deptId) : 0;
            reportDto.setTotalRecommend(totalRecommend);
            int totalORecommend = mapO.containsKey(deptId) ? mapO.get(deptId) : 0;
            reportDto.setTotalORecommend(totalORecommend);
            int totalIRecommend = mapI.containsKey(deptId) ? mapI.get(deptId) : 0;
            reportDto.setTotalIRecommend(totalIRecommend);
            int totalERecommend = mapE.containsKey(deptId) ? mapE.get(deptId) : 0;
            reportDto.setTotalERecommend(totalERecommend);
        });

        if (startTime != null) {
            // 所有就诊类型增量
            qw.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addRecommendCount = this.baseMapper.groupDept(qw);
            Map<Long, Integer> addMap = CollectionUtils.isEmpty(addRecommendCount) ? new LinkedHashMap<>() : addRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

            // 门诊增量
            qwO.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addORecommendCount = this.baseMapper.groupDept(qwO);
            Map<Long, Integer> addOMap = CollectionUtils.isEmpty(addORecommendCount) ? new LinkedHashMap<>() : addORecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

            // 住院增量
            qwI.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addIRecommendCount = this.baseMapper.groupDept(qwI);
            Map<Long, Integer> addIMap = CollectionUtils.isEmpty(addIRecommendCount) ? new LinkedHashMap<>() : addIRecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

            // 急诊增量
            qwE.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addERecommendCount = this.baseMapper.groupDept(qwE);
            Map<Long, Integer> addEMap = CollectionUtils.isEmpty(addERecommendCount) ? new LinkedHashMap<>() : addERecommendCount.stream().collect(Collectors.toMap(ReportDto::getDeptId, ReportDto::getTotalRecommend));

            // 增量值填充
            result.forEach(reportDto -> {
                Long deptId = reportDto.getDeptId();
                int addRecommend = addMap.containsKey(deptId) ? addMap.get(deptId) : 0;
                reportDto.setAddRecommend(addRecommend);
                int addORecommend = addOMap.containsKey(deptId) ? addOMap.get(deptId) : 0;
                reportDto.setAddORecommend(addORecommend);
                int addIRecommend = addIMap.containsKey(deptId) ? addIMap.get(deptId) : 0;
                reportDto.setAddIRecommend(addIRecommend);
                int addERecommend = addEMap.containsKey(deptId) ? addEMap.get(deptId) : 0;
                reportDto.setAddERecommend(addERecommend);

                int dischargesCount = reportDto.getDischargesCount();
                reportDto.setRecommendRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addRecommend * 10000 / reportDto.getDischargesCount()) / 100 + "%");
                reportDto.setTargetRecommendCompeteRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addRecommend * 10000 / reportDto.getTargetRecommend()) / 100 + "%");
            });
        } else {
            // 增量值填充
            result.forEach(reportDto -> {
                reportDto.setAddRecommend(reportDto.getTotalRecommend());
                reportDto.setAddORecommend(reportDto.getTotalORecommend());
                reportDto.setAddIRecommend(reportDto.getTotalIRecommend());
                reportDto.setAddERecommend(reportDto.getTotalERecommend());

                int dischargesCount = reportDto.getDischargesCount();
                int addRecommend = reportDto.getTotalRecommend();
                reportDto.setRecommendRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addRecommend * 10000 / reportDto.getDischargesCount()) / 100 + "%");
                reportDto.setTargetRecommendCompeteRatio(dischargesCount == 0 ? "0.00%" : (double) Math.round(addRecommend * 10000 / reportDto.getTargetRecommend()) / 100 + "%");
            });
        }
    }

    @Override
    public void setPerRecommendCount(List<ReportDto> result, Date startTime, Date endTime) {
        // 所有就诊类型累计
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
        List<Long> doctorIdList = result.stream().map(ReportDto::getDoctorId).distinct().collect(Collectors.toList());
        qw.in("a.recommend_id", doctorIdList);
        List<ReportDto> allRecommendCount = this.baseMapper.groupPer(qw);

        // 门诊累计
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        qwO.eq("a.adm_type", "O");
        qwO.in("a.recommend_id", doctorIdList);
        List<ReportDto> allORecommendCount = this.baseMapper.groupPer(qwO);

        // 住院累计
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        qwI.eq("a.adm_type", "I");
        qwI.in("a.recommend_id", doctorIdList);
        List<ReportDto> allIRecommendCount = this.baseMapper.groupPer(qwI);

        // 急诊累计
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        qwE.eq("a.adm_type", "E");
        qwE.in("a.recommend_id", doctorIdList);
        List<ReportDto> allERecommendCount = this.baseMapper.groupPer(qwE);

        // 累积数量填充
        result.forEach(reportDto -> {
            Long doctorId = reportDto.getDoctorId();
            Long deptId = reportDto.getDeptId();
            List<ReportDto> list = CollectionUtils.isEmpty(allRecommendCount) ? null : allRecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                    && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(list)) {
                reportDto.setTotalRecommend(list.get(0).getTotalRecommend() == null ? 0 : list.get(0).getTotalRecommend());
            }

            // 门诊
            List<ReportDto> listO = CollectionUtils.isEmpty(allORecommendCount) ? null : allORecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                    && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(listO)) {
                reportDto.setTotalORecommend(listO.get(0).getTotalRecommend() == null ? 0 : listO.get(0).getTotalRecommend());
            }

            // 住院
            List<ReportDto> listI = CollectionUtils.isEmpty(allIRecommendCount) ? null : allIRecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                    && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(listI)) {
                reportDto.setTotalIRecommend(listI.get(0).getTotalRecommend() == null ? 0 : listI.get(0).getTotalRecommend());
            }

            // 急诊
            List<ReportDto> listE = CollectionUtils.isEmpty(allERecommendCount) ? null : allERecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                    && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(listE)) {
                reportDto.setTotalERecommend(listE.get(0).getTotalRecommend() == null ? 0 : listE.get(0).getTotalRecommend());
            }
        });

        if (startTime != null) {
            // 所有就诊类型增量
            qw.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addRecommendCount = this.baseMapper.groupPer(qw);

            // 门诊增量
            qwO.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addORecommendCount = this.baseMapper.groupPer(qwO);
            // 住院增量
            qwI.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addIRecommendCount = this.baseMapper.groupPer(qwI);
            // 急诊增量
            qwE.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            List<ReportDto> addERecommendCount = this.baseMapper.groupPer(qwE);

            // 累积数量填充
            result.forEach(reportDto -> {
                Long doctorId = reportDto.getDoctorId();
                Long deptId = reportDto.getDeptId();
                List<ReportDto> list = CollectionUtils.isEmpty(addRecommendCount) ? null : addRecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                        && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(list)) {
                    reportDto.setAddRecommend(list.get(0).getTotalRecommend() == null ? 0 : list.get(0).getTotalRecommend());
                }

                // 门诊
                List<ReportDto> listO = CollectionUtils.isEmpty(addORecommendCount) ? null : addORecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                        && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(listO)) {
                    reportDto.setAddORecommend(listO.get(0).getTotalRecommend() == null ? 0 : listO.get(0).getTotalRecommend());
                }

                // 住院
                List<ReportDto> listI = CollectionUtils.isEmpty(addIRecommendCount) ? null : addIRecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                        && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(listI)) {
                    reportDto.setAddIRecommend(listI.get(0).getTotalRecommend() == null ? 0 : listI.get(0).getTotalRecommend());
                }

                // 急诊
                List<ReportDto> listE = CollectionUtils.isEmpty(addERecommendCount) ? null : addERecommendCount.stream().filter(p -> p.getDeptId() != null && doctorId != null && p.getDeptId().equals(deptId)
                        && p.getDoctorId() != null && deptId != null && p.getDoctorId().equals(doctorId)).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(listE)) {
                    reportDto.setAddERecommend(listE.get(0).getTotalRecommend() == null ? 0 : listE.get(0).getTotalRecommend());
                }
            });
        } else {
            result.forEach(reportDto -> {
                reportDto.setAddRecommend(reportDto.getTotalRecommend());
                // 门诊增量
                reportDto.setAddORecommend(reportDto.getTotalORecommend());
                // 住院增量
                reportDto.setAddIRecommend(reportDto.getTotalIRecommend());
                // 急诊增量
                reportDto.setAddERecommend(reportDto.getTotalERecommend());
            });
        }
    }

    @Override
    public void totalRecommend(ReportDto reportDto, Date startTime, Date endTime) {
        // 所有就诊类型累计总数
        QueryWrapper<ReportDto> qw = new QueryWrapper<>();
//        Integer totalRecommend = this.baseMapper.totalRecommend(qw);
//        reportDto.setTotalRecommend(totalRecommend);

        // 门诊累计总数
        QueryWrapper<ReportDto> qwO = new QueryWrapper<>();
        qwO.eq("a.adm_type", "O");
//        Integer totalORecommend = this.baseMapper.totalRecommend(qwO);
//        reportDto.setTotalORecommend(totalORecommend);

        // 住院累计总数
        QueryWrapper<ReportDto> qwI = new QueryWrapper<>();
        qwI.eq("a.adm_type", "I");
//        Integer totalIRecommend = this.baseMapper.totalRecommend(qwI);
//        reportDto.setTotalIRecommend(totalIRecommend);

        // 急诊累计总数
        QueryWrapper<ReportDto> qwE = new QueryWrapper<>();
        qwE.eq("a.adm_type", "E");
//        Integer totalERecommend = this.baseMapper.totalRecommend(qwE);
//        reportDto.setTotalERecommend(totalERecommend);

        QueryWrapper<RecommendCountEntity> qwMid = new QueryWrapper<>();
        qwMid.lambda().eq(RecommendCountEntity::getAdmType, "ALL");
        qwMid.lambda().eq(RecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<RecommendCountEntity> allType = recommendCountService.list(qwMid);
        Integer totalRecommend = CollectionUtils.isEmpty(allType) ? 0 : allType.get(0).getRecommendCount();
        reportDto.setTotalRecommend(totalRecommend);

        QueryWrapper<RecommendCountEntity> qwOMid = new QueryWrapper<>();
        qwOMid.lambda().eq(RecommendCountEntity::getAdmType, "O");
        qwOMid.lambda().eq(RecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<RecommendCountEntity> oType = recommendCountService.list(qwOMid);
        Integer totalORecommend = CollectionUtils.isEmpty(oType) ? 0 : oType.get(0).getRecommendCount();
        reportDto.setTotalORecommend(totalORecommend);

        QueryWrapper<RecommendCountEntity> qwIMid = new QueryWrapper<>();
        qwIMid.lambda().eq(RecommendCountEntity::getAdmType, "I");
        qwIMid.lambda().eq(RecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<RecommendCountEntity> iType = recommendCountService.list(qwIMid);
        Integer totalIRecommend = CollectionUtils.isEmpty(iType) ? 0 : iType.get(0).getRecommendCount();
        reportDto.setTotalIRecommend(totalIRecommend);

        QueryWrapper<RecommendCountEntity> qwEMid = new QueryWrapper<>();
        qwEMid.lambda().eq(RecommendCountEntity::getAdmType, "E");
        qwEMid.lambda().eq(RecommendCountEntity::getCreateTime, DateUtils.getNowTimeStr());
        List<RecommendCountEntity> eType = recommendCountService.list(qwEMid);
        Integer totalERecommend = CollectionUtils.isEmpty(eType) ? 0 : eType.get(0).getRecommendCount();
        reportDto.setTotalERecommend(totalERecommend);

        if (startTime != null) {
            // 所有就诊类型增量总数
            qw.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            reportDto.setAddRecommend(this.baseMapper.totalRecommend(qw));

            // 门诊增量总数
            qwO.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            reportDto.setAddORecommend(this.baseMapper.totalRecommend(qwO));

            // 住院增量总数
            qwI.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            reportDto.setAddIRecommend(this.baseMapper.totalRecommend(qwI));

            // 急诊增量总数
            qwE.lt("a.recommend_time", endTime).ge("a.recommend_time", startTime);
            reportDto.setAddERecommend(this.baseMapper.totalRecommend(qwE));
        } else {
            reportDto.setAddRecommend(totalRecommend);
            // 门诊增量总数
            reportDto.setAddORecommend(totalORecommend);
            // 住院增量总数
            reportDto.setAddIRecommend(totalRecommend);
            // 急诊增量总数
            reportDto.setAddERecommend(totalERecommend);
        }

        Integer addRecommend = reportDto.getAddRecommend();
        int dischargesCount = reportDto.getDischargesCount();
        reportDto.setRecommendRatio(dischargesCount == 0 ? "0.0%" : (double) Math.round(addRecommend * 10000 / reportDto.getDischargesCount()) / 100 + "%");
        reportDto.setTargetRecommendCompeteRatio(dischargesCount == 0 ? "0.0%" : (double) Math.round(addRecommend * 10000 / reportDto.getTargetRecommend()) / 100 + "%");
    }

    @Override
    public Boolean hasRecommend(String empi, Long subProjectId) {
        return this.count(new QueryWrapper<DoctorRecommendationEntity>()
                .lambda().eq(DoctorRecommendationEntity::getEmpiid, empi)
                .eq(DoctorRecommendationEntity::getSubProjectId, subProjectId)
                .and(qw -> {
                    qw.eq(DoctorRecommendationEntity::getStatus, RECOMMENDED.getCode())
                            .or().eq(DoctorRecommendationEntity::getStatus, PROPOSE_INBOUND.getCode())
                            .or().eq(DoctorRecommendationEntity::getStatus, INBOUNDED.getCode());
                })
        ) > 0;
    }

    /**
     * 病例已推荐项目
     *
     * @param empi
     * @return
     */
    @Override
    public List<DoctorRecommendProjectDiseaseVo> getRecommendProject(String empi) {
        List<DoctorRecommendProjectDiseaseVo> inboundProjectDieaseVoList = baseMapper.getRecommendProject(new QueryWrapper<DoctorRecommendProjectDiseaseVo>()
                .eq("a.empiid", empi)
                .and(qw -> {
                    qw.eq("a.status", RECOMMENDED.getCode())
                            .or().eq("a.status", PROPOSE_INBOUND.getCode())
                            .or().eq("a.status", INBOUNDED.getCode());
                }));
        return inboundProjectDieaseVoList;
    }

    @Override
    public PageUtils getByQuery(Map<String, Object> param) {
        QueryWrapper<DoctorRecommendProjectDiseaseVo> qw = buildQuery(param);
        IPage<DoctorRecommendProjectDiseaseVo> pageFilter = new Query<DoctorRecommendProjectDiseaseVo>().getPage(param);
        return new PageUtils(baseMapper.getRecommendProject(pageFilter, qw));
    }

    @Override
    public List<DoctorRecommendProjectDiseaseVo> getListByQuery(Map<String, Object> param) {
        QueryWrapper<DoctorRecommendProjectDiseaseVo> qw = buildQuery(param);
        return baseMapper.getRecommendProject(qw);
    }

    private QueryWrapper<DoctorRecommendProjectDiseaseVo> buildQuery(Map<String, Object> param) {
        Long userId = MapUtils.getValue(param, "userId", Long.class);
        if (userId == null) {
            throw new BusinessException("当前用户为空");
        }
        Integer status = MapUtils.getValue(param, "status", Integer.class);
        String query = MapUtils.getValue(param, "query", String.class);
        QueryWrapper<DoctorRecommendProjectDiseaseVo> qw = new QueryWrapper<>();
        if (status != null) {
            qw.eq("a.status", status);
        }

        //只能查看当前医生的推荐
        qw.eq("a.recommend_id", userId);
        //只能是移动端入组类型的
        qw.eq("a.type", 2);

        if (StringUtils.isNotBlank(query)) {
            qw.and(andQw -> {
                andQw.like("rp.project_name", query) //项目名称
                        .or()
                        .like("rsu.nickname", query)//推荐人名称
                        .or()
                        .like("d.name", query); //患者名
            });
        }
        return qw;
    }
}
