package com.boot.commons.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 导出状态枚举
 * <AUTHOR>
 */

public enum ExportStatusEnum {

    /**
     * 导出进行中
     */
    EXECUTING(0, "executing", "导出进行中", ""),
    /**
     * 导出成功
     */
    SUCCESS(1, "success", "导出成功", ""),
    /**
     * 导出失败
     */
    FAIL(2, "fail", "导出失败", ""),
    ;
    private int type;
    private String code;
    private String name;
    private String desc;

    private ExportStatusEnum(int type, String code, String name, String desc) {
        this.type = type;
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<String, Object> typeMap = new HashMap<String, Object>();
    public static Map<String, ExportStatusEnum> typeEnumMap = new HashMap<String, ExportStatusEnum>();
    static {
        ExportStatusEnum[] types = ExportStatusEnum.values();
        for (ExportStatusEnum type : types) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", type.type);
            map.put("code", type.code);
            map.put("name", type.name);
            map.put("desc", type.desc);
            typeMap.put(String.valueOf(type.type), map);
            typeEnumMap.put(String.valueOf(type.type), type);
        }
    }

}
