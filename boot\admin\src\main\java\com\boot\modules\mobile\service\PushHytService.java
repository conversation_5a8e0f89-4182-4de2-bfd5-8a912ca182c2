package com.boot.modules.mobile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.mobile.dto.PushHytDto;
import com.boot.modules.mobile.entity.PushHytEntity;

import java.util.Map;

public interface PushHytService extends IService<PushHytEntity> {

    /**
     * 推送华易通
     * @param pushHytDto
     * @param mobilePatientDTO
     */
    void savePathwayPatient(PushHytDto pushHytDto, MobilePatientDTO mobilePatientDTO);

    /**
     * 校验
     * @param mobilePatientDTO
     * @param userId
     * @param projectId
     */
    PushHytDto valid(MobilePatientDTO mobilePatientDTO, Long userId, Long projectId);

    /**
     * 分页查询
     * @param param
     * @return
     */
    PageUtils queryPage(Map<String, Object> param);

    /**
     * 重复推送手动
     * @param id
     */
    void again(Long id);
}
