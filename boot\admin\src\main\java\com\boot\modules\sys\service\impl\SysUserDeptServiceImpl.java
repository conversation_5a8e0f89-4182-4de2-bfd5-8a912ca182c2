//package com.boot.modules.sys.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.boot.commons.exception.BusinessException;
//import com.boot.commons.utils.ListUtils;
//import com.boot.commons.utils.StringUtils;
//import com.boot.modules.patient.dto.DepartmentReportDto;
//import com.boot.modules.patient.dto.PersonalReportDto;
//import com.boot.modules.sys.dao.SysUserDeptDao;
//import com.boot.modules.sys.entity.SysUserDeptEntity;
//import com.boot.modules.sys.service.SysUserDeptService;
//import com.boot.modules.sys.vo.DeptModel;
//import com.boot.modules.sys.vo.UserDeptsVo;
//import com.boot.modules.sys.vo.UserPermissionVo;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @desc :
// * @create 2022-05-19
// */
//@Service
//public class SysUserDeptServiceImpl extends ServiceImpl<SysUserDeptDao, SysUserDeptEntity> implements SysUserDeptService {
//    /**
//     * 获取用户的医联体数据权限
//     *
//     * @param userId
//     * @return
//     */
//    @Override
//    public List<UserPermissionVo> getUserPermission(Long userId) {
//        QueryWrapper<UserPermissionVo> qw = new QueryWrapper<>();
//        qw.eq("a.user_id", userId);
//        List<UserPermissionVo> list = baseMapper.getUserPermission(qw);
//
//        if (!CollectionUtils.isEmpty(list)) {
//            List<UserPermissionVo> result = new ArrayList<>();
//            for (UserPermissionVo userPermissionVo : list) {
//                if (StringUtils.isNotBlank(userPermissionVo.getDeptCode())) {
//                    result.add(userPermissionVo);
//                }
//            }
//            return result;
//        }
//
//        return list;
//    }
//
//    @Override
//    public List<DepartmentReportDto> getDeptUserCount(List<Long> DeptIdList) {
//        return this.baseMapper.getDeptUserCount(new QueryWrapper<DepartmentReportDto>()
//                .in("dept_id", DeptIdList));
//    }
//
//    @Override
//    public List<PersonalReportDto> getAllUserDept() {
//        return this.baseMapper.getAllUserDept();
//    }
//
//    /**
//     * 批量新增或者修改某个用户的机构权限
//     *
//     * @param vo
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void saveOrUpdate(UserDeptsVo vo) {
//        boolean res = true;
//
//        if (vo == null) {
//            return;
//        }
//        if (vo.getUserId() == null) {
//            throw new BusinessException("请先选择用户");
//        }
//
//        List<SysUserDeptEntity> list = new ArrayList<>();
//        for (DeptModel deptModel : vo.getDeptModels()) {
//            SysUserDeptEntity userDeptEntity = new SysUserDeptEntity();
//            userDeptEntity.setUserId(vo.getUserId());
//            userDeptEntity.setDeptId(deptModel.getDeptId());
//            userDeptEntity.setDeptCode(deptModel.getDeptCode());
//            list.add(userDeptEntity);
//        }
//        if (CollectionUtils.isEmpty(list)) {
//            throw new BusinessException("请至少选择一个机构");
//        }
//        List<SysUserDeptEntity> oldUserDeptList = this.list(
//                new QueryWrapper<SysUserDeptEntity>().lambda().eq(SysUserDeptEntity::getUserId, vo.getUserId())
//        );
//
//        if (CollectionUtils.isEmpty(oldUserDeptList)) {
//            res = this.saveBatch(list);
//        } else {
//
//            List<Long> newDeptIds = list.stream().map(SysUserDeptEntity::getDeptId).collect(Collectors.toList());
//            List<Long> oldDeptIds = oldUserDeptList.stream().map(SysUserDeptEntity::getDeptId).collect(Collectors.toList());
//
//            List<Long> reduceDeptIdListThanList = ListUtils.getReduceListThanList(newDeptIds, oldDeptIds);
//            List<Long> addDeptIdListThanList = ListUtils.getAddListThanList(newDeptIds, oldDeptIds);
//
//            if (!CollectionUtils.isEmpty(reduceDeptIdListThanList)) {
//                List<Long> reduceList = new ArrayList<>();
//                for (Long deptId : reduceDeptIdListThanList) {
//                    for (SysUserDeptEntity userDeptEntity : oldUserDeptList) {
//                        if (deptId.equals(userDeptEntity.getDeptId())) {
//                            reduceList.add(userDeptEntity.getId());
//                            break;
//                        }
//                    }
//                }
//                res = res && this.removeByIds(reduceList);
//            }
//
//            if (!CollectionUtils.isEmpty(addDeptIdListThanList)) {
//                List<SysUserDeptEntity> addListThanList = new ArrayList<>();
//                for (Long deptId : addDeptIdListThanList) {
//                    for (SysUserDeptEntity userDeptEntity : list) {
//                        if (deptId.equals(userDeptEntity.getDeptId())) {
//                            addListThanList.add(userDeptEntity);
//                            break;
//                        }
//                    }
//                }
//                res = res && this.saveBatch(addListThanList);
//            }
//        }
//        if (!res) {
//            throw new BusinessException("修改失败");
//        }
//    }
//}
