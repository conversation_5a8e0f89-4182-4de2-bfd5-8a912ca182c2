package com.boot.modules.sys.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.sys.entity.SysMenuEntity;

import java.util.List;


/**
 * 菜单管理
 *
 * <AUTHOR>
 */
public interface SysMenuService extends IService<SysMenuEntity> {

    /**
     * 根据父菜单，查询子菜单
     *
     * @param parentId   父菜单ID
     * @param menuIdList 用户菜单ID
     */
    List<SysMenuEntity> queryListParentId(Long parentId, List<Long> menuIdList);

    List<SysMenuEntity> queryListParentId(Long parentId, List<Long> menuIdList, boolean isIncludeButtonMenu);

    /**
     * 根据父菜单，查询子菜单
     *
     * @param parentId 父菜单ID
     */
    List<SysMenuEntity> queryListParentId(Long parentId);

    List<SysMenuEntity> queryListParentId(Long parentId, boolean isIncludeButtonMenu);

    /**
     * 获取用户菜单列表
     */
    List<SysMenuEntity> getUserMenuList(Long userId);

    List<SysMenuEntity> getUserMenuList(Long userId, boolean isIncludeButtonMenu);

    /**
     * 获取当前用户所有应用中的菜单
     *
     * @param userId              用户id
     * @param isIncludeButtonMenu 是否包含按钮菜单
     * @return
     */
    List<SysMenuEntity> getUserMenuListByAppId(Long userId, Long appId, boolean isIncludeButtonMenu);

    List<SysMenuEntity> getMenuList(boolean isIncludeButtonMenu);

    List<SysMenuEntity> getMenuTreeList(Long appId, boolean isIncludeButtonMenu);

    /**
     * 获取指定角色的菜单树
     *
     * @param roleId
     * @return
     */
    List<SysMenuEntity> getRoleMenus(Long roleId);


    /**
     * 删除
     */
    void delete(Long menuId);

    /**
     * 修改菜单的所属应用
     *
     * @param appId
     * @param menuId
     * @return
     */
    boolean updateMenuApp(Long appId, Long menuId);


    /**
     * 判断当前角色是否有入组权限
     * @param roleId
     * @return
     */
    Boolean hasInputCasePermission(Long roleId);
}
