package com.boot.modules.sync.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/23 15:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KelinSearchExportResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * http状态码
     */
    private Integer code;

    /**
     * 响应数据
     */
    private KelinSearchExportResponseData data;

    /**
     * 响应状态
     */
    private String status;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 组装多页数据
     * @param response 组装后的数据
     */
    public void add(KelinSearchExportResponse response) {
        this.getData().setTotal(
                this.getData().getTotal() + response.getData().getTotal()
        );
        this.getData().setData(
                this.getData().getData()
                        .stream()
                        .sequential()
                        .collect(
                                Collectors.toCollection(
                                        () -> response.getData().getData()
                                )
                        )
        );
    }
}
