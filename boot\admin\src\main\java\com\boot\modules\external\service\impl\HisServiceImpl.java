package com.boot.modules.external.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.constant.HxConst;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.model.BootAdminProperties;
import com.boot.commons.utils.HttpUtil;
import com.boot.commons.utils.ObjectUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.external.model.RdrResponseModel;
import com.boot.modules.external.service.HisService;
import com.boot.modules.external.service.KeLinService;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.vo.InboundProjectDieaseVo;
import com.boot.modules.project.dao.SubProjectDao;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.project.service.GroupConfigService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sync.utils.JwtForKelin;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.model.DieaseProjectModel;
import com.boot.modules.sys.service.DiseaseService;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserHisService;
import com.boot.modules.sys.service.SysUserService;

import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.common.util.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.boot.commons.constant.HxConst.KELIN_PAT_EMPI_ID_FIELD;

@Slf4j
@Service
public class HisServiceImpl implements HisService {
    @Resource
    private SysUserHisService userHisService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private DiseaseService diseaseService;

    @Resource
    private GroupConfigService groupConfigService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private KeLinService keLinService;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysUserService sysUserService;

    @Override
    public SysUserEntity getUserByForeignId(String foreignId, String deptCode) {

        if (StringUtils.isEmpty(foreignId)) {
            log.error("用户的系统工号未录入");
            throw new BusinessException("用户的系统工号未录入");
        }

        List<SysUserEntity> userList = userHisService.getByForeignAndDept(foreignId, deptCode);

        if (CollectionUtils.isEmpty(userList)) {
            log.error("当前用户未在科研系统中绑定用户,请联系管理员");
            throw new BusinessException("当前用户未在科研系统中绑定用户,请联系管理员");
        }

        return userList.get(0);
    }

    @Resource
    private SubProjectDao subProjectDao;

    @Override
    public List<SubProjectVo> myproject(Long userId, String empi) {
        // 查用户参与的项目
        List<SubProjectVo> subProjectVoList = subProjectService.getByUserId(userId).stream().filter(v -> v.getStatus() != 2).collect(Collectors.toList());
        if (StringUtils.isEmpty(empi)) {
            return subProjectVoList;
        }
        // 查病例的redis
        Map<String, List<String>> patientRedis = groupConfigService.getPatientMatchRedis(empi);
        // 判断该患者是否已锁定
        List<InboundProjectDieaseVo> inboundList = inboundProcessService.getInboundProject(empi, true);
        Boolean hasLock = !CollectionUtils.isEmpty(inboundList);

        // 查纳排满足情况
        for (SubProjectVo subProjectVo : subProjectVoList) {
            Map<String, List<Long>> groupConfigs = groupConfigService.getIsMatch(subProjectVo.getId(), patientRedis);

            // 判断本项目是否已经推荐
            Boolean hasRecommend = doctorRecommendationService.hasRecommend(empi, subProjectVo.getId());
            subProjectVo.setIsRecommend(!hasLock && !hasRecommend && groupConfigs.size() > 0 ? 1 : 0);
            subProjectVo.setIsMatched(groupConfigs.size() > 0 ? 1 : 0);
            subProjectVo.setPatStatus(hasLock ? 1 : hasRecommend ? 3 : 0);
            Integer includeCount = groupConfigService.count(new QueryWrapper<GroupConfigEntity>()
                    .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectVo.getId())
                    .eq(GroupConfigEntity::getType, 0));
            subProjectVo.setIncludeCount(includeCount);
            subProjectVo.setVisitConfigMap(groupConfigs);
        }
        return subProjectVoList;
    }

    @Override
    public List<DieaseProjectModel> deptproject(List<Long> deptIdList, String empi) {
        // 查科室病种、项目
        List<DieaseProjectModel> dieaseProjectModelList = diseaseService.getDieaseByDept(deptIdList);
        if (StringUtils.isEmpty(empi)) {
            return dieaseProjectModelList;
        }
        // 判断该患者是否已锁定
        List<InboundProjectDieaseVo> lockList = inboundProcessService.getInboundProject(empi, true);
        Boolean hasLock = !CollectionUtils.isEmpty(lockList);
        // 查病例的redis
        Map<String, List<String>> patientRedis = groupConfigService.getPatientMatchRedis(empi);
        // 查科室病种、项目、纳排满足情况
        for (DieaseProjectModel dieaseProjectModel : dieaseProjectModelList) {
            Boolean isRecommend = false;
            List<SubProjectVo> subProjectVoList = dieaseProjectModel.getProjectList();

            // 查纳排满足情况
            for (SubProjectVo subProjectVo : subProjectVoList) {
                Map<String, List<Long>> groupConfigs = groupConfigService.getIsMatch(subProjectVo.getId(), patientRedis);

                // 判断本项目是否已经推荐
                Boolean hasRecommend = doctorRecommendationService.hasRecommend(empi, subProjectVo.getId());
                if (groupConfigs.size() > 0) {
                    subProjectVo.setIsMatched(1);
                    dieaseProjectModel.setIsMatched(1);
                    if (!hasLock && !hasRecommend) {
                        isRecommend = true;
                        subProjectVo.setIsRecommend(1);
                    }
                }
                subProjectVo.setPatStatus(hasLock ? 1 : hasRecommend ? 3 : 0);
                Integer includeCount = groupConfigService.count(new QueryWrapper<GroupConfigEntity>()
                        .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectVo.getId())
                        .eq(GroupConfigEntity::getType, 0));
                subProjectVo.setIncludeCount(includeCount);
                subProjectVo.setVisitConfigMap(groupConfigs);
            }
            if (isRecommend) {
                dieaseProjectModel.setIsRecommend(1);
            }
        }
        return dieaseProjectModelList;
    }

    @Override
    public Map<String, Boolean> isRecommend(SysUserEntity user, Long deptId, List<Map<String, String>> regNoList) {
        Map<String, Boolean> result = new HashMap<>();
        regNoList.forEach(regNoInfo -> {
            String regNo = regNoInfo.get(HxConst.REGNO);
            String deptCode = regNoInfo.get(HxConst.MED_CODE);
            long start = System.currentTimeMillis();
            // 获取患者基本信息
            RdrResponseModel info = keLinService.getPatientInfo(regNo, deptCode);
            log.info("查询科林患者唯一ID耗时{}ms", (System.currentTimeMillis() - start));
            if (info == null || ObjectUtils.isEmpty(info.getData()) || ObjectUtils.isEmpty(info.getData().getList())) {
                log.error("获取患者唯一ID失败");
                return;
            }
            String empi = info.getData().getList().get(0).get(KELIN_PAT_EMPI_ID_FIELD).toString();
            // 判断用户参与项目是否符合系统推荐
            List<SubProjectVo> subProjectVoList = myproject(user.getId(), empi);
            Boolean isRecommend = !CollectionUtils.isEmpty(subProjectVoList) && subProjectVoList.stream().filter(p -> p.getIsRecommend() == 1).count() > 0 ? true : false;
            result.put(regNo, isRecommend);
            if (isRecommend) {
                return;
            }
            // 判断用户所属科室项目项目是否符合系统推荐
            List<DieaseProjectModel> dieaseProjectModelList = deptproject(Arrays.asList(deptId), empi);
            isRecommend = !CollectionUtils.isEmpty(dieaseProjectModelList) && dieaseProjectModelList.stream().filter(p -> p.getIsRecommend() == 1).count() > 0 ? true : false;
            result.put(regNo, isRecommend);
        });
        return result;
    }

    @Override
    public Map<String, Integer> getDisChgCount(String startDate, String endDate, String deptCode) {

        // 1.设置请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + JwtForKelin.getHisJwt());
        headers.put("SOAPAction", "http://tempuri.org/web.DHCCSMService.GetHisData.GetDischgCount");

        // 2.发送请求获取文档
        String result = HttpUtil.webServiceHttp(BootAdminProperties.dhWebService, getDisChgCountXML(startDate, endDate, deptCode), headers);
        // 3.解析请求结果
        List<HashMap> hashMaps = JSONArray.parseArray(result.substring(result.indexOf("["), result.lastIndexOf("]") + 1), HashMap.class);
        Map<String, Integer> resultMap = new HashMap<>();

        for (Map map : hashMaps) {
            resultMap.put(map.get("id").toString(), Integer.parseInt(map.get("count").toString()));
        }


        return resultMap;
    }

    @Override
    public List<Long> getDeptList(String hisDeptCode, Long userId) {
        List<Long> deptIdList = new ArrayList<>();
        // 特需门诊查询所有结构
        String[] split = BootAdminProperties.vipClinicCode.split(",");
        List<SysDeptEntity> list = sysDeptService.list(new QueryWrapper<SysDeptEntity>().lambda()
                .in(SysDeptEntity::getCode, Arrays.asList(split))
                .select(SysDeptEntity::getId));

        List<Long> vipDeptIdList = list.stream().map(SysDeptEntity::getId).collect(Collectors.toList());
        if (Arrays.binarySearch(split, hisDeptCode) >= 0) {
            SysUserEntity userEntity = sysUserService.getById(userId);
            String[] deptIdArray = userEntity.getDeptId().split(",");
            for(String deptId : deptIdArray) {
                if (vipDeptIdList.contains(Long.parseLong(deptId))) {
                    continue;
                }
                deptIdList.add(Long.parseLong(deptId));
            }
        }else {
            // HIS登陆科室
            deptIdList.add(sysDeptService.getIdByCode(hisDeptCode));
        }

        return deptIdList;
    }
    /**
     * 获取出院接口请求xml请求体
     *
     * @param startDate
     * @param endDate
     * @param deptCode
     * @return
     */
    private static String getDisChgCountXML(String startDate, String endDate, String deptCode) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <tem:GetDischgCount>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:startDate>" + startDate + "</tem:startDate>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:endDate>" + endDate + "</tem:endDate>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:deptInfo>" + deptCode + "</tem:deptInfo>\n" +
                "      </tem:GetDischgCount>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }
}
