package com.boot.modules.external.controller;

import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.ObjectUtils;
import com.boot.modules.external.model.RdrResponseModel;
import com.boot.modules.external.service.HisService;
import com.boot.modules.external.service.KeLinService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.patient.vo.InboundProjectDieaseVo;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.DiseaseService;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * his 系统调用接口
 */
@Api(tags = "his 系统调用接口")
@RestController
@RequestMapping("/his")
@Slf4j
public class HisController extends AbstractController {

    @Resource
    private HisService hisService;

    @Resource
    private KeLinService keLinService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private PatientService patientService;

    @Resource
    private DiseaseService diseaseService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private SysDeptService sysDeptService;

    @ApiOperation(value = "根据his工号查询用户信息", notes = "根据his工号查询用户信息")
    @GetMapping("/user/{foreignId}/{deptCode}")
    public Result foreign(@PathVariable() String foreignId, @PathVariable String deptCode) {
        return R.success(hisService.getUserByForeignId(foreignId, deptCode));
    }

    @ApiOperation(value = "验证患者是否锁定", notes = "验证患者是否锁定")
    @GetMapping("/valid/patient/lock/{empi}")
    public Result valid(@PathVariable() String empi) {
        List<InboundProjectDieaseVo> inboundProjectDieaseVoList = inboundProcessService.getInboundProject(empi, true);
        if (!CollectionUtils.isEmpty(inboundProjectDieaseVoList)) {
            inboundProjectDieaseVoList.forEach(p -> p.setProjectTypeName(ProjectStudyTypeEnum.getByCode(p.getProjectType()).getName()));
        }
        return R.success(inboundProjectDieaseVoList);
    }

    @ApiOperation(value = "获取当前医生参与项目及系统推荐", notes = "获取当前医生参与项目及系统推荐")
    @GetMapping({"/user/subProject/list/{userId}","/user/subProject/list/{userId}/{empi}"})
    public Result myproject(@PathVariable() Long userId, @PathVariable(required = false) String empi) {
        return R.success(hisService.myproject(userId, empi));
    }

    @ApiOperation(value = "获取当前医生所属科室的病种及项目", notes = "获取当前医生所属科室的项目及系统推荐")
    @GetMapping("/dept/subProject/list/{userId}/{hisDeptCode}")
    public Result deptproject(@PathVariable() Long userId, @PathVariable() String hisDeptCode) {
        return R.success(diseaseService.getDieaseByDept(hisService.getDeptList(hisDeptCode, userId)));
    }

    @ApiOperation(value = "获取当前医生所属科室的项目及系统推荐", notes = "获取当前医生所属科室的项目及系统推荐")
    @GetMapping({"/dept/subProject/list/recommend/{userId}/{hisDeptCode}","/dept/subProject/list/recommend/{userId}/{hisDeptCode}/{empi}"})
    public Result deptProjectRecommend(@PathVariable() Long userId, @PathVariable(required = false) String empi, @PathVariable() String hisDeptCode) {
        return R.success(hisService.deptproject(hisService.getDeptList(hisDeptCode, userId), empi));
    }

    @ApiOperation(
            value = "获取患者empiid的就诊信息列表",
            notes = "获取患者empiid的就诊信息列表"
    )
    @GetMapping("/pat/visit/list/{empi}")
    public Result getAdmInfo(@PathVariable String empi) {
        try {
            RdrResponseModel info = keLinService.getAdmInfo(empi);
            return R.success(info.getData());
        } catch (Exception ex) {
            log.error("获取指就诊信息失败：{}", ex.getMessage());
            return R.fail("获取指就诊信息失败");
        }
    }

    @ApiOperation(
            value = "获取指定登记号的基本信息",
            notes = "获取指定登记号的基本信息"
    )
    @GetMapping("/pat/{regNo}/{deptCode}")
    public Result getPatientInfo(@PathVariable String regNo, @PathVariable String deptCode) {
        try {
            RdrResponseModel info = keLinService.getPatientInfo(regNo, deptCode);
            return R.success(info.getData());
        } catch (Exception ex) {
            log.error("获取指定登记号的基本信息失败：{}", ex.getMessage());
            return R.fail("获取指定登记号的基本信息失败");
        }
    }

    @ApiOperation(
            value = "查询患者是否被推荐-第三方",
            notes = "查询患者是否被推荐-第三方")
    @PostMapping("/recommend/pat/{foreignId}/{medCode}/{deptCode}")
    public Result getRecommendByHisNo(@PathVariable String foreignId,
                                      @PathVariable String medCode,
                                      @PathVariable String deptCode,
                                      @RequestBody Map<String, Object> params) {
        // 获取用户信息
        SysUserEntity user = hisService.getUserByForeignId(foreignId, medCode);
        if (ObjectUtils.isEmpty(user)) {
            log.error("获取用户信息失败");
            return R.fail("获取用户信息失败");
        }
        List<Map<String, String>> regNoList = MapUtils.getValue(params, "regNoList", ArrayList.class);
        // HIS登陆科室
        Long deptId = sysDeptService.getIdByCode(deptCode);
        return R.success(hisService.isRecommend(user, deptId, regNoList));
    }

    @ApiOperation(value = "受试者信息列表", notes = "受试者信息列表")
    @GetMapping("/pat/list")
    public Result list(@RequestParam Map<String, Object> params) {
        params.put("hisUserId", getUserId());
        return R.success(patientService.hisList(params));
    }

}
