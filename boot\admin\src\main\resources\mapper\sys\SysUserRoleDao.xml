<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysUserRoleDao">
    <cache-ref namespace="com.boot.modules.sys.dao.SysUserRoleDao"/>
    <delete id="deleteBatch">
        delete from rp_sys_user_role where role_id in
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>
    <select id="getByQuery" resultType="com.boot.modules.sys.entity.SysUserRoleEntity">

        select a.*,
               b.username  AS userName,
               c.role_name AS roleName
        from rp_sys_user_role a
                 left join rp_sys_user b on
            b.id = a.user_id
                 left join rp_sys_role c on
            c.id = a.role_id
            ${ew.customSqlSegment}


    </select>
</mapper>