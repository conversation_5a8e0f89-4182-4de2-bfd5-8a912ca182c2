package com.boot.modules.mobile.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.constants.Const;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.*;
import com.boot.modules.mobile.dao.PushHytDao;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.mobile.dto.PushHytDto;
import com.boot.modules.mobile.entity.PushHytEntity;
import com.boot.modules.mobile.model.CardInfoModel;
import com.boot.modules.mobile.service.PushHytService;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.boot.commons.utils.StringUtils.getBirthDayFromIdCard;

@Service
@Slf4j
public class PushHytServiceImpl extends ServiceImpl<PushHytDao, PushHytEntity> implements PushHytService {

    @Resource
    private ProjectService projectService;

    @Resource
    private SysDeptService deptService;

    @Resource
    private SysUserService sysUserService;

    @Value("${hyt.url:https://hytapi.cd120.com/new-pre-release/netInquiry/patient/api_path/}")
    private String hytPathwayUrl;

    private static final String SAVE_URL = "savePathwayPatient";
    private static final String EXIT_URL = "withdrawPathway";


    /**
     * 调用华医通接口
     *
     */
    @Override
    @Async
    public void savePathwayPatient(PushHytDto pushHytDto, MobilePatientDTO mobilePatientDTO) {
        // 记录推送华医通记录表
        String execTime = DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
        pushHytDto.setPatName(mobilePatientDTO.getName());
        pushHytDto.setGender(mobilePatientDTO.getGender().equals(0) ? "男" : mobilePatientDTO.getGender().equals(1) ? "女" : "不详");
        List<CardInfoModel> cardInfos = new ArrayList<>();
        CardInfoModel cardInfo = new CardInfoModel();
        cardInfo.setCardType(getCardTypeStr(1));
        cardInfo.setCardNo(mobilePatientDTO.getIdCard());
        cardInfos.add(cardInfo);
        pushHytDto.setCardInfo(JSON.toJSONString(cardInfos));
        pushHytDto.setPhone(mobilePatientDTO.getPhone());
        pushHytDto.setNation(mobilePatientDTO.getNation());
        pushHytDto.setMedCode(mobilePatientDTO.getMedCode());
        pushHytDto.setJoinDatetime(execTime);
        pushHytDto.setRecommendTime(execTime);
        pushHytDto.setRequestTime(execTime);
        pushHytDto.setEmpi(mobilePatientDTO.getEmpi());
        String birthday = "";
        try {
            birthday = getBirthDayFromIdCard(mobilePatientDTO.getIdCard());
        } catch (Exception e) {
            throw new BusinessException("身份证获取失败");
        }
        pushHytDto.setBirthday(birthday);
        pushHytDto.setType(1);
        pushHytDto.setStatus(-1);


        PushHytEntity pushHytEntity = new PushHytEntity();
        BeanUtils.copyProperties(pushHytDto, pushHytEntity);
        Boolean res = this.save(pushHytEntity);

        String url = hytPathwayUrl + SAVE_URL;
        // 发送接口
        pushHyt(pushHytDto, url, Arrays.asList(pushHytEntity.getId()));

    }

    @Override
    public PushHytDto valid(MobilePatientDTO mobilePatientDTO, Long userId, Long projectId) {
        if (userId == null) {
            throw new BusinessException("用户为空错误");
        }
        if (projectId == null) {
            throw new BusinessException("项目为空错误");
        }
        if (mobilePatientDTO.getMedId() == null) {
            throw new BusinessException("机构为空错误");
        }
        PushHytDto pushHytDto = new PushHytDto();
        // 查用户信息
        SysUserEntity sysUserEntity = sysUserService.getById(userId);
        pushHytDto.setUserId(userId);
        if (ObjectUtils.isEmpty(sysUserEntity)) {
            pushHytDto.setMessage("用户信息错误");
            PushHytEntity pushHytEntity = new PushHytEntity();
            BeanUtils.copyProperties(pushHytDto, pushHytEntity);
            this.save(pushHytEntity);
            throw new BusinessException("用户信息错误");
        } else {
            pushHytDto.setDoctorName(sysUserEntity.getNickname());
            pushHytDto.setDoctorCode(sysUserEntity.getForeignId());
        }

        // 查项目信息
        ProjectEntity projectEntity = projectService.getById(projectId);
        pushHytDto.setProjectId(projectId);
        if (ObjectUtils.isEmpty(projectEntity)) {
            pushHytDto.setMessage("项目信息错误");
            PushHytEntity pushHytEntity = new PushHytEntity();
            BeanUtils.copyProperties(pushHytDto, pushHytEntity);
            this.save(pushHytEntity);
            throw new BusinessException("项目信息错误");
        } else {
            pushHytDto.setProjectId(projectEntity.getId());
            pushHytDto.setProjectName(projectEntity.getProjectName());
        }

        //查机构信息
        SysDeptEntity deptDto = deptService.getById(mobilePatientDTO.getMedId());
        pushHytDto.setMedId(mobilePatientDTO.getMedId());
        if (ObjectUtils.isEmpty(deptDto)) {
            pushHytDto.setMessage("未找到指定ID的医疗机构，ID为" + mobilePatientDTO.getMedId());
            PushHytEntity pushHytEntity = new PushHytEntity();
            BeanUtils.copyProperties(pushHytDto, pushHytEntity);
            this.save(pushHytEntity);
            throw new BusinessException("未找到指定ID的医疗机构，ID为" + mobilePatientDTO.getMedId());
        } else {
            pushHytDto.setMedName(deptDto.getName());
        }
        return pushHytDto;
    }

    @Override
    public PageUtils queryPage(Map<String, Object> param) {
        //默认字段排序
        String sort = param.get(Const.ORDER_FIELD) == null ? null :
                param.get(Const.ORDER_FIELD).toString();
        if (StringUtils.isEmpty(sort)) {
            param.put(Const.ORDER_FIELD, "a.request_time");
            param.put(Const.ORDER, "desc");
        }
        Long projectId = MapUtils.getValue(param, "projectId", Long.class);
        if (projectId == null) {
            throw new BusinessException("项目ID不能为空");
        }
        QueryWrapper<PushHytDto> qw = new QueryWrapper<>();
        qw.eq("a.project_id", projectId);

        String name = MapUtils.getValue(param, "name", String.class);
        qw.like(StringUtils.isNotBlank(name), "b.name", name);

        IPage<PushHytDto> pageFilter = new Query<PushHytDto>().getPage(param);
        IPage<PushHytDto> page = this.getBaseMapper().getPage(pageFilter, qw);

        List<PushHytDto> list = page.getRecords();
        if (!CollectionUtils.isEmpty(list)) {
            //转换性别
            for (PushHytDto dto : list) {
                dto.setGender(SexMapUtil.getRpSexStr(dto.getGender()));
            }
        }
        page.setRecords(list);


        return new PageUtils(page);
    }

    @Override
    public void again(Long id) {
        //查询记录
        PushHytDto pushHytDto = this.getBaseMapper().getPatientById(id);
        String url = "";
        if (pushHytDto == null || pushHytDto.getType() == null) {
            throw new BusinessException("记录错误，记录为空或者类型为空");
        }
        if (pushHytDto.getType().equals(1)) {
            url = hytPathwayUrl + SAVE_URL;
            String gender = StringUtils.isEmpty(pushHytDto.getGender()) ? "不详" : pushHytDto.getGender().equals("1") ? "男" : pushHytDto.getGender().equals("2") ? "女" : "不详";
            pushHytDto.setGender(gender);
        } else if (pushHytDto.getType().equals(2)) {
            url = hytPathwayUrl + EXIT_URL;
        }
        pushHytDto.setRequestTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        pushHytDto.setStatus(-1);
        pushHytDto.setMessage("");
        pushHyt(pushHytDto, url, Arrays.asList(pushHytDto.getId()));
    }


    /**
     * @param pushHytDto
     * @param url
     */
    private void pushHyt(PushHytDto pushHytDto, String url, List<Long> ids) {
        log.info("推送华医通，url：" + url);
        log.info("推送华医通，body：" + pushHytDto);
        Integer status = 0;
        String message = "";
        try {
            String result = HttpClientUtil.doPostJson(url, JSON.toJSONString(pushHytDto));
            if(StringUtils.isEmpty(result)){
                throw new BusinessException("请求华医通地址失败");
            }
            Map<String, Object> resultMap = JSONObject.parseObject(result, Map.class);
            status = resultMap.containsKey("code") && resultMap.get("code").equals("1") ? 1 : 0;
            message = resultMap.containsKey("msg") ? resultMap.get("msg").toString() : resultMap.containsKey("message") ? resultMap.get("message").toString() : "";
        } catch (Exception ex) {
            status = 0;
            message = ex.getMessage();
        } finally {
            // 更新状态
            UpdateWrapper<PushHytEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().set(PushHytEntity::getStatus, status);
            updateWrapper.lambda().set(PushHytEntity::getMessage, message)
                            .set(StringUtils.isNotBlank(pushHytDto.getRequestTime()),
                                    PushHytEntity::getRequestTime, pushHytDto.getRequestTime());
            updateWrapper.lambda().in(PushHytEntity::getId, ids);
            this.update(updateWrapper);
        }
    }

    private String getCardTypeStr(Integer type) {
        String typeStr = "";
        switch (type) {
            case 1:
                typeStr = "01";
                break;
            default:
                typeStr = "01";
                break;
        }
        return typeStr;
    }
}
