/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package com.boot.commons.config;

import com.boot.commons.model.BootAdminProperties;
import com.boot.interceptor.AuthorizationInterceptor;
import com.boot.interceptor.IpInterceptor;
import com.boot.resolver.LoginUserHandlerMethodArgumentResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;

/**
 * WebMvc配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {


    @Resource
    private AuthorizationInterceptor authorizationInterceptor;

    @Resource
    private LoginUserHandlerMethodArgumentResolver loginUserHandlerMethodArgumentResolver;

    @Bean
    public HandlerInterceptor getIpInterceptor() {
        return new IpInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authorizationInterceptor).addPathPatterns("/app/**");

        if (BootAdminProperties.ipInterceptor) {
            // IP的拦截器,白名单过滤
            registry.addInterceptor(getIpInterceptor()).addPathPatterns("/**");

        }

    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(loginUserHandlerMethodArgumentResolver);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加文档对应的映射
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/", "classpath:/public/");

    }
}