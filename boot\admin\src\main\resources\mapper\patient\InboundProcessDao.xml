<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.patient.dao.InboundProcessDao">

    <select id="getEmpi" resultType="String">
        select distinct (a.empiid)
        from rp_inbound_process a
        left join rp_patient b on a.empiid =b.empiid
        where b.status = 1 and (a.status = 2 or a.status = 3)
    </select>

    <select id="getEmpiByProjectId" resultType="String">
        select distinct (empiid)
        from rp_inbound_process
        where (status = 2 or status = 3) and sub_project_id = #{subProjectId}
    </select>

    <select id="groupByDoctor" resultType="com.boot.modules.patient.dto.PersonalReportDto">
        select
            temp.doctorId as doctorId,
            temp.projectId as projectId,
            temp.intoGroup as intoGroup,
            b.project_name as projectName,
            c.nickname as doctor<PERSON><PERSON>,
            d.name as department
        from
            (SELECT
                COUNT(DISTINCT(empiid)) intoGroup,
                project_id projectId,
                signing_audit_id doctorId
            FROM
                rp_inbound_process
            ${ew.customSqlSegment}
            GROUP BY
                signing_audit_id, project_id) temp
        join
            rp_project b
        on
            temp.projectId = b.id
        join
        	rp_sys_user c
        on
            c.id = temp.doctorId
        join
            rp_sys_dept d
        on
            d.id = c.dept_id
    </select>

    <select id="groupByDept" resultType="com.boot.modules.patient.dto.DepartmentReportDto">
        SELECT
            COUNT(DISTINCT(temp.empiid)) AS intoGroup,
            temp.deptId AS deptId
        FROM
            (SELECT
                a.empiid, a.status,
                b.main_dept_id AS deptId
            FROM
                rp_inbound_process a
            JOIN
                `rp_project` b
            ON
                a.project_id = b.id
            ${ew.customSqlSegment}) temp
        GROUP BY
            temp.deptId
    </select>

    <select id="getInboundProject" resultType="com.boot.modules.patient.vo.InboundProjectDieaseVo">
        SELECT a.*,rp.project_name,rp.project_type ,rsd.disease_name,d.status as patientStatus,rsu.nickname as pi  FROM rp_inbound_process a
        left join rp_patient d on a.empiid = d.empiid
        left join rp_project rp on a.project_id = rp.id
        left join rp_sys_user rsu on rp.project_admin_id =rsu.id
        left join rp_sys_disease rsd on rp.disease_id = rsd .id
        ${ew.customSqlSegment}
    </select>

    <select id="groupDept" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.id) totalInbound,
            c.dept_id
        FROM
            rp_inbound_process a
        JOIN
            rp_project b
        ON
            a.project_id = b.id and a.status in (3,5)
        join
            rp_project_dept c
        ON
            c.project_id = b.id
        ${ew.customSqlSegment}
        GROUP BY
            c.dept_id
    </select>

    <select id="totalInbound" resultType="Integer" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.id) totalInbound
        FROM
            rp_inbound_process a
        JOIN
            rp_project b
        on
            a.project_id = b.id  and a.status in (3,5)
        ${ew.customSqlSegment}
    </select>

    <select id="icoTotalInbound" resultType="Integer" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.id) totalInbound
        FROM
            rp_inbound_process a
        JOIN
            rp_project b
        on
            a.project_id = b.id and a.status in (3,5)
        join rp_doctor_recommendation c on c.id = a.doctor_recommend_id
        join rp_sys_dept d on c.top_recommend_dept_id = d.id
        ${ew.customSqlSegment}
    </select>

    <select id="groupPer" resultType="com.boot.modules.patient.dto.ReportDto" useCache="false">
        SELECT
            COUNT(distinct a.empiid,b.id) totalInbound,
            b.project_admin_id doctorId,
            b.main_dept_id deptId
        FROM
            rp_inbound_process a
        JOIN
            rp_project b
        ON
            a.project_id = b.id and a.status in (3,5)
        ${ew.customSqlSegment}
        GROUP BY
            b.project_admin_id,b.main_dept_id
    </select>

    <select id="groupProject" resultType="com.boot.modules.project.vo.ProjectVo">
        SELECT
            COUNT(distinct a.empiid) inboundCount,
            b.id
        FROM
            rp_inbound_process a
        JOIN
            rp_project b
        ON
            a.project_id = b.id and a.status in (3,5)
        ${ew.customSqlSegment}
        GROUP BY
            b.id
    </select>

    <select id="groupProjectType" resultType="com.boot.modules.project.vo.TotalProjectVo">
        SELECT
            COUNT(distinct a.empiid,b.id) inbound,
            b.project_type
        FROM
            rp_inbound_process a
        JOIN
            rp_project b
        ON
            a.project_id = b.id and a.status in (3,5)
        ${ew.customSqlSegment}
        GROUP BY
            b.project_type
    </select>

    <select id="getInboundPatient" resultType="com.boot.modules.patient.vo.InboundPatientVo">
        SELECT
            a.*,
            b.id_card as idCard
        FROM
            rp_inbound_process a
        LEFT JOIN
            rp_patient b
        ON
            a.empiid = b.empiid
        ${ew.customSqlSegment}
    </select>

    <select id="groupIcoInbound" resultType="com.boot.modules.patient.dto.IcoReportDto" useCache="false">
        SELECT
            COUNT(distinct b.empiid, a.id) totalInbound,
            b.top_recommend_dept_id as medId,
            d.name medName
        FROM
            rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_inbound_process c on b.id = c.doctor_recommend_id and c.status in (3,5)
        join rp_sys_dept d on b.top_recommend_dept_id = d.id
        ${ew.customSqlSegment}
        GROUP BY
            b.top_recommend_dept_id
        order by totalInbound desc
    </select>

    <select id="groupPIInbound" resultType="com.boot.modules.patient.dto.PIReportDto" useCache="false">
        SELECT
            COUNT(distinct b.empiid, a.id) totalInbound,
            a.id projectId,
            a.project_name projectName
        FROM
            rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_inbound_process c on b.id = c.doctor_recommend_id and c.status in (3,5)
        ${ew.customSqlSegment}
        GROUP BY
            a.id
    </select>


    <select id="groupInbProject" resultType="com.boot.modules.mobile.dto.ProjectReportDto" useCache="false">
        SELECT
            COUNT(distinct b.empiid, a.id) inboundCount,
            a.id projectId
        FROM
            rp_project a
        inner join rp_doctor_recommendation b on a.id = b.project_id
        join rp_inbound_process c on b.id = c.doctor_recommend_id and c.status in (3,5)
        join rp_sys_dept d on b.top_recommend_dept_id = d.id
        ${ew.customSqlSegment}
        GROUP BY
            a.id
    </select>
</mapper>