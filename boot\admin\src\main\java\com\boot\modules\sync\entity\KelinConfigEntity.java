package com.boot.modules.sync.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("rp_kelin_config")
public class KelinConfigEntity {
    /**
     * 项目id
     */
    @TableId
    private Long id;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 子项目ID
     */
    private Long subProjectId;
    /**
     * 科林数据集id
     */
    private Long diseaseId;
    /**
     * 科林保存任务id
     */
    private Long expressionId;
    /**
     * 科林检索id
     */
    private String requestId;
    /**
     * 纳排入组配置序号
     */
    private Long groupConfigId;
    /**
     * 导出任务id(用于增量)
     */
    private Long exportId;

    /**
     * 验证表达式创建时间
     */
    private String solrCreateTime;

    /**
     * 任务创建时间
     */
    private String createTime;

    /**
     * 检索-表达式开始时间
     */
    private String solrQureStartTime;

    /**
     * 检索-表达式截止时间
     */
    private String solrQureEndTime;

    /**
     * 状态 1-正常 2-异常
     */
    private Integer status;

    /**
     * 柯林返回状态
     */
    private String responseStatus;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 命中总记录数
     */
    private Long totalRecord;
    /**
     * 通知柯林状态 0-未通知 1-已通知
     */
    private Integer noticeStatus;
    /**
     * 检索开始时间
     */
    private String klStartTime;

    /**
     * 检索截止时间
     */
    private String klEndTime;
}
