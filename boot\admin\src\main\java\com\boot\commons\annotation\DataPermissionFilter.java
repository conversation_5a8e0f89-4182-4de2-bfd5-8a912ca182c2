package com.boot.commons.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @desc : 项目数据权限过滤器 注解
 * @create 2021-06-21
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermissionFilter {

    String tableAlias() default "";

    String subProjectIdStr() default "sub_project_id";

    /**
     * 入组用户
     *
     * @return
     */
    String intoUserId() default "into_user_id";

    /**
     * 病例来源
     */
    String sourceDept() default "source_dept";

}
