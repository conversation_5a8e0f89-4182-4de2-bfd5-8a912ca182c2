package com.boot.interceptor;

import com.boot.commons.enums.ResponseStatusEnum;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.exception.LoginException;
import com.boot.commons.utils.IPUtils;
import com.boot.modules.sys.service.SysIpFilterService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * IP拦截器
 * <AUTHOR>
 */
public class IpInterceptor implements HandlerInterceptor {
    private static final Logger LOG = LoggerFactory.getLogger(IpInterceptor.class.getName());

    @Resource
    private SysIpFilterService ipFilterService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //过滤ip,若用户在白名单内，则放行
        String ipAddress = IPUtils.getIpAddr(request);
        LOG.info("USER IP ADDRESS IS =>" + ipAddress);
        if (!StringUtils.isNotBlank(ipAddress)) {
            return false;
        }

        // 排除掉本机访问
//        if("127.0.0.1".equals(ipAddress) || "0:0:0:0:0:0:0:1".equals(ipAddress)){
//            return true;
//        }

        boolean isWitieList = ipFilterService.isWhiteList(ipAddress);


        if (!isWitieList) {
            LOG.info("ip:" + ipAddress + " not white list;");
            throw new LoginException(ResponseStatusEnum.IP_NOT_IN_WHITELIST,"访问IP不在白名单中，请联系系统管理员处理");
        }

        return true;
    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
