package com.boot.modules.sys.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ExportUserDto implements Serializable {
//    private static final long serialVersionUID = 1L;

    @ExcelProperty(index = 0, value = {"用户名"})
    private String userName;

    @ExcelProperty(index = 1, value = {"昵称"})
    private String nickName;

    @ExcelProperty(index = 2, value = {"密码"})
    private String password;

    @ExcelProperty(index = 3, value = {"所属机构ID（多个医疗机构用英文逗号分割）"})
    private String deptId;

    @ExcelProperty(index = 4, value = {"手机号"})
    private String mobile;

    @ExcelProperty(index = 5, value = {"角色ID"})
    private Long roleId;

    @ExcelProperty(index = 6, value = {"过期时间"})
    private String expiredDate;

    @ExcelProperty(index = 7, value = {"HIS工号"})
    private String hisNo;

    @ExcelProperty(index = 8, value = {"导入失败信息"})
    private String errorInfo;


}
