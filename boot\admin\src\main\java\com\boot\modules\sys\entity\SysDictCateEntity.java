package com.boot.modules.sys.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc : 字典分类表实体类
 * @create 2021-02-25
 */
@Data
@TableName("rp_sys_dict_cate")
public class SysDictCateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    @ExcelProperty(index = 0, value = {"id"})
    private Long id;

    /**
     * 字典分类名称
     */
    @NotBlank(message = "字典分类名称不许为空", groups = {AddGroup.class, UpdateGroup.class})
    @ExcelProperty(index = 1, value = {"cate_name"})
    private String cateName;

    /**
     * 字典分类编码
     */
    @NotBlank(message = "字典分类编码不许为空", groups = {AddGroup.class, UpdateGroup.class})
    @ExcelProperty(index = 2, value = {"cate_code"})
    @Pattern(regexp = "^[A-Za-z0-9|_.*+]+$", message = "编码不能含有除了|_.*+之外的特殊字符", groups = {AddGroup.class, UpdateGroup.class})
    private String cateCode;

    /**
     * 字典分类排序
     */
    @ExcelProperty(index = 3, value = "sort")
    private Integer sort;

    @ApiModelProperty(value = "字典项列表", hidden = true)
    @TableField(exist = false)
    private List<SysDictItemEntity> dictItemList;

}
