package com.boot.commons.config;

import com.boot.modules.sys.shiro.OAuth2Filter;
import com.boot.modules.sys.shiro.OAuth2Realm;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Shiro的配置文件
 *
 * <AUTHOR>
 */
@Configuration
public class ShiroConfig {

    @Bean("sessionManager")
    public SessionManager sessionManager(){
        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        sessionManager.setGlobalSessionTimeout(60 * 60 * 1000);
        sessionManager.setDeleteInvalidSessions(true);
        sessionManager.setSessionValidationInterval(60 * 60 * 1000);

        sessionManager.setSessionIdUrlRewritingEnabled(false);

        return sessionManager;
    }

    @Bean("securityManager")
    public SecurityManager securityManager(OAuth2Realm oAuth2Realm) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(oAuth2Realm);
        securityManager.setRememberMeManager(null);
        securityManager.setSessionManager(sessionManager());
        return securityManager;
    }

    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);

        shiroFilter.setLoginUrl("/login.html");
        shiroFilter.setUnauthorizedUrl("/");

        //oauth过滤
        Map<String, Filter> filters = new HashMap<>();
        filters.put("oauth2", new OAuth2Filter());
        shiroFilter.setFilters(filters);

        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/swagger/**", "anon");
        filterMap.put("/v2/api-docs", "anon");
        filterMap.put("/swagger-ui.html", "anon");
        filterMap.put("/doc.html", "anon");
        filterMap.put("/modeler.html", "anon");
        filterMap.put("/editor-app/**", "anon");
        filterMap.put("/diagram-viewer/**", "anon");
        filterMap.put("/amodeler.html", "anon");
        filterMap.put("/activiti/**", "anon");
        filterMap.put("/editor/**", "anon");
        filterMap.put("/webjars/**", "anon");
        filterMap.put("/swagger-resources/**", "anon");
        //获取加后的密码
        filterMap.put("/sys/encrypted/**", "anon");
        //邮箱找回密码
        filterMap.put("/sys/verifyCode", "anon");
        filterMap.put("/sys/change", "anon");
        filterMap.put("/sys/verification", "anon");
        // 外部接口
        filterMap.put("/outinterface/category/**", "anon");
        filterMap.put("/external/data/**", "anon");
        //用户推送
        filterMap.put("/cas/**", "anon");

        filterMap.put("/sample/search/project", "anon");
        filterMap.put("/sample/sync/**", "anon");

        filterMap.put("/out/interface/**", "anon");
        //单点登录相关接口权限放开
        filterMap.put("/sso/**", "anon");

        filterMap.put("/static/**", "anon");
        filterMap.put("/modules/**", "anon");
        filterMap.put("/formMaker/**", "anon");
        filterMap.put("/backup/**", "perms[\"user:add:*,user:modify:*\"]");
        // 前端静态文件过滤
        filterMap.put("/css/**", "anon");
        filterMap.put("/js/**", "anon");
        filterMap.put("/fonts/**", "anon");
        filterMap.put("/img/**", "anon");
        filterMap.put("/public/**", "anon");
        filterMap.put("/login.html", "anon");
        filterMap.put("/sys/login", "anon");
        filterMap.put("/sys/loginafter", "anon");
        filterMap.put("/sys/logout", "anon");
        filterMap.put("/websocket/**", "anon");
        filterMap.put("/favicon.ico", "anon");
        filterMap.put("/captcha.jpg", "anon");
        filterMap.put("/files/**", "anon");

        // his调用接口
        filterMap.put("/his/**", "anon");

        // 测试临时放开
        filterMap.put("/proj/expression/**", "anon");
        filterMap.put("/pat/**", "anon");
        filterMap.put("/report/**", "anon");
        filterMap.put("/test/**", "anon");
        filterMap.put("/keLin/search/**", "anon");
        filterMap.put("/open/api/edc/log/callback", "anon");
        filterMap.put("/open/api/ico/**", "anon");

        filterMap.put("/**", "oauth2");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        return shiroFilter;
    }

    @Bean("lifecycleBeanPostProcessor")
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }
}
