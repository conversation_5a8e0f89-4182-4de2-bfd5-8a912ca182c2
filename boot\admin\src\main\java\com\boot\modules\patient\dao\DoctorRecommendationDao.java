package com.boot.modules.patient.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.mobile.dto.ProjectReportDto;
import com.boot.modules.mobile.model.PercentModel;
import com.boot.modules.patient.dto.*;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.vo.DoctorRecommendPatientVo;
import com.boot.modules.patient.vo.DoctorRecommendProjectDiseaseVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医生推荐dao
 */
@Mapper
@CacheNamespace
public interface DoctorRecommendationDao extends BaseMapper<DoctorRecommendationEntity> {
    List<String> getEmpiBySubProjectId(Long subProjectId);

    List<PersonalReportDto> groupPat(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendationEntity> qw);

    List<DepartmentReportDto> groupByDept(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendationEntity> qw);

    List<ReportDto> groupDept(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    List<ReportDto> groupPer(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    Integer totalRecommend(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    Integer icoTotalRecommend(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    List<DoctorRecommendProjectDiseaseVo> getRecommendProject(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendProjectDiseaseVo> qw);

    IPage<DoctorRecommendProjectDiseaseVo> getRecommendProject(IPage<DoctorRecommendProjectDiseaseVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendProjectDiseaseVo> qw);

    List<DoctorRecommendPatientVo> getRecommendPatient(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendPatientVo> qw);

    List<PercentModel> groupByTopDeptId(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendationEntity> qw);

    List<MedPatientDto> statisticPatientByMed(@Param(Constants.WRAPPER) QueryWrapper<Object> qw);

    Integer icoProjectCount(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendationEntity> qw);

    List<IcoReportDto> groupIcoProject(@Param(Constants.WRAPPER) QueryWrapper<IcoReportDto> qw);

    List<IcoReportDto> groupIcoRecommend(@Param(Constants.WRAPPER) QueryWrapper<IcoReportDto> qw);

    List<PIReportDto> groupPIRecommend(@Param(Constants.WRAPPER) QueryWrapper<PIReportDto> qw);

    List<Long> getProjectIds(@Param(Constants.WRAPPER) QueryWrapper<DoctorRecommendationEntity> qw);

    IPage<ProjectReportDto> groupProject(IPage<ProjectReportDto> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<ProjectReportDto> qw);

    List<ProjectReportDto> groupProject(@Param(Constants.WRAPPER) QueryWrapper<ProjectReportDto> qw);

    List<Long> getRecommendDeptId(@Param(Constants.WRAPPER) LambdaQueryWrapper<DoctorRecommendationEntity> qw);
}
