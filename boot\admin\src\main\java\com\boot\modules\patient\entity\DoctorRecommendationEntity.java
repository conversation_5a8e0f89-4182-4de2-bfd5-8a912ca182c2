package com.boot.modules.patient.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 医生推荐表
 */
@Data
@TableName("rp_doctor_recommendation")
public class DoctorRecommendationEntity implements Serializable {

    @TableId
    private Long id;

    /**
     * '推荐医生id'
     */
    private Long recommendId;

    /**
     * '推荐项目ID'
     */
    private Long projectId;

    /**
     * '推荐子项目ID'
     */
    private Long subProjectId;

    /**
     * '推荐的患者Empi'
     */
    private String empiid;

    /**
     * '推荐理由'
     */
    private String recommendReason;

    /**
     * '推荐时间'
     */
    private String recommendTime;

    /**
     * 推荐类型 0-系统推荐 1-手动推荐 2-移动端推荐
     */
    private Integer type;

    /**
     * 推荐状态 1已推荐 2-拟入组 3-已入组 4-异常退出 5-正常退出 6-推荐超过30天后回退 7-退回
     */
    private Integer status;

    /**
     * 排序
     */
    private int sort;

    /**
     * 推荐用户的医疗机构Id
     */
    private Long recommendDeptId;

    /**
     * 推荐用户的医疗机构的顶级节点ID
     */
    private Long topRecommendDeptId;

    /**
     * 推荐用户的HIS医疗机构ID
     */
    private String recommendHisDeptCode;

    /**
     * 数据推荐来源：1-受试者系统推荐;2-HIS推荐
     */
    private Integer source;

    /**
     * 0未读；1已读
     */
    private int isRead;

    /**
     * his就诊ID
     */
    private String hisAdmId;

    /**
     * 就诊类型
     */
    private String admType;

    /**
     * '退回理由'
     */
    private String refuseReason;

    /**
     * 备注
     */
    private String content;
}
