package com.boot.commons.utils;

import com.boot.commons.constant.HxConst;
//import com.spire.xls.ExcelVersion;
//import com.spire.xls.Workbook;
//import com.spire.xls.Worksheet;
//import com.spire.xls.collections.WorksheetsCollection;
//import com.spire.xls.core.IXLSRange;
//import com.spire.xls.core.spreadsheet.XlsRange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.util.Map;

public class ExcelOpera {

    private static final Logger log = LoggerFactory.getLogger(ExcelOpera.class);

    public static void main(String[] args) {
        String filePath = "/Users/<USER>/document/sourcecode/sourcecode-work/git_code/edc_java/boot/boot-admin/target/export_14_02.xlsx";

        deleteBlankRowsAndColumns(filePath);
    }

    public static void deleteBlankRowsAndColumns(String filePath) {

//        // 复制一个临时文件
//        File source = new File(filePath);
//        String destPath = filePath + "tmp.xlsx";
//        File dest = new File(destPath);
//        try {
//            copyFileUsingChannel(source, dest);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        //加载测试文档
//        Workbook wb = new Workbook();
//        wb.loadFromFile(destPath);
//
//        WorksheetsCollection sheets = wb.getWorksheets();
//
//        for (int s = 0; s < sheets.size(); s++) {
//            Worksheet sheet = wb.getWorksheets().get(s);
//            //遍历所有行
////            for (int i = sheet.getLastRow(); i >= 1; i--) {
////                //判断行是否空白
////                if (sheet.getRows()[i - 1].isBlank()) {
////                    //删除指定行
////                    sheet.deleteRow(i);
////                }
////            }
//
//            //遍历所有列
//            for (int j = sheet.getLastColumn(); j >= 1; j--) {
//                //判断列是否空白
//                XlsRange xlsRange = sheet.getColumns()[j - 1];
//
//                if (isBlank(sheet.getColumns()[j - 1], 1)) {
//                    sheet.deleteColumn(j);
//                }
//            }
//
//        }
//
//        //保存文档
//        wb.saveToFile(filePath, ExcelVersion.Version2007);
//        dest.delete();
    }

    /**
     * 删除Excel中的空列
     * @param filePath Excel文件路径
     * @param columnMap 空列标记Map
     */
    public static void deleteBlankColumn(String filePath, Map<Integer, Boolean> columnMap) {
        if (CollectionUtils.isEmpty(columnMap)) {
            return;
        }

//        Workbook wb = new Workbook();
//        StopWatch stopWatch=new StopWatch();
//
//        //加载文档
//        stopWatch.start(HxConst.LOAD_FILE);
//        wb.loadFromFile(filePath);
//        stopWatch.stop();
//        log.info("删除空列-1加载Excel文件花费:" + stopWatch.getLastTaskTimeMillis() / 1000.0 + "秒");
//
//        //删除空列
//        Worksheet sheets = wb.getWorksheets().get(0);
//        stopWatch.start(HxConst.DELETE_COLUMN);
//        int i = 0;
//        for (Map.Entry<Integer, Boolean> entry : columnMap.entrySet()) {
//            if (entry.getValue()) {
//                //删除某一列之后下标变了
//                sheets.deleteColumn(entry.getKey() - i);
//                i++;
//            }
//        }
//        stopWatch.stop();
//        log.info("删除空列-2删除空列花费:" + stopWatch.getLastTaskTimeMillis() / 1000.0 + "秒");
//
//        //保存文档
//        stopWatch.start(HxConst.SAVE_FILE);
//        wb.saveToFile(filePath, ExcelVersion.Version2007);
//        wb.dispose();
//        stopWatch.stop();
//        log.info("删除空列-3保存文件花费:" + stopWatch.getLastTaskTimeMillis() / 1000.0 + "秒");
//        log.info("删除空列-总耗时:"+stopWatch.getTotalTimeSeconds() + "秒");

    }



//    private static boolean isBlank(XlsRange xlsRange, int headerRowNum) {
//
//        // 判断每一行是否都为空
//        for (int i = 0; i < xlsRange.getLastRow(); i++) {
//            // 跳过表头验证
//            if (i < headerRowNum) {
//                continue;
//            }
//            IXLSRange range = xlsRange.getRows()[i];
//            System.out.println(range.getText());
//            if (!StringUtils.isBlank(range.getText())) {
//                return false;
//            }
//        }
//
//        return true;
//    }

    /**
     * 复制文件
     * @param source
     * @param dest
     * @throws IOException
     */
    private static void copyFileUsingChannel(File source, File dest) throws IOException {
        FileChannel sourceChannel = null;
        FileChannel destChannel = null;
        try {
            sourceChannel = new FileInputStream(source).getChannel();
            destChannel = new FileOutputStream(dest).getChannel();
            destChannel.transferFrom(sourceChannel, 0, sourceChannel.size());
        }finally{
            if (sourceChannel != null) {
                sourceChannel.close();
            }
            if (destChannel != null) {
                destChannel.close();
            }
        }
    }
}
