package com.boot.modules.patient.dto;

import lombok.Data;

import java.util.List;

/**
 * 受试者推荐入参对象
 */
@Data
public class RecommendDto {

    /**
     * 子项目信息数组
     */
    private List<SubProjectInfo> subProjectList;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 推荐医生的his编码
     */
    private String hisDeptCode;

    /**
     * 推荐医生成员管理处所属医疗机构
     */
    private Long recommendDeptId;

    /**
     * his就诊ID
     */
    private String hisAdmId;

    /**
     * 就诊类型
     */
    private String admType;

    /**
     * 备注
     */
    private String content;

    /**
     * 纳排配置ID
     */
    private List<Long> groupConfigIds;

    @Data
    public static class SubProjectInfo {

        /**
         * 推荐项目列表
         */
        private Long projectId;

        /**
         * 推荐项目列表
         */
        private Long subProjectId;

        /**
         * 推荐类型
         */
        private Integer type;

        /**
         * 满足纳排情况排序
         */
        private Integer sort;
    }
}
