package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.sys.entity.SysDeptEntity;

import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 机构部门管理
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysDeptDao extends BaseMapper<SysDeptEntity> {

    List<SysDeptEntity> queryList(Map<String, Object> params);

    /**
     * 查最大排序的数据
     *
     * @return
     */
    Integer queryMaxSort(@Param(Constants.WRAPPER) QueryWrapper<SysDeptEntity> qw);

    List<SysDeptEntity> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysDeptEntity> qw);

    List<SysDeptEntity> selectByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysDeptEntity> qw);

//    List<ReportDto> listCountDisease(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);
//
//    List<ReportDto> listCountProject(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    List<ReportDto> listCount(@Param(Constants.WRAPPER) QueryWrapper<ReportDto> qw);

    Long getIdByCode(String code);
}
