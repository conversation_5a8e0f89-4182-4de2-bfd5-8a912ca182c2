package com.boot.commons.config;

import com.boot.commons.xss.XssFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.annotation.Resource;
import javax.servlet.DispatcherType;

/**
 * Filter配置
 *
 * <AUTHOR>
 */
@Configuration
public class FilterConfig {

    @Resource
    SSOConfigurationProperties ssoConfigurationProperties;

    @Bean
    public FilterRegistrationBean shiroFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new DelegatingFilterProxy("shiroFilter"));
        //该值缺省为false，表示生命周期由SpringApplicationContext管理，设置为true则表示由ServletContainer管理
        registration.addInitParameter("targetFilterLifecycle", "true");
        registration.setEnabled(true);
        registration.setOrder(Integer.MAX_VALUE - 1);
        registration.addUrlPatterns("/*");
        return registration;
    }

    @Bean
    public FilterRegistrationBean xssFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(new XssFilter());
        registration.addUrlPatterns("/*");
        registration.setName("xssFilter");
        registration.setOrder(Integer.MAX_VALUE);
        return registration;
    }

    //@Bean
    //public FilterRegistrationBean filterAuthenticationRegistration() {
    //    FilterRegistrationBean registration = new FilterRegistrationBean();
    //    registration.setFilter(new AuthenticationFilter());
    //    //设定匹配的路径
    //    registration.addUrlPatterns("/*");
    //    HashMap<String, String> initParameters = new HashMap<>(8);
    //    //设置cas server认证Url
    //    initParameters.put("casServerLoginUrl", ssoConfigurationProperties.getServerLoginUrl());
    //    // Cas Client的service参数 (就是访问地址 )
    //    initParameters.put("service", ssoConfigurationProperties.getClientHostUrl() + "?appcode=" + ssoConfigurationProperties.getAppCode());
    //    //service Url编码
    //    initParameters.put("encodeServiceUrl", "false");
    //    //过滤器白名单
    //    initParameters.put("ignorePattern", "/login/*");
    //    registration.setInitParameters(initParameters);
    //    //设定加载顺序
    //    registration.setOrder(1);
    //    return registration;
    //}
    //
    //@Bean
    //public FilterRegistrationBean filterValidationRegistrationBean() {
    //    FilterRegistrationBean registrationBean = new FilterRegistrationBean();
    //    registrationBean.setFilter(new Cas30ProxyReceivingTicketValidationFilter());
    //    //设定匹配路径
    //    registrationBean.addUrlPatterns("/*");
    //    HashMap<String, String> initParameters = new HashMap<>(8);
    //    initParameters.put("casServerUrlPrefix", ssoConfigurationProperties.getServerLoginUrlPrefix());
    //    initParameters.put("service", ssoConfigurationProperties.getClientHostUrl() + "?appcode=" + ssoConfigurationProperties.getAppCode());
    //    initParameters.put("useSession", "true");
    //    initParameters.put("encodeServiceUrl", "false");
    //    registrationBean.setInitParameters(initParameters);
    //    //设定加载顺序
    //    registrationBean.setOrder(2);
    //    return registrationBean;
    //}
    //
    //@Bean
    //public FilterRegistrationBean filterWrapperRegistration() {
    //    FilterRegistrationBean registration = new FilterRegistrationBean();
    //    registration.setFilter(new HttpServletRequestWrapperFilter());
    //    //设定匹配的路径
    //    registration.addUrlPatterns("/*");
    //    registration.setOrder(3);
    //    return registration;
    //}
}
