package com.boot.modules.patient.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boot.commons.annotation.Login;
import com.boot.commons.excel.utils.ExcelUtils;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.mobile.vo.PatientGroupDetailVO;
import com.boot.modules.patient.dto.ExitDto;
import com.boot.modules.patient.dto.RecommendDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.entity.PatImportRecordEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatImportRecordService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.project.service.GroupConfigService;
import com.boot.modules.project.service.PatCustomRecordService;
import com.boot.modules.project.vo.SubProjectVo;
import com.boot.modules.sys.controller.AbstractController;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysUserDeptSpecialEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysUserDeptSpecialService;
import com.boot.modules.sys.vo.DeptVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Api(tags = "受试者管理")
@RestController
@RequestMapping("/pat")
public class PatientController extends AbstractController {
    @Resource
    private GroupConfigService groupConfigService;

    @Resource
    private PatientService patientService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private PatImportRecordService patImportRecordService;

    @Resource
    private SysUserDeptSpecialService sysUserDeptSpecialService;

    @Resource
    private PatCustomRecordService patCustomRecordService;

    private static ExecutorService executorService = Executors.newFixedThreadPool(10);

    @ApiOperation(value = "获取指定病人所有就诊的匹配纳排条件情况", notes = "获取指定病人所有就诊的匹配纳排条件情况")
    @GetMapping("/match/detail/{empi}/{subProjectId}")
    public Result matchDetail(@PathVariable() Long subProjectId, @PathVariable() String empi) {
        // 查病例的redis
        Map<String, List<String>> patientRedis = groupConfigService.getPatientMatchRedis(empi);
        // 查纳排满足情况
        Map<String, List<Long>> groupConfigs = groupConfigService.getIsMatch(subProjectId, patientRedis);
        SubProjectVo subProjectVo = new SubProjectVo();
        subProjectVo.setIsMatched(groupConfigs.size() > 0 ? 1 : 0);
        Integer includeCount = groupConfigService.count(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId)
                .eq(GroupConfigEntity::getType, 0));
        subProjectVo.setIncludeCount(includeCount);
        subProjectVo.setVisitConfigMap(groupConfigs);

        return R.success(subProjectVo);
    }

    @ApiOperation(value = "受试者信息列表", notes = "受试者信息列表")
    @GetMapping("/list")
    public Result list(@RequestParam Map<String, Object> params) {
        Long userId = getUserId();
        params.put("userId", userId);

        if (!userId.equals(0L)) {
            params.put("deptId", getDeptId());
        }
        return R.success(patientService.listBySubProjectId(params));
    }


    @ApiOperation(value = "同步项目病例", notes = "同步项目病例")
    @PostMapping("/sync")
    public Result sync(@RequestBody Map<String, Object> params) {
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        List<String> empiList = params.get("empiList") == null ? new ArrayList<>() : (List<String>) params.get("empiList");
        patientService.sync(projectId, empiList);
        return R.success();
    }


    @ApiOperation(value = "受试者推荐判断", notes = "受试者推荐判断")
    @PostMapping({"/verify/recommend", "/verify/recommend/{empi}"})
    public Result verifyRecommend(@PathVariable(required = false) String empi,
                                  @RequestBody RecommendDto recommendDto) {
        if (StringUtils.isEmpty(empi)) {
            return R.fail("该患者数据暂未同步系统，具体情况请联系信息中心。");
        }
        return R.success(doctorRecommendationService.verify(empi, recommendDto));
    }

    @ApiOperation(value = "受试者推荐", notes = "受试者推荐")
    @PostMapping({"/recommend", "/recommend/{empi}", "/recommend/{empi}/{regno}"})
    public Result recommend(@PathVariable(name = "empi", required = false) String empi,
                            @PathVariable(name = "regno", required = false) String regno,
                            @RequestBody RecommendDto recommendDto) {
        if (StringUtils.isEmpty(empi)) {
            return R.fail("该患者数据暂未同步系统，具体情况请联系信息中心。");
        }
        Long userId = this.getUserId();
        if (!StringUtils.isEmpty(recommendDto.getHisDeptCode())) {
            Long deptId = 0L;
            // 特殊处理-胰腺炎医疗中心门诊医生工作量归入胰腺炎医疗中心
            List<SysUserDeptSpecialEntity> specialEntityList = sysUserDeptSpecialService.list(new QueryWrapper<SysUserDeptSpecialEntity>().lambda()
                    .eq(SysUserDeptSpecialEntity::getUserId, userId)
                    .eq(SysUserDeptSpecialEntity::getHisDeptCode, recommendDto.getHisDeptCode()));
            if (!CollectionUtils.isEmpty(specialEntityList)) {
                deptId = specialEntityList.get(0).getDeptId();
            } else {
                deptId = sysDeptService.getIdByCode(recommendDto.getHisDeptCode());
            }
            recommendDto.setRecommendDeptId(deptId);
        }
        doctorRecommendationService.recommendation(empi, regno, recommendDto, this.getUserId(), null, null);
        return R.success();
    }

    @ApiOperation(value = "入组受试者", notes = "入组受试者")
    @PostMapping("/input/{id}/{empi}/{projectId}/{subProjectId}")
    public Result inbound(@PathVariable Long id,
                          @PathVariable String empi,
                          @PathVariable Long projectId,
                          @PathVariable Long subProjectId) {
        inboundProcessService.inbound(id, empi, projectId, subProjectId, this.getUserId());
        return R.success();
    }

    @ApiOperation(value = "签署知情同意书", notes = "签署知情同意书")
    @PostMapping("/sign/{id}/{empi}/{subProjectId}")
    public Result sign(@PathVariable Long id,
                       @PathVariable String empi,
                       @PathVariable Long subProjectId,
                       @RequestBody Map<String, Object> params) {
        String date = MapUtils.getValue(params, "signingTime", String.class);
        inboundProcessService.sign(id, empi, subProjectId, date, this.getUserId());
        return R.success();
    }

    @ApiOperation(value = "退出项目", notes = "退出项目")
    @PostMapping("/exit")
    public Result exit(@RequestBody ExitDto exitDto) {
        inboundProcessService.exit(exitDto);
        return R.success();
    }

    @ApiOperation(value = "拟入组退回", notes = "拟入组退回")
    @PostMapping("/revert")
    public Result revert(@RequestBody ExitDto exitDto) {
        // 医联体患者到驳回状态，非医联体患者退回到已推荐给我状态
        if (exitDto.getRecommendType().equals(2)) {
            inboundProcessService.refuse(exitDto);
        } else {
            inboundProcessService.revert(exitDto);
        }
        return R.success();
    }

    @ApiOperation(
            value = "excel导入受试者",
            notes = "excel导入受试者"
    )
    @Login
    @PostMapping("/excel/import/{projectId}/{subProjectId}")
    public Result importDisease(@RequestParam("file") MultipartFile file,
                                @PathVariable Long projectId,
                                @PathVariable Long subProjectId) {
        if (file == null) {
            throw new BusinessException("上传的文件为空");
        }
        // 记录导入日志
        PatImportRecordEntity patImportRecordEntity = new PatImportRecordEntity();
        patImportRecordEntity.setStartTime(DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN));
        patImportRecordEntity.setStatus(0);
        patImportRecordEntity.setUserId(getUserId());
        patImportRecordEntity.setSubProjectId(subProjectId);
        patImportRecordService.save(patImportRecordEntity);
        executorService.execute(() -> {
            try {
                patientService.excelImport(file, patImportRecordEntity, projectId, subProjectId);
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        return R.success("正在导入中");
    }

    @ApiOperation(value = "导入受试者模板下载", notes = "导入受试者模板下载")
    @GetMapping("/excel/download")
    public Result templateDownload(HttpServletResponse response) throws NoSuchFieldException {
        String fileName = "diseaseTemplate";
        List<List<String>> heads = new ArrayList<>();

        // 设置一级表头信息
        heads.add(Collections.singletonList("登记号"));
        heads.add(Collections.singletonList("姓名"));
        heads.add(Collections.singletonList("身份证号"));
        heads.add(Collections.singletonList("入组时间"));
        heads.add(Collections.singletonList("签约时间"));
        heads.add(Collections.singletonList("医疗机构名称"));
        heads.add(Collections.singletonList("导入失败信息"));

        // 下载用户批量导入excel模板
        Map<String, List<List<String>>> headList = new LinkedHashMap<>();
        headList.put("受试者信息", heads);
        Map<String, List<DeptVo>> dataList = new LinkedHashMap<>();

        //设置机构对照
        List<List<String>> headDepts = new ArrayList<>();

        // 设置一级表头信息
        headDepts.add(Collections.singletonList("医疗机构ID"));
        headDepts.add(Collections.singletonList("医疗机构名称"));
        headDepts.add(Collections.singletonList("医疗机构编码"));

        // 下载用户批量导入excel模板
        headList.put("医疗机构标准名称", headDepts);
        QueryWrapper<SysDeptEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(SysDeptEntity::getPid, 0);
        List<SysDeptEntity> list = sysDeptService.list(qw);
        List<DeptVo> deptVos = new ArrayList<>();
        for (SysDeptEntity sysDeptEntity : list) {
            DeptVo deptVo = new DeptVo();
            deptVo.setDeptId(sysDeptEntity.getId());
            deptVo.setDeptName(sysDeptEntity.getName());
            deptVo.setDeptCode(sysDeptEntity.getCode());
            deptVos.add(deptVo);
        }
        dataList.put("医疗机构标准名称", deptVos);
        ExcelUtils.wirteExcel(response, fileName, headList, dataList);
        return R.success("下载成功");
    }

    @ApiOperation(value = "已读/未读", notes = "已读/未读")
    @PostMapping("/read/{id}/{read}")
    public Result read(@PathVariable Long id, @PathVariable Long read) {
        UpdateWrapper<DoctorRecommendationEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DoctorRecommendationEntity::getId, id)
                .set(DoctorRecommendationEntity::getIsRead, read);
        doctorRecommendationService.update(updateWrapper);
        return R.success();
    }

    @ApiOperation(value = "移动端患者纳排明细情况", notes = "移动端患者纳排明细情况")
    @GetMapping({"/detail"})
    public Result detail(@RequestParam String empi, @RequestParam Long subProjectId) {

        PatientGroupDetailVO vo = patCustomRecordService.getDetail(empi, subProjectId);
        return R.success(vo);
    }
}
