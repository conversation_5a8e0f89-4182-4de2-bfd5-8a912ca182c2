package com.boot.modules.patient.service.Impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boot.commons.constants.Const;
import com.boot.commons.utils.*;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.patient.service.DoctorRecommendationService;
import com.boot.modules.patient.service.InboundProcessService;
import com.boot.modules.patient.service.PatientService;
import com.boot.modules.patient.service.PreRecommendService;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.project.redis.EnrollDetailRedis;
import com.boot.modules.project.service.GroupConfigService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.sys.entity.SysUserEntity;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.boot.modules.project.redis.EnrollDetailRedis.*;

@Service
public class PreRecommendServiceImpl implements PreRecommendService {
    @Resource
    private GroupConfigService groupConfigService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private EnrollDetailRedis enrollDetailRedis;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    @Lazy
    private PatientService patientService;

    @Resource
    private SubProjectService subProjectService;

    /**
     * 获取漏斗条数，type 1-今日，2-全部
     *
     * @param subProjectId
     * @param type
     * @return
     */
    @Override
    public Map<Long, Integer> getCount(Long projectId, Long subProjectId, Integer type, Map<String, Object> params) {
        // 纳入条件
        List<GroupConfigEntity> includeConfig = groupConfigService.list(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId)
                .eq(GroupConfigEntity::getType, 0)).stream().sorted(Comparator.comparing(GroupConfigEntity::getSort)).collect(Collectors.toList());
        // 排除条件
        List<GroupConfigEntity> excludeConfig = groupConfigService.list(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId)
                .eq(GroupConfigEntity::getType, 1)).stream().sorted(Comparator.comparing(GroupConfigEntity::getSort)).collect(Collectors.toList());

        // 获取项目纳排条件
        List<GroupConfigEntity> groupConfigEntityList = new ArrayList<>();
        groupConfigEntityList.addAll(includeConfig);
        groupConfigEntityList.addAll(excludeConfig);

        // 查本项目已推荐
        List<String> doctorRecomEmpiList = doctorRecommendationService.getEmpiBySubProjectId(subProjectId);

        // 非队列项目查拟入组或已入组且未退出
        List<String> inboundEmpiList = inboundProcessService.getEmpi();

        String startDate = MapUtils.getValue(params, Const.START, String.class);
        String endDate = MapUtils.getValue(params, Const.END, String.class);
        List<String> includes = new ArrayList<>();
        List<String> excludes = new ArrayList<>();
        Map<Long, Integer> map = new LinkedHashMap<>();
        for (GroupConfigEntity groupConfigEntity : groupConfigEntityList) {
            //屏蔽医联体条件，避免沙漏导致的交集全为空
            if (groupConfigEntity.getIsYlt() == 1) {
                continue;
            }
            String key = REDIS_CONFIG + groupConfigEntity.getSubProjectId() + EnrollDetailRedis.UNDER_SCORE + groupConfigEntity.getId();
            if (groupConfigEntity.getType() == 0) {
                includes.add(key);
            } else {
                excludes.add(key);
            }
            // 查预推荐empi
            List<String> empiList = getEmpiList(includes, excludes, doctorRecomEmpiList, inboundEmpiList, type, projectId, subProjectId, startDate, endDate);
            // 记录条数
            map.put(groupConfigEntity.getId(), empiList.size());
        }
        return map;
    }

    /**
     * 分页获取推荐病例
     *
     * @param subProjectId
     * @param type
     * @param params
     * @return
     */
    @Override
    public PageUtils getByPage(Long projectId, Long subProjectId, Integer type, Map<String, Object> params) {
        List<String> include = MapUtils.getValue(params, Const.INCLUDE, ArrayList.class);
        List<String> exclude = MapUtils.getValue(params, Const.EXCLUDE, ArrayList.class);
        Integer page = MapUtils.getValue(params, Const.PAGE, Integer.class);
        Integer limit = MapUtils.getValue(params, Const.LIMIT, Integer.class);
        String startDate = MapUtils.getValue(params, Const.START, String.class);
        String endDate = MapUtils.getValue(params, Const.END, String.class);

        // 查本项目已推荐
        List<String> doctorRecomEmpiList = doctorRecommendationService.getEmpiBySubProjectId(subProjectId);

        // 查拟入组未退出
        List<String> inboundEmpiList = inboundProcessService.getEmpi();

        List<String> empiList = getEmpiList(include, exclude, doctorRecomEmpiList, inboundEmpiList, type, projectId, subProjectId, startDate, endDate);
        if (CollectionUtils.isEmpty(empiList)) {
            return new PageUtils(new ArrayList<PatientEntity>(), empiList.size(), limit, page);
        }

        // 分页查患者信息
        List<String> currentPage = empiList.size() < page * limit ? empiList.subList((page - 1) * limit, empiList.size()) : empiList.subList((page - 1) * limit, page * limit);
        List<PatientEntity> patientEntityList = patientService.list(new QueryWrapper<PatientEntity>().lambda().in(PatientEntity::getEmpiid, currentPage));

        PageUtils pageUtils = new PageUtils(patientEntityList, empiList.size(), limit, page);
        return pageUtils;
    }

    /**
     * 查预推荐empi
     *
     * @param includes
     * @param excludes
     * @param doctorRecomEmpiList
     * @param inboundEmpiList
     * @param type
     * @return
     */
    private List<String> getEmpiList(List<String> includes,
                                     List<String> excludes,
                                     List<String> doctorRecomEmpiList,
                                     List<String> inboundEmpiList,
                                     Integer type,
                                     Long projectId,
                                     Long subProjectId,
                                     String startDate,
                                     String endDate) {
        // 查纳入的交集
        List<String> includeEmpiList = enrollDetailRedis.intersect(includes);

        // 查排除的并集
        List<String> excludeEmpiList = enrollDetailRedis.uinon(excludes);

        // 取差集
        List<String> empiList = ListUtils.getDifferenceListThanList(includeEmpiList, excludeEmpiList);
        empiList = ListUtils.getDifferenceListThanList(empiList, doctorRecomEmpiList);
        empiList = ListUtils.getDifferenceListThanList(empiList, inboundEmpiList);

        // 查当前项目下用户
        List<SysUserEntity> sysUserEntityList = subProjectService.getUserBySubProjectId(projectId, subProjectId);
        List<String> userKeys = new ArrayList<>();
        for (SysUserEntity userEntity : sysUserEntityList) {
            String key = REDIS_DOCTOR + userEntity.getNickname();
            userKeys.add(key);
        }
        // 查属于当前项目下用户的患者
        List<String> doctorEmpi = enrollDetailRedis.uinon(userKeys);

        // 取交集
        empiList = ListUtils.getIntersectListThanList(empiList, doctorEmpi);

        // 今日就诊 - 过滤就诊时间为今天的患者
        if (type.equals(1)) {
            // 获取今日就诊的患者
            List<String> todayEmpiList = new ArrayList<>();
            // 方案一：从redis中获取，因每天凌晨执行，可能会遗漏掉当天挂号的患者
            todayEmpiList = enrollDetailRedis.get(REDIS_ONEDAY + DateUtils.getNowTimeStr());
//            // 方案二：从柯林实时取当日就诊，可能慢
//            RdrResponseModel today = keLinService.getTodayEmpiList(Arrays.asList("HID0101"));
//            if (ObjectUtils.isEmpty(today.getData()) || ObjectUtils.isEmpty(today.getData().getList())) {
//            } else {
//                todayEmpiList = today.getData().getList().stream().map(p -> p.get(KELIN_VISIT_TIME_FIELD).toString()).collect(Collectors.toList());
//            }
            // 取交集
            return ListUtils.getIntersectListThanList(empiList, todayEmpiList).stream().sorted().collect(Collectors.toList());
        } else {
            if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) {
                // todo 筛选指定就诊时间内的患者
                List<String> finalEmpiList = new ArrayList<>();
                for (String empi : empiList) {
                    List<String> patAdmList = enrollDetailRedis.get(REDIS_PATIENT + empi);
                    for (String adm : patAdmList) {
                        String admDate = adm.split(UNDER_SCORE).length > 1 ? adm.split(UNDER_SCORE)[1].split(" ")[0] : "";
                        if (!StringUtils.isEmpty(admDate)
                                && DateUtils.compareStrDate(admDate, startDate)
                                && DateUtils.compareStrDate(endDate, admDate)) {
                            finalEmpiList.add(empi);
                            break;
                        }
                    }
                }
                return finalEmpiList.stream().sorted().collect(Collectors.toList());
            }
        }
        return empiList.stream().sorted().collect(Collectors.toList());
    }

    @Override
    public void todayEmpi() {
        // 查匹配key
        List<String> keys = enrollDetailRedis.matchkey(REDIS_PATIENT);
        for (String key : keys) {
            List<String> visitList = enrollDetailRedis.get(key);
            String empi = key.split("_").length > 1 ? key.split("_")[1] : "";
            visitList.forEach(visit -> {
                if (!visit.equals("")
                        && visit.split("_").length > 1
                        && visit.split("_")[1].contains(DateUtils.getNowTimeStr())) {
                    enrollDetailRedis.saveOnedayEmpi(empi);
                }
            });
        }
    }

    /**
     * 判断患者是否符合系统推荐
     *
     * @param subProjectId
     * @param empi
     * @return
     */
    @Override
    public Boolean isMatch(Long projectId, Long subProjectId, String empi) {
        // 纳入必要条件
        List<GroupConfigEntity> includeNessaryConfig = groupConfigService.list(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId)
                .eq(GroupConfigEntity::getType, 0)
                .eq(GroupConfigEntity::getNecessary, 1)).stream().sorted(Comparator.comparing(GroupConfigEntity::getSort)).collect(Collectors.toList());
        // 排除条件
        List<GroupConfigEntity> excludeConfig = groupConfigService.list(new QueryWrapper<GroupConfigEntity>()
                .lambda().eq(GroupConfigEntity::getSubProjectId, subProjectId)
                .eq(GroupConfigEntity::getType, 1)).stream().sorted(Comparator.comparing(GroupConfigEntity::getSort)).collect(Collectors.toList());

        // 获取项目纳排条件
        List<GroupConfigEntity> groupConfigEntityList = new ArrayList<>();
        groupConfigEntityList.addAll(includeNessaryConfig);
        groupConfigEntityList.addAll(excludeConfig);

        List<String> includes = new ArrayList<>();
        List<String> excludes = new ArrayList<>();
        for (GroupConfigEntity groupConfigEntity : groupConfigEntityList) {
            String key = REDIS_CONFIG + groupConfigEntity.getSubProjectId() + EnrollDetailRedis.UNDER_SCORE + groupConfigEntity.getId();
            if (groupConfigEntity.getType() == 0) {
                includes.add(key);
            } else {
                excludes.add(key);
            }

        }
        // 查预推荐empi
        List<String> empiList = getEmpiList(includes, excludes, null, null, 0, projectId, subProjectId, null, null);
        return empiList.contains(empi);
    }
}
