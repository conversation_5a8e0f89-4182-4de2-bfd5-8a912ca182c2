package com.boot.modules.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.commons.validator.group.AddGroup;
import com.boot.commons.validator.group.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@TableName("rp_projrole_setting")
public class ProjectRoleSettingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;
    /**
     * 子项目id
     */
    @NotBlank(message = "项目ID不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long subProjectId;
    /**
     * 项目角色ID
     */
    @NotBlank(message = "角色ID不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private Long roleId;

    /**
     * 数据权限值， 1:全部数据 2. 所在中心 3. 自己录入的
     */
    private Integer dataAuth;

    /**
     * 是否脱敏  0：否    1：是
     */
    private Integer isTuomin;

    @ApiModelProperty(hidden = true)
    @TableField(exist = false)
    private String roleName;
}
