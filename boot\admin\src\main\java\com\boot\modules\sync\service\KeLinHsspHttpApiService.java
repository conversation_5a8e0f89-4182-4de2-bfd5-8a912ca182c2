package com.boot.modules.sync.service;


import com.alibaba.fastjson.JSONObject;
import com.github.lianjiatech.retrofit.spring.boot.annotation.OkHttpClientBuilder;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import okhttp3.OkHttpClient;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.math.BigInteger;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/22 11:48
 */
@RetrofitClient(
        baseUrl = "${boot.external.kelin-hssp-api}",
        poolName = "kelinHsspApi"
)
public interface KeLinHsspHttpApiService {

    @OkHttpClientBuilder
    static OkHttpClient.Builder okhttpClientBuilder() {
        return new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.MINUTES)
                .readTimeout(5, TimeUnit.MINUTES)
                .writeTimeout(5, TimeUnit.MINUTES);
    }

    /**
     * 柯林检索表达式验证接口
     *
//     * @param expression   检索表达式
//     * @param searchByType VKey/Empiid，对应查询数据保存到哪个维度下的数据集
//     * @param platformCode 平台id
     * @return 接口返回的数据
     */
    @POST("qureService/solrQure")
//    Map<String, Object> solrQure(String expression, String searchByType, String platformCode, @Header("hssp-Authorization") String token);
    Map<String, Object> solrQure(@Body JSONObject jsonObject,
                                 @Header("hssp-Authorization") String hsspToken,
                                 @Header("Accept") String accpet,
                                 @Query("jwt")String jwt);
//                                 @Header("User-Agent") String agent, @Header("Cookie")String cookie, @Header("Accept") String accept);

    /**
     * 数据集列表查询接口
     *
     * @param DataType     VKey/Empiid，对应查询数据保存到哪个维度下的数据集
     * @param diseaseName  传空查所有，传值模糊检索对应的数据集名称
     * @param platformCode 平台id
     * @return 接口返回的数据
     */
    @POST("tail/getDiseaseDBNameListLocal")
    Map<String, Object> getDiseaseDBNameListLocal(String DataType, String diseaseName, String platformCode, @Header("hssp-Authorization") String hsspToken, @Query("jwt")String jwt);

    /**
     * 数据集创建接口
     *
//     * @param SubCategoryName 数据集名称
//     * @param platformCode    平台id
//     * @param Expression      检索表达式
//     * @param Description     数据集描述
     * @return 接口返回的数据
     */
    @POST("tail/saveDiseaseDBPlatform")
    Map<String, Object> saveDiseaseDBPlatform(@Query("SubCategoryName") String SubCategoryName, @Query("platformCode") String platformCode, @Query("Expression") String Expression, @Query("Description") String Description,
                                              @Header("hssp-Authorization") String hsspToken,
                                              @Header("Accept") String accpet,
                                              @Query("jwt")String jwt);
//    Map<String, Object> saveDiseaseDBPlatform(@Body JSONObject jsonObject, @Header("hssp-Authorization") String token);

    /**
     * 数据集保存接口
     *
//     * @param dieaseID 数据集ID
//     * @param RequestID    检索返回唯一id
//     * @param diseaseFilterMode      固定值0
//     * @param platformCode     平台ID
     * @return 接口返回的数据
     */
    @POST("tail/saveDiseaseDBLocal")
    Map<String, Object> saveDiseaseDBLocal(@Query("diseaseID") int dieaseID, @Query("RequestID") BigInteger RequestID, @Query("diseaseFilterMode") int diseaseFilterMode, @Query("platformCode") String platformCode,
                                           @Header("hssp-Authorization") String hsspToken,
                                           @Header("Accept") String accpet,
                                           @Query("jwt")String jwt);
//    Map<String, Object> saveDiseaseDBLocal(@Body JSONObject jsonObject, @Header("hssp-Authorization") String token);
    /**
     * 增量任务
     *
//     * @param exportId 导出任务id
//     * @param platformCode    平台id
//     * @param expressionId      检索表达式ID
     * @return 接口返回的数据
     */
    @POST("exportDateWebService/startDiseaseSave")
    Map<String, Object> startDiseaseSave(@Query("exportID") String exportId, @Query("platformCode") String platformCode, @Query("expressionIdList") String expressionId,
                                         @Header("hssp-Authorization") String hsspToken,
                                         @Header("Accept") String accpet,
                                         @Query("jwt")String jwt);
//    Map<String, Object> startDiseaseSave(@Body JSONObject jsonObject ,
//                                         @Header("hssp-Authorization") String hsspToken,
//                                         @Header("Accept") String accpet);

}
