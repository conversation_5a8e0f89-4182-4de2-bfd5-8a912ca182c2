package com.boot.modules.sys.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.sys.entity.SysMenuEntity;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单管理
 *
 * <AUTHOR>
 */
@Mapper
@CacheNamespace
public interface SysMenuDao extends BaseMapper<SysMenuEntity> {


    /**
     * 查询用户的所有权限
     *
     * @param userId 用户ID
     */
    List<String> queryAllPerms(long userId);

    /**
     * 查询用户的所有权限
     *
     * @param userId 用户ID
     * @param type 内外网
     */
    List<String> queryAllPermsByType(long userId, long type);

    /**
     * 根据角色id 查询菜单列表
     *
     * @param qw
     * @return
     */
    List<SysMenuEntity> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<SysMenuEntity> qw);
}
