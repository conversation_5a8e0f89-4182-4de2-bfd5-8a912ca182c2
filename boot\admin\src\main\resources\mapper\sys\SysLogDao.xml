<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.boot.modules.sys.dao.SysLogDao">
    <cache-ref namespace="com.boot.modules.sys.dao.SysLogDao"/>
    <!--删除30天前的水质数据-->
    <delete id="deleteBeforeDays" >
        delete from rp_sys_log where create_date &lt; #{deleteTimeStr};
    </delete>
</mapper>