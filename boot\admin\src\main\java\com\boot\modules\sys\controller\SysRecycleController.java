package com.boot.modules.sys.controller;

import com.boot.commons.annotation.SysLog;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.sys.dao.SysRecycleDao;
import com.boot.modules.sys.service.SysRecycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :回收站管理接口
 * @create 2021-04-16
 */
@Api(tags = "回收站管理")
@RestController
@RequestMapping("/sys/recycle")
public class SysRecycleController extends AbstractController {

    @Resource
    private SysRecycleService sysRecycleService;

    /**
     * 查询回收站信息
     */
    @ApiOperation(value = "查询回收站信息", notes = "查询回收站信息")
    @RequiresPermissions("sys:recycle:list")
    @GetMapping("/list")
    public Result list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysRecycleService.list(params);
        return R.success(page);
    }


    /**
     * 恢复数据，可选基于sql语句恢复数据方法名为：recover
     * 或通过反射方法恢复数据，方法名为：recoverByReflect
     */
    @ApiOperation(value = "恢复数据", notes = "恢复数据")
    @SysLog("恢复数据")
    @RequiresPermissions("sys:recycle:recover")
    @GetMapping("/recover")
    public Result recover(@RequestParam Map<String, Object> params) {
        boolean res = false;
        res = sysRecycleService.recoverByReflect(params);
        return res ? R.success("恢复成功") : R.fail("恢复失败");
    }

    /**
     * 删除回收站信息
     */
    @ApiOperation(value = "删除回收站信息", notes = "删除回收站信息")
    @SysLog("删除项目")
    @RequiresPermissions("sys:recycle:delete")
    @DeleteMapping()
    public Result delete(@RequestBody Long[] ids) {
        boolean res = sysRecycleService.delete(ids);
        return res ? R.success("删除成功") : R.fail("删除失败");
    }

}

