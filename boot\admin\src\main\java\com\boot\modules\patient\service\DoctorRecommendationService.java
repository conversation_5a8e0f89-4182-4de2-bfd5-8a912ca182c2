package com.boot.modules.patient.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.commons.utils.PageUtils;
import com.boot.modules.mobile.dto.MobilePatientDTO;
import com.boot.modules.patient.dto.PersonalReportDto;
import com.boot.modules.patient.dto.RecommendDto;
import com.boot.modules.patient.dto.ReportDto;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.vo.DoctorRecommendProjectDiseaseVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DoctorRecommendationService extends IService<DoctorRecommendationEntity> {
    /**
     * 验证是否可推荐
     * @param empi
     * @param recommendDto
     */
    Map<String,Object> verify(String empi, RecommendDto recommendDto);

    /**
     * 受试者推荐
     * @param empi
     * @param recommendDto
     */
    void recommendation(String empi, String regno, RecommendDto recommendDto,
                        Long userId, Integer type, String nation);


    /**
     * 受试者推荐通过移动端推荐
     */
    String recommendationByMobile(MobilePatientDTO mobilePatientDTO, Long userId, Integer type);

    /**
     * 查本项目已推荐
     * @param subProjectId
     * @return
     */
    List<String> getEmpiBySubProjectId(Long subProjectId);

    /**
     * 按月份统计医生推荐量
     * @param date
     * @return
     */
    List<PersonalReportDto> personalRecommendData(String date);
//
//    /**
//     * 科室推荐的患者量接口
//     * @param date
//     * @return
//     */
//    List<DepartmentReportDto> departmentRecommendData(String date);

    /**
     * 设置科室相关推荐数
     * @param result
     * @param startTime
     * @param endTime
     */
    void setRecommendCount(List<ReportDto> result, Date startTime, Date endTime);

    /**
     * 设置医生相关推荐数
     * @param result
     * @param startTime
     * @param endTime
     */
    void setPerRecommendCount(List<ReportDto> result, Date startTime, Date endTime);

//    /**
//     * 设置departmentReportDtoList中结构相关参数
//     * @param departmentReportDtoList
//     */
//    void setProperties(List<DepartmentReportDto> departmentReportDtoList);

    void totalRecommend(ReportDto reportDto, Date startTime, Date endTime);

    Boolean hasRecommend(String empi, Long subProjectId);

    List<DoctorRecommendProjectDiseaseVo> getRecommendProject(String empi);

    /**
     * 通过条件查询记录
     * @param param
     * @return
     */
    PageUtils getByQuery(Map<String, Object> param);

    /**
     * 通过条件查询记录
     * @param param
     * @return
     */
    List<DoctorRecommendProjectDiseaseVo> getListByQuery(Map<String, Object> param);
}
