<?xml version="1.0" encoding="UTF-8"?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">    
<mapper namespace="com.boot.modules.sys.dao.SysConfigDao">
	<cache-ref namespace="com.boot.modules.sys.dao.SysConfigDao"/>
	<!-- 根据key，更新value -->
	<update id="updateValueByKey" parameterType="map">
		update rp_sys_config set param_value = #{paramValue} where param_key = #{paramKey}
	</update>

	<!-- 根据key，查询value -->
	<select id="queryByKey" parameterType="string" resultType="com.boot.modules.sys.entity.SysConfigEntity">
		select * from rp_sys_config where param_key = #{paramKey}
	</select>
	
</mapper>