package com.boot.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.commons.exception.BusinessException;
import com.boot.commons.utils.MapUtils;
import com.boot.commons.utils.PageUtils;
import com.boot.commons.utils.Query;
import com.boot.commons.utils.StringUtils;
import com.boot.modules.sys.dao.SysUserHisDao;
import com.boot.modules.sys.entity.SysUserEntity;
import com.boot.modules.sys.entity.SysUserHisEntity;
import com.boot.modules.sys.service.SysUserHisService;
import com.boot.modules.sys.vo.UserHisForeignVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc :
 * @create 2022-05-05
 */
@Service
public class SysUserHisServiceImpl extends ServiceImpl<SysUserHisDao, SysUserHisEntity> implements SysUserHisService {
    /**
     * 分页获取
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        String userName = MapUtils.getValue(params, "username", String.class);

        QueryWrapper<UserHisForeignVo> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(userName), "b.nickname", userName);
        IPage<UserHisForeignVo> pageFilter = new Query<UserHisForeignVo>().getPage(params);
        IPage<UserHisForeignVo> page = baseMapper.queryPage(pageFilter, qw);

        return new PageUtils(page);
    }

    @Override
    public List<UserHisForeignVo> selectByQuery(Map<String, Object> params) {
        Long userId = MapUtils.getValue(params, "userId", Long.class);
        String userName = MapUtils.getValue(params, "username", String.class);

        QueryWrapper<UserHisForeignVo> qw = new QueryWrapper<>();
        qw.eq(userId != null, "a.user_id", userId);
        qw.like(StringUtils.isNotBlank(userName), "b.nickname", userName);


        return baseMapper.selectByQuery(qw);
    }

    @Override
    public List<UserHisForeignVo> selectByQuery(Long userId) {

        QueryWrapper<UserHisForeignVo> qw = new QueryWrapper<>();
        qw.eq(userId != null, "a.user_id", userId);

        return baseMapper.selectByQuery(qw);
    }

    @Override
    public UserHisForeignVo selectById(Long id) {
        QueryWrapper<UserHisForeignVo> qw = new QueryWrapper<>();
        qw.eq("a.id", id);

        List<UserHisForeignVo> list = baseMapper.selectByQuery(qw);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<UserHisForeignVo> selectByQuery(Long userId,String deptCode) {
        QueryWrapper<UserHisForeignVo> qw = new QueryWrapper<>();
        qw.eq(userId != null, "a.user_id", userId);
        qw.eq("c.code", deptCode);
        return baseMapper.selectByQuery(qw);
    }

    @Override
    public List<SysUserEntity> getByForeignAndDept(String foreignId, String deptCode) {
        QueryWrapper<SysUserEntity> qw = new QueryWrapper<>();
        qw.eq("a.his_foreign_id", foreignId);
        qw.eq("c.code", deptCode);

        return baseMapper.getByForeignAndDept(qw);
    }

    @Override
    public boolean add(SysUserHisEntity entity) {

        //唯一性验证，一个用户在一个机构中，只能有一个his工号
        int count = this.count(
                new QueryWrapper<SysUserHisEntity>()
                        .lambda().eq(SysUserHisEntity::getUserId, entity.getUserId())
                        .eq(SysUserHisEntity::getDeptId, entity.getUserId())
                        .eq(SysUserHisEntity::getHisForeignId, entity.getHisForeignId())
        );
        if (count > 0) {
            throw new BusinessException("当前用户在当前机构中已存在His工号");
        }

        return this.add(entity);
    }

    @Override
    public boolean updateUserHis(SysUserHisEntity entity) {
        //唯一性验证，一个用户在一个机构中，只能有一个his工号
        int count = this.count(
                new QueryWrapper<SysUserHisEntity>()
                        .lambda()
                        .ne(SysUserHisEntity::getId, entity.getId())
                        .eq(SysUserHisEntity::getUserId, entity.getUserId())
                        .eq(SysUserHisEntity::getDeptId, entity.getUserId())
                        .eq(SysUserHisEntity::getHisForeignId, entity.getHisForeignId())
        );
        int count2 = this.count(
                new QueryWrapper<SysUserHisEntity>()
                        .lambda()
                        .ne(SysUserHisEntity::getId, entity.getId())
                        .eq(SysUserHisEntity::getDeptId, entity.getUserId())
                        .eq(SysUserHisEntity::getHisForeignId, entity.getHisForeignId())
        );
        if (count > 0 || count2 > 0) {
            throw new BusinessException("当前his工号已存在");
        }
        return this.updateById(entity);
    }
}
