package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.vo.ProjDeptVo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @desc :  项目dept
 * @create 2021-05-20
 */
@Mapper
@CacheNamespace
public interface ProjDeptDao extends BaseMapper<ProjDeptEntity> {
    List<ProjDeptVo> getByQuery(@Param(Constants.WRAPPER) QueryWrapper<ProjDeptEntity> qw);

    List<ProjDeptEntity> getBySubProjectId(@Param(Constants.WRAPPER) QueryWrapper<ProjDeptEntity> qw);
}
