/*
 添加基础数据
 research_participant 版本：V1.0.0

 Date: 15/05/2023 10:38:24
*/

/*Data for the table `rp_sys_dict_cate` */

insert  into `rp_sys_dict_cate`(`id`,`cate_name`,`cate_code`,`sort`) values
(1,'性别','gender',1),
(6,'项目级别','projectLevel',4),
(7,'项目状态','projectStatus',5),
(9,'用户状态','userStatus',7),
(10,'职能级别','roleLevel',8),
(30,'项目类型','projectType',17),
(36,'时间范围','timeRange',23),
(43,'是否是本院病例','isHisCaseEnum',18),
(44,'华西审核状态','hxAuditStatus',0);

/*Data for the table `rp_sys_role` */

insert  into `rp_sys_role`(`id`,`role_name`,`remark`,`create_time`,`role_level`,`index_url`,`is_sys_admin`,`is_proj_admin`,`scope`,`is_input`) values 
(1,'系统管理员','系统管理员','2021-02-09 22:42:02',1,'/project/project-list',1,0,'',0),
(7,'临管部管理者','临管部管理者','2021-03-29 16:18:23',1,'/user-center',0,0,'',0),
(13,'PI','Identity_PI','2023-05-16 10:40:27',2,'',0,1,'1,2',0),
(14,'SubI','Identity_PIASSISTANT','2023-05-16 10:40:36',2,'',0,0,'1,2',0),
(15,'受试者平台用户','','2023-05-18 15:40:05',1,'/user-center',0,0,'',0),
(16,'分中心PI','','2023-05-19 09:12:50',2,'',0,0,'1,2',0),
(17,'科室管理员','科室管理员','2021-02-09 22:42:02',1,'sys/statistical-report-dept',1,0,'',0);

/*Data for the table `rp_sys_role_menu` */
INSERT INTO `rp_sys_role_menu` VALUES
(2518,13,451,1),
(2519,13,452,1),
(2520,13,453,1),
(2521,13,454,1),
(2522,15,451,1),
(2523,15,452,1),
(2524,15,453,1),
(2525,15,454,1),
(2526,16,451,1),
(2527,16,453,1),
(2528,14,451,1),
(2536,1,59,1),
(2537,1,1,1),
(2538,1,58,1),
(2539,1,76,1),
(2540,1,77,1),
(2541,1,78,1),
(2542,1,79,1),
(2543,1,80,1),
(2544,1,31,1),
(2545,1,32,1),
(2546,1,33,1),
(2547,1,34,1),
(2548,1,35,1),
(2549,1,208,1),
(2550,1,3,1),
(2551,1,19,1),
(2552,1,20,1),
(2553,1,21,1),
(2554,1,22,1),
(2555,1,198,1),
(2556,1,200,1),
(2557,1,15,1),
(2558,1,2,1),
(2559,1,16,1),
(2560,1,17,1),
(2561,1,18,1),
(2562,1,211,1),
(2563,1,351,1),
(2564,1,232,1),
(2565,1,414,1),
(2566,1,23,1),
(2567,1,4,1),
(2568,1,24,1),
(2569,1,25,1),
(2570,1,26,1),
(2571,1,314,1),
(2572,1,315,1),
(2573,1,212,1),
(2574,1,214,1),
(2575,1,36,1),
(2576,1,215,1),
(2577,1,216,1),
(2578,1,217,1),
(2579,1,218,1),
(2580,1,37,1),
(2581,1,38,1),
(2582,1,39,1),
(2583,1,40,1),
(2584,1,213,1),
(2585,1,240,1),
(2586,1,67,1),
(2587,1,219,1),
(2588,1,238,1),
(2589,1,239,1),
(2590,1,220,1),
(2591,1,68,1),
(2592,1,221,1),
(2593,1,81,1),
(2594,1,74,1),
(2595,1,82,1),
(2596,1,83,1),
(2597,1,84,1),
(2598,1,103,1),
(2599,1,327,1),
(2600,1,145,1),
(2601,1,241,1),
(2602,1,242,1),
(2603,1,243,1),
(2604,1,244,1),
(2605,1,260,1),
(2606,1,63,1),
(2607,1,261,1),
(2608,1,262,1),
(2609,1,263,1),
(2610,1,64,1),
(2611,1,264,1),
(2612,1,265,1),
(2613,1,266,1),
(2614,1,65,1),
(2615,1,267,1),
(2616,1,268,1),
(2617,1,172,1),
(2618,1,75,1),
(2619,1,173,1),
(2620,1,174,1),
(2621,1,271,1),
(2622,1,60,1),
(2623,1,77,2),
(2624,1,76,2),
(2625,1,78,2),
(2626,1,79,2),
(2627,1,80,2),
(2628,1,32,2),
(2629,1,31,2),
(2630,1,33,2),
(2631,1,34,2),
(2632,1,35,2),
(2633,1,208,2),
(2634,1,19,2),
(2635,1,3,2),
(2636,1,20,2),
(2637,1,21,2),
(2638,1,22,2),
(2639,1,198,2),
(2640,1,200,2),
(2641,1,15,2),
(2642,1,2,2),
(2643,1,16,2),
(2644,1,17,2),
(2645,1,18,2),
(2646,1,211,2),
(2647,1,351,2),
(2648,1,232,2),
(2649,1,414,2),
(2650,1,23,2),
(2651,1,4,2),
(2652,1,24,2),
(2653,1,25,2),
(2654,1,26,2),
(2655,1,314,2),
(2656,1,315,2),
(2657,1,212,2),
(2658,1,214,2),
(2659,1,36,2),
(2660,1,215,2),
(2661,1,216,2),
(2662,1,217,2),
(2663,1,218,2),
(2664,1,37,2),
(2665,1,38,2),
(2666,1,39,2),
(2667,1,40,2),
(2668,1,213,2),
(2669,1,240,2),
(2670,1,67,2),
(2671,1,219,2),
(2672,1,238,2),
(2673,1,239,2),
(2674,1,220,2),
(2675,1,68,2)
,(2676,1,221,2),
(2677,1,81,2),
(2678,1,74,2),
(2679,1,82,2),
(2680,1,83,2),
(2681,1,84,2),
(2682,1,103,2),
(2683,1,327,2),
(2684,1,145,2),
(2685,1,241,2),
(2686,1,242,2),
(2687,1,243,2),
(2688,1,244,2),
(2689,1,260,2),
(2690,1,63,2),
(2691,1,261,2),
(2692,1,262,2),
(2693,1,263,2),
(2694,1,64,2),
(2695,1,264,2),
(2696,1,265,2),
(2697,1,266,2),
(2698,1,65,2),
(2699,1,267,2),
(2700,1,268,2),
(2701,1,172,2),
(2702,1,75,2),
(2703,1,173,2),
(2704,1,174,2),
(2705,1,271,2)
,(2706,1,60,2),
(2707,1,59,2),
(2708,1,1,2),
(2709,1,58,2),
(2714,15,74,1),
(2715,15,81,1),
(2716,15,74,2),
(2717,15,81,2),
(2718,15,457,1),
(2720,13,457,1),
(2721,14,457,1),
(2722,16,457,1),
(2726,15,458,1),
(2727,15,459,1),
(2728,15,461,1),
(2729,15,460,1),
(2730,13,458,1),
(2731,13,459,1),
(2732,13,461,1),
(2733,13,460,1),
(2734,16,458,1),
(2735,16,459,1),
(2736,16,461,1),
(2737,16,460,1),
(2738,15,463,1),
(2739,15,462,1),
(2740,13,463,1),
(2741,13,462,1),
(2742,16,462,1),
(2743,16,463,1),
(2744,15,464,1),
(2745,13,464,1);


/*Data for the table `rp_sys_dict_item` */

insert  into `rp_sys_dict_item`(`id`,`name`,`code`,`dict_cate_id`,`value`,`sort`,`remark`,`del_flag`,`pinyin`,`first_letter`) values
(1,'男','gender1',1,'1',1,'',0,'nan','n'),
(2,'女','gender2',1,'2',0,'备注1',0,'nv','n'),
(5,'进行中','exportStatus0',4,'0',0,'',-1,'jinxingzhong','jxz'),
(6,'成功','exportStatus1',4,'1',1,'',-1,'chenggong','cg'),
(7,'失败','exportStatus2',4,'2',2,'',-1,'shibai','sb'),
(8,'待审核','auditStatus0',5,'0',0,'',-1,'daishenhe','dsh'),
(9,'不通过','auditStatus2',5,'2',1,'',-1,'butongguo','btg'),
(10,'通过','auditStatus1',5,'1',2,'',-1,'tongguo','tg'),
(11,'院级','level1',6,'1',0,'',0,'yuanji','yj'),
(12,'科室级','level2',6,'2',1,'',0,'keshiji','ksj'),
(13,'新建','projectStatus0',7,'0',0,'',0,'xinjian','xj'),
(14,'启用','projectStatus1',7,'1',1,'',0,'qiyong','qy'),
(15,'未发布','formStatus0',8,'0',0,'',-1,'weifabu','wfb'),
(16,'已发布','formStatus1',8,'1',1,'',-1,'yifabu','yfb'),
(17,'停用','userStatus0',9,'0',1,'',0,'tingyong','ty'),
(18,'正常','userStatus1',9,'1',0,'',0,'zhengchang','zc'),
(19,'系统级','roleLevel1',10,'1',0,'',0,'xitongji','xtj'),
(20,'项目级','roleLevel2',10,'2',1,'',0,'xiangmuji','xmj'),
(23,'单病种','projectType1',30,'1',0,'',0,'danbingzhong','dbz'),
(24,'多病种','projectType2',30,'2',1,'',0,'duobingzhong','dbz'),
(25,'随机分组','projectType3',30,'3',0,'',-1,'suijifenzu','sjfz'),
(26,'无计划','noPlan',31,'1',0,'',-1,'wujihua','wjh'),
(27,'自定义','zidingyi',31,'2',1,'',-1,'zidingyi','zdy'),
(28,'基线','baseLine',32,'1',0,'',-1,'jixian','jx'),
(29,'随访','followUp',32,'2',1,'',-1,'suifang','sf'),
(30,'入组时间','joinDate',33,'1',0,'',-1,'ruzushijian','rzsj'),
(31,'手术时间','operationDate',33,'2',1,'',-1,'shoushushijian','sssj'),
(32,'指定时间','designateDate',33,'3',2,'',-1,'zhidingshijian','zdsj'),
(33,'相对时间','relativeTime',34,'1',0,'',-1,'xiangduishijian','xdsj'),
(34,'指定时间','absoluteTime',34,'2',1,'',-1,'zhidingshijian','zdsj'),
(35,'入组时间','relativeTimeJoin',35,'1',0,'',-1,'ruzushijian','rzsj'),
(36,'手术时间','relativeTimeOperation',35,'2',1,'',-1,'shoushushijian','sssj'),
(37,'上次计划访视时间','relativeTimeLastPlan',35,'3',2,'',-1,'shangcijihuafangshishijian','scjhfssj'),
(38,'上次实际访视时间','relativeTimeLastReal',35,'4',3,'',-1,'shangcishijifangshishijian','scsjfssj'),
(39,'年','year',36,'1',0,'',0,'nian','n'),
(40,'月','month',36,'2',1,'',0,'yue','y'),
(41,'日','day',36,'3',3,'',0,'ri','r'),
(42,'周','week',36,'4',2,'',0,'zhou','z'),
(43,'停用','projectStatus2',7,'2',2,'',0,'tingyong','ty'),
(51,'带瘤生存','dlsc',42,'1',0,'',-1,'dailiushengcun','dlsc'),
(52,'无瘤生存','wlsc',42,'2',0,'',-1,'wuliushengcun','wlsc'),
(53,'本院病例','1',43,'1',0,'',0,'benyuanbingli','bybl'),
(54,'外院病例','0',43,'0',1,'',0,'waiyuanbingli','wybl'),
(55,'待认证','hxAuditStatus0',44,'0',1,'',0,'drz','d'),
(56,'认证通过，申请单待提交','hxAuditStatus1',44,'1',2,'',0,'rztg','r'),
(57,'认证不通过','hxAuditStatus2',44,'2',3,'',0,'rzbtg','r'),
(59,'申请单待审核','hxAuditStatus3',44,'3',4,'',0,'sqddsh','s'),
(60,'申请单审核通过','hxAuditStatus4',44,'4',5,'',0,'sqdshtg','s'),
(61,'申请单审核不通过','hxAuditStatus5',44,'5',6,'',0,'sqdshbtg','s'),
(62,'文件待审核','hxAuditStatus6',44,'6',7,'',0,'wjdsh','w'),
(63,'文件审核通过','hxAuditStatus7',44,'7',8,'',0,'wjshtg','w'),
(64,'文件审核不通过','hxAuditStatus8',44,'8',9,'',0,'wjshbtg','w');

/*Data for the table `rp_sys_application` */

insert  into `rp_sys_application`(`id`,`app_name`,`app_code`,`is_default`,`app_desc`,`external`,`sort`,`is_project_app`,`external_link`,`is_new_windows_open`,`home_router`) values
(1,'后台系统配置','EDC',1,NULL,0,1,0,NULL,NULL,NULL),
(3,'受试者入组平台','rp',0,NULL,1,3,1,'user-center',0,NULL);

/*Data for the table `rp_sys_menu` */

INSERT INTO `rp_sys_menu` VALUES
(1,0,'系统管理',NULL,'',0,'sys-manage',1,1,1),
(2,1,'用户管理','sys/user','',0,'icon-user',4,1,1),
(3,1,'角色管理','sys/role','',0,'icon-team',3,1,1),
(4,1,'菜单管理','sys/menu','',0,'icon-unorderedlist',5,1,1),
(9,6,'修改',NULL,'sys:schedule:update',1,NULL,0,1,1),
(10,6,'删除',NULL,'sys:schedule:delete',1,NULL,0,1,1),
(11,6,'暂停',NULL,'sys:schedule:pause',1,NULL,0,1,1),
(12,6,'恢复',NULL,'sys:schedule:resume',1,NULL,0,1,1),
(13,6,'立即执行',NULL,'sys:schedule:run',1,NULL,0,1,1),
(14,6,'日志列表',NULL,'sys:schedule:log',1,NULL,0,1,1),
(15,2,'查看用户列表',NULL,'sys:user:list,sys:user:info',1,NULL,0,1,1),
(16,2,'新增用户',NULL,'sys:user:save',1,NULL,0,1,1),
(17,2,'修改用户',NULL,'sys:user:update',1,NULL,0,1,1),
(18,2,'删除用户',NULL,'sys:user:delete',1,NULL,0,1,1),
(19,3,'查看角色列表',NULL,'sys:role:list,sys:role:info,sys:role:select',1,NULL,0,1,1),
(20,3,'新增角色',NULL,'sys:role:save',1,NULL,0,1,1),
(21,3,'修改角色',NULL,'sys:role:update',1,NULL,0,1,1),
(22,3,'删除角色',NULL,'sys:role:delete',1,NULL,0,1,1),
(23,4,'查看菜单列表',NULL,'sys:menu:list,sys:menu:info,sys:menu:projnav',1,NULL,0,1,1),
(24,4,'新增菜单',NULL,'sys:menu:save',1,NULL,0,1,1),
(25,4,'修改菜单',NULL,'sys:menu:update',1,NULL,0,1,1),
(26,4,'删除菜单',NULL,'sys:menu:delete',1,NULL,0,1,1),
(31,1,'机构管理','sys/dept','',0,'icon-apartment',2,1,1),
(32,31,'查看机构列表',NULL,'sys:dept:list,sys:dept:info,sys:dept:select',1,NULL,0,1,1),
(33,31,'新增机构',NULL,'sys:dept:save',1,NULL,0,1,1),
(34,31,'修改机构',NULL,'sys:dept:update',1,NULL,0,1,1),
(35,31,'删除机构',NULL,'sys:dept:delete',1,NULL,0,1,1),
(36,1,'字典管理','sys/dict','',0,'dict',6,1,1),
(37,36,'查看字典分类列表',NULL,'sys:dictitem:list,sys:dictitem:info,sys:dictitem:code',1,NULL,6,1,1),
(38,36,'字典分类新增',NULL,'sys:dictcate:save',1,NULL,6,1,1),
(39,36,'字典分类修改',NULL,'sys:dictcate:update',1,NULL,6,1,1),
(40,36,'字典分类删除',NULL,'sys:dictcate:delete',1,NULL,6,1,1),
(48,0,'权限管理','','',0,'premission',2,1,1),
(58,1,'系统设置','sys/sys-setting','',0,'icon-setting',0,0,1),
(59,0,'首页','/index','',0,'icon-project',0,1,1),
(60,107,'系统信息','sys/system-info','',0,'icon-dashboard',3,1,1),
(61,0,'日志管理','','',0,'log-4',7,1,1),
(63,61,'操作日志','sys/log-operation','',0,'icon-check-circle',1,1,1),
(64,61,'登录日志','sys/log-login','',0,'icon-up-circle',2,1,1),
(65,61,'错误日志','sys/log-error','',0,'icon-issuesclose',4,1,1),
(67,48,'功能权限','sys/permission-func','',0,'premission-2',1,1,1),
(68,48,'数据权限','sys/permission-data','',0,'data-source-manage',2,1,1),
(73,0,'项目管理','','',0,'icon-container',3,1,1),
(74,73,'项目列表','project/project-list','',0,'project-list',1,1,1),
(75,107,'回收中心','sys/recycle-center','',0,'icon-rest',2,1,1),
(76,1,'应用管理','sys/application','',0,'project-manage',1,1,1),
(77,76,'获取应用列表','','sys:app:list,sys:app:info',1,'',1,1,1),
(78,76,'保存应用','','sys:app:save',1,'',1,1,1),
(79,76,'修改应用','','sys:app:update',1,'',2,1,1),
(80,76,'删除应用','','sys:app:delete',1,'',4,1,1),
(81,74,'查看项目列表','','proj:project:list,proj:project:info',1,'',0,1,1),
(82,74,'新增项目','','proj:project:save',1,'',0,1,1),
(83,74,'修改项目','','proj:project:update',1,'',0,1,1),
(84,74,'删除项目','','proj:project:delete',1,'',0,1,1),
(103,74,'设置项目用户','','proj:user:save,sys:app:update,sys:app:delete',1,'',0,1,1),
(107,0,'系统运维','','',0,'info-watch',20,1,1),
(145,74,'查询子项目','','proj:subproject:list,proj:subproject:info',1,'',1,1,1),
(172,75,'删除回收站数据','','sys:recycle:delete',1,'',0,1,1),
(173,75,'恢复数据','','sys:recycle:recover',1,'',0,1,1),
(174,75,'查看回收站信息','','sys:recycle:list',1,'',0,1,1),
(198,3,'返回后台','','sys:back-to-index',1,'',0,1,1),
(200,3,'返回个人中心','','sys:back-to-user-index',1,'',0,1,1),
(208,31,'选择上级机构','','sys:dept:parentid',1,'',4,1,1),
(211,2,'用户excel导入',NULL,'sys:user:excelimport',1,NULL,0,1,1),
(212,4,'修改菜单应用',NULL,'sys:menu:updateapp',1,NULL,0,1,1),
(213,36,'字典分类excel导入',NULL,'sys:dictcate:excelimport',1,NULL,6,1,1),
(214,36,'查询字典项列表',NULL,'sys:dictcate:list,sys:dictcate:info',1,NULL,0,1,1),
(215,36,'字典项新增',NULL,'sys:dictitem:save',1,NULL,0,1,1),
(216,36,'字典项修改',NULL,'sys:dictitem:update',1,NULL,0,1,1),
(217,36,'字典项删除',NULL,'sys:dictitem:delete',1,NULL,0,1,1),
(218,36,'字典项excel导入',NULL,'sys:dictitem:excelimport',1,NULL,0,1,1),
(219,67,'保存角色菜单权限',NULL,'sys:perm:operation',1,NULL,1,1,1),
(220,68,'保存角色数据权限',NULL,'sys:perm:data',1,NULL,0,1,1),
(221,68,'获取角色数据权限',NULL,'sys:perm:roledata',1,NULL,0,1,1),
(232,2,'查看全部用户',NULL,'sys:user:all',1,NULL,1,1,1),
(238,67,'获取角色操作权限','','sys:perm:role',1,'',1,1,1),
(239,67,'获取患者隐私数据可选',NULL,'sys:perm:patient',1,NULL,1,1,1),
(240,67,'获取全部权限注解',NULL,'sys:perm:all',1,NULL,0,1,1),
(241,74,'新增子项目','','proj:subproject:save',1,'',1,1,1),
(242,74,'修改子项目','','proj:subproject:update',1,'',1,1,1),
(243,74,'删除子项目','','proj:subproject:delete',1,'',1,1,1),
(244,74,'项目纳排查询','','proj:form:searchforpatient',1,'',1,1,1),
(260,63,'查看操作日志','','sys:log:list',1,'',0,1,1),
(261,63,'导出操作日志','','sys:log:export',1,'',0,1,1),
(262,63,'删除操作日志','','sys:log:delete',1,'',0,1,1),
(263,64,'查看登录日志','','sys:log:list',1,'',0,1,1),
(264,64,'导出登录日志','','sys:log:export',1,'',0,1,1),
(265,64,'删除登录日志','','sys:log:delete',1,'',0,1,1),
(266,65,'查案错误日志','','sys:log:list',1,'',0,1,1),
(267,65,'导出错误日志','','sys:log:export',1,'',0,1,1),
(268,65,'删除错误日志','','sys:log:delete',1,'',0,1,1),
(271,60,'获取系统服务器信息','','sys:system:info',1,'',0,1,1),
(314,4,'获取全部的菜单','','sys:menu:nav',1,'',0,1,1),
(315,4,'获取用户权限',NULL,'sys:menu:userperm',1,NULL,0,1,1),
(327,74,'获取全部项目','','proj:project:all',1,'',1,1,1),
(351,2,'修改用户密码','','sys:user:updatePwd',1,'',1,1,1),
(414,2,'用户导入模板下载','','sys:user:exceldownload',1,'',2,1,1),
(434,48,'用户权限管理','sys/permission-user','',0,'icon-safetycertificate',1,1,1),
(451,0,'受试者列表','project/project-dashboard','',0,'csm-shujuluru',0,1,3),
(452,0,'纳排条件','project/in-out-condition','',0,'csm-tiaojianchaxun',1,1,3),
(453,0,'成员分配','project/peoject-member','',0,'icon-addteam',3,1,3),
(454,0,'数据权限管理','project/data-permission','',0,'icon-fileprotect',4,1,3),
(455,73,'病种列表','sys/disease-list','',0,'csm-gongzuoliugongzuoliuguanli',2,1,1),
(457,451,'获取用户所属中心科室信息','','proj:user:info',1,'',0,1,3),
(458,453,'查看项目成员列表','','proj:user:info,proj:user:list',1,'',0,1,3),
(459,453,'修改项目成员','','proj:user:update',1,'',0,1,3),
(460,453,'新增项目成员','','proj:user:savebatch,proj:user:save',1,'',0,1,3),
(461,453,'删除项目成员','','proj:user:delete',1,'',0,1,3),
(462,453,'指定范围的角色列表','','sys:role:select',1,'',0,1,3),
(463,453,'获取系统机构列表','','sys:dept:list',1,'',0,1,3),
(464,454,'获取指定项目的角色配置','','project:setting:list',1,'',0,1,3),
(465,0,'统计报表','sys/statistical-report','',0,'icon-linechart',11,1,1),
(466,0,'统计报表','sys/statistical-report-dept','',0,'icon-linechart',12,1,1);


-- ----------------------------
-- Records of rp_rdr_table
-- ----------------------------
INSERT INTO `rp_rdr_table` VALUES (12, '报告', 'report', 'ordreport', 'mt_report_main', NULL, 1, 2, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (13, '手麻手术事件', 'operation', 'op', 'opsn_opsncolumn', NULL, 0, 14, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (14, '检验', 'lisitem', 'lis', 'report_labmeasuresub', NULL, 1, 2, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (16, '病人', 'patient', 'pat', 'pers_basicinfo', NULL, 1, 0, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (17, '就诊', 'adm', 'adm', 'visit_info', NULL, 1, 1, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (18, '超声', 'ordris', 'or', 'report_exammeasuresub', NULL, 1, 2, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (19, '医嘱', 'orderitem', 'ord', 'order_order', NULL, 1, 2, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (20, '诊断', 'diagnose', 'diag', 'diaginfo', NULL, 1, 2, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (21, '护理病历', 'nursingdet', 'nursingdet', 'case_nursingdet', NULL, 1, 2, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (24, '病案首页', 'medicalrecordhomepage', 'mrhp', 'mr_mrfp', NULL, 0, 10, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (26, '电子病历', 'emr', 'emr', 'case_cdadet', NULL, 1, 2, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (27, '检验-新', 'lisitemnew', 'lisnew', 'report_labmeasuresub', NULL, 0, 20, 1, 0);
INSERT INTO `rp_rdr_table` VALUES (30, '手术记录', 'ssjl', 'ssjl', 'rdr_solr_ssjl', NULL, 1, -1, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (32, '病案首页', 'basy', 'basy', 'rdr_solr_basy', NULL, 1, -1, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (33, '出院记录', 'cyjl', 'cyjl', 'rdr_solr_cyjl', NULL, 1, -1, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (34, '入院记录', 'ryjl', 'ryjl', 'rdr_solr_ryjl', NULL, 1, -1, 0, 0);
INSERT INTO `rp_rdr_table` VALUES (35, '医院质量监测表', 'TB_HQMS', 'TB_HQMS', 'TB_HQMS', NULL, 1, -1, 0, 0);



-- ----------------------------
-- Records of rp_rdr_field
-- ----------------------------
INSERT INTO `rp_rdr_field` VALUES (490, 16, '患者唯一ID', 'empiid', '1', NULL, 'int', NULL, 'pers_basicinfo_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (491, 13, '手术ID', 'op_id', '0', NULL, 'varchar', NULL, 'operateid', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (492, 17, '患者唯一ID', 'empiid', '1', NULL, 'int', NULL, 'visit_info_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (493, 13, '就诊时间', 'op_admtime', '0', NULL, 'varchar', NULL, 'admtime', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (494, 16, '登记号(住院号)', 'pat_regno', '0', NULL, 'varchar', NULL, 'pers_basicinfo_persno', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (495, 16, '姓名', 'pat_name', '0', NULL, 'varchar', NULL, 'pers_basicinfo_persname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (496, 16, '性别', 'pat_gender', '0', NULL, 'varchar', NULL, 'pers_basicinfo_sexname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (497, 16, '性别编码', 'pat_gendercode', '0', NULL, 'varchar', NULL, 'pers_basicinfo_sexcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (498, 16, '身份证号', 'pat_idcard', '0', NULL, 'varchar', NULL, 'pers_basicinfo_certno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (499, 16, '出生日期', 'pat_birthday', '0', NULL, 'varchar', NULL, 'pers_basicinfo_birthday', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (500, 16, '国籍', 'pat_country', '0', NULL, 'varchar', NULL, 'pers_basicinfo_countryname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (501, 16, '出生地-省', 'pat_province', '0', NULL, 'varchar', NULL, 'pers_basicinfo_birthplaceprovincename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (502, 16, '出生地-市', 'pat_city', '0', NULL, 'varchar', NULL, 'pers_basicinfo_birthplacecityname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (503, 16, '出生地-区县', 'pat_district', '0', NULL, 'varchar', NULL, 'pers_basicinfo_birthplacecountyname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (504, 16, '民族', 'pat_nation', '0', NULL, 'varchar', NULL, 'pers_basicinfo_nationname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (505, 16, '民族编码', 'pat_nationcode', '0', NULL, 'varchar', NULL, 'pers_basicinfo_nationcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (506, 16, '婚姻状况名称', 'pat_maritalstatus', '0', NULL, 'varchar', NULL, 'pers_basicinfo_maritalconditionname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (507, 16, '婚姻状况代码', 'pat_maritalcode', '0', NULL, 'varchar', NULL, 'pers_basicinfo_maritalconditioncode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (508, 16, '是否死亡', 'pat_isdied', '0', NULL, 'varchar', NULL, 'pers_basicinfo_deathflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (509, 16, '职业', 'pat_occupation', '0', NULL, 'varchar', NULL, 'pers_basicinfo_occupationname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (510, 16, '电话号码', 'pat_telephone', '0', NULL, 'varchar', NULL, 'pers_basicinfo_contactperstel1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (511, 16, '手机号码', 'pat_mobphone', '0', NULL, 'varchar', NULL, 'pers_basicinfo_phoneno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (512, 16, '联系地址', 'pat_location', '0', NULL, 'varchar', NULL, 'pers_basicinfo_contactaddr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (513, 16, '医保卡号', 'pat_payerplancardid', '0', NULL, 'varchar', NULL, 'pers_basicinfo_insuid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (514, 16, '医保类型代码', 'pat_payerplancardcode', '0', NULL, 'varchar', NULL, 'pers_basicinfo_insutypecode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (515, 16, '医保类型', 'pat_payerplancardtype', '0', NULL, 'varchar', NULL, 'pers_basicinfo_insutypename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (516, 16, '医疗机构名称', 'pat_medorgname', '0', NULL, 'varchar', NULL, 'pers_basicinfo_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (517, 16, '最后更新时间', 'pat_lastupdatedttm', '3', NULL, 'varchar', NULL, 'pers_basicinfo_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (518, 17, '就诊ID', 'adm_id', '2', NULL, 'int', NULL, 'visit_info_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (519, 17, '流水号', 'adm_serialno', '0', NULL, 'varchar', NULL, 'visit_info_serialno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (520, 17, '姓名', 'adm_name', '0', NULL, 'varchar', NULL, 'visit_info_persname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (521, 17, '性别', 'adm_gender', '0', NULL, 'varchar', NULL, 'visit_info_sexname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (522, 17, '就诊年龄', 'adm_birthday', '0', NULL, 'varchar', NULL, 'visit_info_currentage', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (523, 17, '就诊医院', 'adm_hospital', '0', NULL, 'varchar', NULL, 'visit_info_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (524, 17, '就诊类型', 'adm_type', '0', NULL, 'varchar', NULL, 'visit_info_visittype', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (525, 17, '就诊时间', 'adm_time', '0', NULL, 'varchar', NULL, 'visit_info_visitdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (526, 17, '入院科室', 'adm_inhospdept', '0', NULL, 'varchar', NULL, 'visit_info_inhospdept', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (527, 17, '当前科室', 'adm_currentdeptname', '0', NULL, 'varchar', NULL, 'visit_info_currentdeptname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (528, 17, '主治医生姓名', 'adm_admdoc', '0', NULL, 'varchar', NULL, 'visit_info_attendingdoctname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (529, 17, '就诊状态', 'adm_visitstatus', '0', NULL, 'varchar', NULL, 'visit_info_statusname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (530, 17, '病区', 'adm_ward', '0', NULL, 'varchar', NULL, 'visit_info_currentwardname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (531, 17, '病房', 'adm_room', '0', NULL, 'varchar', NULL, 'visit_info_inhospward', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (532, 17, '床号', 'adm_bed', '0', NULL, 'varchar', NULL, 'visit_info_currentbedname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (533, 17, '出院时间', 'adm_dischtime', '0', NULL, 'varchar', NULL, 'visit_info_outhospdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (534, 17, '出院病房', 'adm_dischward', '0', NULL, 'varchar', NULL, 'visit_info_outhospward', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (535, 17, '出院科室', 'adm_dischdept', '0', NULL, 'varchar', NULL, 'visit_info_outhospdept', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (537, 14, '就诊ID', 'lis_admid', '2', NULL, 'int', NULL, 'report_labmeasuresub_visitid', NULL, 1, 1, 0, 1);
INSERT INTO `rp_rdr_field` VALUES (538, 14, '患者唯一ID', 'empiid', '1', NULL, 'int', NULL, 'report_labmeasuresub_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (539, 14, '检验项名称', 'lis_itemname', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labsitemcnname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (540, 14, '检验子项编码', 'lis_itemcode', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labsitemcode', NULL, 1, 0, 0, 2);
INSERT INTO `rp_rdr_field` VALUES (541, 14, '检验套名称', 'lis_groupitemname', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labgroupitemname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (542, 14, '检验套编码', 'lis_groupitemcode', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labgroupitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (543, 14, '标本类型', 'lis_specimentype', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_specimentype', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (544, 14, '标本来源说明', 'lis_specimensourcedesc', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_specimensourceexplain', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (545, 14, '采集数量', 'lis_samplingquantity', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_collectquantity', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (546, 14, '采集单位', 'lis_samplingunit', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_collectunit', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (547, 14, '采样时间', 'lis_samplingtime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_samplingdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (548, 14, '送检时间', 'lis_inspcttime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_inspectdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (549, 14, '报告时间', 'lis_reporttime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_reportdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (550, 14, '审核时间', 'lis_checktime', '7', NULL, 'varchar', 100, 'report_labmeasuresub_checkdttm', 0, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (551, 14, '检验时间', 'lis_listime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (552, 14, '检验项值', 'lis_itemvalue', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_resultproperty', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (553, 14, '单位', 'lis_itemunit', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_unit', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (554, 14, '检验项结果描述', 'lis_result', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labresulttextdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (555, 14, '参考范围', 'lis_range', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_referrange', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (556, 14, '异常标志', 'lis_retstatus', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_abnormalflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (558, 18, 'ID', 'empiid', '1', NULL, 'int', NULL, 'report_exammeasuresub_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (559, 18, '就诊ID', 'or_admid', '2', NULL, 'int', NULL, 'report_exammeasuresub_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (560, 18, '检查时间', 'or_examtime', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (561, 18, '检查项目编码', 'or_itemnamecode', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examgroupitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (562, 18, '检查项目名称', 'or_itemname', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examgroupitemname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (563, 18, '检查部位', 'or_part', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examsitename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (564, 18, '检查方法', 'or_exammethodname', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_exammethodname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (565, 18, '审核时间', 'or_audittime', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_checkdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (566, 18, '检查所见', 'or_desc', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_resultproperty', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (567, 18, '检查结论', 'or_result', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_resulttextdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (568, 18, '检查号', 'or_examno', '6', NULL, 'varchar', NULL, 'report_exammeasuresub_applyno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (569, 18, '请求时间', 'or_requesttime', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_requestdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (570, 18, '报告时间', 'or_rpttime', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_reportdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (571, 18, '审核时间1', 'or_checktime', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_checkdttm', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (572, 18, '参考范围', 'or_range', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_referrange', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (573, 18, '异常标志', 'or_retstatus', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_abnormalflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (574, 18, '结果文字描述', 'or_resultdesc', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_resulttextdesc', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (575, 18, '取值', 'or_value', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examvalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (576, 19, '医嘱表唯一ID', 'ord_row', '0', NULL, 'varchar', NULL, 'order_order_serialno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (577, 19, '患者唯一ID', 'empiid', '1', NULL, 'int', NULL, 'order_order_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (578, 19, '就诊号ID', 'ord_admid', '2', NULL, 'int', NULL, 'order_order_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (579, 19, '医嘱大类', 'ord_cate', '0', NULL, 'varchar', NULL, 'order_order_orderpclassname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (581, 19, '医嘱子类', 'ord_childcate', '0', NULL, 'varchar', NULL, 'order_order_ordersclassname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (583, 19, '医嘱名称', 'ord_name', '0', NULL, 'varchar', NULL, 'order_order_orderitemname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (585, 19, '医嘱类型', 'ord_type', '0', NULL, 'varchar', NULL, 'order_order_ordertypename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (587, 19, '医嘱开始时间', 'ord_starttime', '0', NULL, 'varchar', NULL, 'order_order_orderbegindttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (588, 19, '医嘱开立时间', 'ord_time', '0', NULL, 'varchar', NULL, 'order_order_orderdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (589, 19, '医嘱状态', 'ord_status', '0', NULL, 'varchar', NULL, 'order_order_orderstatusname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (590, 19, '医嘱停止时间', 'ord_endtime', '0', NULL, 'varchar', NULL, 'order_order_orderenddttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (605, 19, '医嘱数量', 'ord_qty', '0', NULL, 'varchar', NULL, 'order_order_quantity', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (609, 19, '用法', 'ord_usage', '0', NULL, 'varchar', NULL, 'order_order_usage', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (610, 13, '手术表唯一ID', 'op_id', '0', NULL, 'varchar', NULL, 'opsn_opsevent_seqno', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (611, 13, '患者唯一ID', 'op_empiid', '1', NULL, 'int', NULL, 'opsn_opsevent_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (612, 13, '就诊ID', 'op_admid', '2', NULL, 'int', NULL, 'opsn_opsevent_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (613, 13, '手术名称', 'op_name', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opsname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (614, 13, 'ICD手术名称', 'op_icdname', '0', NULL, 'varchar', NULL, 'opsn_opsevent_icdopsname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (615, 13, '手术级别', 'op_grade', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opslevelcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (616, 13, '手术开始时间', 'op_starttime', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opsbegindatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (617, 13, '手术结束时间', 'op_endtime', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opsenddatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (618, 13, '手术记录时间', 'op_recordtime', '0', NULL, '', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (619, 13, '手术持续时间', 'op_durationtime', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opssustaindttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (620, 13, '科室名称', 'op_dept', '0', NULL, 'varchar', NULL, 'opsn_opsevent_deptname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (621, 13, '手术术者', 'op_opdoc', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opsdoctname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (622, 13, '第一助手', 'op_ass', '0', NULL, 'varchar', NULL, 'opsn_opsevent_firstopsassistantname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (623, 13, '器械护士', 'op_instrumentsnurse', '0', NULL, 'varchar', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (624, 13, '巡回护士', 'op_tournurse', '0', NULL, 'varchar', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (625, 13, '麻醉医师', 'op_anaesthesiadoc', '0', NULL, 'varchar', NULL, 'opsn_opsevent_narcosisdoctname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (626, 13, '麻醉方法', 'op_anaesthesiamode', '0', NULL, 'varchar', NULL, 'opsn_opsevent_narcosismethod', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (627, 13, '手术过程描述', 'op_desc', '0', NULL, 'varchar', NULL, 'opsn_opsevent_opsresult', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (628, 13, '术中发现', 'op_surgeryseen', '0', NULL, 'varchar', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (629, 13, '术中出血量', 'op_bloodloss', '0', NULL, 'varchar', NULL, 'opsn_opsevent_losebloodvolume', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (630, 13, '输血种类', 'op_bloodtranstype', '0', NULL, 'varchar', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (631, 13, '输血量', 'op_bloodtrans', '0', NULL, 'varchar', NULL, 'opsn_opsevent_bloodvolume', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (632, 13, '输血反应', 'op_bloodtransreaction', '0', NULL, 'varchar', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (633, 13, '输液量', 'op_infusionvol', '0', NULL, 'varchar', NULL, 'opsn_opsevent_transfusionmeasure', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (634, 13, '送病理情况', 'op_sendpathology', '0', NULL, 'varchar', NULL, 'opsn_opsevent_', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (635, 13, '术前诊断', 'op_diagbeforeop', '0', NULL, 'varchar', NULL, 'opsn_opsevent_beforeopsmaindiag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (636, 13, '术后诊断', 'op_diagafterop', '0', NULL, 'varchar', NULL, 'opsn_opsevent_afteropsdiag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (637, 20, '诊断id', 'diag_id', '0', NULL, 'varchar', NULL, 'diaginfo_serialno', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (638, 20, '患者唯一索引id', 'empiid', '1', NULL, 'int', NULL, 'diaginfo_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (639, 20, '就诊id', 'diag_admid', '2', NULL, 'int', NULL, 'diaginfo_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (640, 20, '诊断时间', 'diag_time', '0', NULL, 'varchar', NULL, 'diaginfo_diagdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (641, 20, '诊断类型', 'diag_type', '0', NULL, 'varchar', NULL, 'diaginfo_diagtypename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (642, 20, '诊断类型代码', 'diag_typecode', '0', NULL, 'varchar', NULL, 'diaginfo_diagtypecode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (643, 20, '诊断icd代码', 'diag_icdcode', '0', NULL, 'varchar', NULL, 'diaginfo_icddiagcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (644, 20, '诊断icd名称', 'diag_icdname', '0', NULL, 'varchar', NULL, 'diaginfo_icddiagname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (645, 20, '诊断名称', 'diag_name', '0', NULL, 'varchar', NULL, 'diaginfo_hospdiagname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (646, 20, '诊断编码', 'diag_code', '0', NULL, 'varchar', NULL, 'diaginfo_hospdiagcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (647, 20, '诊断医生代码', 'diag_doctorcode', '0', NULL, 'varchar', NULL, 'diaginfo_doctcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (648, 20, '诊断医生', 'diag_doctor', '0', NULL, 'varchar', NULL, 'diaginfo_doctname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (649, 20, '诊断科室', 'diag_deptname', '0', NULL, 'varchar', NULL, 'diaginfo_deptname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (650, 20, '诊断科室代码', 'diag_deptcode', '0', NULL, 'varchar', NULL, 'diaginfo_deptcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (651, 20, '最后更新时间', 'diag_lastupdatedttm', '3', NULL, 'varchar', NULL, 'diaginfo_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (652, 20, '医疗机构名称', 'diag_medorgname', '0', NULL, 'varchar', NULL, 'diaginfo_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (654, 21, '患者唯一id', 'empiid', '1', NULL, 'int', NULL, 'case_nursingdet_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (655, 21, '就诊id', 'case_nursingdet_admid', '2', NULL, 'int', NULL, 'case_nursingdet_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (656, 21, '医疗机构代码', 'case_nursingdet_medorgcode', '5', NULL, 'varchar', NULL, 'case_nursingdet_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (657, 21, '创建时间', 'case_nursingdet_createdttm', '0', NULL, 'varchar', NULL, 'case_nursingdet_createdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (658, 21, '数据来源标识', 'case_nursingdet_datasourceflag', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (659, 21, '接入数据源的表', 'case_nursingdet_dstable', '0', NULL, 'varchar', NULL, 'case_nursingdet_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (660, 21, '接入数据源的key', 'case_nursingdet_dstablekey', '0', NULL, 'varchar', NULL, 'case_nursingdet_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (661, 21, '接入数据源的key值', 'case_nursingdet_dstablevalue', '0', NULL, 'varchar', NULL, 'case_nursingdet_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (662, 21, '医疗机构名称', 'case_nursingdet_medorgname', '0', NULL, 'varchar', NULL, 'case_nursingdet_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (663, 21, '登记号', 'case_nursingdet_persno', '0', NULL, 'int', NULL, 'case_nursingdet_persno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (664, 21, '病案号', 'case_nursingdet_visitno', '0', NULL, 'int', NULL, 'case_nursingdet_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (665, 44, '患者姓名', 'ran_patname', '0', NULL, 'varchar', NULL, 'case_nursingdet_patname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (666, 44, '性别', 'ran_gender', '0', NULL, 'varchar', NULL, 'case_nursingdet_sexname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (667, 44, '入院时年龄', 'ran_age', '0', NULL, 'varchar', NULL, 'case_nursingdet_age', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (668, 44, '入院时间', 'ran_inhosptime', '0', NULL, 'varchar', NULL, 'case_nursingdet_inhospdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (669, 44, '主诉', 'ran_complain', '0', NULL, 'varchar', NULL, 'case_nursingdet_chiefcomplaint', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (670, 44, '现病史', 'ran_presentillness', '0', NULL, 'varchar', NULL, 'case_nursingdet_presenthistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (671, 44, '一般健康状况标识', 'ran_generalhealthconditionflag', '0', NULL, 'varchar', NULL, 'case_nursingdet_generalhealthconditionflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (672, 44, '疾病史', 'ran_diseasehistory', '0', NULL, 'varchar', NULL, 'case_nursingdet_diseasehistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (673, 44, '患者传染性标识', 'ran_patinfectflag', '0', NULL, 'varchar', NULL, 'case_nursingdet_patinfectflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (674, 44, '传染病史', 'ran_infectioushistory', '0', NULL, 'varchar', NULL, 'case_nursingdet_infectioushistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (675, 44, '预防接种史', 'ran_preventinoculatehistory', '0', NULL, 'varchar', NULL, 'case_nursingdet_preventinoculatehistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (676, 44, '手术史', 'ran_surgeryhistory', '0', NULL, 'varchar', NULL, 'case_nursingdet_opshistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (677, 44, '输血史', 'ran_bloodtranshistory', '0', NULL, 'varchar', NULL, 'case_nursingdet_bloodtranshistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (678, 44, '过敏史', 'ran_allergyhistory', '0', NULL, 'varchar', NULL, 'case_nursingdet_allergyhistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (679, 44, '个人史', 'ran_sociology', '0', NULL, 'varchar', NULL, 'case_nursingdet_pershistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (680, 44, '婚育史', 'ran_marriage', '0', NULL, 'varchar', NULL, 'case_nursingdet_maritalhistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (681, 44, '月经史', 'ran_menstrual', '0', NULL, 'varchar', NULL, 'case_nursingdet_menstrualhistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (682, 44, '家族史', 'ran_family', '0', NULL, 'varchar', NULL, 'case_nursingdet_familyhistory', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (683, 44, '体温', 'ran_bodytemp', '0', NULL, 'varchar', NULL, 'case_nursingdet_bodytemp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (684, 44, '脉率', 'ran_pulserate', '0', NULL, 'varchar', NULL, 'case_nursingdet_pulserate', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (685, 44, '呼吸频率', 'ran_breathrate', '0', NULL, 'varchar', NULL, 'case_nursingdet_breathrate', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (686, 44, '收缩压', 'ran_sbp', '0', NULL, 'varchar', NULL, 'case_nursingdet_sbp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (687, 44, '收缩压', 'ran_sbp', '0', NULL, 'varchar', NULL, 'case_nursingdet_sbp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (688, 44, '舒张压', 'ran_dbp', '0', NULL, 'varchar', NULL, 'case_nursingdet_dbp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (689, 44, '身高', 'ran_height', '0', NULL, 'varchar', NULL, 'case_nursingdet_height', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (690, 44, '体重', 'ran_weight', '0', NULL, 'varchar', NULL, 'case_nursingdet_weight', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (691, 44, '一般状况检查结果', 'ran_generalconditionexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_generalconditionexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (692, 44, '皮肤和黏膜检查结果', 'ran_skinexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_skinexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (693, 44, '全身浅表淋巴结检查结果', 'ran_wholetableexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_wholetableexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (694, 44, '头部及其器官检查结果', 'ran_organexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_organexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (695, 44, '颈部检查结果', 'ran_neckexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_neckexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (696, 44, '胸部检查结果', 'ran_chestexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_chestexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (697, 44, '腹部检查结果', 'ran_abdomenexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_abdomenexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (698, 44, '肛门指诊检查结果描述', 'ran_anusdreexamresultdesc', '0', NULL, 'varchar', NULL, 'case_nursingdet_anusdreexamresultdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (699, 44, '外生殖器检查结果', 'ran_externaliaexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_externaliaexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (700, 44, '脊柱检查结果', 'ran_spineexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_spineexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (701, 44, '四肢检查结果', 'ran_limbsexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_limbsexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (702, 44, '神经系统检查结果', 'ran_sysexamresult', '0', NULL, 'varchar', NULL, 'case_nursingdet_sysexamresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (703, 44, '接诊医师签名', 'ran_receptiondoctsignature', '0', NULL, 'varchar', NULL, 'case_nursingdet_receptiondoctsignature', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (704, 44, '住院医师签名', 'ran_residentsignature', '0', NULL, 'varchar', NULL, 'case_nursingdet_residentsignature', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (705, 44, '主治医师签名', 'ran_attendingdoctsignature', '0', NULL, 'varchar', NULL, 'case_nursingdet_attendingdoctsignature', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (706, 44, '主任医师签名', 'ran_directordoctsignature', '0', NULL, 'varchar', NULL, 'case_nursingdet_directordoctsignature', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (707, 21, '人员唯一标识id', 'dr_empiid', '0', NULL, 'int8', NULL, 'cda_struct_ipcourserecord outhosprecord_empiid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (708, 21, '就诊id', 'dr_visitid', '0', NULL, 'int8', NULL, 'cda_struct_ipcourserecord outhosprecord_visitid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (709, 21, '科室名称', 'dr_deptname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_deptname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (710, 21, '病区名称', 'dr_wardname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_wardname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (711, 21, '病房名称', 'dr_wardname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_wardname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (712, 21, '床位名称', 'dr_bedname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_bedname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (713, 21, '患者姓名', 'dr_patname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_patname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (714, 21, '性别名称', 'dr_sexname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_sexname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (715, 21, '入院时间', 'dr_inhospdttm', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_inhospdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (716, 21, '入院情况名称', 'dr_inhospcondname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_inhospcondname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (717, 21, '入院诊断名称', 'dr_inhospdiagname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_inhospdiagname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (718, 21, '出院情况名称', 'dr_outhospcondname', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_outhospcondname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (719, 21, '出院时间', 'dr_outhospdttm', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_outhospdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (720, 21, '出院诊断西医诊断描述', 'dr_outhospdiagwmdiagdesc', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_outhospdiagwmdiagdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (721, 21, '出院诊断中医病名名称', 'dr_outhospdiagtcmdiseasenamename', '0', NULL, 'varchar', NULL, 'cda_struct_ipcourserecord outhosprecord_outhospdiagtcmdiseasenamename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (722, 24, '人员唯一标识id', 'mrhp_empiid', '1', NULL, 'int', NULL, 'empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (723, 24, '就诊id', 'mrhp_admid', '2', NULL, 'int', NULL, 'visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (724, 24, '医院名称', 'mrhp_hospname', '0', NULL, 'varchar', NULL, 'mr_mrfp_hospname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (725, 24, '病案号', 'mrhp_mrno', '0', NULL, 'varchar', NULL, 'mr_mrfp_mrno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (726, 24, '住院号', 'mrhp_ipno', '0', NULL, 'varchar', NULL, 'mr_mrfp_ipno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (727, 24, '患者姓名', 'mrhp_patname', '0', NULL, 'varchar', NULL, 'mr_mrfp_patname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (728, 24, '性别名称', 'mrhp_sexname', '0', NULL, 'varchar', NULL, 'mr_mrfp_sexname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (729, 24, '出生时间', 'mrhp_birthdttm', '0', NULL, 'varchar', NULL, 'mr_mrfp_birthdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (730, 24, '婴儿年龄', 'mrhp_babyage', '0', NULL, 'varchar', NULL, 'mr_mrfp_babyage', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (731, 24, '年龄', 'mrhp_age', '0', NULL, 'varchar', NULL, 'mr_mrfp_age', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (732, 24, '婚姻状况', 'mrhp_maritalcondition', '0', NULL, 'varchar', NULL, 'mr_mrfp_maritalcondition', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (733, 24, '职业描述', 'mrhp_occupationdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_occupationdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (734, 24, '出生省份', 'mrhp_birthprovince', '0', NULL, 'varchar', NULL, 'mr_mrfp_birthprovince', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (735, 24, '出生县市', 'mrhp_birthcountycity', '0', NULL, 'varchar', NULL, 'mr_mrfp_birthcountycity', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (736, 24, '出生地县', 'mrhp_birthplacecounty', '0', NULL, 'varchar', NULL, 'mr_mrfp_birthplacecounty', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (737, 24, '民族名称', 'mrhp_nationname', '0', NULL, 'varchar', NULL, 'mr_mrfp_nationname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (738, 24, '国家名称', 'mrhp_countryname', '0', NULL, 'varchar', NULL, 'mr_mrfp_countryname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (739, 24, '身份证号', 'mrhp_idcardno', '0', NULL, 'varchar', NULL, 'mr_mrfp_idcardno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (740, 24, '家庭地址', 'mrhp_homeaddr', '0', NULL, 'varchar', NULL, 'mr_mrfp_homeaddr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (741, 24, '家庭电话', 'mrhp_hometel', '0', NULL, 'varchar', NULL, 'mr_mrfp_hometel', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (742, 24, '家庭邮编', 'mrhp_homepostal', '0', NULL, 'varchar', NULL, 'mr_mrfp_homepostal', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (743, 24, '工作单位', 'mrhp_workunit', '0', NULL, 'varchar', NULL, 'mr_mrfp_workunit', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (744, 24, '单位地址', 'mrhp_unitaddr', '0', NULL, 'varchar', NULL, 'mr_mrfp_unitaddr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (745, 24, '单位电话', 'mrhp_unittel', '0', NULL, 'varchar', NULL, 'mr_mrfp_unittel', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (746, 24, '单位邮编', 'mrhp_unitpostal', '0', NULL, 'varchar', NULL, 'mr_mrfp_unitpostal', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (747, 24, '户口地址', 'mrhp_registeredaddr', '0', NULL, 'varchar', NULL, 'mr_mrfp_registeredaddr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (748, 24, '户口电话', 'mrhp_registeredtel', '0', NULL, 'varchar', NULL, 'mr_mrfp_registeredtel', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (749, 24, '户口邮编', 'mrhp_registeredpostal', '0', NULL, 'varchar', NULL, 'mr_mrfp_registeredpostal', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (750, 24, '联系人姓名', 'mrhp_contactpersname', '0', NULL, 'varchar', NULL, 'mr_mrfp_contactpersname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (751, 24, '关系', 'mrhp_relation', '0', NULL, 'varchar', NULL, 'mr_mrfp_relation', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (752, 24, '联系人地址', 'mrhp_contactpersaddr', '0', NULL, 'varchar', NULL, 'mr_mrfp_contactpersaddr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (753, 24, '联系人电话', 'mrhp_contactperstel', '0', NULL, 'varchar', NULL, 'mr_mrfp_contactperstel', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (754, 24, '出院时是否危重', 'mrhp_outhospisurgent', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospisurgent', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (755, 24, '是否疑难', 'mrhp_isdifficult', '0', NULL, 'varchar', NULL, 'mr_mrfp_isdifficult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (756, 24, '入院病情描述', 'mrhp_inhospconditiondesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospconditiondesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (757, 24, '入院途径描述', 'mrhp_inhosppathwaydesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhosppathwaydesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (758, 24, '入院科室名称', 'mrhp_inhospdeptname', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospdeptname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (759, 24, '入院病区描述', 'mrhp_inhospwarddesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospwarddesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (760, 24, '入院床号', 'mrhp_inhospbedno', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospbedno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (761, 24, '出院科室', 'mrhp_outhospdept', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospdept', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (762, 24, '出院科室描述', 'mrhp_outhospdeptdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospdeptdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (763, 24, '出院病区', 'mrhp_outhospward', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospward', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (764, 24, '出院病区描述', 'mrhp_outhospwarddesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospwarddesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (765, 24, '转科情况', 'mrhp_transdeptcond', '0', NULL, 'varchar', NULL, 'mr_mrfp_transdeptcond', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (766, 24, '出院床号', 'mrhp_outhospbedno', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospbedno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (767, 24, '主任医师姓名', 'mrhp_directordoctname', '0', NULL, 'varchar', NULL, 'mr_mrfp_directordoctname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (768, 24, '住院医师姓名', 'mrhp_residentname', '0', NULL, 'varchar', NULL, 'mr_mrfp_residentname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (769, 24, '入院操作员工号', 'mrhp_inhospoperatorno', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospoperatorno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (770, 24, '出院操作员工号', 'mrhp_outhospoperatorno', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospoperatorno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (771, 24, '出院操作员姓名', 'mrhp_outhospoperatorname', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospoperatorname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (772, 24, '术前住院日', 'mrhp_beforeopsipday', '0', NULL, 'varchar', NULL, 'mr_mrfp_beforeopsipday', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (773, 24, '术后天数', 'mrhp_afteropsdaynum', '0', NULL, 'varchar', NULL, 'mr_mrfp_afteropsdaynum', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (774, 24, '住院天数', 'mrhp_hlos', '0', NULL, 'varchar', NULL, 'mr_mrfp_hlos', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (775, 24, '入院时间', 'mrhp_inhospdttm', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (776, 24, '入院确诊时间', 'mrhp_inhospconfirmdttm', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospconfirmdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (777, 24, '出院时间', 'mrhp_outhospdttm', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (778, 24, '死亡时间', 'mrhp_deathdttm', '0', NULL, 'varchar', NULL, 'mr_mrfp_deathdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (779, 24, '入院诊断icd-10代码', 'mrhp_inhospdiagicd10code', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospdiagicd10code', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (780, 24, '入院诊断代码', 'mrhp_inhospdiagcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospdiagcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (781, 24, '入院诊断描述', 'mrhp_inhospdiagdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospdiagdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (782, 24, '门诊诊断icd代码', 'mrhp_opdiagicdcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_opdiagicdcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (783, 24, '门诊诊断代码', 'mrhp_opdiagcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_opdiagcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (784, 24, '门诊诊断描述', 'mrhp_opdiagdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_opdiagdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (785, 24, '病理icd号', 'mrhp_pathologyicdno', '0', NULL, 'varchar', NULL, 'mr_mrfp_pathologyicdno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (786, 24, '病理诊断编码', 'mrhp_pathologydiagcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_pathologydiagcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (787, 24, '病理诊断', 'mrhp_pathologydiag', '0', NULL, 'varchar', NULL, 'mr_mrfp_pathologydiag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (788, 24, '病理号', 'mrhp_pathologyno', '0', NULL, 'varchar', NULL, 'mr_mrfp_pathologyno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (789, 24, '主诊断icd代码', 'mrhp_maindiagicdcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_maindiagicdcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (790, 24, '主诊断代码', 'mrhp_maindiagcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_maindiagcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (791, 24, '主要诊断描述', 'mrhp_maindiagdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_maindiagdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (792, 24, '主要诊断', 'mrhp_maindiag', '0', NULL, 'varchar', NULL, 'mr_mrfp_maindiag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (793, 24, '损伤、中毒的外部因素icd编码', 'mrhp_injurypoisonouterfactoricdcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_injurypoisonouterfactoricdcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (794, 24, '损伤、中毒的外部因素编码', 'mrhp_injurypoisonouterfactorcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_injurypoisonouterfactorcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (795, 24, '损伤、中毒的外部因素描述', 'mrhp_injurypoisonouterfactordesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_injurypoisonouterfactordesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (796, 24, '颅脑损伤患者昏迷时间_入院前', 'mrhp_injurypatcomadttmbefore', '0', NULL, 'varchar', NULL, 'mr_mrfp_injurypatcomadttmbefore', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (797, 24, '颅脑损伤患者昏迷时间_入院后', 'mrhp_injurypatcomadttmafter', '0', NULL, 'varchar', NULL, 'mr_mrfp_injurypatcomadttmafter', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (798, 24, '门诊与出院诊断符合', 'mrhp_opouthospdiagconform', '0', NULL, 'varchar', NULL, 'mr_mrfp_opouthospdiagconform', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (799, 24, '门诊与出院诊断符合描述', 'mrhp_opouthospdiagconformdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_opouthospdiagconformdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (800, 24, '入院与出院诊断符合', 'mrhp_inhospouthospdiagconform', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospouthospdiagconform', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (801, 24, '入院与出院诊断符合描述', 'mrhp_inhospouthospdiagconformdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_inhospouthospdiagconformdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (802, 24, '手术前后诊断符合', 'mrhp_opslastnextdiagconform', '0', NULL, 'varchar', NULL, 'mr_mrfp_opslastnextdiagconform', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (803, 24, '手术前后诊断符合描述', 'mrhp_opslastnextdiagconformdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_opslastnextdiagconformdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (804, 24, '临床与病理诊断符合', 'mrhp_clinicpathologydiagconform', '0', NULL, 'varchar', NULL, 'mr_mrfp_clinicpathologydiagconform', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (805, 24, '临床与病理诊断符合描述', 'mrhp_clinicpathologydiagconformdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_clinicpathologydiagconformdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (806, 24, '放射与病理诊断符合', 'mrhp_radiatepathologydiagconform', '0', NULL, 'varchar', NULL, 'mr_mrfp_radiatepathologydiagconform', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (807, 24, '放射与病理诊断符合描述', 'mrhp_radiatepathologydiagconformdesc', '0', NULL, 'varchar', NULL, 'mr_mrfp_radiatepathologydiagconformdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (808, 24, 'hbsag', 'mrhp_hbsag', '0', NULL, 'varchar', NULL, 'mr_mrfp_hbsag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (809, 24, 'hcvab', 'mrhp_hcvab', '0', NULL, 'varchar', NULL, 'mr_mrfp_hcvab', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (810, 24, 'hivab', 'mrhp_hivab', '0', NULL, 'varchar', NULL, 'mr_mrfp_hivab', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (811, 24, '医源性手术', 'mrhp_iatrogenicops', '0', NULL, 'varchar', NULL, 'mr_mrfp_iatrogenicops', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (812, 24, '医源性本院', 'mrhp_iatrogenichosp', '0', NULL, 'varchar', NULL, 'mr_mrfp_iatrogenichosp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (813, 24, '传染病报告', 'mrhp_infectiousreport', '0', NULL, 'varchar', NULL, 'mr_mrfp_infectiousreport', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (814, 24, '肿瘤报告', 'mrhp_tumourreport', '0', NULL, 'varchar', NULL, 'mr_mrfp_tumourreport', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (815, 24, '肿瘤icd编码', 'mrhp_tumouricdcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_tumouricdcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (816, 24, '肿瘤编码', 'mrhp_tumourcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_tumourcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (817, 24, '肿瘤名称', 'mrhp_tumourname', '0', NULL, 'varchar', NULL, 'mr_mrfp_tumourname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (818, 24, '新生儿死亡报告', 'mrhp_newborndeathreport', '0', NULL, 'varchar', NULL, 'mr_mrfp_newborndeathreport', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (819, 24, '孕产妇死亡报告', 'mrhp_pregdeathreport', '0', NULL, 'varchar', NULL, 'mr_mrfp_pregdeathreport', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (820, 24, '血型', 'mrhp_bloodtype', '0', NULL, 'varchar', NULL, 'mr_mrfp_bloodtype', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (821, 24, 'rh对应情况', 'mrhp_rhcond', '0', NULL, 'varchar', NULL, 'mr_mrfp_rhcond', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (822, 24, '输血量', 'mrhp_bloodvolume', '0', NULL, 'varchar', NULL, 'mr_mrfp_bloodvolume', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (823, 24, '红细胞', 'mrhp_rbc', '0', NULL, 'varchar', NULL, 'mr_mrfp_rbc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (824, 24, '血小板', 'mrhp_plt', '0', NULL, 'varchar', NULL, 'mr_mrfp_plt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (825, 24, '血浆', 'mrhp_plasma', '0', NULL, 'varchar', NULL, 'mr_mrfp_plasma', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (826, 24, '全血', 'mrhp_wholeblood', '0', NULL, 'varchar', NULL, 'mr_mrfp_wholeblood', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (827, 24, '其他输血品种', 'mrhp_otherbloodtransvariety', '0', NULL, 'varchar', NULL, 'mr_mrfp_otherbloodtransvariety', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (828, 24, '输血反应', 'mrhp_bloodtransreaction', '0', NULL, 'varchar', NULL, 'mr_mrfp_bloodtransreaction', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (829, 24, '药物过敏', 'mrhp_drugallergy', '0', NULL, 'varchar', NULL, 'mr_mrfp_drugallergy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (830, 24, '过敏药物', 'mrhp_allergydrug', '0', NULL, 'varchar', NULL, 'mr_mrfp_allergydrug', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (831, 24, '过敏原因', 'mrhp_allergyreason', '0', NULL, 'varchar', NULL, 'mr_mrfp_allergyreason', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (832, 24, '尸检', 'mrhp_autopsy', '0', NULL, 'varchar', NULL, 'mr_mrfp_autopsy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (833, 24, '病案质量', 'mrhp_mrquality', '0', NULL, 'varchar', NULL, 'mr_mrfp_mrquality', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (834, 24, '产后出血', 'mrhp_postpartumbleed', '0', NULL, 'varchar', NULL, 'mr_mrfp_postpartumbleed', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (835, 24, '新生儿性别', 'mrhp_newbornsex', '0', NULL, 'varchar', NULL, 'mr_mrfp_newbornsex', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (836, 24, '新生儿体重', 'mrhp_newbornweight', '0', NULL, 'varchar', NULL, 'mr_mrfp_newbornweight', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (837, 24, '新生儿入院体重', 'mrhp_newborninhospweight', '0', NULL, 'varchar', NULL, 'mr_mrfp_newborninhospweight', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (838, 24, '院内感染', 'mrhp_hospinfect', '0', NULL, 'varchar', NULL, 'mr_mrfp_hospinfect', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (839, 24, '抢救次数', 'mrhp_rescuenum', '0', NULL, 'varchar', NULL, 'mr_mrfp_rescuenum', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (840, 24, '成功次数', 'mrhp_successnum', '0', NULL, 'varchar', NULL, 'mr_mrfp_successnum', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (841, 24, '离院方式', 'mrhp_outhospmode', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhospmode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (842, 24, '治疗结果', 'mrhp_treatresult', '0', NULL, 'varchar', NULL, 'mr_mrfp_treatresult', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (843, 24, '是否有出院31天内再住院计划', 'mrhp_isouthosp31dayretplan', '0', NULL, 'varchar', NULL, 'mr_mrfp_isouthosp31dayretplan', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (844, 24, '出院31天再住院计划目的', 'mrhp_outhosp31dayretplanpurpose', '0', NULL, 'varchar', NULL, 'mr_mrfp_outhosp31dayretplanpurpose', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (845, 24, '危重级别', 'mrhp_urgentlevel', '0', NULL, 'varchar', NULL, 'mr_mrfp_urgentlevel', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (846, 24, '危重级别名称', 'mrhp_urgentlevelname', '0', NULL, 'varchar', NULL, 'mr_mrfp_urgentlevelname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (847, 24, '住院急症', 'mrhp_ipemergency', '0', NULL, 'varchar', NULL, 'mr_mrfp_ipemergency', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (848, 24, '转入医院名称', 'mrhp_transinhospname', '0', NULL, 'varchar', NULL, 'mr_mrfp_transinhospname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (849, 24, '转入社区服务机构', 'mrhp_transinserveorg', '0', NULL, 'varchar', NULL, 'mr_mrfp_transinserveorg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (850, 24, '是否有手术并发症', 'mrhp_isopscomplication', '0', NULL, 'varchar', NULL, 'mr_mrfp_isopscomplication', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (851, 24, '手术并发症', 'mrhp_opscomplication', '0', NULL, 'varchar', NULL, 'mr_mrfp_opscomplication', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (852, 20, '医疗机构编码(联合主键)', 'diag_medorgcode', '5', NULL, 'varchar', NULL, 'diaginfo_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (853, 20, '数据来源标识(联合主键)', 'diag_datasourceflag', '0', NULL, 'varchar', NULL, 'diaginfo_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (854, 20, '接入源表(联合主键)', 'diag_dstable', '0', NULL, 'varchar', NULL, 'diaginfo_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (855, 20, '接入数据源key(联合主键)', 'diag_dstablekey', '0', NULL, 'varchar', NULL, 'diaginfo_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (856, 20, '接入数据源key值(联合主键)', 'diag_dstablevalue', '0', NULL, 'varchar', NULL, 'diaginfo_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (857, 14, '医疗机构编码', 'lis_medorgcode', '5', NULL, 'varchar', NULL, 'report_labmeasuresub_medorgcode', NULL, 1, 0, 1, 0);
INSERT INTO `rp_rdr_field` VALUES (858, 14, '接入源表', 'lis_dstable', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_dstable', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (859, 14, '接入数据源key', 'lis_dstablekey', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_dstablekey', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (860, 14, '接入数据源key值', 'lis_dstablevalue', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_dstablevalue', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (861, 18, '医疗机构编码', 'or_medorgcode', '5', NULL, 'varchar', NULL, 'report_exammeasuresub_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (862, 18, '接入源表', 'or_dstable', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (863, 18, '接入数据源key', 'or_dstablekey', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (864, 18, '接入数据源value', 'or_dstablevalue', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (865, 13, '医疗机构编码', 'op_medorgcode', '0', NULL, 'varchar', NULL, 'opsn_opsevent_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (866, 13, '接入源表', 'op_dstable', '0', NULL, 'varchar', NULL, 'opsn_opsevent_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (867, 13, '数据源key', 'op_dstablekey', '0', NULL, 'varchar', NULL, 'opsn_opsevent_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (868, 13, '数据源value', 'op_dstablevalue', '0', NULL, 'varchar', NULL, 'opsn_opsevent_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (869, 12, '检查报告患者唯一id', 'empiid', '1', NULL, 'int', NULL, 'report_main_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (870, 12, '就诊id', 'ordreport_admid', '2', NULL, 'int', NULL, 'report_main_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (871, 12, '报告号', 'ordreport_reportno', '0', NULL, 'varchar', NULL, 'report_main_reportno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (872, 12, '病理号', 'ordreport_pathologyno', '0', NULL, 'varchar', NULL, 'report_main_pathologyno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (873, 12, '检查部位', 'ordreport_examsite', '0', NULL, 'varchar', NULL, 'report_main_examsite', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (874, 12, '分类名称', 'ordreport_classname', '0', NULL, 'varchar', NULL, 'report_main_classname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (875, 12, '结果类型名称', 'ordreport_resulttypename', '0', NULL, 'varchar', NULL, 'report_main_resulttypename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (876, 12, '结果描述', 'ordreport_resultdesc', '0', NULL, 'varchar', NULL, 'report_main_resultdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (877, 12, '临床诊断名称', 'ordreport_clinicdiagname', '0', NULL, 'varchar', NULL, 'report_main_clinicdiagname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (878, 12, '检查部位名称', 'ordreport_examsitename', '0', NULL, 'varchar', NULL, 'report_main_examsitename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (879, 12, '检查方法名称', 'ordreport_examdirectionname', '0', NULL, 'varchar', NULL, 'report_main_examdirectionname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (880, 12, '检查所见', 'ordreport_examfinding', '0', NULL, 'varchar', NULL, 'report_main_examfinding', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (881, 12, '检查结论', 'ordreport_examconclusionbody', '0', NULL, 'varchar', NULL, 'report_main_examconclusionbody', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (882, 12, '建议', 'ordreport_suggest', '0', NULL, 'varchar', NULL, 'report_main_suggest', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (883, 12, '异常标识', 'ordreport_abnormalflag', '0', NULL, 'varchar', NULL, 'report_main_abnormalflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (884, 12, '检查开始时间', 'ordreport_exambegindttm', '0', NULL, 'varchar', NULL, 'report_main_exambegindttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (885, 12, '医疗机构编码', 'ordreport_medorgcode', '5', NULL, 'varchar', NULL, 'report_main_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (886, 12, '报告时间', 'ordreport_reportdttm', '0', NULL, 'varchar', NULL, 'report_main_reportdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (887, 12, '接入源表', 'ordreport_dstable', '0', NULL, 'varchar', NULL, 'report_main_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (888, 12, '数据源key', 'ordreport_dstablekey', '0', NULL, 'varchar', NULL, 'report_main_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (889, 12, '数据源value', 'ordreport_dstablevalue', '0', NULL, 'varchar', NULL, 'report_main_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (890, 12, '数据来源', 'ordreport_datasourceflag', '0', NULL, 'varchar', NULL, 'report_main_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (891, 12, '分类编码', 'ordreport_classcode', '0', NULL, 'varchar', NULL, 'report_main_classcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (892, 12, '申请项目名称', 'ordreport_applyitemname', '0', NULL, 'varchar', NULL, 'report_main_applyitemname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (893, 12, '申请项目编码', 'ordreport_applyitemcode', '0', NULL, 'varchar', NULL, 'report_main_applyitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (894, 24, '医院编码', 'mrhp_hospcode', '0', NULL, 'varchar', NULL, 'mr_mrfp_hospcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (895, 24, '就诊号', 'mrhp_visitno', '0', NULL, 'varchar', NULL, 'mr_mrfp_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (896, 24, '接入源表', 'mrhp_dstable', '0', NULL, 'varchar', NULL, 'mr_mrfp_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (897, 24, '接入数据源key', 'mrhp_dstablekey', '0', NULL, 'varchar', NULL, 'mr_mrfp_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (898, 24, '接入数据源key值', 'mrhp_dstablevalue', '0', NULL, 'varchar', NULL, 'mr_mrfp_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (899, 26, '医院编码', 'emr_medorgcode', '5', NULL, 'varchar', NULL, 'case_cdadet_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (900, 26, '医院名称', 'emr_medorgname', '0', NULL, 'varchar', NULL, 'case_cdadet_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (901, 26, '患者唯一ID', 'empiid', '1', NULL, 'int', NULL, 'case_cdadet_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (902, 26, '就诊ID', 'emr_admid', '2', NULL, 'int', NULL, 'case_cdadet_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (903, 26, '登记号', 'emr_persno', '0', NULL, 'varchar', NULL, 'case_cdadet_persno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (904, 26, '病历分类名称', 'emr_templatename', '0', NULL, 'varchar', NULL, 'case_cdadet_templatename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (905, 26, '病历项名称', 'emr_datasetname', '0', NULL, 'varchar', NULL, 'case_cdadet_datasetname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (906, 26, '病历项值', 'emr_datasetvalue', '0', NULL, 'varchar', NULL, 'case_cdadet_datasetvalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (907, 26, '最后更新时间', 'emr_lastupdatedttm', '3', NULL, 'varchar', NULL, 'case_cdadet_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (908, 26, '数据来源标识', 'emr_datasourceflag', '0', NULL, 'varchar', NULL, 'case_cdadet_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (909, 26, '接入源表', 'emr_dstable', '0', NULL, 'varchar', NULL, 'case_cdadet_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (910, 26, '接入数据源KEY', 'emr_dstablekey', '0', NULL, 'varchar', NULL, 'case_cdadet_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (911, 26, '接入数据源KEY值', 'emr_dstablevalue', '0', NULL, 'varchar', NULL, 'case_cdadet_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (912, 26, '病历分类编码', 'emr_templatecode', '0', NULL, 'varchar', NULL, 'case_cdadet_templatecode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (913, 26, '病历项编码', 'emr_datasetcode', '0', NULL, 'varchar', NULL, 'case_cdadet_datasetcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (914, 12, '最后更新日期', 'ordreport__lastupdatedttm', '3', NULL, 'varchar', NULL, 'report_main_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (915, 14, '最后更新日期', 'lis_lastupdatedttm', '3', NULL, 'varchar', NULL, 'report_labmeasuresub_lastupdatedttm', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (916, 17, '最后更新时间', 'adm_lastupdatedttm', '3', NULL, 'varchar', NULL, 'visit_info_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (917, 18, '最后更新时间', 'report_lastupdatedttm', '3', NULL, 'varchar', NULL, 'report_exammeasuresub_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (918, 14, '检验报告ID', 'lis_reportid', '6', NULL, 'varchar', NULL, 'report_labmeasuresub_applyno', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (919, 20, '诊断类别代码', 'diag_classcode', '0', NULL, 'varchar', NULL, 'diaginfo_diagclasscode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (920, 17, '医疗机构代码', 'adm_medorgcode', '5', NULL, 'varchar', NULL, 'visit_info_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (921, 17, '病人唯一编码', 'adm_persid', '0', NULL, 'varchar', NULL, 'visit_info_persid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (922, 17, '数据来源标识', 'adm_datasourceflag', '0', NULL, 'varchar', NULL, 'visit_info_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (923, 17, '接入数据源的表', 'adm_dstable', '0', NULL, 'varchar', NULL, 'visit_info_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (924, 17, '接入数据源的key', 'adm_dstablekey', '0', NULL, 'varchar', NULL, 'visit_info_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (925, 17, '接入数据源的key值', 'adm_dstablevalue', '0', NULL, 'varchar', NULL, 'visit_info_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (926, 26, '更新时间', 'emr_updatedttm', '0', NULL, 'varchar', NULL, 'case_cdadet_updatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (927, 18, '数据标识来源', 'or_datasourceflag', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (928, 14, '业务系统数据来源', 'lis_datasourceflag', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_datasourceflag', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (929, 14, 'rowkey', 'lis_rowkey', '0', NULL, 'varchar', NULL, 'rowkey', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (930, 12, 'key', 'ordreport_key', '0', NULL, 'varchar', NULL, 'mt_report_key', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (931, 17, '入病区时间', 'adm_inwarddttm', '0', NULL, 'varchar', NULL, 'visit_info_inwarddttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (932, 17, '出病区时间', 'adm_outwarddttm', '0', NULL, 'varchar', NULL, 'visit_info_outwarddttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (933, 19, '药品通用名', 'ord_genericdesc', '0', NULL, 'varchar', NULL, 'order_order_genericdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (934, 19, '医疗机构代码', 'ord_medorgcode', '5', NULL, 'varvhar', NULL, 'order_order_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (935, 19, '数据来源标识', 'ord_datasourceflag', '0', NULL, 'varchar', NULL, 'order_order_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (936, 19, '接入数据源的表', 'ord_dstable', '0', NULL, 'varvhar', NULL, 'order_order_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (937, 19, '接入数据源的key', 'ord_dstablekey', '0', NULL, 'varvhar', NULL, 'order_order_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (938, 19, '接入数据源的key值', 'ord_dstablevalue', '0', NULL, 'varvhar', NULL, 'order_order_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (939, 19, '医嘱表时间戳', 'ord_lastupdatedttm', '3', NULL, 'varvhar', NULL, 'order_order_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (940, 19, '医嘱名称编码', 'ord_code', '0', NULL, 'varchar', NULL, 'order_order_orderitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (941, 18, '医疗机构名称', 'or_medorgname', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (942, 12, '医疗机构名称', 'ordreport_medorgname', '0', NULL, 'varchar', NULL, 'report_main_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (953, 17, '登记号(住院号)', 'adm_persno', '0', NULL, 'varchar', NULL, 'visit_info_persno', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (958, 18, '超声检查项目名称', 'or_examsitemcnname', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examsitemcnname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (959, 18, '超声检查项目编码', 'or_examsitemcode', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_examsitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (960, 16, '病人唯一编码', 'pat_persid', '0', NULL, 'varchar', NULL, 'pers_basicinfo_persid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (961, 16, '医疗机构编码', 'pat_medorgcode', '5', NULL, 'varchar', NULL, 'pers_basicinfo_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (962, 16, '接入源表', 'pat_dstable', '0', NULL, 'varchar', NULL, 'pers_basicinfo_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (963, 16, '接入数据源key', 'pat_dstablekey', '0', NULL, 'varchar', NULL, 'pers_basicinfo_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (964, 16, '接入数据源key值', 'pat_dstablevalue', '0', NULL, 'varchar', NULL, 'pers_basicinfo_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (965, 16, '登记日期', 'pat_regdttm', '0', NULL, 'varchar', NULL, 'pers_basicinfo_regdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (966, 16, '数据来源标识', 'pat_datasourceflag', '0', NULL, 'varchar', NULL, 'pers_basicinfo_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (968, 17, '就诊ID(未处理)', 'adm_extstr3', '0', NULL, 'varchar', NULL, 'visit_info_extstr3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (969, 17, '就诊号(病案号)', 'adm_visitno', '0', NULL, 'varchar', NULL, 'visit_info_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (970, 12, '就诊号', 'ordreport_visitno', '0', NULL, 'varchar', NULL, 'report_main_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (971, 14, '就诊号', 'lis_visitno', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_visitno', NULL, 1, 0, 3, 0);
INSERT INTO `rp_rdr_field` VALUES (972, 18, '就诊号', 'or_visitno', '0', NULL, 'varchar', NULL, 'report_exammeasuresub_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (973, 19, '就诊号', 'ord_visitno', '0', NULL, 'varchar', NULL, 'order_order_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (974, 20, '就诊号', 'diag_visitno', '0', NULL, 'varchar', NULL, 'diaginfo_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (975, 26, '就诊号', 'emr_visitno', '0', NULL, 'varchar', NULL, 'case_cdadet_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (976, 17, '就诊卡号', 'adm_cardno', '0', NULL, 'varchar', NULL, 'visit_info_cardno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (985, 27, '就诊ID', 'lis_admid', '2', NULL, 'int', NULL, 'report_labmeasuresub_visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (986, 27, '患者唯一ID', 'empiid', '1', NULL, 'int', NULL, 'report_labmeasuresub_empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (988, 27, '检验子项编码', 'lis_itemcode', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labsitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (989, 27, '检验套名称', 'lis_groupitemname', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labgroupitemname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (990, 27, '检验套编码', 'lis_groupitemcode', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labgroupitemcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (991, 27, '标本类型', 'lis_specimentype', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_specimentype', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (992, 27, '标本来源说明', 'lis_specimensourcedesc', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_specimensourceexplain', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (993, 27, '采集数量', 'lis_samplingquantity', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_collectquantity', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (994, 27, '采集单位', 'lis_samplingunit', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_collectunit', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (995, 27, '采样时间', 'lis_samplingtime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_samplingdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (996, 27, '送检时间', 'lis_inspcttime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_inspectdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (997, 27, '报告时间', 'lis_reporttime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_reportdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (998, 27, '审核时间', 'lis_checktime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_checkdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (999, 27, '检验时间', 'lis_listime', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1000, 27, '检验项值', 'lis_itemvalue', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_resultproperty', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1001, 27, '单位', 'lis_itemunit', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_unit', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1002, 27, '检验项结果描述', 'lis_result', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labresulttextdesc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1003, 27, '参考范围', 'lis_range', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_referrange', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1004, 27, '异常标志', 'lis_retstatus', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_abnormalflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1005, 27, '医疗机构编码', 'lis_medorgcode', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1006, 27, '接入源表', 'lis_dstable', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1007, 27, '接入数据源key', 'lis_dstablekey', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1008, 27, '接入数据源key值', 'lis_dstablevalue', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1009, 27, '最后更新日期', 'lis_lastupdatedttm', '3', NULL, 'varchar', NULL, 'report_labmeasuresub_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1010, 27, '检验报告ID', 'lis_reportid', '6', NULL, 'varchar', NULL, 'report_labmeasuresub_applyno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1011, 27, '业务系统数据来源', 'lis_datasourceflag', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1012, 27, 'rowkey', 'lis_rowkey', '0', NULL, 'varchar', NULL, 'rowkey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1013, 27, '就诊号', 'lis_visitno', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1015, 27, '检验项名称', 'lis_itemname', '0', NULL, 'varchar', NULL, 'report_labmeasuresub_labsitemcnname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1016, 28, '患者唯一索引ID', 'K_EmpiID', '1', NULL, 'varchar', NULL, 'empiid', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1017, 29, '患者唯一索引ID', 'K_EmpiID', '1', NULL, 'varchar', NULL, 'empiid', NULL, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1018, 30, '医疗机构编码', 'ssjl_medorgcode', '0', NULL, 'varchar', NULL, 'medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1019, 30, '医疗机构名称', 'ssjl_medorgname', '0', NULL, 'varchar', NULL, 'medorgname', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1020, 30, '人员唯一标识ID', 'ssjl_empiid', '1', NULL, 'INT', NULL, 'empiid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1021, 30, '人员唯一标识号', 'ssjl_persuniqueflagno', '0', NULL, 'varchar', NULL, 'persuniqueflagno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1022, 30, '人员ID', 'ssjl_persid', '0', NULL, 'varchar', NULL, 'persid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1023, 30, '就诊id', 'ssjl_admid', '2', NULL, 'INT', NULL, 'visitid', NULL, 1, 1, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1024, 30, '人员号', 'ssjl_persno', '0', NULL, 'varchar', NULL, 'persno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1025, 30, '更新时间', 'ssjl_updatedttm', '0', NULL, 'varchar', NULL, 'updatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1026, 30, '病人姓名', 'ssjl_xm', '0', NULL, 'varchar', NULL, 'xm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1027, 30, '手术科室', 'ssjl_ks', '0', NULL, 'varchar', NULL, 'ks', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1028, 30, '手术时间', 'ssjl_sssj', '0', NULL, 'varchar', NULL, 'sssj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1029, 30, '住院号', 'ssjl_zyh', '0', NULL, 'varchar', NULL, 'zyh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1030, 30, '床号', 'ssjl_ch', '0', NULL, 'varchar', NULL, 'ch', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1031, 30, '主管医生', 'ssjl_zgys', '0', NULL, 'varchar', NULL, 'zgys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1032, 30, '术前诊断', 'ssjl_sqzd', '0', NULL, 'varchar', NULL, 'sqzd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1033, 30, '术后诊断', 'ssjl_shzd', '0', NULL, 'varchar', NULL, 'shzd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1034, 30, '手术名称', 'ssjl_ssmc1', '0', NULL, 'varchar', NULL, 'ssmc1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1035, 30, '手术发现', 'ssjl_ssfx', '0', NULL, 'varchar', NULL, 'ssfx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1036, 30, '手术程序', 'ssjl_sscx', '0', NULL, 'varchar', NULL, 'sscx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1037, 30, '麻醉医师姓名', 'ssjl_mzysxm', '0', NULL, 'varchar', NULL, 'mzysxm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1038, 30, '就诊号', 'ssjl_visitno', '2', NULL, 'varchar', NULL, 'visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1039, 30, '流水号', 'ssjl_serialno', '0', NULL, 'varchar', NULL, 'serialno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1040, 30, '性别', 'ssjl_xb', '0', NULL, 'varchar', NULL, 'xb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1041, 30, '年龄', 'xxjl_nl', '0', NULL, 'varchar', NULL, 'nl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1042, 30, '手术类型', 'ssjl_sslx', '0', NULL, 'varchar', NULL, 'sslx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1043, 30, '手术类别', 'ssjl_sslb', '0', NULL, 'varchar', NULL, 'sslb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1044, 30, '手术开始时间', 'ssjl_sskssj', '0', NULL, 'varchar', NULL, 'sskssj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1045, 30, '手术结束时间', 'ssjl_ssjssj', '0', NULL, 'varchar', NULL, 'ssjssj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1046, 30, '术前诊断编码', 'ssjl_sqzdbm', '0', NULL, 'varchar', NULL, 'sqzdbm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1047, 30, '术后诊断编码', 'ssjl_shzdbm', '0', NULL, 'varchar', NULL, 'shzdbm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1048, 30, '麻醉方式', 'ssjl_mzfs', '0', NULL, 'varchar', NULL, 'mzfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1049, 30, '手术名称2', 'ssjl_ssmc2', '0', NULL, 'varchar', NULL, 'ssmc2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1050, 30, '手术名称3', 'ssjl_ssmc3', '0', NULL, 'varchar', NULL, 'ssmc3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1051, 30, '手术名称4', 'ssjl_ssmc4', '0', NULL, 'varchar', NULL, 'ssmc4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1052, 30, '主刀医师签字', 'ssjl_zdysqz', '0', NULL, 'varchar', NULL, 'zdysqz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1053, 30, '本次手术是该病人此次住院第几次手术', 'ssjl_bcsssgbrcczydjcss', '0', NULL, 'varchar', NULL, 'bcsssgbrcczydjcss', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1054, 30, '本次手术是否为计划内手术', 'ssjl_bcsssfwjhnss', '0', NULL, 'varchar', NULL, 'bcsssfwjhnss', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1055, 30, '手术级别代码', 'ssjl_ssjbdm', '0', NULL, 'varchar', NULL, 'ssjbdm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1056, 30, '手术目标部位名称', 'ssjl_ssmbbwmc', '0', NULL, 'varchar', NULL, 'ssmbbwmc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1057, 30, '手术体位代码', 'ssjl_sstwdm', '0', NULL, 'varchar', NULL, 'sstwdm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1058, 30, '手术切口描述', 'ssjl_ssqkms', '0', NULL, 'varchar', NULL, 'ssqkms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1059, 30, '引流标志', 'ssjl_ylbz', '0', NULL, 'varchar', NULL, 'ylbz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1060, 30, '出血量-mL', 'ssjl_cxlm', '0', NULL, 'varchar', NULL, 'cxlm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1061, 30, '输液量-mL', 'ssjl_sylm', '0', NULL, 'varchar', NULL, 'sylm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1062, 30, '输血量-mL', 'ssjl_sxlm', '0', NULL, 'varchar', NULL, 'sxlm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1063, 30, '术前是否用抗菌药物', 'ssjl_sqsfykjyw', '0', NULL, 'varchar', NULL, 'sqsfykjyw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1064, 30, '术中追加抗菌药物', 'ssjl_szzjkjyw', '0', NULL, 'varchar', NULL, 'szzjkjyw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1065, 30, '引流材料名称', 'ssjl_ylclmc', '0', NULL, 'varchar', NULL, 'ylclmc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1066, 30, '引流材料数目', 'ssjl_ylclsm', '0', NULL, 'varchar', NULL, 'ylclsm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1067, 30, '引流放置部位', 'ssjl_ylfzbw', '0', NULL, 'varchar', NULL, 'ylfzbw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1068, 30, '麻醉分级', 'ssjl_mzfj', '0', NULL, 'varchar', NULL, 'mzfj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1069, 30, '手术持续时长', 'ssjl_sscxsc', '0', NULL, 'varchar', NULL, 'sscxsc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1070, 30, '是否有再次手术计划', 'ssjl_sfyzcssjh', '0', NULL, 'varchar', NULL, 'sfyzcssjh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1071, 30, 'Ⅱ助姓名', 'ssjl_llzxm', '0', NULL, 'varchar', NULL, 'llzxm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1072, 30, '签名日期时间', 'ssjl_qmrqsj', '0', NULL, 'varchar', NULL, 'qmrqsj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1073, 30, '数据来源', 'ssjl_datasourceflag', '0', NULL, 'varchar', NULL, 'datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1074, 30, '扩展1字符类型', 'ssjl_extstr1', '0', NULL, 'varchar', NULL, 'extstr1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1075, 30, '扩展2字符类型', 'ssjl_extstr2', '0', NULL, 'varchar', NULL, 'extstr2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1076, 30, '扩展3字符类型', 'ssjl_extstr3', '0', NULL, 'varchar', NULL, 'extstr3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1077, 30, '扩展4字符类型', 'ssjl_extstr4', '0', NULL, 'varchar', NULL, 'extstr4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1078, 30, '扩展5字符类型', 'ssjl_extstr5', '0', NULL, 'varchar', NULL, 'extstr5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1079, 30, '扩展6字符类型', 'ssjl_extstr6', '0', NULL, 'varchar', NULL, 'extstr6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1080, 30, '扩展7数值类型', 'ssjl_extnum1', '0', NULL, 'varchar', NULL, 'extnum1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1081, 30, '扩展8数值类型', 'ssjl_extnum2', '0', NULL, 'varchar', NULL, 'extnum2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1082, 30, '扩展9时间类型', 'ssjl_extdate1', '0', NULL, 'varchar', NULL, 'extdate1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1083, 30, '扩展10时间类型', 'ssjl_extdate2', '0', NULL, 'varchar', NULL, 'extdate2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1086, 30, '接入数据源的KEY值', 'ssjl_dstablevalue', '0', NULL, 'varchar', NULL, 'dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1087, 30, '是否物理删除 0=否 1=是', 'ssjl_isdeleted', '0', NULL, 'varchar', NULL, 'isdeleted', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1088, 30, '数据创建时间', 'ssjl_datacreatedttm', '0', NULL, 'varchar', NULL, 'datacreatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1089, 30, '最后更新时间', 'ssjl_lastupdatedttm', '0', NULL, 'varchar', NULL, 'lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1090, 30, '接入数据源的表', 'ssjl_dstable', '0', NULL, 'varchar', NULL, 'dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1091, 30, '接入数据源的KEY', 'ssjl_dstablekey', '0', NULL, 'varchar', NULL, 'dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1092, 30, '介入物名称', 'ssjl_jrwmc', '0', NULL, 'varchar', NULL, 'jrwmc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1093, 30, 'Ⅰ助姓名', 'ssjl_lzxm', '0', NULL, 'varchar', NULL, 'lzxm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1094, 21, '患者主索引', 'empiid', '0', NULL, 'int', NULL, 'case_nursingdet_empiid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1095, 21, '就诊号', 'case_nursingdet_admid', '0', NULL, 'varchar', NULL, 'case_nursingdet_visitid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1096, 21, '医疗机构编码', 'case_nursingdet_medorgcode', '0', NULL, 'varchar', NULL, 'case_nursingdet_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1097, 21, '创建日期', 'case_nursingdet_createdttm', '0', NULL, 'varchar', NULL, 'case_nursingdet_createdttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1098, 21, '数据来源标识', 'case_nursingdet_datasourceflag', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasourceflag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1099, 21, '接入数据源的表', 'case_nursingdet_dstable', '0', NULL, 'varchar', NULL, 'case_nursingdet_dstable', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1100, 21, '接入数据源的key', 'case_nursingdet_dstablekey', '0', NULL, 'varchar', NULL, 'case_nursingdet_dstablekey', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1101, 21, '接入数据源的key值', 'case_nursingdet_dstablevalue', '0', NULL, 'varchar', NULL, 'case_nursingdet_dstablevalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1102, 21, '医疗机构名称', 'case_nursingdet_medorgname', '0', NULL, 'varchar', NULL, 'case_nursingdet_medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1103, 21, '登记号', 'case_nursingdet_persno', '0', NULL, 'varchar', NULL, 'case_nursingdet_persno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1104, 21, '病案号', 'case_nursingdet_visitno', '0', NULL, 'varchar', NULL, 'case_nursingdet_visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1105, 21, '模板代码', 'case_nursingdet_templatecode', '0', NULL, 'varchar', NULL, 'case_nursingdet_templatecode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1106, 21, '模板名称', 'case_nursingdet_templatename', '0', NULL, 'varchar', NULL, 'case_nursingdet_templatename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1107, 21, '字段值', 'case_nursingdet_datasetvalue', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetvalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1108, 21, '字段编码', 'case_nursingdet_datasetcode', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1109, 21, '字段名称', 'case_nursingdet_datasetname', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1110, 21, '最后更新时间', 'case_nursingdet_lastupdatedttm', '0', NULL, 'varchar', NULL, 'case_nursingdet_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1111, 32, '医疗机构代码', 'ba_medorgcode', '0', NULL, 'varchar', NULL, 'ba_medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1112, 32, '医疗机构名称', 'medorgname', '0', NULL, 'varchar', NULL, 'medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1113, 32, '人员唯一标识ID', 'empiid', '0', NULL, 'int8', NULL, 'empiid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1117, 32, '就诊ID', 'visitid', '0', NULL, 'varchar', NULL, 'visitid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1118, 32, '就诊号', 'visitno', '0', NULL, 'varchar', NULL, 'visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1120, 32, '更新时间', 'ba_updatedttm', '0', NULL, 'varchar', NULL, 'ba_updatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1121, 32, '组织机构代码', 'zzjgdm', '0', NULL, 'varchar', NULL, 'zzjgdm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1122, 32, '登记号', 'djh', '0', NULL, 'varchar', NULL, 'djh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1123, 32, '病案号', 'bah', '0', NULL, 'varchar', NULL, 'bah', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1124, 32, '医疗保险支付方式', 'ylbxzffs', '0', NULL, 'varchar', NULL, 'ylbxzffs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1125, 32, '健康卡号', 'jkqh', '0', NULL, 'varchar', NULL, 'jkqh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1126, 32, '住院次数', 'zycs', '0', NULL, 'varchar', NULL, 'zycs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1127, 32, '姓名', 'xm', '0', NULL, 'varchar', NULL, 'xm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1128, 32, '性别', 'xb', '0', NULL, 'varchar', NULL, 'xb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1129, 32, '出生日期', 'csrq', '0', NULL, 'varchar', NULL, 'csrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1130, 32, '年龄', 'nl', '0', NULL, 'varchar', NULL, 'nl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1131, 32, '年龄（月）', 'nly', '0', NULL, 'varchar', NULL, 'nly', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1132, 32, '年龄（日）', 'nlr', '0', NULL, 'varchar', NULL, 'nlr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1133, 32, '国籍', 'gj', '0', NULL, 'varchar', NULL, 'gj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1134, 32, '新生儿出生体重', 'xsecstz', '0', NULL, 'varchar', NULL, 'xsecstz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1135, 32, '新生儿入院体重', 'xserytz', '0', NULL, 'varchar', NULL, 'xserytz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1136, 32, '出生地省份', 'csdsf', '0', NULL, 'text', NULL, 'csdsf', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1137, 32, '出生地（区、市）', 'csdqs', '0', NULL, 'text', NULL, 'csdqs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1138, 32, '出生地县', 'csdx', '0', NULL, 'text', NULL, 'csdx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1139, 32, '籍贯（省）', 'jgs', '0', NULL, 'text', NULL, 'jgs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1140, 32, '籍贯（区、市）', 'jgqs', '0', NULL, 'text', NULL, 'jgqs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1141, 32, '民族', 'mz', '0', NULL, 'varchar', NULL, 'mz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1142, 32, '身份证号', 'sfzh', '0', NULL, 'varchar', NULL, 'sfzh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1143, 32, '职业', 'zy', '0', NULL, 'varchar', NULL, 'zy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1144, 32, '婚姻', 'hy', '0', NULL, 'varchar', NULL, 'hy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1145, 32, '现住址（省）', 'xzzs', '0', NULL, 'text', NULL, 'xzzs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1146, 32, '现住址（区、市）', 'xzzqs', '0', NULL, 'text', NULL, 'xzzqs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1147, 32, '现住址（县）', 'xzzx', '0', NULL, 'text', NULL, 'xzzx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1148, 32, '现住址电话', 'xzzdh', '0', NULL, 'text', NULL, 'xzzdh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1149, 32, '现住址详细', 'xzzxx', '0', NULL, 'text', NULL, 'xzzxx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1150, 32, '现住址邮编', 'xzzyb', '0', NULL, 'text', NULL, 'xzzyb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1151, 32, '户口地址（省）', 'hkdzs', '0', NULL, 'text', NULL, 'hkdzs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1152, 32, '户口地址（区、市）', 'hkdzqs', '0', NULL, 'text', NULL, 'hkdzqs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1153, 32, '户口地址（县）', 'hkdzx', '0', NULL, 'text', NULL, 'hkdzx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1154, 32, '户口地址详细', 'hkdzxx', '0', NULL, 'text', NULL, 'hkdzxx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1155, 32, '户口邮编', 'hkyb', '0', NULL, 'varchar', NULL, 'hkyb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1156, 32, '工作单位及地址', 'gzdwjdz', '0', NULL, 'text', NULL, 'gzdwjdz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1157, 32, '工作电话', 'gzdh', '0', NULL, 'varchar', NULL, 'gzdh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1158, 32, '工作邮码', 'gzym', '0', NULL, 'varchar', NULL, 'gzym', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1159, 32, '联系人姓名', 'lxrxm', '0', NULL, 'varchar', NULL, 'lxrxm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1160, 32, '联系人关系', 'lxrgx', '0', NULL, 'varchar', NULL, 'lxrgx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1161, 32, '联系人地址', 'lxrdz', '0', NULL, 'text', NULL, 'lxrdz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1162, 32, '联系人电话', 'lxrdh', '0', NULL, 'varchar', NULL, 'lxrdh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1163, 32, '入院途径', 'rytj', '0', NULL, 'varchar', NULL, 'rytj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1164, 32, '是否由其他医疗机构转入', 'sfyqtyljgzr', '0', NULL, 'varchar', NULL, 'lxrdh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1165, 32, '入院途径其他', 'rytjqt', '0', NULL, 'varchar', NULL, 'rytj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1166, 32, '入院时间', 'rysj', '0', NULL, 'varchar', NULL, 'rysj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1167, 32, '入院科别', 'rykb', '0', NULL, 'varchar', NULL, 'rykb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1168, 32, '入病室', 'rbs', '0', NULL, 'varchar', NULL, 'rbs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1169, 32, '是否转科', 'sfzk', '0', NULL, 'varchar', NULL, 'sfzk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1170, 32, '转科科别', 'zkkb', '0', NULL, 'varchar', NULL, 'zkkb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1171, 32, '出院时间', 'cysj', '0', NULL, 'varchar', NULL, 'cysj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1172, 32, '死亡时间', 'swsj', '0', NULL, 'varchar', NULL, 'swsj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1173, 32, '出院科别', 'cykb', '0', NULL, 'varchar', NULL, 'cykb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1174, 32, '出病室', 'cbs', '0', NULL, 'varchar', NULL, 'cbs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1175, 32, '住院天数', 'zyts', '0', NULL, 'varchar', NULL, 'zyts', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1176, 32, '主要诊断确诊日期', 'zyzdqzrq', '0', NULL, 'varchar', NULL, 'zyzdqzrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1177, 32, '入院诊断', 'ryzd', '0', NULL, 'text', NULL, 'ryzd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1178, 32, '入院诊断编码', 'ryzdbm', '0', NULL, 'varchar', NULL, 'ryzdbm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1179, 32, '住院期间是否告病危或病重', 'zyqjsfgbwhbz', '0', NULL, 'varchar', NULL, 'zyqjsfgbwhbz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1180, 32, '入院时情况', 'rysqk', '0', NULL, 'text', NULL, 'rysqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1181, 32, '出院主要诊断', 'cyzyzd', '0', NULL, 'text', NULL, 'cyzyzd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1182, 32, '出院其他诊断1', 'cyqtzd1', '0', NULL, 'text', NULL, 'cyqtzd1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1183, 32, '出院其他诊断2', 'cyqtzd2', '0', NULL, 'text', NULL, 'cyqtzd2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1184, 32, '出院其他诊断3', 'cyqtzd3', '0', NULL, 'text', NULL, 'cyqtzd3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1185, 32, '出院其他诊断4', 'cyqtzd4', '0', NULL, 'text', NULL, 'cyqtzd4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1186, 32, '出院其他诊断5', 'cyqtzd5', '0', NULL, 'text', NULL, 'cyqtzd5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1187, 32, '出院其他诊断6', 'cyqtzd6', '0', NULL, 'text', NULL, 'cyqtzd6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1188, 32, '出院其他诊断7', 'cyqtzd7', '0', NULL, 'text', NULL, 'cyqtzd7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1189, 32, '出院其他诊断8', 'cyqtzd8', '0', NULL, 'text', NULL, 'cyqtzd8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1190, 32, '出院其他诊断9', 'cyqtzd9', '0', NULL, 'text', NULL, 'cyqtzd9', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1191, 32, '出院其他诊断10', 'cyqtzd10', '0', NULL, 'text', NULL, 'cyqtzd10', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1192, 32, '出院其他诊断11', 'cyqtzd11', '0', NULL, 'text', NULL, 'cyqtzd11', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1193, 32, '出院其他诊断12', 'cyqtzd12', '0', NULL, 'text', NULL, 'cyqtzd12', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1194, 32, '出院其他诊断13', 'cyqtzd13', '0', NULL, 'text', NULL, 'cyqtzd13', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1195, 32, '出院其他诊断14', 'cyqtzd14', '0', NULL, 'text', NULL, 'cyqtzd14', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1196, 32, '出院其他诊断15', 'cyqtzd15', '0', NULL, 'text', NULL, 'cyqtzd15', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1197, 32, '出院其他诊断16', 'cyqtzd16', '0', NULL, 'text', NULL, 'cyqtzd16', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1198, 32, '出院其他诊断17', 'cyqtzd17', '0', NULL, 'text', NULL, 'cyqtzd17', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1199, 32, '出院其他诊断18', 'cyqtzd18', '0', NULL, 'text', NULL, 'cyqtzd18', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1200, 32, '出院其他诊断19', 'cyqtzd19', '0', NULL, 'text', NULL, 'cyqtzd19', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1201, 32, '出院其他诊断20', 'cyqtzd20', '0', NULL, 'text', NULL, 'cyqtzd20', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1202, 32, '出院其他诊断21', 'cyqtzd21', '0', NULL, 'text', NULL, 'cyqtzd21', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1203, 32, '出院主要诊断编码', 'cyzyzdbm', '0', NULL, 'varchar', NULL, 'cyzyzdbm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1204, 32, '出院其他诊断编码1', 'cyqtzdbm1', '0', NULL, 'varchar', NULL, 'cyqtzdbm1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1205, 32, '出院其他诊断编码2', 'cyqtzdbm2', '0', NULL, 'varchar', NULL, 'cyqtzdbm2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1206, 32, '出院其他诊断编码3', 'cyqtzdbm3', '0', NULL, 'varchar', NULL, 'cyqtzdbm3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1207, 32, '出院其他诊断编码4', 'cyqtzdbm4', '0', NULL, 'varchar', NULL, 'cyqtzdbm4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1208, 32, '出院其他诊断编码5', 'cyqtzdbm5', '0', NULL, 'varchar', NULL, 'cyqtzdbm5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1209, 32, '出院其他诊断编码6', 'cyqtzdbm6', '0', NULL, 'varchar', NULL, 'cyqtzdbm6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1210, 32, '出院其他诊断编码7', 'cyqtzdbm7', '0', NULL, 'varchar', NULL, 'cyqtzdbm7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1211, 32, '出院其他诊断编码8', 'cyqtzdbm8', '0', NULL, 'varchar', NULL, 'cyqtzdbm8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1212, 32, '出院其他诊断编码9', 'cyqtzdbm9', '0', NULL, 'varchar', NULL, 'cyqtzdbm9', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1213, 32, '出院其他诊断编码10', 'cyqtzdbm10', '0', NULL, 'varchar', NULL, 'cyqtzdbm10', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1214, 32, '出院其他诊断编码11', 'cyqtzdbm11', '0', NULL, 'varchar', NULL, 'cyqtzdbm11', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1215, 32, '出院其他诊断编码12', 'cyqtzdbm12', '0', NULL, 'varchar', NULL, 'cyqtzdbm12', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1216, 32, '出院其他诊断编码13', 'cyqtzdbm13', '0', NULL, 'varchar', NULL, 'cyqtzdbm13', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1217, 32, '出院其他诊断编码14', 'cyqtzdbm14', '0', NULL, 'varchar', NULL, 'cyqtzdbm14', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1218, 32, '出院其他诊断编码15', 'cyqtzdbm15', '0', NULL, 'varchar', NULL, 'cyqtzdbm15', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1219, 32, '出院其他诊断编码16', 'cyqtzdbm16', '0', NULL, 'varchar', NULL, 'cyqtzdbm16', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1220, 32, '出院其他诊断编码17', 'cyqtzdbm17', '0', NULL, 'varchar', NULL, 'cyqtzdbm17', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1221, 32, '出院其他诊断编码18', 'cyqtzdbm18', '0', NULL, 'varchar', NULL, 'cyqtzdbm18', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1222, 32, '出院其他诊断编码19', 'cyqtzdbm19', '0', NULL, 'varchar', NULL, 'cyqtzdbm19', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1223, 32, '出院其他诊断编码20', 'cyqtzdbm20', '0', NULL, 'varchar', NULL, 'cyqtzdbm20', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1224, 32, '出院其他诊断编码21', 'cyqtzdbm21', '0', NULL, 'varchar', NULL, 'cyqtzdbm21', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1225, 32, '出院主要诊断（入院病情）', 'cyzyzdrybq', '0', NULL, 'text', NULL, 'cyzyzdrybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1226, 32, '出院其他诊断1（入院病情）', 'cyqtzd1rybq', '0', NULL, 'text', NULL, 'cyqtzd1rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1227, 32, '出院其他诊断2（入院病情）', 'cyqtzd2rybq', '0', NULL, 'text', NULL, 'cyqtzd2rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1228, 32, '出院其他诊断3（入院病情）', 'cyqtzd3rybq', '0', NULL, 'text', NULL, 'cyqtzd3rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1229, 32, '出院其他诊断4（入院病情）', 'cyqtzd4rybq', '0', NULL, 'text', NULL, 'cyqtzd4rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1230, 32, '出院其他诊断5（入院病情）', 'cyqtzd5rybq', '0', NULL, 'text', NULL, 'cyqtzd5rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1231, 32, '出院其他诊断6（入院病情）', 'cyqtzd6rybq', '0', NULL, 'text', NULL, 'cyqtzd6rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1232, 32, '出院其他诊断7（入院病情）', 'cyqtzd7rybq', '0', NULL, 'text', NULL, 'cyqtzd7rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1233, 32, '出院其他诊断8（入院病情）', 'cyqtzd8rybq', '0', NULL, 'text', NULL, 'cyqtzd8rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1234, 32, '出院其他诊断9（入院病情）', 'cyqtzd9rybq', '0', NULL, 'text', NULL, 'cyqtzd9rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1235, 32, '出院其他诊断10（入院病情）', 'cyqtzd10rybq', '0', NULL, 'text', NULL, 'cyqtzd10rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1236, 32, '出院其他诊断11（入院病情）', 'cyqtzd11rybq', '0', NULL, 'text', NULL, 'cyqtzd11rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1237, 32, '出院其他诊断12（入院病情）', 'cyqtzd12rybq', '0', NULL, 'text', NULL, 'cyqtzd12rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1238, 32, '出院其他诊断13（入院病情）', 'cyqtzd13rybq', '0', NULL, 'text', NULL, 'cyqtzd13rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1239, 32, '出院其他诊断14（入院病情）', 'cyqtzd14rybq', '0', NULL, 'text', NULL, 'cyqtzd14rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1240, 32, '出院其他诊断15（入院病情）', 'cyqtzd15rybq', '0', NULL, 'text', NULL, 'cyqtzd15rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1241, 32, '出院其他诊断16（入院病情）', 'cyqtzd16rybq', '0', NULL, 'text', NULL, 'cyqtzd16rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1242, 32, '出院其他诊断17（入院病情）', 'cyqtzd17rybq', '0', NULL, 'text', NULL, 'cyqtzd17rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1243, 32, '出院其他诊断18（入院病情）', 'cyqtzd18rybq', '0', NULL, 'text', NULL, 'cyqtzd18rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1244, 32, '出院其他诊断19（入院病情）', 'cyqtzd19rybq', '0', NULL, 'text', NULL, 'cyqtzd19rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1245, 32, '出院其他诊断20（入院病情）', 'cyqtzd20rybq', '0', NULL, 'text', NULL, 'cyqtzd20rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1246, 32, '出院其他诊断21（入院病情）', 'cyqtzd21rybq', '0', NULL, 'text', NULL, 'cyqtzd21rybq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1247, 32, '出院主要诊断（出院情况）', 'cyzyzdcyqk', '0', NULL, 'text', NULL, 'cyzyzdcyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1248, 32, '出院其他诊断1（出院情况）', 'cyqtzd1cyqk', '0', NULL, 'text', NULL, 'cyqtzd1cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1249, 32, '出院其他诊断2（出院情况）', 'cyqtzd2cyqk', '0', NULL, 'text', NULL, 'cyqtzd2cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1250, 32, '出院其他诊断3（出院情况）', 'cyqtzd3cyqk', '0', NULL, 'text', NULL, 'cyqtzd3cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1251, 32, '出院其他诊断4（出院情况）', 'cyqtzd4cyqk', '0', NULL, 'text', NULL, 'cyqtzd4cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1252, 32, '出院其他诊断5（出院情况）', 'cyqtzd5cyqk', '0', NULL, 'text', NULL, 'cyqtzd5cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1253, 32, '出院其他诊断6（出院情况）', 'cyqtzd6cyqk', '0', NULL, 'text', NULL, 'cyqtzd6cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1254, 32, '出院其他诊断7（出院情况）', 'cyqtzd7cyqk', '0', NULL, 'text', NULL, 'cyqtzd7cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1255, 32, '出院其他诊断8（出院情况）', 'cyqtzd8cyqk', '0', NULL, 'text', NULL, 'cyqtzd8cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1256, 32, '出院其他诊断9（出院情况）', 'cyqtzd9cyqk', '0', NULL, 'text', NULL, 'cyqtzd9cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1257, 32, '出院其他诊断10（出院情况）', 'cyqtzd10cyqk', '0', NULL, 'text', NULL, 'cyqtzd10cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1258, 32, '出院其他诊断11（出院情况）', 'cyqtzd11cyqk', '0', NULL, 'text', NULL, 'cyqtzd11cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1259, 32, '出院其他诊断12（出院情况）', 'cyqtzd12cyqk', '0', NULL, 'text', NULL, 'cyqtzd12cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1260, 32, '出院其他诊断13（出院情况）', 'cyqtzd13cyqk', '0', NULL, 'text', NULL, 'cyqtzd13cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1261, 32, '出院其他诊断14（出院情况）', 'cyqtzd14cyqk', '0', NULL, 'text', NULL, 'cyqtzd14cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1262, 32, '出院其他诊断15（出院情况）', 'cyqtzd15cyqk', '0', NULL, 'text', NULL, 'cyqtzd15cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1263, 32, '出院其他诊断16（出院情况）', 'cyqtzd16cyqk', '0', NULL, 'text', NULL, 'cyqtzd16cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1264, 32, '出院其他诊断17（出院情况）', 'cyqtzd17cyqk', '0', NULL, 'text', NULL, 'cyqtzd17cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1265, 32, '出院其他诊断18（出院情况）', 'cyqtzd18cyqk', '0', NULL, 'text', NULL, 'cyqtzd18cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1266, 32, '出院其他诊断19（出院情况）', 'cyqtzd19cyqk', '0', NULL, 'text', NULL, 'cyqtzd19cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1267, 32, '出院其他诊断20（出院情况）', 'cyqtzd20cyqk', '0', NULL, 'text', NULL, 'cyqtzd20cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1268, 32, '出院其他诊断21（出院情况）', 'cyqtzd21cyqk', '0', NULL, 'text', NULL, 'cyqtzd21cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1269, 32, '损伤、中毒外部原因', 'sszdwbyy', '0', NULL, 'text', NULL, 'sszdwbyy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1270, 32, '损伤、中毒编码', 'sszdbm', '0', NULL, 'varchar', NULL, 'sszdbm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1271, 32, '病理号1', 'blh1', '0', NULL, 'text', NULL, 'blh1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1272, 32, '病理号2', 'blh2', '0', NULL, 'text', NULL, 'blh2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1273, 32, '病理号3', 'blh3', '0', NULL, 'text', NULL, 'blh3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1274, 32, '病理诊断1', 'blzd1', '0', NULL, 'text', NULL, 'blzd1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1275, 32, '病理诊断2', 'blzd2', '0', NULL, 'text', NULL, 'blzd2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1276, 32, '病理诊断3', 'blzd3', '0', NULL, 'text', NULL, 'blzd3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1277, 32, '病理诊断疾病编码1', 'blzdjbbm1', '0', NULL, 'varchar', NULL, 'blzdjbbm1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1278, 32, '病理诊断疾病编码2', 'blzdjbbm2', '0', NULL, 'varchar', NULL, 'blzdjbbm2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1279, 32, '病理诊断疾病编码3', 'blzdjbbm3', '0', NULL, 'varchar', NULL, 'blzdjbbm3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1280, 32, '药物过敏', 'ywgm', '0', NULL, 'varchar', NULL, 'ywgm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1281, 32, '过敏药物', 'gmyw', '0', NULL, 'varchar', NULL, 'gmyw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1282, 32, '死亡患者尸检', 'swhzsj', '0', NULL, 'varchar', NULL, 'swhzsj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1283, 32, '血型', 'xx', '0', NULL, 'varchar', NULL, 'xx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1284, 32, 'RH', 'rh', '0', NULL, 'varchar', NULL, 'rh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1285, 32, '肿瘤分期', 'zlfq', '0', NULL, 'varchar', NULL, 'zlfq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1286, 32, '输血品种-红细胞', 'sxpz_hxb', '0', NULL, 'varchar', NULL, 'sxpz_hxb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1287, 32, '输血品种-血小板', 'sxpz_xxb', '0', NULL, 'varchar', NULL, 'sxpz_xxb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1288, 32, '输血品种-血浆', 'sxpz_xj', '0', NULL, 'varchar', NULL, 'sxpz_xj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1289, 32, '输血品种-全血', 'sxpz_qx', '0', NULL, 'varchar', NULL, 'sxpz_qx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1290, 32, '输血品种-自体回输', 'sxpz_zths', '0', NULL, 'varchar', NULL, 'sxpz_zths', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1291, 32, '输血品种-白蛋白', 'sxpz_bdb', '0', NULL, 'varchar', NULL, 'sxpz_bdb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1292, 32, '输血品种-冷沉淀', 'sxpz_lcd', '0', NULL, 'varchar', NULL, 'sxpz_lcd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1293, 32, '输血品种-其它', 'sxpz_qt', '0', NULL, 'varchar', NULL, 'sxpz_qt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1294, 32, '输血品种-输血反应', 'sxpz_sxfy', '0', NULL, 'varchar', NULL, 'sxpz_sxfy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1295, 32, '随诊', 'sz', '0', NULL, 'varchar', NULL, 'sz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1296, 32, '随诊期限周', 'szqxz', '0', NULL, 'varchar', NULL, 'szqxz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1297, 32, '随诊期限月', 'szqxy', '0', NULL, 'varchar', NULL, 'szqxy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1298, 32, '随诊期限年', 'szqxn', '0', NULL, 'varchar', NULL, 'szqxn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1299, 32, '科主任', 'kzr', '0', NULL, 'varchar', NULL, 'kzr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1300, 32, '主任（副主任）医师', 'zrfzrys', '0', NULL, 'varchar', NULL, 'zrfzrys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1301, 32, '主诊医师', 'zzys', '0', NULL, 'varchar', NULL, 'zzys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1302, 32, '主治医师', 'zzys1', '0', NULL, 'varchar', NULL, 'zzys1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1303, 32, '住院医师', 'zyys', '0', NULL, 'varchar', NULL, 'zyys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1304, 32, '责任护士', 'zrhs', '0', NULL, 'varchar', NULL, 'zrhs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1305, 32, '进修医师', 'jxys', '0', NULL, 'varchar', NULL, 'jxys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1306, 32, '实习医师', 'sxys', '0', NULL, 'varchar', NULL, 'sxys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1307, 32, '编码员', 'bmy', '0', NULL, 'varchar', NULL, 'bmy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1308, 32, '病案质量', 'bazl', '0', NULL, 'varchar', NULL, 'bazl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1309, 32, '质控医师', 'zkys', '0', NULL, 'varchar', NULL, 'zkys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1310, 32, '质控护士', 'zkhs', '0', NULL, 'varchar', NULL, 'zkhs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1311, 32, '质控日期', 'zkrq', '0', NULL, 'varchar', NULL, 'zkrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1312, 32, 'Ⅰ类手术切口预防性应用抗菌药物', 'ilssqkyfxyykjyw', '0', NULL, 'varchar', NULL, 'ilssqkyfxyykjyw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1313, 32, '使用持续时间', 'sycxsj', '0', NULL, 'varchar', NULL, 'sycxsj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1314, 32, '联合用药', 'lhyy', '0', NULL, 'varchar', NULL, 'lhyy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1315, 32, '是否急诊手术1', 'sfjzss1', '0', NULL, 'varchar', NULL, 'sfjzss1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1316, 32, '是否急诊手术2', 'sfjzss2', '0', NULL, 'varchar', NULL, 'sfjzss2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1317, 32, '是否急诊手术3', 'sfjzss3', '0', NULL, 'varchar', NULL, 'sfjzss3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1318, 32, '是否急诊手术4', 'sfjzss4', '0', NULL, 'varchar', NULL, 'sfjzss4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1319, 32, '是否急诊手术5', 'sfjzss5', '0', NULL, 'varchar', NULL, 'sfjzss5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1320, 32, '是否急诊手术6', 'sfjzss6', '0', NULL, 'varchar', NULL, 'sfjzss6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1321, 32, '是否急诊手术7', 'sfjzss7', '0', NULL, 'varchar', NULL, 'sfjzss7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1322, 32, '是否急诊手术8', 'sfjzss8', '0', NULL, 'varchar', NULL, 'sfjzss8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1323, 32, '是否主手术1', 'sfzss1', '0', NULL, 'varchar', NULL, 'sfzss1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1324, 32, '是否主手术2', 'sfzss2', '0', NULL, 'varchar', NULL, 'sfzss2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1325, 32, '是否主手术3', 'sfzss3', '0', NULL, 'varchar', NULL, 'sfzss3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1326, 32, '是否主手术4', 'sfzss4', '0', NULL, 'varchar', NULL, 'sfzss4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1327, 32, '是否主手术5', 'sfzss5', '0', NULL, 'varchar', NULL, 'sfzss5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1328, 32, '是否主手术6', 'sfzss6', '0', NULL, 'varchar', NULL, 'sfzss6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1329, 32, '是否主手术7', 'sfzss7', '0', NULL, 'varchar', NULL, 'sfzss7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1330, 32, '是否主手术8', 'sfzss8', '0', NULL, 'varchar', NULL, 'sfzss8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1331, 32, '手术及操作名称1', 'ssjczmc1', '0', NULL, 'varchar', NULL, 'ssjczmc1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1332, 32, '手术及操作名称2', 'ssjczmc2', '0', NULL, 'varchar', NULL, 'ssjczmc2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1333, 32, '手术及操作名称3', 'ssjczmc3', '0', NULL, 'varchar', NULL, 'ssjczmc3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1334, 32, '手术及操作名称4', 'ssjczmc4', '0', NULL, 'varchar', NULL, 'ssjczmc4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1335, 32, '手术及操作名称5', 'ssjczmc5', '0', NULL, 'varchar', NULL, 'ssjczmc5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1336, 32, '手术及操作名称6', 'ssjczmc6', '0', NULL, 'varchar', NULL, 'ssjczmc6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1337, 32, '手术及操作名称7', 'ssjczmc7', '0', NULL, 'varchar', NULL, 'ssjczmc7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1338, 32, '手术及操作名称8', 'ssjczmc8', '0', NULL, 'varchar', NULL, 'ssjczmc8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1339, 32, '手术及操作日期1', 'ssjczrq1', '0', NULL, 'varchar', NULL, 'ssjczrq1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1340, 32, '手术及操作日期2', 'ssjczrq2', '0', NULL, 'varchar', NULL, 'ssjczrq2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1341, 32, '手术及操作日期3', 'ssjczrq3', '0', NULL, 'varchar', NULL, 'ssjczrq3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1342, 32, '手术及操作日期4', 'ssjczrq4', '0', NULL, 'varchar', NULL, 'ssjczrq4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1343, 32, '手术及操作日期5', 'ssjczrq5', '0', NULL, 'varchar', NULL, 'ssjczrq5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1344, 32, '手术及操作日期6', 'ssjczrq6', '0', NULL, 'varchar', NULL, 'ssjczrq6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1345, 32, '手术及操作日期7', 'ssjczrq7', '0', NULL, 'varchar', NULL, 'ssjczrq7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1346, 32, '手术及操作日期8', 'ssjczrq8', '0', NULL, 'varchar', NULL, 'ssjczrq8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1347, 32, '手术及操作编码1', 'ssjczbm1', '0', NULL, 'varchar', NULL, 'ssjczbm1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1348, 32, '手术及操作编码2', 'ssjczbm2', '0', NULL, 'varchar', NULL, 'ssjczbm2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1349, 32, '手术及操作编码3', 'ssjczbm3', '0', NULL, 'varchar', NULL, 'ssjczbm3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1350, 32, '手术及操作编码4', 'ssjczbm4', '0', NULL, 'varchar', NULL, 'ssjczbm4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1351, 32, '手术及操作编码5', 'ssjczbm5', '0', NULL, 'varchar', NULL, 'ssjczbm5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1352, 32, '手术及操作编码6', 'ssjczbm6', '0', NULL, 'varchar', NULL, 'ssjczbm6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1353, 32, '手术及操作编码7', 'ssjczbm7', '0', NULL, 'varchar', NULL, 'ssjczbm7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1354, 32, '手术及操作编码8', 'ssjczbm8', '0', NULL, 'varchar', NULL, 'ssjczbm8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1355, 32, '手术级别1', 'ssjb1', '0', NULL, 'varchar', NULL, 'ssjb1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1356, 32, '手术级别2', 'ssjb2', '0', NULL, 'varchar', NULL, 'ssjb2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1357, 32, '手术级别3', 'ssjb3', '0', NULL, 'varchar', NULL, 'ssjb3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1358, 32, '手术级别4', 'ssjb4', '0', NULL, 'varchar', NULL, 'ssjb4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1359, 32, '手术级别5', 'ssjb5', '0', NULL, 'varchar', NULL, 'ssjb5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1360, 32, '手术级别6', 'ssjb6', '0', NULL, 'varchar', NULL, 'ssjb6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1361, 32, '手术级别7', 'ssjb7', '0', NULL, 'varchar', NULL, 'ssjb7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1362, 32, '手术级别8', 'ssjb8', '0', NULL, 'varchar', NULL, 'ssjb8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1363, 32, '手术及操作医师（术者）1', 'ssjczyssz1', '0', NULL, 'text', NULL, 'ssjczyssz1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1364, 32, '手术及操作医师（术者）2', 'ssjczyssz2', '0', NULL, 'text', NULL, 'ssjczyssz2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1365, 32, '手术及操作医师（术者）3', 'ssjczyssz3', '0', NULL, 'text', NULL, 'ssjczyssz3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1366, 32, '手术及操作医师（术者）4', 'ssjczyssz4', '0', NULL, 'text', NULL, 'ssjczyssz4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1367, 32, '手术及操作医师（术者）5', 'ssjczyssz5', '0', NULL, 'text', NULL, 'ssjczyssz5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1368, 32, '手术及操作医师（术者）6', 'ssjczyssz6', '0', NULL, 'text', NULL, 'ssjczyssz6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1369, 32, '手术及操作医师（术者）7', 'ssjczyssz7', '0', NULL, 'text', NULL, 'ssjczyssz7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1370, 32, '手术及操作医师（术者）8', 'ssjczyssz8', '0', NULL, 'text', NULL, 'ssjczyssz8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1371, 32, '手术及操作医师（I助）1', 'ssjczysiz1', '0', NULL, 'text', NULL, 'ssjczysiz1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1372, 32, '手术及操作医师（I助）2', 'ssjczysiz2', '0', NULL, 'text', NULL, 'ssjczysiz2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1373, 32, '手术及操作医师（I助）3', 'ssjczysiz3', '0', NULL, 'text', NULL, 'ssjczysiz3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1374, 32, '手术及操作医师（I助）4', 'ssjczysiz4', '0', NULL, 'text', NULL, 'ssjczysiz4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1375, 32, '手术及操作医师（I助）5', 'ssjczysiz5', '0', NULL, 'text', NULL, 'ssjczysiz5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1376, 32, '手术及操作医师（I助）6', 'ssjczysiz6', '0', NULL, 'text', NULL, 'ssjczysiz6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1377, 32, '手术及操作医师（I助）7', 'ssjczysiz7', '0', NULL, 'text', NULL, 'ssjczysiz7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1378, 32, '手术及操作医师（I助）8', 'ssjczysiz8', '0', NULL, 'text', NULL, 'ssjczysiz8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1379, 32, '手术及操作医师（II助）1', 'ssjczysiiz1', '0', NULL, 'text', NULL, 'ssjczysiiz1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1380, 32, '手术及操作医师（II助）2', 'ssjczysiiz2', '0', NULL, 'text', NULL, 'ssjczysiiz2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1381, 32, '手术及操作医师（II助）3', 'ssjczysiiz3', '0', NULL, 'text', NULL, 'ssjczysiiz3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1382, 32, '手术及操作医师（II助）4', 'ssjczysiiz4', '0', NULL, 'text', NULL, 'ssjczysiiz4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1383, 32, '手术及操作医师（II助）5', 'ssjczysiiz5', '0', NULL, 'text', NULL, 'ssjczysiiz5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1384, 32, '手术及操作医师（II助）6', 'ssjczysiiz6', '0', NULL, 'text', NULL, 'ssjczysiiz6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1385, 32, '手术及操作医师（II助）7', 'ssjczysiiz7', '0', NULL, 'text', NULL, 'ssjczysiiz7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1386, 32, '手术及操作医师（II助）8', 'ssjczysiiz8', '0', NULL, 'text', NULL, 'ssjczysiiz8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1387, 32, '切口愈合等级1', 'qkyhdj1', '0', NULL, 'varchar', NULL, 'qkyhdj1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1388, 32, '切口愈合等级2', 'qkyhdj2', '0', NULL, 'varchar', NULL, 'qkyhdj2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1389, 32, '切口愈合等级3', 'qkyhdj3', '0', NULL, 'varchar', NULL, 'qkyhdj3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1390, 32, '切口愈合等级4', 'qkyhdj4', '0', NULL, 'varchar', NULL, 'qkyhdj4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1391, 32, '切口愈合等级5', 'qkyhdj5', '0', NULL, 'varchar', NULL, 'qkyhdj5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1392, 32, '切口愈合等级6', 'qkyhdj6', '0', NULL, 'varchar', NULL, 'qkyhdj6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1393, 32, '切口愈合等级7', 'qkyhdj7', '0', NULL, 'varchar', NULL, 'qkyhdj7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1394, 32, '切口愈合等级8', 'qkyhdj8', '0', NULL, 'varchar', NULL, 'qkyhdj8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1395, 32, '麻醉方式1', 'mzfs1', '0', NULL, 'varchar', NULL, 'mzfs1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1396, 32, '麻醉方式2', 'mzfs2', '0', NULL, 'varchar', NULL, 'mzfs2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1397, 32, '麻醉方式3', 'mzfs3', '0', NULL, 'varchar', NULL, 'mzfs3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1398, 32, '麻醉方式4', 'mzfs4', '0', NULL, 'varchar', NULL, 'mzfs4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1399, 32, '麻醉方式5', 'mzfs5', '0', NULL, 'varchar', NULL, 'mzfs5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1400, 32, '麻醉方式6', 'mzfs6', '0', NULL, 'varchar', NULL, 'mzfs6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1401, 32, '麻醉方式7', 'mzfs7', '0', NULL, 'varchar', NULL, 'mzfs7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1402, 32, '麻醉方式8', 'mzfs8', '0', NULL, 'varchar', NULL, 'mzfs8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1403, 32, '麻醉医师1', 'mzys1', '0', NULL, 'varchar', NULL, 'mzys1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1404, 32, '麻醉医师2', 'mzys2', '0', NULL, 'varchar', NULL, 'mzys2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1405, 32, '麻醉医师3', 'mzys3', '0', NULL, 'varchar', NULL, 'mzys3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1406, 32, '麻醉医师4', 'mzys4', '0', NULL, 'varchar', NULL, 'mzys4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1407, 32, '麻醉医师5', 'mzys5', '0', NULL, 'varchar', NULL, 'mzys5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1408, 32, '麻醉医师6', 'mzys6', '0', NULL, 'varchar', NULL, 'mzys6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1409, 32, '麻醉医师7', 'mzys7', '0', NULL, 'varchar', NULL, 'mzys7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1410, 32, '麻醉医师8', 'mzys8', '0', NULL, 'varchar', NULL, 'mzys8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1411, 32, '是否实施临床路径管理', 'sfsslcljgl', '0', NULL, 'varchar', NULL, 'sfsslcljgl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1412, 32, '是否完成临床路径', 'sfwclclj', '0', NULL, 'varchar', NULL, 'sfwclclj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1413, 32, '退出原因', 'tcyy', '0', NULL, 'varchar', NULL, 'tcyy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1414, 32, '是否变异', 'sfby', '0', NULL, 'varchar', NULL, 'sfby', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1415, 32, '变异原因', 'byyy', '0', NULL, 'varchar', NULL, 'byyy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1416, 32, '离院方式', 'lyfs', '0', NULL, 'varchar', NULL, 'lyfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1417, 32, '是否因同一病种再入院', 'sfytybzzry', '0', NULL, 'varchar', NULL, 'sfytybzzry', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1418, 32, '与上次出院日期间隔天', 'ysccyrqjgt', '0', NULL, 'varchar', NULL, 'ysccyrqjgt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1419, 32, '医嘱转院，转出医疗机构', 'yzzy_zcyljg', '0', NULL, 'varchar', NULL, 'yzzy_zcyljg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1420, 32, '是否转到省外', 'sfzdsw', '0', NULL, 'varchar', NULL, 'sfzdsw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1421, 32, '省外医院', 'swyy', '0', NULL, 'varchar', NULL, 'swyy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1422, 32, '医嘱转社区卫生服务机构/乡镇卫生院', 'yzzsqwsfwjgxzwsy', '0', NULL, 'varchar', NULL, 'yzzsqwsfwjgxzwsy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1423, 32, '离院方式其他', 'lyfsqt', '0', NULL, 'varchar', NULL, 'lyfsqt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1424, 32, '拟接收医疗机构名称', 'njsyljgmc', '0', NULL, 'varchar', NULL, 'njsyljgmc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1425, 32, '检查情况-CT', 'jcqk_ct', '0', NULL, 'varchar', NULL, 'jcqk_ct', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1426, 32, '检查情况-PETCT', 'jcqk_petct', '0', NULL, 'varchar', NULL, 'jcqk_petct', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1427, 32, '检查情况-双源CT', 'jcqk_syct', '0', NULL, 'varchar', NULL, 'jcqk_syct', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1428, 32, '检查情况-B超', 'jcqk_bc', '0', NULL, 'varchar', NULL, 'jcqk_bc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1429, 32, '检查情况-X片', 'jcqk_xp', '0', NULL, 'varchar', NULL, 'jcqk_xp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1430, 32, '检查情况-超声心动图', 'jcqk_csxdt', '0', NULL, 'varchar', NULL, 'jcqk_csxdt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1431, 32, '检查情况-MRI', 'jcqk_mri', '0', NULL, 'varchar', NULL, 'jcqk_mri', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1432, 32, '检查情况-同位素检查', 'jcqk_twsjc', '0', NULL, 'varchar', NULL, 'jcqk_twsjc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1433, 32, '是否有31天内再住院计划', 'sfy31tnzzyjh', '0', NULL, 'varchar', NULL, 'sfy31tnzzyjh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1434, 32, '再住院目的', 'zzymd', '0', NULL, 'varchar', NULL, 'zzymd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1435, 32, '再住院目的其他', 'zzymdqt', '0', NULL, 'varchar', NULL, 'zzymdqt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1436, 32, '随访期限', 'sfqx', '0', NULL, 'varchar', NULL, 'sfqx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1437, 32, '颅脑损伤患者昏迷时间（入院后天）', 'lnsshzhmsjryht', '0', NULL, 'varchar', NULL, 'lnsshzhmsjryht', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1438, 32, '颅脑损伤患者昏迷时间（入院后小时）', 'lnsshzhmsjryhxs', '0', NULL, 'varchar', NULL, 'lnsshzhmsjryhxs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1439, 32, '颅脑损伤患者昏迷时间（入院后分钟）', 'lnsshzhmsjryhfz', '0', NULL, 'varchar', NULL, 'lnsshzhmsjryhfz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1440, 32, '颅脑损伤患者昏迷时间（入院前天）', 'lnsshzhmsjryqt', '0', NULL, 'varchar', NULL, 'lnsshzhmsjryqt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1441, 32, '颅脑损伤患者昏迷时间（入院前小时）', 'lnsshzhmsjryqxs', '0', NULL, 'varchar', NULL, 'lnsshzhmsjryqxs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1442, 32, '颅脑损伤患者昏迷时间（入院前分钟）', 'lnsshzhmsjryqfz', '0', NULL, 'varchar', NULL, 'lnsshzhmsjryqfz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1443, 32, '病人来源', 'brly', '0', NULL, 'varchar', NULL, 'brly', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1444, 32, '入住日期', 'rzrq', '0', NULL, 'varchar', NULL, 'rzrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1445, 32, '入住时间', 'rzsj', '0', NULL, 'varchar', NULL, 'rzsj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1446, 32, '入院后确诊天数', 'ryhqzts', '0', NULL, 'varchar', NULL, 'ryhqzts', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1447, 32, '死亡原因', 'swyy_1', '0', NULL, 'varchar', NULL, 'swyy_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1448, 32, '门诊与出院诊断符合情况', 'mzycyzdfhqk', '0', NULL, 'varchar', NULL, 'mzycyzdfhqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1449, 32, '入院与出院诊断符合情况', 'ryycyzdfhqk', '0', NULL, 'varchar', NULL, 'ryycyzdfhqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1450, 32, '术前与术后诊断符合情况', 'sqyshzdfhqk', '0', NULL, 'varchar', NULL, 'sqyshzdfhqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1451, 32, '临床与病理诊断符合情况', 'lcyblzdfhqk', '0', NULL, 'varchar', NULL, 'lcyblzdfhqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1452, 32, '放射与病理诊断符合情况', 'fsyblzdfhqk', '0', NULL, 'varchar', NULL, 'fsyblzdfhqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1453, 32, '抢救次数', 'qjcs', '0', NULL, 'varchar', NULL, 'qjcs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1454, 32, '抢救成功次数', 'qjcgcs', '0', NULL, 'varchar', NULL, 'qjcgcs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1455, 32, '有无感染', 'ywgr', '0', NULL, 'varchar', NULL, 'ywgr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1456, 32, '感染是否送病原学检查', 'grsfsbyxjc', '0', NULL, 'varchar', NULL, 'grsfsbyxjc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1457, 32, '病原菌', 'byj', '0', NULL, 'varchar', NULL, 'byj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1458, 32, '有无医院感染', 'ywyygr', '0', NULL, 'varchar', NULL, 'ywyygr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1459, 32, '医院感染名称', 'yygrmc', '0', NULL, 'varchar', NULL, 'yygrmc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1460, 32, '医院感染是否送病原学检查', 'yygrsfsbyxjc', '0', NULL, 'varchar', NULL, 'yygrsfsbyxjc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1461, 32, '特殊检查情况', 'tsjcqk', '0', NULL, 'varchar', NULL, 'tsjcqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1462, 32, '特殊检查情况1', 'tsjcqk1', '0', NULL, 'varchar', NULL, 'tsjcqk1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1463, 32, '特殊检查情况2', 'tsjcqk2', '0', NULL, 'varchar', NULL, 'tsjcqk2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1464, 32, '特殊检查情况3', 'tsjcqk3', '0', NULL, 'varchar', NULL, 'tsjcqk3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1465, 32, '是否对输血病人进行了输血前检查', 'sfdsxbrjxlsxqjc', '0', NULL, 'varchar', NULL, 'sfdsxbrjxlsxqjc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1466, 32, 'HbsAg', 'hbsag', '0', NULL, 'varchar', NULL, 'hbsag', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1467, 32, 'HCV-Ab', 'hcv_ab', '0', NULL, 'varchar', NULL, 'hcv_ab', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1468, 32, 'HIV-Ab', 'hiv_ab', '0', NULL, 'varchar', NULL, 'hiv_ab', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1469, 32, '示教病例', 'sjbl', '0', NULL, 'varchar', NULL, 'sjbl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1470, 32, '手术\\治疗\\检查\\诊断为本院第一例', 'sszljczdwbydyl', '0', NULL, 'varchar', NULL, 'sszljczdwbydyl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1471, 32, '办理入院时间', 'blrysj', '0', NULL, 'varchar', NULL, 'blrysj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1490, 33, '医疗机构代码', 'medorgcode', '0', NULL, 'varchar', NULL, 'medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1491, 33, '医疗机构名称', 'medorgname', '0', NULL, 'varchar', NULL, 'medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1492, 33, '人员唯一标识ID', 'empiid', '0', NULL, 'int8', NULL, 'empiid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1495, 33, '就诊ID', 'visitid', '0', NULL, 'int8', NULL, 'visitid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1496, 33, '就诊号', 'visitno', '0', NULL, 'varchar', NULL, 'visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1498, 33, '更新时间', 'updatedttm', '0', NULL, 'timestamp', NULL, 'updatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1499, 33, '登记号', 'djh', '0', NULL, 'varchar', NULL, 'djh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1500, 33, '科室', 'ks', '0', NULL, 'varchar', NULL, 'ks', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1501, 33, '病区', 'bq', '0', NULL, 'varchar', NULL, 'bq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1502, 33, '姓名', 'xm', '0', NULL, 'varchar', NULL, 'xm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1503, 33, '联系电话', 'lxdh', '0', NULL, 'varchar', NULL, 'lxdh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1504, 33, '婚配', 'hp', '0', NULL, 'varchar', NULL, 'hp', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1505, 33, '性别', 'xb', '0', NULL, 'varchar', NULL, 'xb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1506, 33, '年龄', 'nl', '0', NULL, 'varchar', NULL, 'nl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1507, 33, '病房号', 'bfh', '0', NULL, 'varchar', NULL, 'bfh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1508, 33, '床号', 'ch', '0', NULL, 'varchar', NULL, 'ch', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1509, 33, '民族', 'mz', '0', NULL, 'varchar', NULL, 'mz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1510, 33, '职业', 'zy', '0', NULL, 'varchar', NULL, 'zy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1511, 33, '出院情况', 'cyqk', '0', NULL, 'text', NULL, 'cyqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1512, 33, '患者出生日期', 'hzcsrq', '0', NULL, 'varchar', NULL, 'hzcsrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1513, 33, '入院日期', 'ryrq', '0', NULL, 'varchar', NULL, 'ryrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1514, 33, '出院日期', 'cyrq', '0', NULL, 'varchar', NULL, 'cyrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1515, 33, '住院天数', 'zyts', '0', NULL, 'varchar', NULL, 'zyts', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1516, 33, '入院诊断', 'ryzd', '0', NULL, 'varchar', NULL, 'ryzd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1517, 33, '病情及诊治经过（出院小结）', 'bqjzzjgcyxj', '0', NULL, 'text', NULL, 'bqjzzjgcyxj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1518, 33, '出院时症状与体征', 'cyszzytz', '0', NULL, 'text', NULL, 'cyszzytz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1519, 33, '出院诊断', 'cyzd', '0', NULL, 'text', NULL, 'cyzd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1520, 33, '出院医嘱', 'cyyz', '0', NULL, 'text', NULL, 'cyyz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1521, 33, '温馨提示', 'wxts', '0', NULL, 'varchar', NULL, 'wxts', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1522, 33, '医疗组长', 'ylzc', '0', NULL, 'varchar', NULL, 'ylzc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1523, 33, '医师签名', 'ysqm', '0', NULL, 'varchar', NULL, 'ysqm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1524, 33, '签名日期', 'qmrq', '0', NULL, 'varchar', NULL, 'qmrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1525, 33, '主任（副主任）医师', 'zrfzrys', '0', NULL, 'varchar', NULL, 'zrfzrys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1526, 33, '住院医师', 'zyys', '0', NULL, 'varchar', NULL, 'zyys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1527, 33, '主治医师', 'zzys', '0', NULL, 'varchar', NULL, 'zzys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1528, 33, '病理检查号', 'bljch', '0', NULL, 'varchar', NULL, 'bljch', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1529, 33, '超声心动图号', 'csxdth', '0', NULL, 'varchar', NULL, 'csxdth', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1530, 33, '心电图号', 'xdth', '0', NULL, 'varchar', NULL, 'xdth', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1531, 33, 'mri号', 'mrih', '0', NULL, 'text', NULL, 'mrih', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1532, 33, 'ct号', 'cth', '0', NULL, 'varchar', NULL, 'cth', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1533, 33, 'x光照片号', 'xgzph', '0', NULL, 'varchar', NULL, 'xgzph', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1535, 34, '医疗机构代码', 'medorgcode', '0', NULL, 'varchar', NULL, 'medorgcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1536, 34, '医疗机构名称', 'medorgname', '0', NULL, 'varchar', NULL, 'medorgname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1537, 34, '人员唯一id', 'empiid', '0', NULL, 'int8', NULL, 'empiid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1540, 34, '就诊ID', 'visitid', '0', NULL, 'varchar', NULL, 'visitid', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1541, 34, '就诊号', 'visitno', '0', NULL, 'int8', NULL, 'visitno', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1544, 34, '一般情况-科室', 'ybqk_ks', '0', NULL, 'varchar', NULL, 'ybqk_ks', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1545, 34, '一般情况-病区', 'ybqk_bq', '0', NULL, 'text', NULL, 'ybqk_bq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1546, 34, '一般情况-床号', 'ybqk_ch', '0', NULL, 'text', NULL, 'ybqk_ch', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1547, 34, '登记号', 'djh', '0', NULL, 'text', NULL, 'djh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1548, 34, '姓名', 'xm', '0', NULL, 'text', NULL, 'xm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1549, 34, '性别', 'xb', '0', NULL, 'text', NULL, 'xb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1550, 34, '年龄', 'nl', '0', NULL, 'text', NULL, 'nl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1551, 34, '职业', 'zy', '0', NULL, 'text', NULL, 'zy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1552, 34, '国籍', 'gj', '0', NULL, 'text', NULL, 'gj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1553, 34, '民族', 'mz', '0', NULL, 'text', NULL, 'mz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1554, 34, '籍贯', 'jg', '0', NULL, 'text', NULL, 'jg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1555, 34, '婚姻', 'hy', '0', NULL, 'text', NULL, 'hy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1556, 34, '家庭住址', 'jtzz', '0', NULL, 'text', NULL, 'jtzz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1557, 34, '出生地省份', 'csdsf', '0', NULL, 'text', NULL, 'csdsf', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1558, 34, '出生地县', 'csdx', '0', NULL, 'text', NULL, 'csdx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1559, 34, '入院日期', 'ryrq', '0', NULL, 'text', NULL, 'ryrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1560, 34, '记录时间', 'jlsj', '0', NULL, 'text', NULL, 'jlsj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1561, 34, '一般情况-病史叙述者关系', 'ybqk_bsxszgx', '0', NULL, 'text', NULL, 'ybqk_bsxszgx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1562, 34, '一般情况-病史叙述者姓名', 'ybqk_bsxszxm', '0', NULL, 'text', NULL, 'ybqk_bsxszxm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1563, 34, '一般情况-可靠程度', 'ybqk_kkcd', '0', NULL, 'text', NULL, 'ybqk_kkcd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1564, 34, '主诉', 'zs', '0', NULL, 'text', NULL, 'zs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1565, 34, '现病史', 'xbs', '0', NULL, 'text', NULL, 'xbs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1566, 34, '既往史-总体描述', 'jws_ztms', '0', NULL, 'text', NULL, 'jws_ztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1567, 34, '既往史-一般健康状况标志', 'jws_ybjkzkbz', '0', NULL, 'text', NULL, 'jws_ybjkzkbz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1568, 34, '既往史-患者传染性标志', 'jws_hzcrxbz', '0', NULL, 'text', NULL, 'jws_hzcrxbz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1569, 34, '既往史-平素健康状况', 'jws_psjkzk', '0', NULL, 'text', NULL, 'jws_psjkzk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1570, 34, '既往史-传染病史', 'jws_crbs', '0', NULL, 'text', NULL, 'jws_crbs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1571, 34, '既往史-预防接种史', 'jws_yfjzs', '0', NULL, 'text', NULL, 'jws_yfjzs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1572, 34, '既往史-过敏史', 'jws_gms', '0', NULL, 'text', NULL, 'jws_gms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1573, 34, '既往史-疾病史(含外伤)', 'jws_jbs', '0', NULL, 'text', NULL, 'jws_jbs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1574, 34, '既往史-手术史', 'jws_sss', '0', NULL, 'text', NULL, 'jws_sss', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1575, 34, '既往史-输血史', 'jws_sxs', '0', NULL, 'text', NULL, 'jws_sxs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1576, 34, '既往史-其它', 'jws_qt', '0', NULL, 'text', NULL, 'jws_qt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1577, 34, '系统回顾', 'xthg', '0', NULL, 'text', NULL, 'xthg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1578, 34, '个人史-总体描述', 'grs_ztms', '0', NULL, 'text', NULL, 'grs_ztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1579, 34, '个人史-长期居留地', 'grs_cqjld', '0', NULL, 'text', NULL, 'grs_cqjld', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1580, 34, '个人史-地方病地区居住情况', 'grs_dfbdqjzqk', '0', NULL, 'text', NULL, 'grs_dfbdqjzqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1581, 34, '个人史-牧区及疫区', 'grs_mqjyq', '0', NULL, 'text', NULL, 'grs_mqjyq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1582, 34, '个人史-冶游史', 'grs_yys', '0', NULL, 'text', NULL, 'grs_yys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1583, 34, '个人史-职业与工作条件有无工业毒物、粉尘、放射性物质及接触史', 'grs_zyygztjywgydwfcfsxwzjjcs', '0', NULL, 'text', NULL, 'grs_zyygztjywgydwfcfsxwzjjcs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1584, 34, '个人史-嗜烟', 'grs_sy', '0', NULL, 'text', NULL, 'grs_sy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1585, 34, '个人史-嗜烟时间（年）', 'grs_sysjn', '0', NULL, 'text', NULL, 'grs_sysjn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1586, 34, '个人史-嗜烟平均数（支/日）', 'grs_sypjsz', '0', NULL, 'text', NULL, 'grs_sypjsz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1587, 34, '个人史-戒烟', 'grs_jy', '0', NULL, 'text', NULL, 'grs_jy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1588, 34, '个人史-戒烟时间（年）', 'grs_jysjn', '0', NULL, 'text', NULL, 'grs_jysjn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1589, 34, '个人史-嗜酒', 'grs_sj', '0', NULL, 'text', NULL, 'grs_sj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1590, 34, '个人史-嗜酒时间（年）', 'grs_sjsjn', '0', NULL, 'text', NULL, 'grs_sjsjn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1591, 34, '个人史-嗜酒平均数（g/日）', 'grs_sjpjsg', '0', NULL, 'text', NULL, 'grs_sjpjsg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1592, 34, '个人史-戒酒', 'grs_jj', '0', NULL, 'text', NULL, 'grs_jj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1593, 34, '个人史-戒酒时间（年）', 'grs_jjsjn', '0', NULL, 'text', NULL, 'grs_jjsjn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1594, 34, '个人史-药物', 'grs_yw', '0', NULL, 'text', NULL, 'grs_yw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1595, 34, '个人史-药物时间（年）', 'grs_ywsjn', '0', NULL, 'text', NULL, 'grs_ywsjn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1596, 34, '个人史-吸毒', 'grs_xd', '0', NULL, 'text', NULL, 'grs_xd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1597, 34, '婚姻史', 'hys', '0', NULL, 'text', NULL, 'hys', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1598, 34, '月经及生育史-描述', 'yjjsys_ms', '0', NULL, 'text', NULL, 'yjjsys_ms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1599, 34, '月经及生育史-初潮年龄', 'yjjsys_ccnl', '0', NULL, 'text', NULL, 'yjjsys_ccnl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1600, 34, '月经及生育史-行经天数', 'yjjsys_xjts', '0', NULL, 'text', NULL, 'yjjsys_xjts', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1601, 34, '月经及生育史-月经周期', 'yjjsys_yjzq', '0', NULL, 'text', NULL, 'yjjsys_yjzq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1602, 34, '月经及生育史-末次月经日期', 'yjjsys_mcyjrq', '0', NULL, 'text', NULL, 'yjjsys_mcyjrq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1603, 34, '月经及生育史-绝经年龄', 'yjjsys_jjnl', '0', NULL, 'text', NULL, 'yjjsys_jjnl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1604, 34, '家族史总体描述', 'jzsztms', '0', NULL, 'text', NULL, 'jzsztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1605, 34, '家族史-父亲', 'jzs_fq', '0', NULL, 'text', NULL, 'jzs_fq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1606, 34, '家族史-父亲-死因', 'jzs_fq_sy', '0', NULL, 'text', NULL, 'jzs_fq_sy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1607, 34, '家族史-父亲-所患疾病', 'jzs_fq_shjb', '0', NULL, 'text', NULL, 'jzs_fq_shjb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1608, 34, '家族史-母亲', 'jzs_mq', '0', NULL, 'text', NULL, 'jzs_mq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1609, 34, '家族史-母亲-死因', 'jzs_mq_sy', '0', NULL, 'text', NULL, 'jzs_mq_sy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1610, 34, '家族史-母亲-所患疾病', 'jzs_mq_shjb', '0', NULL, 'text', NULL, 'jzs_mq_shjb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1611, 34, '家族史-兄弟姐妹及子女', 'jzs_xdjmjzn', '0', NULL, 'text', NULL, 'jzs_xdjmjzn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1612, 34, '家族史-其他', 'jzs_qt', '0', NULL, 'text', NULL, 'jzs_qt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1613, 34, '体格检查-体温', 'tgjc_tw', '0', NULL, 'text', NULL, 'tgjc_tw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1614, 34, '体格检查-脉搏', 'tgjc_mb', '0', NULL, 'text', NULL, 'tgjc_mb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1615, 34, '体格检查-呼吸', 'tgjc_hx', '0', NULL, 'text', NULL, 'tgjc_hx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1616, 34, '体格检查-血压-舒张压', 'tgjc_xy_szy', '0', NULL, 'text', NULL, 'tgjc_xy_szy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1617, 34, '体格检查-血压-收缩压', 'tgjc_xy_ssy', '0', NULL, 'text', NULL, 'tgjc_xy_ssy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1618, 34, '体格检查-体重', 'tgjc_tz', '0', NULL, 'text', NULL, 'tgjc_tz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1619, 34, '体格检查-身高', 'tgjc_sg', '0', NULL, 'text', NULL, 'tgjc_sg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1620, 34, '体格检查-发育', 'tgjc_fy', '0', NULL, 'text', NULL, 'tgjc_fy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1621, 34, '体格检查-营养', 'tgjc_yy', '0', NULL, 'text', NULL, 'tgjc_yy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1622, 34, '体格检查-表情', 'tgjc_bq', '0', NULL, 'text', NULL, 'tgjc_bq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1623, 34, '体格检查-步态', 'tgjc_bt', '0', NULL, 'text', NULL, 'tgjc_bt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1624, 34, '体格检查-面容', 'tgjc_mr', '0', NULL, 'text', NULL, 'tgjc_mr', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1625, 34, '体格检查-面容-其他面容描述', 'tgjc_mr_qtmrms', '0', NULL, 'text', NULL, 'tgjc_mr_qtmrms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1626, 34, '体格检查-体位', 'tgjc_1', '0', NULL, 'text', NULL, 'tgjc_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1627, 34, '体格检查-体位-其他体位描述', 'tgjc_tw_qttwms', '0', NULL, 'text', NULL, 'tgjc_tw_qttwms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1628, 34, '体格检查-神志', 'tgjc_sz', '0', NULL, 'text', NULL, 'tgjc_sz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1629, 34, '体格检查-配合检查', 'tgjc_phjc', '0', NULL, 'text', NULL, 'tgjc_phjc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1630, 34, '皮肤粘膜-总体描述', 'pfzm_ztms', '0', NULL, 'text', NULL, 'pfzm_ztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1631, 34, '皮肤粘膜-色泽', 'pfzm_sz', '0', NULL, 'text', NULL, 'pfzm_sz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1632, 34, '皮肤粘膜-皮疹', 'pfzm_pz', '0', NULL, 'text', NULL, 'pfzm_pz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1633, 34, '皮肤粘膜-皮下出血', 'pfzm_pxcx', '0', NULL, 'text', NULL, 'pfzm_pxcx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1634, 34, '皮肤粘膜-毛发分布', 'pfzm_mffb', '0', NULL, 'text', NULL, 'pfzm_mffb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1635, 34, '皮肤粘膜-温度与湿度', 'pfzm_wdysd', '0', NULL, 'text', NULL, 'pfzm_wdysd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1636, 34, '皮肤粘膜-水肿', 'pfzm_1', '0', NULL, 'text', NULL, 'pfzm_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1637, 34, '皮肤粘膜-淋巴结', 'pfzm_lbj', '0', NULL, 'text', NULL, 'pfzm_lbj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1638, 34, '皮肤粘膜-肝掌', 'pfzm_gz', '0', NULL, 'text', NULL, 'pfzm_gz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1639, 34, '皮肤粘膜-蜘蛛痣', 'pfzm_zzz', '0', NULL, 'text', NULL, 'pfzm_zzz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1640, 34, '皮肤粘膜-其他表现', 'pfzm_qtbx', '0', NULL, 'text', NULL, 'pfzm_qtbx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1641, 34, '头部总体描述', 'tbztms', '0', NULL, 'text', NULL, 'tbztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1642, 34, '头部-头颅', 'tb_tl', '0', NULL, 'text', NULL, 'tb_tl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1643, 34, '头部-眼', 'tb_y', '0', NULL, 'text', NULL, 'tb_y', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1644, 34, '头部-耳', 'tb_e', '0', NULL, 'text', NULL, 'tb_e', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1645, 34, '头部-鼻', 'tb_b', '0', NULL, 'text', NULL, 'tb_b', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1646, 34, '头部-咽喉', 'tb_yh', '0', NULL, 'text', NULL, 'tb_yh', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1647, 34, '头部-咽喉声音', 'tb_yhsy', '0', NULL, 'text', NULL, 'tb_yhsy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1648, 34, '头部-口腔', 'tb_kq', '0', NULL, 'text', NULL, 'tb_kq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1649, 34, '头部-扁桃体', 'tb_btt', '0', NULL, 'text', NULL, 'tb_btt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1650, 34, '头部-扁桃体左', 'tb_bttz', '0', NULL, 'text', NULL, 'tb_bttz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1651, 34, '头部-扁桃体右', 'tb_btty', '0', NULL, 'text', NULL, 'tb_btty', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1652, 34, '头部-扁桃体表面', 'tb_bttbm', '0', NULL, 'text', NULL, 'tb_bttbm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1653, 34, '颈部-整体描述', 'jb_ztms', '0', NULL, 'text', NULL, 'jb_ztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1654, 34, '颈部-颈部运动', 'jb_jbyd', '0', NULL, 'text', NULL, 'jb_jbyd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1655, 34, '颈部-气管', 'jb_qg', '0', NULL, 'text', NULL, 'jb_qg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1656, 34, '颈部-颈静脉', 'jb_jjm', '0', NULL, 'text', NULL, 'jb_jjm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1657, 34, '颈部-颈静脉回流征', 'jb_jjmhlz', '0', NULL, 'text', NULL, 'jb_jjmhlz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1658, 34, '颈部-颈动脉', 'jb_jdm', '0', NULL, 'text', NULL, 'jb_jdm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1659, 34, '颈部-甲状腺', 'jb_jzx', '0', NULL, 'text', NULL, 'jb_jzx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1660, 34, '胸部总体描述', 'xbztms', '0', NULL, 'text', NULL, 'xbztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1661, 34, '胸部-乳房', 'xb_rf', '0', NULL, 'text', NULL, 'xb_rf', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1662, 34, '胸部-胸廓', 'xb_xk', '0', NULL, 'text', NULL, 'xb_xk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1663, 34, '肺部-视诊', 'fb_sz', '0', NULL, 'text', NULL, 'fb_sz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1664, 34, '肺部-触诊', 'fb_cz', '0', NULL, 'text', NULL, 'fb_cz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1665, 34, '肺部-叩诊', 'fb_kz', '0', NULL, 'text', NULL, 'fb_kz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1666, 34, '肺部-听诊', 'fb_tz', '0', NULL, 'text', NULL, 'fb_tz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1667, 34, '心脏-视诊', 'xz_sz', '0', NULL, 'text', NULL, 'xz_sz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1668, 34, '心脏-触诊', 'xz_cz', '0', NULL, 'text', NULL, 'xz_cz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1669, 34, '心脏-叩诊', 'xz_kz', '0', NULL, 'text', NULL, 'xz_kz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1670, 34, '心脏-听诊-心率', 'xz_tz_xl', '0', NULL, 'text', NULL, 'xz_tz_xl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1671, 34, '心脏-听诊-心律', 'xz_1', '0', NULL, 'text', NULL, 'xz_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1672, 34, '心脏-听诊-A2P2', 'xz_tz_a2p2', '0', NULL, 'text', NULL, 'xz_tz_a2p2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1673, 34, '心脏-听诊-心音S1', 'xz_tz_xys1', '0', NULL, 'text', NULL, 'xz_tz_xys1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1674, 34, '心脏-听诊-心音S2', 'xz_tz_xys2', '0', NULL, 'text', NULL, 'xz_tz_xys2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1675, 34, '心脏-听诊-心音S3', 'xz_tz_xys3', '0', NULL, 'text', NULL, 'xz_tz_xys3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1676, 34, '心脏-听诊-奔马律', 'xz_tz_bml', '0', NULL, 'text', NULL, 'xz_tz_bml', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1677, 34, '心脏-听诊-开瓣音', 'xz_tz_kby', '0', NULL, 'text', NULL, 'xz_tz_kby', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1678, 34, '心脏-听诊-心包摩擦音', 'xz_tz_xbmcy', '0', NULL, 'text', NULL, 'xz_tz_xbmcy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1679, 34, '心脏-杂音', 'xz_zy', '0', NULL, 'text', NULL, 'xz_zy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1680, 34, '心脏-其他', 'xz_qt', '0', NULL, 'text', NULL, 'xz_qt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1681, 34, '心脏-周围血管征', 'xz_zwxgz', '0', NULL, 'text', NULL, 'xz_zwxgz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1682, 34, '心脏-听诊-左侧II', 'xz_tz_zcii', '0', NULL, 'text', NULL, 'xz_tz_zcii', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1683, 34, '心脏-听诊-左侧III', 'xz_tz_zciii', '0', NULL, 'text', NULL, 'xz_tz_zciii', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1684, 34, '心脏-听诊-左侧IV', 'xz_tz_zciv', '0', NULL, 'text', NULL, 'xz_tz_zciv', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1685, 34, '心脏-听诊-左侧V', 'xz_tz_zcv', '0', NULL, 'text', NULL, 'xz_tz_zcv', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1686, 34, '心脏-听诊-左锁骨中线距前正中线', 'xz_tz_zsgzxjqzzx', '0', NULL, 'text', NULL, 'xz_tz_zsgzxjqzzx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1687, 34, '心脏-听诊-右侧II', 'xz_tz_ycii', '0', NULL, 'text', NULL, 'xz_tz_ycii', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1688, 34, '心脏-听诊-右侧III', 'xz_tz_yciii', '0', NULL, 'text', NULL, 'xz_tz_yciii', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1689, 34, '心脏-听诊-右侧IV', 'xz_tz_yciv', '0', NULL, 'text', NULL, 'xz_tz_yciv', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1690, 34, '心脏-听诊-右侧V', 'xz_tz_ycv', '0', NULL, 'text', NULL, 'xz_tz_ycv', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1691, 34, '腹部总体描述', 'fbztms', '0', NULL, 'text', NULL, 'fbztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1692, 34, '腹部-视诊-外形', 'fb_sz_wx', '0', NULL, 'text', NULL, 'fb_sz_wx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1693, 34, '腹部-视诊-腹围', 'fb_sz_fw', '0', NULL, 'text', NULL, 'fb_sz_fw', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1694, 34, '腹部-视诊-腹式呼吸', 'fb_sz_fshx', '0', NULL, 'text', NULL, 'fb_sz_fshx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1695, 34, '腹部-视诊-胃形', 'fb_sz_wx_1', '0', NULL, 'text', NULL, 'fb_sz_wx_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1696, 34, '腹部-视诊-肠形', 'fb_sz_cx', '0', NULL, 'text', NULL, 'fb_sz_cx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1697, 34, '腹部-视诊-脐部', 'fb_sz_qb', '0', NULL, 'text', NULL, 'fb_sz_qb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1698, 34, '腹部-视诊-腹壁静脉曲张', 'fb_sz_fbjmqz', '0', NULL, 'text', NULL, 'fb_sz_fbjmqz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1699, 34, '腹部-视诊-腹壁其他情况', 'fb_sz_fbqtqk', '0', NULL, 'text', NULL, 'fb_sz_fbqtqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1700, 34, '腹部-触诊', 'fb_cz_1', '0', NULL, 'text', NULL, 'fb_cz_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1701, 34, '腹部-压痛反跳痛', 'fb_ytftt', '0', NULL, 'text', NULL, 'fb_ytftt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1702, 34, '腹部-波动感', 'fb_bdg', '0', NULL, 'text', NULL, 'fb_bdg', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1703, 34, '腹部-振水声', 'fb_zss', '0', NULL, 'text', NULL, 'fb_zss', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1704, 34, '腹部-腹部包块', 'fb_fbbk', '0', NULL, 'text', NULL, 'fb_fbbk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1705, 34, '腹部-肝脏', 'fb_gz', '0', NULL, 'text', NULL, 'fb_gz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1706, 34, '腹部-胆囊', 'fb_dn', '0', NULL, 'text', NULL, 'fb_dn', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1707, 34, '腹部-脾脏', 'fb_pz', '0', NULL, 'text', NULL, 'fb_pz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1708, 34, '腹部-肾脏', 'fb_sz_1', '0', NULL, 'text', NULL, 'fb_sz_1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1709, 34, '腹部-输尿管压痛点', 'fb_sngytd', '0', NULL, 'text', NULL, 'fb_sngytd', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1710, 34, '腹部-叩诊肝浊音界', 'fb_kzgzyj', '0', NULL, 'text', NULL, 'fb_kzgzyj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1711, 34, '腹部-肝上界位于锁骨中线第几肋间', 'fb_gsjwysgzxdjlj', '0', NULL, 'text', NULL, 'fb_gsjwysgzxdjlj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1712, 34, '腹部-移动浊音', 'fb_ydzy', '0', NULL, 'text', NULL, 'fb_ydzy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1713, 34, '腹部-肾区叩痛', 'fb_sqkt', '0', NULL, 'text', NULL, 'fb_sqkt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1714, 34, '腹部-听诊肠鸣音', 'fb_tzcmy', '0', NULL, 'text', NULL, 'fb_tzcmy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1715, 34, '腹部-生殖器', 'fb_szq', '0', NULL, 'text', NULL, 'fb_szq', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1716, 34, '腹部-肛门直肠', 'fb_gmzc', '0', NULL, 'text', NULL, 'fb_gmzc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1717, 34, '脊柱四肢-脊柱外形总体描述', 'jzsz_jzwxztms', '0', NULL, 'text', NULL, 'jzsz_jzwxztms', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1718, 34, '脊柱四肢-脊柱外形', 'jzsz_jzwx', '0', NULL, 'text', NULL, 'jzsz_jzwx', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1719, 34, '脊柱四肢-棘突压痛', 'jzsz_jtyt', '0', NULL, 'text', NULL, 'jzsz_jtyt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1720, 34, '脊柱四肢-棘突叩痛', 'jzsz_jtkt', '0', NULL, 'text', NULL, 'jzsz_jtkt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1721, 34, '脊柱四肢-四肢', 'jzsz_sz', '0', NULL, 'text', NULL, 'jzsz_sz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1722, 34, '脊柱四肢-关节', 'jzsz_gj', '0', NULL, 'text', NULL, 'jzsz_gj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1723, 34, '神经系统-肌张力', 'sjxt_jzl', '0', NULL, 'text', NULL, 'sjxt_jzl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1724, 34, '神经系统-肢体瘫痪', 'sjxt_ztth', '0', NULL, 'text', NULL, 'sjxt_ztth', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1725, 34, '神经系统-肌力', 'sjxt_jl', '0', NULL, 'text', NULL, 'sjxt_jl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1726, 34, '神经系统-腹壁反射', 'sjxt_fbfs', '0', NULL, 'text', NULL, 'sjxt_fbfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1727, 34, '神经系统-提睾反射', 'sjxt_tgfs', '0', NULL, 'text', NULL, 'sjxt_tgfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1728, 34, '神经系统-膝腱反射', 'sjxt_xjfs', '0', NULL, 'text', NULL, 'sjxt_xjfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1729, 34, '神经系统-肱二头肌腱反射', 'sjxt_getjjfs', '0', NULL, 'text', NULL, 'sjxt_getjjfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1730, 34, '神经系统-肱三头肌腱反射', 'sjxt_gstjjfs', '0', NULL, 'text', NULL, 'sjxt_gstjjfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1731, 34, '神经系统-跟腱反射', 'sjxt_gjfs', '0', NULL, 'text', NULL, 'sjxt_gjfs', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1732, 34, '神经系统-Babinski征', 'sjxt_babinskiz', '0', NULL, 'text', NULL, 'sjxt_babinskiz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1733, 34, '神经系统-Hoffmann氏征', 'sjxt_hoffmannsz', '0', NULL, 'text', NULL, 'sjxt_hoffmannsz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1734, 34, '神经系统-Oppenheim氏征', 'sjxt_oppenheimsz', '0', NULL, 'text', NULL, 'sjxt_oppenheimsz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1735, 34, '神经系统-Gordon氏征', 'sjxt_gordonsz', '0', NULL, 'text', NULL, 'sjxt_gordonsz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1736, 34, '神经系统-Lasegue氏征', 'sjxt_laseguesz', '0', NULL, 'text', NULL, 'sjxt_laseguesz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1737, 34, '神经系统-Kernig征', 'sjxt_kernigz', '0', NULL, 'text', NULL, 'sjxt_kernigz', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1738, 34, '神经系统-踝阵挛', 'sjxt_hzl', '0', NULL, 'text', NULL, 'sjxt_hzl', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1739, 34, '神经系统-其他', 'sjxt_qt', '0', NULL, 'text', NULL, 'sjxt_qt', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1740, 34, '专科情况', 'zkqk', '0', NULL, 'text', NULL, 'zkqk', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1741, 34, '辅助检查', 'fzjc', '0', NULL, 'text', NULL, 'fzjc', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1742, 34, '病历摘要', 'blzy', '0', NULL, 'text', NULL, 'blzy', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1743, 34, '诊疗计划-诊疗计划文本', 'zljh_zljhwb', '0', NULL, 'text', NULL, 'zljh_zljhwb', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1744, 34, '所有初步诊断拼接', 'sycbzdpj', '0', NULL, 'text', NULL, 'sycbzdpj', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1745, 34, '诊疗计划-初步诊断1', 'zljh_cbzd1', '0', NULL, 'text', NULL, 'zljh_cbzd1', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1746, 34, '诊疗计划-初步诊断2', 'zljh_cbzd2', '0', NULL, 'text', NULL, 'zljh_cbzd2', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1747, 34, '诊疗计划-初步诊断3', 'zljh_cbzd3', '0', NULL, 'text', NULL, 'zljh_cbzd3', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1748, 34, '诊疗计划-初步诊断4', 'zljh_cbzd4', '0', NULL, 'text', NULL, 'zljh_cbzd4', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1749, 34, '诊疗计划-初步诊断5', 'zljh_cbzd5', '0', NULL, 'text', NULL, 'zljh_cbzd5', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1750, 34, '诊疗计划-初步诊断6', 'zljh_cbzd6', '0', NULL, 'text', NULL, 'zljh_cbzd6', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1751, 34, '诊疗计划-初步诊断7', 'zljh_cbzd7', '0', NULL, 'text', NULL, 'zljh_cbzd7', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1752, 34, '诊疗计划-初步诊断8', 'zljh_cbzd8', '0', NULL, 'text', NULL, 'zljh_cbzd8', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1753, 34, '诊疗计划-初步诊断9', 'zljh_cbzd9', '0', NULL, 'text', NULL, 'zljh_cbzd9', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1754, 34, '诊疗计划-初步诊断10', 'zljh_cbzd10', '0', NULL, 'text', NULL, 'zljh_cbzd10', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1755, 34, '诊疗计划-初步诊断11', 'zljh_cbzd11', '0', NULL, 'text', NULL, 'zljh_cbzd11', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1756, 34, '诊疗计划-初步诊断12', 'zljh_cbzd12', '0', NULL, 'text', NULL, 'zljh_cbzd12', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1757, 34, '诊疗计划-初步诊断13', 'zljh_cbzd13', '0', NULL, 'text', NULL, 'zljh_cbzd13', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1758, 34, '诊疗计划-初步诊断14', 'zljh_cbzd14', '0', NULL, 'text', NULL, 'zljh_cbzd14', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1759, 34, '诊疗计划-初步诊断15', 'zljh_cbzd15', '0', NULL, 'text', NULL, 'zljh_cbzd15', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1760, 34, '诊疗计划-初步诊断16', 'zljh_cbzd16', '0', NULL, 'text', NULL, 'zljh_cbzd16', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1761, 34, '诊疗计划-初步诊断17', 'zljh_cbzd17', '0', NULL, 'text', NULL, 'zljh_cbzd17', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1762, 34, '诊疗计划-初步诊断18', 'zljh_cbzd18', '0', NULL, 'text', NULL, 'zljh_cbzd18', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1763, 34, '诊疗计划-初步诊断19', 'zljh_cbzd19', '0', NULL, 'text', NULL, 'zljh_cbzd19', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1764, 34, '诊疗计划-初步诊断20', 'zljh_cbzd20', '0', NULL, 'text', NULL, 'zljh_cbzd20', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1774, 35, '住院总费用', 'D01', '0', NULL, 'varchar', NULL, 'D01', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1775, 35, '住院总费用其中自付金额', 'D09', '0', NULL, 'varchar', NULL, 'D09', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1776, 35, '1.一般医疗服务费', 'D11', '0', NULL, 'varchar', NULL, 'D11', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1777, 35, '2.一般治疗操作费', 'D12', '0', NULL, 'varchar', NULL, 'D12', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1778, 35, '3.护理费', 'D13', '0', NULL, 'varchar', NULL, 'D13', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1779, 35, '4.综合医疗服务类其他费用', 'D14', '0', NULL, 'varchar', NULL, 'D14', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1780, 35, '5.病理诊断费', 'D15', '0', NULL, 'varchar', NULL, 'D15', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1781, 35, '6.实验室诊断费', 'D16', '0', NULL, 'varchar', NULL, 'D16', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1782, 35, '7.影像学诊断费', 'D17', '0', NULL, 'varchar', NULL, 'D17', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1783, 35, '8.临床诊断项目费', 'D18', '0', NULL, 'varchar', NULL, 'D18', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1784, 35, '9.非手术治疗项目费', 'D19', '0', NULL, 'varchar', NULL, 'D19', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1785, 35, '其中：临床物理治疗费', 'D19x01', '0', NULL, 'varchar', NULL, 'D19x01', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1786, 35, '10.手术治疗费', 'D20', '0', NULL, 'varchar', NULL, 'D20', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1787, 35, '其中：麻醉费', 'D20x01', '0', NULL, 'varchar', NULL, 'D20x01', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1788, 35, '其中：手术费', 'D20x02', '0', NULL, 'varchar', NULL, 'D20x02', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1789, 35, '11.康复费', 'D21', '0', NULL, 'varchar', NULL, 'D21', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1790, 35, '12.中医治疗费', 'D22', '0', NULL, 'varchar', NULL, 'D22', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1791, 35, '13.西药费', 'D23', '0', NULL, 'varchar', NULL, 'D23', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1792, 35, '其中：抗菌药物费', 'D23x01', '0', NULL, 'varchar', NULL, 'D23x01', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1793, 35, '14.中成药费', 'D24', '0', NULL, 'varchar', NULL, 'D24', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1794, 35, '15.中草药费', 'D25', '0', NULL, 'varchar', NULL, 'D25', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1795, 35, '16.血费', 'D26', '0', NULL, 'varchar', NULL, 'D26', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1796, 35, '17.白蛋白类制品费', 'D27', '0', NULL, 'varchar', NULL, 'D27', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1797, 35, '18.球蛋白类制品费', 'D28', '0', NULL, 'varchar', NULL, 'D28', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1798, 35, '19.凝血因子类制品费', 'D29', '0', NULL, 'varchar', NULL, 'D29', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1799, 35, '20.细胞因子类制品费', 'D30', '0', NULL, 'varchar', NULL, 'D30', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1800, 35, '21.检查用一次性医用材料费', 'D31', '0', NULL, 'varchar', NULL, 'D31', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1801, 35, '22.治疗用一次性医用材料费', 'D32', '0', NULL, 'varchar', NULL, 'D32', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1802, 35, '23.手术用一次性医用材料费', 'D33', '0', NULL, 'varchar', NULL, 'D33', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1803, 35, '24.其他费：', 'D34', '0', NULL, 'varchar', NULL, 'D34', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1804, 35, '数据来源标识', 'DataSrcCode', '0', NULL, 'varchar', NULL, 'DataSrcCode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1805, 35, '是否物理删除', 'IsDeleted', '0', NULL, 'varchar', NULL, 'IsDeleted', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1806, 35, '源最后更新时间', 'LastUpdateDtTm', '0', NULL, 'varchar', NULL, 'LastUpdateDtTm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1807, 35, '目标最后更新时间', 'LastImportDtTm', '0', NULL, 'varchar', NULL, 'LastImportDtTm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1808, 35, '组织机构代码', 'A01', '0', NULL, 'varchar', NULL, 'A01', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1809, 35, '医疗机构名称', 'A02', '0', NULL, 'varchar', NULL, 'A02', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1810, 35, '病案号', 'A48', '0', NULL, 'varchar', NULL, 'A48', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1811, 35, '住院次数', 'A49', '0', NULL, 'int', NULL, 'A49', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1812, 35, '姓名', 'A11', '0', NULL, 'varchar', NULL, 'A11', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1813, 35, '出院时间', 'B15', '0', NULL, 'varchar', NULL, 'B15', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1816, 21, '模板代码', 'case_nursingdet_templatecode', '0', NULL, 'varchar', NULL, 'case_nursingdet_templatecode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1817, 21, '模板名称', 'case_nursingdet_templatename', '0', NULL, 'varchar', NULL, 'case_nursingdet_templatename', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1818, 21, '字段内容', 'case_nursingdet_datasetvalue', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetvalue', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1819, 21, '数据集代码', 'case_nursingdet_datasetcode', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetcode', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1820, 21, '字段名称', 'case_nursingdet_datasetname', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetname', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1821, 21, '最后更新时间', 'case_nursingdet_lastupdatedttm', '0', NULL, 'varchar', NULL, 'case_nursingdet_lastupdatedttm', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1822, 21, '评估时间', 'case_nursingdet_datasetvalue_datetime', '0', NULL, 'varchar', NULL, 'case_nursingdet_datasetvalue_datetime', NULL, 1, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1823, 12, '检查报告ID', 'report_imageno', '0', 0, 'varchar', 100, 'report_main_imageno', 0, 0, 0, 0, 0);
INSERT INTO `rp_rdr_field` VALUES (1825, 19, '执行医生', 'ord_execpersname', '0', 0, 'varchar', 100, 'order_order_execpersname', 0, 1, 0, 0, 0);



