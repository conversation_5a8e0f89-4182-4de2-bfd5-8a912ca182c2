package com.boot.modules.test;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boot.commons.enums.PatStatusEnum;
import com.boot.commons.enums.PatTypeEnum;
import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.commons.utils.DateUtils;
import com.boot.commons.utils.ListUtils;
import com.boot.commons.utils.MapUtils;
import com.boot.modules.external.service.HisService;
import com.boot.modules.openApi.service.EdcService;
import com.boot.modules.patient.dao.DoctorRecommendationDao;
import com.boot.modules.patient.entity.DoctorRecommendationEntity;
import com.boot.modules.patient.entity.InboundProcessEntity;
import com.boot.modules.patient.entity.PatientEntity;
import com.boot.modules.patient.service.*;
import com.boot.modules.project.entity.ProjDeptEntity;
import com.boot.modules.project.entity.ProjectEntity;
import com.boot.modules.project.entity.SubProjectEntity;
import com.boot.modules.project.entity.UserSubProjectEntity;
import com.boot.modules.project.enums.ProjectStudyTypeEnum;
import com.boot.modules.project.service.ProjDeptService;
import com.boot.modules.project.service.ProjectService;
import com.boot.modules.project.service.SubProjectService;
import com.boot.modules.project.service.UserSubProjectService;
import com.boot.modules.project.vo.ProjectVo;
import com.boot.modules.sync.utils.JwtForKelin;
import com.boot.modules.sys.entity.SysDeptEntity;
import com.boot.modules.sys.entity.SysRoleEntity;
import com.boot.modules.sys.service.SysDeptService;
import com.boot.modules.sys.service.SysRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.Cache;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "测试")
@RestController
@RequestMapping("/test")
public class TestController {
    @Resource
    private ProjectService projectService;

    @Resource
    private SysRoleService roleService;

    @Resource
    private ProjDeptService projDeptService;

    @Resource
    private UserSubProjectService userSubProjectService;

    @Resource
    private SubProjectService subProjectService;

    @Resource
    private PatientService patientService;

    @Resource
    private InboundProcessService inboundProcessService;

    @Resource
    private DoctorRecommendationService doctorRecommendationService;

    @Resource
    private SysDeptService deptService;

    @Resource
    private EdcService edcService;

    /**
     * 增加假数据
     *
     * @return
     */
    @ApiOperation(
            value = "优化添加项目管理员数据",
            notes = "优化添加项目管理员数据"
    )
    @GetMapping("/upgrade/project/admin")
    public Result list() {
        List<ProjectEntity> list = projectService.list();
        List<SysDeptEntity> deptEntities = deptService.getAll();
        if (CollectionUtils.isEmpty(list)) {
            return R.success();
        }

        List<SysRoleEntity> roleList = roleService.list(new QueryWrapper<SysRoleEntity>().lambda()
                .eq(SysRoleEntity::getIsProjAdmin, 1));

        Long adminRoleId = roleList.get(0).getId();

        List<ProjDeptEntity> list1 = projDeptService.list(new QueryWrapper<ProjDeptEntity>().lambda()
                .eq(ProjDeptEntity::getIsPrimary, 1));

        Map<Long, Long> map = list1.stream().collect(Collectors.toMap(ProjDeptEntity::getProjectId, ProjDeptEntity::getDeptId));
        Map<Long, Long> projAdminMap = list.stream().collect(Collectors.toMap(ProjectEntity::getId, ProjectEntity::getProjectAdminId));

        Set<Long> projectIdSet = projAdminMap.keySet();
        List<SubProjectEntity> subprojectList = subProjectService.list(new QueryWrapper<SubProjectEntity>().lambda().in(SubProjectEntity::getProjectId, projectIdSet));
        Map<Long, Long> subproProjectMap = subprojectList.stream().collect(Collectors.toMap(SubProjectEntity::getId, SubProjectEntity::getProjectId));
        List<UserSubProjectEntity> userSubProjectEntityList = new ArrayList<>();

        for (Map.Entry<Long, Long> subproProjectEntry : subproProjectMap.entrySet()) {
            Long subprojectId = subproProjectEntry.getKey();
            Long projectId = subproProjectEntry.getValue();
            Long adminId = projAdminMap.get(projectId);

            int count = userSubProjectService.count(new QueryWrapper<UserSubProjectEntity>().lambda()
                    .eq(UserSubProjectEntity::getSubProjectId, subprojectId)
                    .eq(UserSubProjectEntity::getUserId, adminId));

            if (count > 0) {
                continue;
            }

            UserSubProjectEntity userSubProjectEntity = new UserSubProjectEntity();
            userSubProjectEntity.setUserId(adminId);
            userSubProjectEntity.setSubProjectId(subprojectId);
            userSubProjectEntity.setProjRoleId(adminRoleId);
            userSubProjectEntity.setProjectDeptId(map.get(projectId));
            // 递归获取最上层科室信息
            SysDeptEntity sysDeptEntity = deptService.getTopDeptById(deptEntities, map.get(projectId));
            userSubProjectEntity.setTopDeptId(sysDeptEntity.getId());
            userSubProjectEntityList.add(userSubProjectEntity);
        }

        userSubProjectService.addBatch(userSubProjectEntityList);

        return R.success();
    }

    /**
     * 更新患者、医生推荐表的状态值
     */
    @ApiOperation(
            value = "更新状态值",
            notes = "更新状态值"
    )
    @GetMapping("/upgrade/status")
    public Result upgrade() {
        List<ProjectEntity> projectEntityList = projectService.list();
        if (CollectionUtils.isEmpty(projectEntityList)) {
            return R.success();
        }

        List<InboundProcessEntity> inboundProcessEntityList = inboundProcessService.list();
        Map<Long, List<InboundProcessEntity>> inboundGroupByPrj = inboundProcessEntityList.stream().collect(Collectors.groupingBy(o -> o.getProjectId()));
        List<DoctorRecommendationEntity> doctorRecommendationEntityList = doctorRecommendationService.list();
        Map<Long, List<DoctorRecommendationEntity>> doctorGroupByPrj = doctorRecommendationEntityList.stream().collect(Collectors.groupingBy(o -> o.getProjectId()));
        for (ProjectEntity project : projectEntityList) {
            List<InboundProcessEntity> inboundProjectList = inboundGroupByPrj.get(project.getId());
            List<DoctorRecommendationEntity> doctorProjectList = doctorGroupByPrj.get(project.getId());
            if (CollectionUtils.isEmpty(doctorProjectList)) {
                continue;
            }
            for (DoctorRecommendationEntity recommendationEntity : doctorProjectList) {
                if (CollectionUtils.isEmpty(inboundProjectList)) {
                    continue;
                }
                List<InboundProcessEntity> curRecommend = inboundProjectList.stream().filter(p -> p.getDoctorRecommendId().equals(recommendationEntity.getId())).collect(Collectors.toList());

                // 已推荐无入组，推荐表记录为推荐状态
                if (CollectionUtils.isEmpty(curRecommend)) {
                    recommendationEntity.setStatus(PatTypeEnum.RECOMMENDED.getCode());
                    doctorRecommendationService.updateById(recommendationEntity);
                    continue;
                }
                // 已推荐已入组，按入组状态设置推荐表状态，撤回状态特殊处理为推荐状态
                Integer status = curRecommend.get(curRecommend.size() - 1).getStatus().equals(PatTypeEnum.REVERT.getCode()) ? PatTypeEnum.RECOMMENDED.getCode() : curRecommend.get(curRecommend.size() - 1).getStatus();
                recommendationEntity.setStatus(status);
                doctorRecommendationService.updateById(recommendationEntity);
                // GCP或干预性IIT项目下拟入组或已入组时，锁定病例
                if ((status.equals(PatTypeEnum.INBOUNDED.getCode()) || status.equals(PatTypeEnum.PROPOSE_INBOUND.getCode()))
                        && inboundProcessService.isLockProject(project)) {
                    UpdateWrapper<PatientEntity> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.set("status", PatStatusEnum.LOCK.getCode());
                    updateWrapper.eq("empiid", recommendationEntity.getEmpiid());
                    patientService.update(updateWrapper);
                }
            }
        }

        return R.success();
    }

    /**
     * 更新到期时长
     */
    @ApiOperation(
            value = "更新到期时长",
            notes = "更新到期时长"
    )
    @GetMapping("/upgrade/time")
    public Result upgradeTime() throws ParseException {
        List<ProjectEntity> projectEntityList = projectService.list();
        if (CollectionUtils.isEmpty(projectEntityList)) {
            return R.success();
        }
        for (ProjectEntity projectEntity : projectEntityList) {

            //1.获取所有未签署确认书入组记录
            LambdaQueryWrapper<InboundProcessEntity> qw = new QueryWrapper<InboundProcessEntity>().lambda()
                    .eq(InboundProcessEntity::getProjectId, projectEntity.getId())
                    .eq(InboundProcessEntity::getStatus, PatTypeEnum.PROPOSE_INBOUND.getCode());

            List<InboundProcessEntity> inboundProcessEntities = inboundProcessService.list(qw);

            if (CollectionUtils.isEmpty(inboundProcessEntities)) {
                continue;
            }

            String now = DateUtils.getNowTimeStr(DateUtils.DATE_TIME_PATTERN);
            // 2.更新入组入组记录剩余时间
            for (InboundProcessEntity inboundProcessEntity : inboundProcessEntities) {
                String inboundTime = inboundProcessEntity.getInboundTime();
                String signEndTime = DateUtils.getAfterDay(inboundTime, projectEntity.getSignDateline() == null ? "3" : projectEntity.getSignDateline().toString());
                Integer signExpireDays = DateUtils.getAbDistanceDays(now, signEndTime);

                inboundProcessEntity.setSignExpireDays(signExpireDays);
            }

            // 4.批量更新受试者入组状态
            inboundProcessService.updateBatchById(inboundProcessEntities);
        }
        return R.success();
    }

    /**
     * 批量更新项目成员的科室ID
     */
    @ApiOperation(
            value = "批量更新项目成员的科室ID",
            notes = "批量更新项目成员的科室ID"
    )
    @GetMapping("/upgrade/userSubProject")
    public Result upgradeUserSubProject() throws ParseException {
        List<UserSubProjectEntity> userSubProjectEntityList = userSubProjectService.list();
        List<ProjDeptEntity> projDeptEntityList = projDeptService.list();

        // 获取所有机构信息
        List<SysDeptEntity> deptEntities = deptService.getAll();

        // 更新项目科室的top级信息
        for (ProjDeptEntity projDeptEntity : projDeptEntityList) {
            // 递归获取最上层科室信息
            SysDeptEntity sysDeptEntity = deptService.getTopDeptById(deptEntities, projDeptEntity.getDeptId());
            projDeptEntity.setTopDeptId(sysDeptEntity.getId());
        }
        projDeptService.updateBatchById(projDeptEntityList);

        for (UserSubProjectEntity userSubProjectEntity : userSubProjectEntityList) {
            Long id = userSubProjectEntity.getProjectDeptId();
            List<ProjDeptEntity> projDeptEntities = projDeptEntityList.stream().filter(p -> p.getId().equals(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(projDeptEntities)) {
                userSubProjectEntity.setProjectDeptId(projDeptEntities.get(0).getDeptId());
                userSubProjectEntity.setTopDeptId(projDeptEntities.get(0).getTopDeptId());
            }
        }
        userSubProjectService.updateBatchById(userSubProjectEntityList);
        return R.success();
    }

    /**
     * 更新到期时长
     */
    @ApiOperation(
            value = "删除项目",
            notes = "删除项目"
    )
    @GetMapping("/delete/project/{projectId}")
    public Result deleteProject(@PathVariable(value = "projectId") Long projectId, @RequestParam String skipIdStr) throws ParseException {
        List<ProjectEntity> projectEntityList = projectService.list(
                new QueryWrapper<ProjectEntity>().lambda().lt(ProjectEntity::getId, projectId)
        );
        List<Long> projectIds = projectEntityList.stream().map(ProjectEntity::getId).collect(Collectors.toList());
        List<Long> skipIds = new ArrayList<>();
        for (String id : skipIdStr.split(",")) {
            skipIds.add(Long.parseLong(id));
        }
        List<Long> deleteIds = ListUtils.getAddListThanList(projectIds, skipIds);
        boolean res = projectService.removeProjects(deleteIds);
        return R.success();
    }

    @Resource
    private HisService hisService;

    @ApiOperation(
            value = "获取his",
            notes = "获取his"
    )
    @GetMapping("/his")
    public Object getHis() {
        return hisService.getDisChgCount("2020-01-01", "2020-01-06", "=");
    }

    @ApiOperation(
            value = "获取his",
            notes = "获取his"
    )
    @GetMapping("/his/jwt")
    public Object getHisJwt() {
        return JwtForKelin.getHisJwt();
    }

    @ApiOperation(
            value = "同步项目信息",
            notes = "同步项目信息"
    )
    @GetMapping("/sync/project")
    public Object syncProject() {
        List<ProjectVo> projectVos = projectService.listAll();
        projectVos.forEach(projectVo -> {
            projectVo.setIsImport(true);
            projectVo.setType(4);
            projectVo.setStatus(1);
            Integer projectType = projectVo.getProjectType();

            if (projectType != null) {
                switch (ProjectStudyTypeEnum.getByCode(projectType)) {
                    case GCP:
                        projectVo.setSource(2);
                        break;
                    case INTERVENTION_IIT:
                        projectVo.setSource(3);
                        break;
                    case SPECIALIZED_DISEASE_QUEUE:
                        projectVo.setSource(4);
                        break;
                    case PROSPECTIVE_OBSERVATION:
                        projectVo.setSource(5);
                        break;
                    default:
                        break;
                }
            }
            edcService.addProject(projectVo);
        });
        return projectVos;
    }


    @ApiOperation(
            value = "同步指定项目病例信息",
            notes = "同步指定项目病例信息"
    )
    @GetMapping("/sync/patient")
    public void syncPatient(@RequestParam Map<String, Object> params) {
        Long projectId = MapUtils.getValue(params, "projectId", Long.class);
        List<String> empiList = params.get("empiList") == null ? new ArrayList<>() : (List<String>) params.get("empiList");
        patientService.sync(projectId, empiList);
    }

    @Resource
    private DoctorRecommendationDao doctorRecommendationDao;

    @ApiOperation(
            value = "更新推荐医疗机构首级的ID",
            notes = "更新推荐医疗机构首级的ID"
    )
    @GetMapping("/update/topDeptId")
    public void updateTopDeptId() {
        // 递归获取最上层科室信息
        List<SysDeptEntity> allDept = deptService.getAll();
        List<Long> recommendDeptIds = doctorRecommendationDao.getRecommendDeptId(new QueryWrapper<DoctorRecommendationEntity>().lambda().isNull(DoctorRecommendationEntity::getTopRecommendDeptId));

        for (Long deptId : recommendDeptIds) {
            if (deptId == null) {
                continue;
            }
            SysDeptEntity top = deptService.getTopDeptById(allDept, deptId);
            UpdateWrapper<DoctorRecommendationEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(DoctorRecommendationEntity::getRecommendDeptId, deptId);
            updateWrapper.lambda().set(DoctorRecommendationEntity::getTopRecommendDeptId, top.getId());
            doctorRecommendationService.update(updateWrapper);
        }
    }

    @ApiOperation(
            value = "更新项目所属机构ID",
            notes = "更新项目所属机构ID"
    )
    @GetMapping("/update/medId")
    public void updateMedId() {
        // 递归获取最上层科室信息
        List<SysDeptEntity> allDept = deptService.getAll();

        List<ProjectEntity> projectEntityList = projectService.list();
        for (ProjectEntity entity : projectEntityList) {
            if (entity.getMainDeptId() == null) {
                continue;
            }
            SysDeptEntity top = deptService.getTopDeptById(allDept, entity.getMainDeptId());
            UpdateWrapper<ProjectEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ProjectEntity::getId, entity.getId());
            updateWrapper.lambda().set(ProjectEntity::getMainMedId, top.getId());
            projectService.update(updateWrapper);
        }
    }

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @ApiOperation(
            value = "清理mapper缓存",
            notes = "清理mapper缓存"
    )
    @PostMapping("/clear-mapper")
    public Result clearAllCaches() {

        Collection<Cache> caches = sqlSessionFactory.getConfiguration().getCaches();

        caches.forEach(cache -> {
            cache.clear();
        });

        return R.success();
    }

    @ApiOperation(
            value = "对移动端患者信息同步",
            notes = "对移动端患者信息同步"
    )
    @PostMapping("/reset-info")
    public Result resetInfo() {
        patientService.resetInfo();

        return R.success();
    }

    @ApiOperation(
            value = "测试",
            notes = "测试"
    )
    @PostMapping("/test")
    public Result test() {
        String admDeptValue = "aa\n^^bb\n^^cc";
        List<String> filterDeptList = Arrays.asList(admDeptValue.replaceAll("\\n", "").split("\\^\\^"));

        return R.success();
    }

    @Resource
    private DeptRecommendCountService deptRecommendCountService;
    @Resource
    private RecommendCountService recommendCountService;

    @ApiOperation(
            value = "生成报表中间表",
            notes = "生成报表中间表"
    )
    @PostMapping("/update/mid")
    public Result mid() {
        deptRecommendCountService.updateNewDate();
        recommendCountService.updateNewDate();
        return R.success();
    }
}
