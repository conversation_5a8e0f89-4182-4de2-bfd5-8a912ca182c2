package com.boot.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boot.modules.mobile.vo.GroupConfigVO;
import com.boot.modules.project.entity.GroupConfigEntity;
import com.boot.modules.sys.entity.SysUserEntity;

import java.util.List;
import java.util.Map;

public interface GroupConfigService extends IService<GroupConfigEntity> {

    /**
     * 根据子项目id与类型获取对应的纳排条件
     *
     * @param subProjectId
     * @param type
     * @return
     */
    List<GroupConfigEntity> listBySubProjId(long subProjectId, Integer type, Integer isYlt);

    /**
     * 获取简略信息纳排
     * @param subProjectId
     * @param type
     * @return
     */
    List<GroupConfigVO> listYltVOBySubProjId(long subProjectId, Integer type);


    /**
     * 批量更新指定纳排条件排序
     * @param groupConfigEntityList
     */
    void updateSort(List<GroupConfigEntity> groupConfigEntityList);

    /**
     * 新增纳排
     *
     * @param groupConfigEntity
     * @param user
     * @return
     */
    Boolean saveConfig(GroupConfigEntity groupConfigEntity, SysUserEntity user);

    /**
     * 修改纳排
     *
     * @param groupConfigEntity
     * @param user
     * @return
     */
    Boolean updateConfig(GroupConfigEntity groupConfigEntity, SysUserEntity user);

    /**
     * 执行纳排
     *
     * @param subProjectId
     * @param user
     * @return
     */
    Boolean execConfig(Long subProjectId, SysUserEntity user);

    /**
     * 查病例的redis
     *
     * @param empi
     * @return
     */
    Map<String, List<String>> getPatientMatchRedis(String empi);

    Map<String, List<Long>> getIsMatch(Long subProjectId, Map<String, List<String>> patientRedis);

    /**
     * 批量新增纳排
     *
     * @param groupConfigEntitys
     * @param user
     * @return
     */
    Boolean saveConfigs(List<GroupConfigEntity> groupConfigEntitys, SysUserEntity user);
}
