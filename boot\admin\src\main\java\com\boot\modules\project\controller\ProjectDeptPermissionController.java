package com.boot.modules.project.controller;

import com.boot.commons.result.R;
import com.boot.commons.result.Result;
import com.boot.modules.project.entity.ProjDeptPermissionEntity;
import com.boot.modules.project.service.ProjectDeptPermissionService;
import com.boot.modules.project.vo.ProjDeptPermissionVo;
import com.boot.modules.project.vo.ProjDeptsVo;
import com.boot.modules.sys.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

//import com.boot.modules.sys.entity.SysUserDeptEntity;
//import com.boot.modules.sys.service.SysUserDeptService;

/**
 * <AUTHOR>
 * @desc : 项目机构权限管理
 * @create 2022-12-05
 */
@Api(value = "项目机构权限管理", tags = "项目机构权限管理")
@RestController
@RequestMapping("/project/dept/permission")
public class ProjectDeptPermissionController extends AbstractController {

    @Resource
    private ProjectDeptPermissionService projectDeptPermissionService;

    /**
     * 指定项目的权限列表
     *
     * @param projectId
     * @return
     */
    @ApiOperation(
            value = "指定项目的权限列表",
            notes = "指定项目的权限列表"
    )
    @GetMapping("/list")
//    @RequiresPermissions("user:dept:list")
    public Result list(@RequestParam(required = false) Long projectId) {
        List<ProjDeptPermissionVo> list = projectDeptPermissionService.getProjectPermission(projectId);
        return R.success(list);
    }

    /**
     * 获取指定id的数据
     *
     * @param id
     * @return
     */
    @ApiOperation(
            value = "指定Id的数据",
            notes = "指定Id的数据"
    )
    @GetMapping("{id}")
//    @RequiresPermissions("user:dept:info")
    public Result info(@PathVariable Long id) {
        ProjDeptPermissionEntity entity = projectDeptPermissionService.getById(id);
        return R.success(entity);
    }

    @ApiOperation(
            value = "新增或者修改用户的机构权限",
            notes = "新增或者修改用户的机构权限"
    )
    @PostMapping("saveOrUpdate")
//    @RequiresPermissions("user:dept:save")
    public Result saveOrUpdate(@RequestBody ProjDeptsVo vo) {

        projectDeptPermissionService.saveOrUpdate(vo);
        return R.success("修改成功");
    }
}

