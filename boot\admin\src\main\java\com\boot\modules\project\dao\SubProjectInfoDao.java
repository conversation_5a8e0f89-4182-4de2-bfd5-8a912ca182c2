package com.boot.modules.project.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.boot.modules.project.vo.ProjDeptVo;
import com.boot.modules.project.vo.SubProjectInfoVo;
import com.boot.modules.project.vo.SubProjectVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SubProjectInfoDao extends BaseMapper<SubProjectInfoVo> {
    IPage<SubProjectInfoVo> queryPage(IPage<SubProjectInfoVo> pageFilter, @Param(Constants.WRAPPER) QueryWrapper<SubProjectInfoVo> qw);

    List<SubProjectInfoVo> myProject(@Param(Constants.WRAPPER) QueryWrapper<SubProjectInfoVo> qw);

    List<SubProjectInfoVo> theProject(@Param(Constants.WRAPPER) QueryWrapper<SubProjectInfoVo> qw);

    List<ProjDeptVo> projectCenter(@Param(Constants.WRAPPER) QueryWrapper<ProjDeptVo> qw);
}
